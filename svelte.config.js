import adapter from '@sveltejs/adapter-static';
import { sveltePreprocess } from 'svelte-preprocess';

/** @type {import('@sveltejs/kit').Config} */
const config = {
	// Consult https://github.com/sveltejs/svelte-preprocess
	// for more information about preprocessors
	preprocess: sveltePreprocess(),
	kit: {
		adapter: adapter(),
	},
	vitePlugin: {
		inspector: true,
	},
	compilerOptions: {
		experimental: {
			async: true,
		},
	},
};

export default config;
