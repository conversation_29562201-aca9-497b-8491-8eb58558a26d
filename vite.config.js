import { sentrySvelteKit } from '@sentry/sveltekit';
import { sveltekit } from '@sveltejs/kit/vite';
import { svelte } from '@sveltejs/vite-plugin-svelte';
import tailwindcss from '@tailwindcss/vite';
import dotenv from 'dotenv';

dotenv.config();

/** @type {import('vite').UserConfig} */
const config = {
	plugins: [
		sentrySvelteKit({
			sourceMapsUploadOptions: {
				authToken: process.env.SENTRY_AUTH_TOKEN,
				org: 'lofi-and-games',
				project: 'lofi-and-games',
				url: 'https://sentry.io/',
			},
		}),
		sveltekit(),
		tailwindcss(),
	],
	worker: {
		plugins: () => [svelte()],
		format: 'es',
	},
	optimizeDeps: {
		exclude: ['fsevents'],
	},
};

export default config;
