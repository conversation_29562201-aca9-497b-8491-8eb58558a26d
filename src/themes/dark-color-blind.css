[data-theme='dark'][data-color-blind='true'],
[data-theme='dark-classic'][data-color-blind='true'] {
	--color-game-tents-floor: var(--color-gray-700);
	--color-game-tents-grass: var(--color-teal-500);
	--color-game-tents-grass-autofill: var(--color-teal-300);
	--color-game-tents-tree-leaves: var(--color-emerald-700);
	--color-game-tents-tree-wood: var(--color-yellow-900);
	--color-game-tents-tent-100: var(--color-yellow-700);
	--color-game-tents-tent-200: var(--color-yellow-800);
	--color-game-tents-tent-300: var(--color-yellow-950);
	--color-game-tents-tent-error-100: var(--color-red-700);
	--color-game-tents-tent-error-200: var(--color-red-800);
	--color-game-tents-tent-error-300: var(--color-red-950);
	--color-game-tents-number-error: var(--color-red-700);
	--color-game-tents-number-success: var(--color-emerald-200);
	--color-game-tents-tent-error-indicator-stroke: var(--color-gray-300);
	--color-game-tents-tent-error-indicator: var(--color-red-700);
	--color-game-wordle-not-present: var(--color-gray-400);
	--color-game-wordle-not-present-hover: var(--color-gray-500);
	--color-game-wordle-correct: var(--color-purple-900);
	--color-game-wordle-correct-hover: var(--color-purple-950);
	--color-game-wordle-misplaced: var(--color-orange-700);
	--color-game-wordle-misplaced-hover: var(--color-orange-800);
	--color-game-wordle-validated-text: var(--color-white);
	--color-game-wordle-border: var(--color-gray-500);
	--color-game-solitaire-field: #219653;
	--color-game-breakout-tile-1: var(--color-red-400);
	--color-game-breakout-tile-2: var(--color-orange-400);
	--color-game-breakout-tile-3: var(--color-yellow-400);
	--color-game-breakout-tile-4: var(--color-green-400);
	--color-game-breakout-tile-5: var(--color-blue-400);
	--color-game-breakout-tile-6: var(--color-purple-400);
	--color-game-tic-tac-toe-x: var(--color-orange-200);
	--color-game-tic-tac-toe-o: var(--color-purple-500);
	--color-game-color-memory-1: var(--color-cyan-300);
	--color-game-color-memory-2: var(--color-lime-200);
	--color-game-color-memory-3: var(--color-yellow-500);
	--color-game-color-memory-4: var(--color-purple-700);
	--color-game-word-search-1: var(--color-red-700);
	--color-game-word-search-2: var(--color-orange-700);
	--color-game-word-search-3: var(--color-amber-700);
	--color-game-word-search-4: var(--color-yellow-700);
	--color-game-word-search-5: var(--color-lime-700);
	--color-game-word-search-6: var(--color-green-700);
	--color-game-word-search-7: var(--color-emerald-700);
	--color-game-word-search-8: var(--color-teal-700);
	--color-game-word-search-9: var(--color-cyan-700);
	--color-game-word-search-10: var(--color-sky-700);
	--color-game-word-search-11: var(--color-blue-700);
	--color-game-word-search-12: var(--color-indigo-700);
	--color-game-word-search-13: var(--color-violet-700);
	--color-game-word-search-14: var(--color-purple-700);
	--color-game-word-search-15: var(--color-fuchsia-700);
	--color-game-word-search-16: var(--color-pink-700);
	--color-game-word-search-17: var(--color-rose-700);
	--color-game-2048-2: var(--color-orange-100);
	--color-game-2048-4: var(--color-orange-200);
	--color-game-2048-8: var(--color-orange-500);
	--color-game-2048-16: var(--color-orange-600);
	--color-game-2048-32: var(--color-green-600);
	--color-game-2048-64: var(--color-green-700);
	--color-game-2048-128: var(--color-purple-700);
	--color-game-2048-512: var(--color-purple-800);
	--color-game-2048-4096: var(--color-violet-900);
	--color-game-2048-16384: var(--color-gray-600);
	--color-game-2048-text-below-8: var(--color-gray-700);
	--color-game-2048-text-above-8: var(--color-white);
	--color-game-2048-bg: var(--color-base-300);
	--color-game-sudoku-tile-normal: var(--color-base-100);
	--color-game-sudoku-tile-highlighted: var(--color-gray-600);
	--color-game-sudoku-tile-selected: var(--color-gray-800);
	--color-game-sudoku-tile-immutable: var(--color-gray-700);
	--color-game-sudoku-tile-hint: var(--color-yellow-600);
	--color-game-sudoku-tile-error: var(--color-red-900);
	--color-game-sudoku-tile-border-match: var(--color-gray-500);
	--color-game-sudoku-tile-border-match-selected: var(--color-gray-200);
	--color-game-sudoku-number-normal: var(--color-blue-500);
	--color-game-sudoku-number-hint: var(--color-yellow-200);
	--color-game-sudoku-number-error: var(--color-red-400);
	--color-game-sudoku-check-line: var(--color-red-500);
	--color-game-sudoku-board-inner-lines: var(--color-base-300);
	--color-game-sudoku-board-outer-lines: var(--color-base-300);
	--color-game-minesweeper-grid: var(--color-base-content);
	--color-game-minesweeper-background: var(--color-base-300);
	--color-game-minesweeper-1: #3477f5;
	--color-game-minesweeper-2: #1fbd53;
	--color-game-minesweeper-3: #ed3c3c;
	--color-game-minesweeper-4: #9e4bf6;
	--color-game-minesweeper-5: #f86816;
	--color-game-minesweeper-6: #e7aa0c;
	--color-game-minesweeper-7: #d43ded;
	--color-game-minesweeper-8: #11b076;
	--color-game-minesweeper-highlight: var(--color-yellow-300);
	--color-game-minesweeper-highlight-border: var(--color-yellow-500);
	--color-game-lights-out-highlight: var(--color-amber-300);
	--color-game-lights-out-grid: var(--color-base-300);
	--color-game-hitori-initial-bg: var(--color-base-100);
	--color-game-hitori-initial-text: var(--color-base-content);
	--color-game-hitori-eliminated-bg: var(--color-base-300);
	--color-game-hitori-not-eliminated-bg: var(--color-base-content);
	--color-game-hitori-not-eliminated-text: var(--color-base-300);
	--color-game-hitori-board-bg: var(--color-base-300);
	--color-game-star-battle-board-bg: var(--color-base-300);
	--color-game-star-battle-region-1: color-mix(in oklab, var(--color-blue-300) 80%, transparent);
	--color-game-star-battle-region-2: color-mix(in oklab, var(--color-green-300) 80%, transparent);
	--color-game-star-battle-region-3: color-mix(in oklab, var(--color-red-300) 80%, transparent);
	--color-game-star-battle-region-4: color-mix(in oklab, var(--color-yellow-300) 80%, transparent);
	--color-game-star-battle-region-5: color-mix(in oklab, var(--color-purple-300) 80%, transparent);
	--color-game-star-battle-region-6: color-mix(in oklab, var(--color-pink-300) 80%, transparent);
	--color-game-star-battle-region-7: color-mix(in oklab, var(--color-indigo-300) 80%, transparent);
	--color-game-star-battle-region-8: color-mix(in oklab, var(--color-gray-300) 80%, transparent);
	--color-game-star-battle-region-9: color-mix(in oklab, var(--color-orange-300) 80%, transparent);
	--color-game-star-battle-region-10: color-mix(in oklab, var(--color-amber-300) 80%, transparent);
	--color-game-star-battle-region-11: color-mix(in oklab, var(--color-lime-300) 80%, transparent);
	--color-game-star-battle-region-12: color-mix(in oklab, var(--color-emerald-300) 80%, transparent);
	--color-game-star-battle-region-13: color-mix(in oklab, var(--color-teal-300) 80%, transparent);
	--color-game-star-battle-region-14: color-mix(in oklab, var(--color-cyan-300) 80%, transparent);
	--color-game-star-battle-region-15: color-mix(in oklab, var(--color-sky-300) 80%, transparent);
	--color-game-star-battle-region-16: color-mix(in oklab, var(--color-violet-300) 80%, transparent);
	--color-game-star-battle-region-17: color-mix(in oklab, var(--color-fuchsia-300) 80%, transparent);
	--color-game-star-battle-region-18: color-mix(in oklab, var(--color-rose-300) 80%, transparent);
	--color-game-star-battle-star: var(--color-yellow-400);
	--color-game-star-battle-star-stroke: var(--color-base-300);
	--color-game-star-battle-error: var(--color-red-600);

	--confetti-1: var(--color-green-400);
	--confetti-2: var(--color-cyan-400);
	--confetti-3: var(--color-pink-400);
	--confetti-4: var(--color-purple-700);
	--confetti-5: var(--color-teal-400);
	--confetti-6: var(--color-yellow-400);
}