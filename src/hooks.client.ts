import * as Sentry from '@sentry/sveltekit';

const httpIntegration = Sentry.httpClientIntegration
	? Sentry.httpClientIntegration({
			failedRequestStatusCodes: [[400, 599]],
			failedRequestTargets: [/api\.lofiandgames\.com.*/, /.*\.supabase\.co/],
		})
	: null;

const replayIntegration = Sentry.replayIntegration
	? Sentry.replayIntegration({
			// Additional SDK configuration goes in here, for example:
			maskAllText: false,
			blockAllMedia: false,
		})
	: null;

Sentry.init({
	dsn: 'https://<EMAIL>/4507924882456576',

	tracesSampleRate: 1.0,

	enabled: import.meta.env.PROD,
	environment: import.meta.env.PROD ? 'production' : 'development',

	// This sets the sample rate to be 10%. You may want this to be 100% while
	// in development and sample at a lower rate in production
	replaysSessionSampleRate: 0,

	// If the entire session is not sampled, use the below sample rate to sample
	// sessions when an error occurs.
	replaysOnErrorSampleRate: 1.0,

	integrations: [httpIntegration, replayIntegration].filter(Boolean) as Array<
		NonNullable<typeof httpIntegration>
	>,
});

// If you have a custom error handler, pass it to `handleErrorWithSentry`
export const handleError = Sentry.handleErrorWithSentry();
