<script lang="ts">
	import { authClient } from '$lib/auth/client';
	import { untrack } from 'svelte';
	import AccountTemplate from './components/AccountTemplate.svelte';
	import { toast } from 'svelte-sonner';
	import { getAccountType } from './utils/getAccountType';
	import AccountAction from './components/AccountAction.svelte';
	import capitalize from 'lodash/capitalize';
	import { MetaTags } from 'svelte-meta-tags';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import TrashIcon from '$lib/components/Icons/TrashIcon.svelte';
	import ImageIcon from '$lib/components/Icons/ImageIcon.svelte';
	import PencilIcon from '$lib/components/Icons/PencilIcon.svelte';
	import Avatar from '$lib/components/Avatar.svelte';
	import { cn } from '$lib/util/cn';

	const session = authClient.useSession();
	const maxSizeInMb = 2;
	const maxImageSize = maxSizeInMb * 1024 * 1024;

	let user = $derived($session.data?.user);
	let name = $state('');
	let username = $state('');
	let isLoading = $state(false);
	let avatarInput = $state() as HTMLInputElement;
	let image = $state('') as string | null;
	let isProfileDropdownOpen = $state(false);
	let error = $state(null) as { code?: string; message?: string } | null;

	class UploadImageError extends Error {}
	class DeleteImageError extends Error {}

	$effect(() => {
		if (user) {
			untrack(() => {
				name = user.name;
				username = user.displayUsername ?? '';
				image = user.image ?? null;
			});
		}
	});

	async function save() {
		if (isLoading || !user) {
			return;
		}

		error = null;

		const mustUpdateImage = image !== user.image;

		if (mustUpdateImage) {
			isLoading = true;

			try {
				if (avatarInput.files && avatarInput.files.length > 0) {
					const formData = new FormData();
					formData.append('file', avatarInput.files[0]);

					const res = await fetch(`${import.meta.env.VITE_BETTER_AUTH_URL}/user/image`, {
						method: 'PATCH',
						body: formData,
						credentials: 'include',
					});

					if (!res.ok) {
						throw new UploadImageError('Failed to upload image');
					}

					avatarInput.value = '';
				} else {
					const res = await fetch(`${import.meta.env.VITE_BETTER_AUTH_URL}/user/image`, {
						method: 'DELETE',
						credentials: 'include',
					});

					if (!res.ok) {
						throw new DeleteImageError('Failed to delete image');
					}
				}
			} catch (error) {
				if (error instanceof DeleteImageError) {
					toast.error('Oops, we could not delete your picture. Please try again later.');

					return;
				}

				toast.error('Failed to upload image', {
					description: `Please keep the image size under ${maxSizeInMb}MB and use a supported format (.png, .jpeg, .avif, and .webp)`,
				});

				return;
			} finally {
				isLoading = false;
			}
		}

		const payload = {
			name: user.name !== name ? name : undefined,
			username: user.displayUsername !== username ? username.trim() : undefined,
		};

		isLoading = true;

		authClient.updateUser(payload, {
			onSuccess: () => {
				toast.success('Account updated successfully!');
			},
			onError: (e) => {
				toast.error(
					capitalize(
						e.error.message ?? 'Oops, something went wrong. Please try again later',
					),
				);

				error = e.error;
			},
			onResponse: () => {
				isLoading = false;
			},
			onRequest: () => {
				isLoading = true;
			},
		});
	}

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter' && !isLoading) {
			save();
		}
	}
</script>

<MetaTags
	title="Account Settings"
	titleTemplate="%s | Lofi and Games"
	description="Manage your Lofi and Games account settings, update your profile, and customize your experience."
	canonical="https://www.lofiandgames.com/account"
	openGraph={{
		url: 'https://www.lofiandgames.com/account',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'website',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Account Settings - Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com/account',
	}}
/>

<AccountTemplate title="Account">
	<div class="indicator">
		{#if user}
			<Avatar
				user={{
					...user,
					image,
					name,
				}}
				class="size-20 text-3xl shrink-0"
			/>
		{/if}

		<input
			type="file"
			style="opacity: 0; position: absolute; top: -9999px;"
			accept="image/png, image/jpeg, image/avif, image/webp"
			multiple={false}
			aria-label="Avatar image"
			onchange={() => {
				const file = avatarInput?.files?.[0];

				if (!file?.type.startsWith('image/')) {
					return;
				}

				if (file.size > maxImageSize) {
					toast.error(`Please keep the image size under ${maxSizeInMb}MB`);
					return;
				}

				image = URL.createObjectURL(file);
			}}
			bind:this={avatarInput}
		/>

		<div class="indicator-item">
			<Dropdown bind:open={isProfileDropdownOpen} noBackdrop>
				<DropdownButton aria-label="Edit avatar" class="btn-circle btn-sm">
					<PencilIcon class="size-5" />
				</DropdownButton>

				<DropdownContent menu class="w-48">
					<DropdownItem>
						<button
							onclick={() => {
								avatarInput?.click();
								isProfileDropdownOpen = false;
							}}
						>
							<ImageIcon class="size-5" /> Upload picture
						</button>
					</DropdownItem>

					<DropdownItem>
						<button
							onclick={() => {
								image = null;
								isProfileDropdownOpen = false;
								avatarInput.value = '';
							}}
						>
							<TrashIcon class="size-5" />
							Delete picture
						</button>
					</DropdownItem>
				</DropdownContent>
			</Dropdown>
		</div>
	</div>

	<label class="form-control w-full">
		<div class="label">
			<span class="label-text">Username</span>
		</div>
		<input
			type="text"
			placeholder="Username"
			class={cn('input input-bordered w-full max-w-full', {
				'input-error': [
					'USERNAME_IS_ALREADY_TAKEN_PLEASE_TRY_ANOTHER',
					'INVALID_USERNAME_OR_PASSWORD',
					'USERNAME_IS_TOO_SHORT',
					'USERNAME_IS_TOO_LONG',
					'USERNAME_IS_INVALID',
					'ACCOUNT_NOT_FOUND',
					'CREDENTIAL_ACCOUNT_NOT_FOUND',
					'USER_EMAIL_NOT_FOUND',
					'USER_NOT_FOUND',
				].includes($state.snapshot(error?.code)!),
			})}
			disabled={isLoading}
			bind:value={username}
			maxlength="20"
			onkeydown={handleKeyDown}
		/>
	</label>

	<label class="form-control w-full">
		<div class="label">
			<span class="label-text">Name</span>
		</div>
		<input
			type="text"
			placeholder="Name"
			class="input input-bordered w-full max-w-full"
			disabled={isLoading}
			bind:value={name}
			onkeydown={handleKeyDown}
		/>
	</label>

	<label class="form-control w-full">
		<div class="label">
			<span class="label-text">Email</span>
		</div>
		<input
			type="email"
			placeholder="Email"
			class="input input-bordered w-full max-w-full"
			disabled
			value={user?.email}
		/>
	</label>

	<div class="form-control w-full">
		<div class="label">
			<span class="label-text">Accounts</span>
		</div>

		<div class="flex flex-row gap-2 items-center flex-wrap">
			{#await authClient.listAccounts()}
				<div class="btn">
					<span class="loading loading-spinner"></span>
				</div>
			{:then accounts}
				{#each accounts.data! as account}
					<div class="btn">
						{getAccountType(account.providerId)}
					</div>
				{/each}
			{/await}
		</div>
	</div>

	<AccountAction onAction={save} {isLoading} />
</AccountTemplate>
