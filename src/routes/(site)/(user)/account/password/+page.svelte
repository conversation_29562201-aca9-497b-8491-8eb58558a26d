<script lang="ts">
	import Turnstile from '../../../(auth)/components/Turnstile.svelte';
	import { authClient } from '$lib/auth/client';
	import { toast } from 'svelte-sonner';
	import AccountAction from '../components/AccountAction.svelte';
	import AccountTemplate from '../components/AccountTemplate.svelte';
	import { cn } from '$lib/util/cn';
	import capitalize from 'lodash/capitalize';
	import { page } from '$app/state';
	import type { TurnstileHandler } from '../../../(auth)/components/Turnstile.svelte';
	import { MetaTags } from 'svelte-meta-tags';

	let session = authClient.useSession();
	let isLoading = $state(false);
	let currentPassword = $state('');
	let newPassword = $state('');
	let error = $state(null) as { code?: string; message?: string } | null;
	let turnstileToken = $state('');
	let turnstileHandler = $state() as TurnstileHandler;

	function save() {
		if (isLoading) {
			return;
		}

		authClient.changePassword(
			{
				currentPassword,
				newPassword,
				revokeOtherSessions: true,
			},
			{
				onRequest: () => {
					isLoading = true;
					error = null;
				},
				onResponse: () => {
					isLoading = false;
				},
				onSuccess() {
					toast.success('Password changed successfully!');
					currentPassword = '';
					newPassword = '';
				},
				onError(context) {
					error = context.error;
					toast.error(
						context.error.message ??
							'Oops, something went wrong. Please try again later',
					);
				},
			},
		);
	}

	async function sendResetEmail() {
		if (!turnstileToken || isLoading) {
			return;
		}

		isLoading = true;
		error = null;

		const { error: apiError } = await authClient.forgetPassword({
			email: $session.data!.user.email,
			redirectTo: `${page.url.origin}/reset-password`,
			fetchOptions: {
				headers: {
					'x-captcha-response': turnstileToken,
				},
			},
		});

		isLoading = false;
		error = apiError;

		if (error) {
			turnstileHandler.reset();

			toast.error(
				error?.code === 'VALIDATION_ERROR'
					? 'Invalid email'
					: capitalize(
							error?.message ?? 'Oops, something went wrong. Please try again later',
						),
				{
					duration: 5_000,
				},
			);
		} else {
			// success = true;
			toast.success('Check your email for instructions');
		}
	}

	let accounts = await authClient.listAccounts();

	let hasPassword = $derived(
		accounts.data?.some((account: any) => account.providerId === 'credential'),
	);

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter' && !isLoading) {
			if (hasPassword) {
				save();
			} else if (turnstileToken) {
				sendResetEmail();
			}
		}
	}
</script>

<MetaTags
	title="Password Settings"
	titleTemplate="%s | Lofi and Games"
	description="Change or update your password for your Lofi and Games account."
	canonical="https://www.lofiandgames.com/account/password"
	openGraph={{
		url: 'https://www.lofiandgames.com/account/password',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'website',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Password Settings - Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com/account/password',
	}}
/>

<AccountTemplate title="Password">
	{#await accounts}
		<div class="absolute inset-0 flex justify-center items-center h-full">
			<span class="loading loading-spinner size-10"></span>
		</div>
	{:then}
		{#if hasPassword}
			<label class="form-control w-full">
				<div class="label">
					<span class="label-text">Current Password</span>
				</div>
				<input
					type="password"
					placeholder="Current Password"
					disabled={isLoading}
					bind:value={currentPassword}
					onkeydown={handleKeyDown}
					class={cn('input input-bordered w-full max-w-full', {
						'input-error': ['INVALID_PASSWORD'].includes($state.snapshot(error?.code)!),
					})}
				/>
			</label>

			<div>
				<label class="form-control w-full">
					<div class="label">
						<span class="label-text">New Password</span>
					</div>
					<input
						type="password"
						placeholder="New Password"
						disabled={isLoading}
						bind:value={newPassword}
						onkeydown={handleKeyDown}
						class={cn('input input-bordered w-full max-w-full', {
							'input-error': ['PASSWORD_TOO_LONG', 'PASSWORD_TOO_SHORT'].includes(
								$state.snapshot(error?.code)!,
							),
						})}
					/>
				</label>

				<div class="label mt-2">
					<span class="label-text">Password should be at least 8 characters</span>
				</div>
			</div>

			<AccountAction onAction={save} {isLoading} />
		{:else}
			<p>
				You don't have a password set, click the button bellow to receive an email with
				instructions to set one.
			</p>

			<Turnstile bind:token={turnstileToken} bind:this={turnstileHandler} />

			<AccountAction
				action="Send instructions"
				onAction={sendResetEmail}
				isLoading={isLoading || !turnstileToken}
			/>
		{/if}
	{/await}
</AccountTemplate>
