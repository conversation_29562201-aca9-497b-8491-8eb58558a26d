<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		action?: string;
		isLoading: boolean;
		class?: string;
		onAction: () => void;
	}

	let { onAction, action = 'Save', isLoading, class: className }: Props = $props();
</script>

<div class={cn('flex justify-end items-end grow', className)}>
	<button class="btn btn-primary w-full sm:btn-wide mt-4" disabled={isLoading} onclick={onAction}>
		{#if isLoading}
			<span class="loading loading-spinner"></span>
		{/if}

		{action}
	</button>
</div>
