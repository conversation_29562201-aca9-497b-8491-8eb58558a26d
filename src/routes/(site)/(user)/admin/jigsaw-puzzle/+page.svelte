<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { fade, fly } from 'svelte/transition';
	import CheckIcon from '$lib/components/Icons/CheckIcon.svelte';
	import { SvelteSet } from 'svelte/reactivity';
	import JigsawFloatingBar from '../../../(games)/jigsaw-puzzle/JigsawFloatingBar.svelte';
	import { cn } from '$lib/util/cn';
	import { page as sveltePage } from '$app/state';
	import Alert from '$lib/components/Alert.svelte';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';
	import ChevronRightIcon from '$lib/components/Icons/ChevronRightIcon.svelte';
	import SearchIcon from '$lib/components/Icons/SearchIcon.svelte';
	import Backdrop from '$lib/components/Backdrop.svelte';

	enum JigsawStatus {
		Pending = 'pending',
		Approved = 'approved',
		Rejected = 'rejected',
	}

	enum JigsawImageUploadStatus {
		Success = 'success',
		Uploading = 'uploading',
		Error = 'error',
	}

	const tabs = [
		{
			href: '#all',
			name: 'All',
			statuses: [JigsawStatus.Approved, JigsawStatus.Pending, JigsawStatus.Rejected],
		},
		{
			href: '#pending',
			name: 'Pending',
			statuses: [JigsawStatus.Pending],
		},
		{
			href: '#approved',
			name: 'Approved',
			statuses: [JigsawStatus.Approved],
		},
		{
			href: '#rejected',
			name: 'Rejected',
			statuses: [JigsawStatus.Rejected],
		},
	];
	let auditPuzzlesState = $state('idle' as 'idle' | 'loading' | 'success' | 'error');

	interface JigsawPuzzle {
		id: string;
		userId: string;
		imageUrl: string;
		originalImageUrl: string;
		status: JigsawStatus;
		createdAt: string;
		updatedAt: string;
		auditedAt: string | null;
		auditedBy: string | null;
		imageUploadStatus: JigsawImageUploadStatus | null;
	}

	interface FetchPuzzlesResponse {
		puzzles: JigsawPuzzle[];
		totalPages: number;
	}

	async function fetchPuzzles({
		statuses,
		pageSize,
		page,
	}: {
		statuses: JigsawStatus[];
		pageSize: number;
		page: number;
	}) {
		try {
			const res = await fetch(
				`${import.meta.env.VITE_API_URL}/jigsaw-puzzle/audit?status=${statuses.join(',')}&pageSize=${pageSize}&page=${page}`,
				{
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json',
					},
				},
			);

			if (res.status >= 300) {
				throw new Error('Something went wrong');
			}

			const response = await res.json<FetchPuzzlesResponse>();

			totalPages = response.totalPages;
			return response;
		} catch (error) {
			if (error instanceof Error) {
				toast.error(error.message);
			} else {
				toast.error('Oops, something went wrong');
			}

			throw error;
		}
	}

	async function auditPuzzles(status: JigsawStatus) {
		try {
			auditPuzzlesState = 'loading';

			const res = await fetch(`${import.meta.env.VITE_API_URL}/jigsaw-puzzle/audit`, {
				method: 'PUT',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ ids: Array.from(selectedIds.values()), status }),
			});

			if (res.status >= 300) {
				throw new Error('Oops, something went wrong');
			}

			const json = res.json<JigsawPuzzle[]>();

			selectedIds.clear();
			auditPuzzlesState = 'success';

			return json;
		} catch (error) {
			if (error instanceof Error) {
				toast.error(error.message);
			} else {
				toast.error('Oops, something went wrong');
			}
			auditPuzzlesState = 'error';
		} finally {
			page = 1;
			puzzlesPromiseRefresh += 1;
		}
	}

	function next() {
		if (page + 1 > totalPages) {
			return;
		}

		page += 1;
	}

	function previous() {
		page = Math.max(1, page - 1);
	}

	let activeTab = $derived(tabs.find((tab) => tab.href === sveltePage.url.hash) || tabs[1]);
	let statuses = $derived(activeTab.statuses);
	let pageSize = $state(30);
	let page = $state(1);
	let totalPages = $state(1);
	let puzzlesPromiseRefresh = $state(0);
	let puzzlesPromise = $derived.by(() => {
		// Listen to refresh
		puzzlesPromiseRefresh;

		return fetchPuzzles({
			page,
			pageSize,
			statuses,
		});
	});
	let selectedIds = new SvelteSet<string>();
	let zoomedPuzzle = $state<JigsawPuzzle>();
</script>

<div class="flex flex-col size-full" transition:fade|global={{ duration: 300 }}>
	<div class="flex flex-wrap gap-4 mb-4 items-center justify-center w-full">
		<div class="flex items-center justify-center">
			<div role="tablist" class="tabs tabs-box grid grid-cols-4">
				{#each tabs as tab}
					<a
						href={tab.href}
						role="tab"
						class={cn('tab', {
							'tab-active': activeTab.href === tab.href,
						})}
					>
						{tab.name}
					</a>
				{/each}
			</div>
		</div>
	</div>

	<div class="flex flex-col grow pb-40">
		{#await puzzlesPromise}
			<div class="absolute inset-0 flex items-center justify-center">
				<div class="loading loading-spinner"></div>
			</div>
		{:then { puzzles }}
			<div class="grow">
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2">
					{#each puzzles as puzzle}
						<div class="relative">
							<button
								class={cn(
									'cursor-pointer size-full aspect-video peer border-2 border-transparent rounded-md overflow-clip',
									{
										'border-secondary': selectedIds.has(puzzle.id),
									},
								)}
								onclick={() => {
									if (selectedIds.has(puzzle.id)) {
										selectedIds.delete(puzzle.id);
									} else {
										selectedIds.add(puzzle.id);
									}
								}}
							>
								{#key puzzle.imageUrl}
									<img
										src={puzzle.imageUrl}
										alt=""
										class="object-cover size-full"
									/>
								{/key}
							</button>

							<button
								transition:fly={{ y: -5, duration: 300 }}
								class="btn btn-circle btn-sm absolute bottom-1 right-1 transition-transform duration-150 scale-0 hover:scale-100 peer-hover:scale-100"
								onclick={() => {
									zoomedPuzzle = puzzle;
								}}
							>
								<SearchIcon class="size-6" />
							</button>

							{#if selectedIds.has(puzzle.id)}
								<div
									transition:fly={{ y: -5, duration: 300 }}
									class="btn btn-secondary btn-circle btn-sm absolute top-1 right-1 pointer-events-none"
								>
									<CheckIcon class="size-8" />
								</div>
							{/if}

							<div
								transition:fly={{ y: -5, duration: 300 }}
								class={cn(
									'badge absolute top-1 left-1 pointer-events-none capitalize',
									{
										'badge-success': puzzle.status === JigsawStatus.Approved,
										'badge-warning': puzzle.status === JigsawStatus.Pending,
										'badge-error': puzzle.status === JigsawStatus.Rejected,
									},
								)}
							>
								{puzzle.status}
							</div>

							{#if puzzle.imageUploadStatus === JigsawImageUploadStatus.Error}
								<div
									transition:fly={{ y: -5, duration: 300 }}
									class="badge badge-error absolute bottom-1 left-1 pointer-events-none capitalize"
								>
									{puzzle.imageUploadStatus}
								</div>
							{/if}
						</div>
					{/each}
				</div>
			</div>

			{#if totalPages > 0}
				<div class="join flex items-end justify-self-end self-end mt-4">
					<button class="join-item btn" onclick={previous}>
						<ChevronLeftIcon class="size-4" />
					</button>
					<button class="join-item btn">Page {page} of {totalPages}</button>
					<button class="join-item btn" onclick={next}>
						<ChevronRightIcon class="size-4" />
					</button>
				</div>
			{/if}

			{#if zoomedPuzzle}
				<Backdrop onclose={() => (zoomedPuzzle = undefined)} />
				<div
					transition:fly={{ y: 20, duration: 300 }}
					class="fixed z-40 left-1/2 top-1/2 -translate-1/2"
				>
					<img
						alt=""
						class="size-full object-contain max-h-[80vh] max-w-[80vw]"
						src={zoomedPuzzle.imageUrl}
					/>
				</div>
			{/if}
		{:catch}
			<Alert sentiment="error" title="Oops, something went wrong" class="max-w-sm mx-auto" />
		{/await}
	</div>

	<JigsawFloatingBar>
		<p class="text-md flex gap-2">
			{#if auditPuzzlesState === 'loading'}
				<span class="loading loading-spinner"></span>
				Saving...
			{:else}
				{selectedIds.size} selected
			{/if}
		</p>
		<button
			onclick={() => {
				auditPuzzles(JigsawStatus.Rejected);
			}}
			disabled={selectedIds.size === 0 || auditPuzzlesState === 'loading'}
			class="btn btn-soft btn-error"
		>
			Reject
		</button>
		<button
			onclick={() => {
				auditPuzzles(JigsawStatus.Approved);
			}}
			disabled={selectedIds.size === 0 || auditPuzzlesState === 'loading'}
			class="btn btn-soft btn-success"
		>
			Approve
		</button>
	</JigsawFloatingBar>
</div>
