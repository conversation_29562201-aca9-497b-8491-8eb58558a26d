<script lang="ts">
	import { toast } from 'svelte-sonner';
	import { fade, fly } from 'svelte/transition';
	import CheckIcon from '$lib/components/Icons/CheckIcon.svelte';
	import { SvelteSet } from 'svelte/reactivity';
	import ZoomImage from './ZoomImage.svelte';

	enum JigsawStatus {
		Pending = 'pending',
		Approved = 'approved',
		Rejected = 'rejected',
	}

	interface JigsawPuzzle {
		id: string;
		userId: string;
		imageUrl: string;
		originalImageUrl: string;
		status: JigsawStatus;
		createdAt: string;
		updatedAt: string;
		auditedAt: string;
		auditedBy: string;
	}

	async function fetchPuzzles({
		statuses,
		pageSize,
		page,
	}: {
		statuses: JigsawStatus[];
		pageSize: number;
		page: number;
	}) {
		try {
			const res = await fetch(
				`${import.meta.env.VITE_API_URL}/jigsaw-puzzle/audit?status=${statuses.join(',')}&pageSize=${pageSize}&page=${page}`,
				{
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json',
					},
				},
			);

			if (res.status >= 300) {
				throw new Error('Something went wrong');
			}

			return res.json<JigsawPuzzle[]>();
		} catch (error) {
			if (error instanceof Error) {
				toast.error(error.message);
			} else {
				toast.error('Oops, something went wrong');
			}

			throw error;
		}
	}

	let statuses = $state([JigsawStatus.Pending]);
	let pageSize = $state(30);
	let page = $state(1);
	let puzzlesPromise = $derived(
		fetchPuzzles({
			page,
			pageSize,
			statuses,
		}),
	);
	let selectedIds = new SvelteSet<string>();
</script>

<div class="size-full" transition:fade|global={{ duration: 300 }}>
	{#await puzzlesPromise}
		<div class="absolute inset-0 flex items-center justify-center">
			<div class="loading loading-spinner"></div>
		</div>
	{:then puzzles}
		<div class="grid grid-cols-1 md:grid-cols-2 gap-2">
			{#each puzzles as puzzle}
				<div class="relative">
					<button
						class="rounded-md cursor-pointer size-full aspect-video"
						onclick={() => {
							if (selectedIds.has(puzzle.id)) {
								selectedIds.delete(puzzle.id);
							} else {
								selectedIds.add(puzzle.id);
							}
						}}
					>
						<ZoomImage src={puzzle.imageUrl} class="size-full" />
					</button>

					{#if selectedIds.has(puzzle.id)}
						<div
							transition:fly={{ y: -5, duration: 300 }}
							class="btn btn-primary btn-circle btn-sm absolute top-1 right-1"
						>
							<CheckIcon class="size-8" />
						</div>
					{/if}
				</div>
			{/each}
		</div>
	{/await}
</div>
