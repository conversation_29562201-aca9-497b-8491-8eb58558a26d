<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		src: string;
		class?: string;
	}

	let { src, class: className, ...props }: Props = $props();
	let img = $state() as HTMLDivElement;
</script>

<div
	class={cn('overflow-clip size-full', className)}
	role="img"
	onmousemove={(e) => {
		img.style.transitionProperty = 'transform';
		const offsetX = e.offsetX ? e.offsetX : e.screenX;
		const offsetY = e.offsetY ? e.offsetY : e.screenY;
		const x = (offsetX / img.offsetWidth) * 100;
		const y = (offsetY / img.offsetHeight) * 100;

		img.style.backgroundPosition = x + '% ' + y + '%';
	}}
	onmouseout={() => {
		img.style.transitionProperty = 'all';
		img.style.backgroundPosition = '50%';
	}}
	onfocus={(e) => {
		// Ignore
	}}
	{...props}
>
	<div
		bind:this={img}
		class="transition-all duration-300 bg-no-repeat bg-cover size-full"
		style="background-image: url('{src}'); background-position: 50%;"
	></div>
</div>
