<script lang="ts">
	/** @todo Maybe delete this component */
	import { cn } from '$lib/util/cn';

	interface Props {
		src: string;
		class?: string;
		imageClass?: string;
	}

	let { src, class: className, imageClass, ...props }: Props = $props();
	let img = $state() as HTMLDivElement;
</script>

<div
	class={cn('overflow-clip size-full', className)}
	role="img"
	onmouseenter={() => {
		img.style.transitionProperty = 'background-size';
	}}
	onmousemove={(e) => {
		const rect = e.currentTarget.getBoundingClientRect();
		const offsetX = e.clientX - rect.left;
		const offsetY = e.clientY - rect.top;
		const xPercentage = (100 * offsetX) / rect.width;
		const yPercentage = (100 * offsetY) / rect.height;

		img.style.backgroundPosition = `${xPercentage}% ${yPercentage}%`;
	}}
	onmouseout={() => {
		img.style.transitionProperty = 'background-size,background-position';
		img.style.backgroundPosition = '50%';
	}}
	onfocus={() => {
		// Ignore
	}}
	{...props}
>
	<div
		bind:this={img}
		class={cn(
			'transition-all duration-300 bg-no-repeat size-full origin-center bg-size-[100%] hover:bg-size-[150%]',
			imageClass,
		)}
		style="background-image: url('{src}'); background-position: 50%;"
	></div>
</div>
