<script lang="ts">
	import { authClient, type User } from '$lib/auth/client';
	import Avatar from '$lib/components/Avatar.svelte';
	import throttle from 'lodash/throttle';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';
	import ChevronRightIcon from '$lib/components/Icons/ChevronRightIcon.svelte';
	import DotsVerticalIcon from '$lib/components/Icons/DotsVerticalIcon.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import { MetaTags } from 'svelte-meta-tags';
	import { toast } from 'svelte-sonner';
	import { fade } from 'svelte/transition';
	import Dialog from '$lib/components/Dialog.svelte';

	let users = $state<User[]>([]);
	const pageSize = 12;
	let offset = $state(0);
	let searchValue = $state('');
	let updateSearch = throttle(
		(value: string) => {
			searchValue = value;
			offset = 0;
		},
		1000,
		{
			leading: false,
			trailing: true,
		},
	);
	let total = $state(0);
	let totalPages = $derived(Math.ceil(total / pageSize));
	let page = $derived(offset / pageSize);
	let isBanUserDialogOpen = $state(false);
	let userToBan = $state<User | null>(null);

	function closeDropdown() {
		document.body.click();
	}

	function next() {
		if (page + 1 >= totalPages) {
			return;
		}

		offset += pageSize;
	}

	function previous() {
		offset = Math.max(0, offset - pageSize);
	}

	function ban(user: User) {
		authClient.admin.banUser(
			{
				userId: user.id,
			},
			{
				onRequest() {
					toast.loading('Banning user...', {
						id: 'banning-user',
					});
				},
				onError(context) {
					toast.error(context.error.message);
				},
				onSuccess() {
					toast.success('User banned successfully!');
				},
				onResponse() {
					toast.dismiss('banning-user');
					listUsers();
				},
			},
		);
	}

	function unban(user: User) {
		authClient.admin.unbanUser(
			{
				userId: user.id,
			},
			{
				onRequest() {
					toast.loading('Unbanning user...', {
						id: 'unbanning-user',
					});
				},
				onError(context) {
					toast.error(context.error.message);
				},
				onSuccess() {
					toast.success('User unbanned successfully!');
				},
				onResponse() {
					toast.dismiss('unbanning-user');
					listUsers();
				},
			},
		);
	}

	function sendEmailVerification(user: User) {
		authClient.sendVerificationEmail(
			{
				email: user.email,
			},
			{
				onSuccess() {
					toast.success('Email sent successfully!');
				},
				onError(context) {
					toast.error(context.error.message);
				},
				onRequest() {
					toast.loading('Sending email...', {
						id: 'sending-email',
					});
				},
				onResponse() {
					toast.dismiss('sending-email');
				},
			},
		);
	}

	function listUsers() {
		authClient.admin.listUsers(
			{
				query: {
					limit: pageSize,
					offset,
					searchField: 'email',
					searchOperator: 'contains',
					searchValue,
					sortBy: 'createdAt',
					sortDirection: 'desc',
				},
			},
			{
				onSuccess: (data) => {
					users = data.data.users;
					total = data.data.total ?? pageSize;
				},
				onError: (context) => {
					toast.error(context.error.message);
				},
			},
		);
	}

	$effect.pre(() => {
		listUsers();
	});
</script>

<MetaTags
	title="Admin Dashboard"
	titleTemplate="%s | Lofi and Games"
	description="Administrative dashboard for managing Lofi and Games users and settings."
	canonical="https://www.lofiandgames.com/admin"
	openGraph={{
		url: 'https://www.lofiandgames.com/admin',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'website',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Admin Dashboard - Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com/admin',
	}}
/>

{#if userToBan}
	<Dialog bind:isOpen={isBanUserDialogOpen}>
		<article>
			<h2>Would you like to ban the user?</h2>

			<p>
				{userToBan?.displayUsername || userToBan?.name} will be banned from the platform and
				removed from all game leaderboards
			</p>

			<div class="flex gap-2 items-center justify-end">
				<button class="btn btn-ghost" onclick={() => (isBanUserDialogOpen = false)}
					>Cancel</button
				>

				<button
					class="btn btn-error"
					onclick={() => {
						ban(userToBan!);
						isBanUserDialogOpen = false;
					}}
				>
					Ban User
				</button>
			</div>
		</article>
	</Dialog>
{/if}

<div class="size-full" transition:fade|global={{ duration: 300 }}>
	<div class="flex gap-4 items-center mb-4">
		<input
			type="search"
			class="input input-bordered min-w-0"
			placeholder="Search"
			oninput={(event) => updateSearch((event.target as HTMLInputElement).value)}
		/>

		<span>
			{total} users
		</span>
	</div>

	<div class="flex gap-4 flex-col">
		<table class="table">
			<!-- head -->
			<thead>
				<tr>
					<th>User</th>
					<th>Email</th>
					<th></th>
				</tr>
			</thead>
			<tbody>
				{#each users as user}
					<tr>
						<td>
							<div class="flex items-center gap-3">
								<Avatar {user} class="size-12" />

								<div>
									<div class="flex gap-2">
										<span class="font-bold">
											{user.name}
										</span>

										{#if user.banned}
											<div class="badge badge-error">Banned</div>
										{/if}
									</div>
									<div class="text-sm opacity-60">
										{user.displayUsername
											? `@${user.displayUsername}`
											: 'No username'}
									</div>
									<div
										class="text-sm opacity-60 text-ellipsis text-wrap max-w-28 overflow-clip"
									>
										{user.id}
									</div>
								</div>
							</div>
						</td>
						<td>
							<div class="flex flex-col gap-2 text-wrap">
								{user.email}

								{#if !user.emailVerified}
									<div class="badge badge-warning">Unverified</div>
								{/if}
							</div>
						</td>
						<td class="w-8">
							<Dropdown class="dropdown-end">
								<DropdownButton class="btn-circle btn-ghost btn-sm">
									<DotsVerticalIcon class="size-4" />
								</DropdownButton>

								<DropdownContent menu class="w-60">
									<DropdownItem>
										{#if user.banned}
											<button
												onclick={() => {
													closeDropdown();
													unban(user);
												}}
											>
												Unban
											</button>
										{:else}
											<button
												onclick={() => {
													closeDropdown();
													userToBan = user;
													isBanUserDialogOpen = true;
												}}
											>
												Ban
											</button>
										{/if}
									</DropdownItem>
									{#if !user.emailVerified}
										<DropdownItem>
											<button
												onclick={() => {
													closeDropdown();
													sendEmailVerification(user);
												}}
											>
												Send verification email
											</button>
										</DropdownItem>
									{/if}
								</DropdownContent>
							</Dropdown>
						</td>
					</tr>
				{/each}
			</tbody>
		</table>

		<div class="join self-end grow flex items-end">
			<button class="join-item btn" onclick={previous}>
				<ChevronLeftIcon class="size-4" />
			</button>
			<button class="join-item btn">Page {page + 1} of {totalPages}</button>
			<button class="join-item btn" onclick={next}>
				<ChevronRightIcon class="size-4" />
			</button>
		</div>
	</div>
</div>
