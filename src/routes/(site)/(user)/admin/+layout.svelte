<script lang="ts">
	import type { Snippet } from 'svelte';
	import { goto } from '$app/navigation';
	import { authClient } from '$lib/auth/client';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { fade } from 'svelte/transition';
	import PeopleIcon from '$lib/components/Icons/PeopleIcon.svelte';
	import AuthMetaColor from '../../(auth)/components/AuthMetaColor.svelte';
	import StatsIcon from '$lib/components/Icons/StatsIcon.svelte';
	import JoystickIcon from '$lib/components/Icons/JoystickIcon.svelte';
	import { page } from '$app/state';

	interface Props {
		children: Snippet;
	}

	let { children }: Props = $props();
	const session = authClient.useSession();
	const menuItems = [
		{
			text: 'Users',
			href: '/admin',
			icon: PeopleIcon,
		},
		{
			text: 'Leaderboards',
			href: '/admin/leaderboards',
			icon: StatsIcon,
		},
		{
			text: 'Jigsaw Puzzle',
			href: '/admin/jigsaw-puzzle',
			icon: JoystickIcon,
		},
	];

	$effect(() => {
		if ($session.isPending) {
			return;
		}

		if (!$session.data?.user) {
			goto('/signin');
		} else {
			if ($session.data.user.role !== 'admin') {
				goto('/wp-admin');
			}
		}
	});

	let activeMenuItem = $derived.by(() => {
		return menuItems.find((item) => item.href === page.url.pathname) ?? menuItems[0];
	});
</script>

<AuthMetaColor />

<PageTransition>
	<Navbar variant="transparent" />

	{#if $session.data?.user?.role === 'admin'}
		<div class="drawer lg:drawer-open w-full">
			<input id="my-drawer-4" type="checkbox" class="drawer-toggle" />
			<div class="drawer-content flex flex-col">
				<nav class="navbar w-full bg-base-300">
					<label
						for="my-drawer-4"
						aria-label="open sidebar"
						class="btn btn-square btn-ghost"
					>
						<!-- Sidebar toggle icon -->
						<svg
							xmlns="http://www.w3.org/2000/svg"
							viewBox="0 0 24 24"
							stroke-linejoin="round"
							stroke-linecap="round"
							stroke-width="2"
							fill="none"
							stroke="currentColor"
							class="my-1.5 inline-block size-4"
						>
							<path
								d="M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z"
							>
							</path>
							<path d="M9 4v16"> </path>
							<path d="M14 10l2 2l-2 2"> </path>
						</svg>
					</label>
					<div class="px-4">{activeMenuItem.text}</div>
				</nav>
				<!-- Page content here -->
				<div class="p-4 grow">
					{@render children()}
				</div>
			</div>

			<div class="drawer-side is-drawer-close:overflow-visible">
				<label for="my-drawer-4" aria-label="close sidebar" class="drawer-overlay"> </label>
				<div
					class="flex min-h-full flex-col items-start bg-base-200 is-drawer-close:w-16 is-drawer-open:w-56"
				>
					<!-- Sidebar content here -->
					<ul class="menu menu-md w-full grow">
						{#each menuItems as item}
							<li>
								<a
									href={item.href}
									class="is-drawer-close:tooltip is-drawer-close:tooltip-right"
									data-tip={item.text}
								>
									<item.icon class="size-6 aspect-square" />
									<span class="is-drawer-close:hidden text-nowrap">
										{item.text}
									</span>
								</a>
							</li>
						{/each}
					</ul>
				</div>
			</div>
		</div>
	{:else}
		<div
			transition:fade
			class="loading loading-spinner absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 size-10"
		></div>
	{/if}
</PageTransition>
