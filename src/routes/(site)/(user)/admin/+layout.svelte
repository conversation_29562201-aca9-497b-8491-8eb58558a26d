<script lang="ts">
	import type { Snippet } from 'svelte';
	import { goto } from '$app/navigation';
	import { authClient } from '$lib/auth/client';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { fade } from 'svelte/transition';
	import AuthContainer from '../../(auth)/components/AuthContainer.svelte';
	import SideMenuLayout from '../../(auth)/components/SideMenuLayout.svelte';
	import PeopleIcon from '$lib/components/Icons/PeopleIcon.svelte';
	import AuthMetaColor from '../../(auth)/components/AuthMetaColor.svelte';
	import StatsIcon from '$lib/components/Icons/StatsIcon.svelte';

	interface Props {
		children: Snippet;
	}

	let { children }: Props = $props();
	const session = authClient.useSession();
	const menuItems = [
		{
			text: 'Users',
			href: '/admin',
			icon: PeopleIcon,
		},
		{
			text: 'Leaderboards',
			href: '/admin/leaderboards',
			icon: StatsIcon,
		},
	];

	$effect(() => {
		if ($session.isPending) {
			return;
		}

		if (!$session.data?.user) {
			goto('/signin');
		} else {
			if ($session.data.user.role !== 'admin') {
				goto('/wp-admin');
			}
		}
	});
</script>

<AuthMetaColor />

<PageTransition>
	<Navbar variant="transparent" />

	{#if $session.data?.user?.role === 'admin'}
		<AuthContainer cardClass="max-w-6xl min-h-[80vh]">
			<SideMenuLayout {menuItems}>
				{@render children()}
			</SideMenuLayout>
		</AuthContainer>
	{:else}
		<div
			transition:fade
			class="loading loading-spinner absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 size-10"
		></div>
	{/if}
</PageTransition>
