<script lang="ts">
	import { onMount, type Snippet } from 'svelte';
	import { theme } from '$lib/stores/theme.svelte';
	import { MetaTags } from 'svelte-meta-tags';
	import { page } from '$app/state';
	import { toast } from 'svelte-sonner';
	import { musicPlayer, startedOnFirstUserInteraction } from '$lib/stores/musicPlayer.svelte';
	import { backgroundSoundManager } from '$lib/stores/backgroundSoundManager.svelte';
	import { Playlight } from '$lib/stores/playlightSdk.svelte';
	import GameSearchMenu from '$lib/components/GameSearchMenu/GameSearchMenu.svelte';

	interface Props {
		children: Snippet;
	}

	let { children }: Props = $props();

	onMount(() => {
		window.addEventListener('vite:preloadError', () => {
			toast('New site update available!', {
				id: 'site-update-available',
				description: 'Reload the page to prevent errors.',
				action: {
					label: 'Reload',
					onClick: () => {
						window.location.reload();
					},
				},
				duration: 10_000,
			});
		});
	});

	function startMusicAndBackgroundSounds() {
		if (!startedOnFirstUserInteraction.value) {
			musicPlayer.play();
			backgroundSoundManager.play();
			startedOnFirstUserInteraction.value = true;
		}
	}

	const pagesWithCustomMetaTags = [
		'/flappy-birdie',
		'/signin',
		'/signup',
		'/signout',
		'/reset-password',
		'/forgot-password',
		'/account',
		'/admin',
		'/wp-admin',
	];

	let canRenderMetaTags = $derived(
		!pagesWithCustomMetaTags.some((p) => page.url.pathname.includes(p)),
	);

	$effect(() => {
		Playlight.downloadAndInit();
		Playlight.loadAllGames();
	});
</script>

<svelte:window onclick={startMusicAndBackgroundSounds} onkeydown={startMusicAndBackgroundSounds} />

{#if theme.themeColor && canRenderMetaTags}
	<MetaTags
		additionalMetaTags={[
			{
				name: 'theme-color',
				content: theme.themeColor,
			},
		]}
	/>
{/if}

<GameSearchMenu />

{@render children()}
