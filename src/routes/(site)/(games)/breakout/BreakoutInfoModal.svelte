<script lang="ts">
	import { breakoutSoundResources as sounds } from './breakoutSoundResources';
	import InfoModal from '$lib/components/InfoModal.svelte';

	interface Props {
		isOpen?: boolean;
	}

	let { isOpen = $bindable(false) }: Props = $props();
</script>

<InfoModal {sounds} bind:isOpen>
	<h1>Breakout Game</h1>

	<p>
		Breakout is a classic arcade game that was first released in 1976. The game was developed
		and published by Atari Inc., and was designed by <PERSON> and <PERSON>. It is
		considered one of the most popular and influential games of the 1970s.
	</p>

	<p>
		The goal of the game is to use a ball and a paddle to break through a wall of bricks. The
		player controls the paddle, which is located at the bottom of the screen, and must use it to
		keep the ball in play and hit it towards the bricks. The player loses a turn if the ball
		touches the bottom of the screen.
	</p>

	<h2>How to Play Breakout: The Game Rules</h2>

	<ul>
		<li>
			Use the paddle to hit the ball and break the bricks. Each time a brick is hit, it
			disappears and the player earns points.
		</li>

		<li>
			The game ends when all of the bricks have been broken or the player loses all of their
			turns.
		</li>
	</ul>

	<h2>Useful Breakout Game Tips</h2>

	<ul>
		<li>
			One useful tip for playing Breakout is to pay attention to the angle of the ball when it
			hits the paddle. The angle at which the ball hits the paddle will determine the
			direction it goes, so try to hit the ball at different angles to control its trajectory.
			This mechanic was not implemented in the Lofi and Games' Breakout game, but we can add
			it if enough players desire.
		</li>

		<li>
			Another tip is to focus on the bottom row of bricks first. These bricks are the closest
			to the paddle and are therefore the easiest to hit. Once the bottom row is cleared, the
			player can focus on the higher rows of bricks.
		</li>

		<li>
			Additionally, try to keep the paddle moving and be ready to quickly adjust the angle of
			the paddle. This will make it easier to hit the ball and keep it in play.
		</li>
	</ul>

	<h2>Conclusion</h2>

	<p>
		Breakout is a simple yet highly addictive game that can be enjoyed by players of all ages.
		With its fast-paced action and simple gameplay, it is easy to see why it has remained a
		popular game for over four decades. Whether you are a seasoned veteran or a first-time
		player, Breakout is sure to provide hours of entertainment.
	</p>
</InfoModal>
