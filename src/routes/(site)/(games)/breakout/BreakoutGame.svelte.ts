import { fixCanvasDpi } from '$lib/functions/fixCanvasDpi';
import { isTouchDevice } from '$lib/functions/isTouchDevice';
import { ads } from '$lib/stores/ads.svelte';
import type { GameSound } from '$lib/util/GameSound.svelte';
import type { SettingsManager } from '$lib/util/SettingsManager.svelte';
import { Timer } from '$lib/util/Timer.svelte';
import { untrack } from 'svelte';
import { BreakoutBall } from './BreakoutBall';
import { BreakoutPlatform } from './BreakoutPlatform';
import { BreakoutTiles } from './BreakoutTiles.svelte';

export type BreakoutGameSounds = {
	tileHit: GameSound;
	start: GameSound;
	gameOver: GameSound;
	decreaseLife: GameSound;
};

export type BreakoutSettings = {
	mouseSensitivity: number;
};

export type BreakoutDifficulty = 'easy' | 'normal' | 'hard';

const speedIncrements: Record<BreakoutDifficulty, number> = {
	easy: 0,
	normal: 0.04,
	hard: 0.08,
};

const platformCollisionSpeedFactor: Record<BreakoutDifficulty, number> = {
	easy: 0.1,
	normal: 0.3,
	hard: 0.5,
};

const platformWidthFactor: Record<BreakoutDifficulty, number> = {
	easy: 1 / 8,
	normal: 1 / 10,
	hard: 1 / 12,
};

const maxBallSpeedFactor: Record<BreakoutDifficulty, number> = {
	easy: 2,
	normal: 3.5,
	hard: 4.5,
};

const scoreFactor: Record<BreakoutDifficulty, number> = {
	easy: 0.5,
	normal: 1,
	hard: 1.5,
};

interface BreakoutGameProps {
	targetElement: HTMLElement;
	canvas: HTMLCanvasElement;
	sounds: BreakoutGameSounds;
	settingsManager: SettingsManager<{
		difficulty: BreakoutDifficulty;
		mouseSensitivity: number;
	}>;
	difficulty: BreakoutDifficulty;
	timer: Timer;
	onStart: () => void;
	onGameOver: () => void;
}

export class BreakoutGame {
	id = Math.random() * 1e9;
	settingsManager: SettingsManager<{
		difficulty: BreakoutDifficulty;
		mouseSensitivity: number;
	}>;
	private timer: Timer;
	private canvas: HTMLCanvasElement;
	private context: CanvasRenderingContext2D;
	private lastTickTimeStamp = -1;
	private animationFrame = -1;
	private _running = $state(false);
	private _isLost = $state(false);
	private _score = $state(0);
	private platform!: BreakoutPlatform;
	private tiles!: BreakoutTiles;
	private ball!: BreakoutBall;
	private sounds: BreakoutGameSounds;
	private targetElement: HTMLElement;
	private isPointerLocked = false;
	private _life = $state(0);
	private disposed = false;
	private _difficulty: BreakoutDifficulty;
	private _onStart: () => void;
	private _onGameOver: () => void;
	private _cleanUpTimerListener?: () => void;
	private _cleanupAdsListener?: () => void;

	constructor({
		canvas,
		onGameOver,
		onStart,
		settingsManager,
		sounds,
		targetElement,
		difficulty,
		timer,
	}: BreakoutGameProps) {
		this.timer = timer;
		this._onStart = onStart;
		this._onGameOver = onGameOver;
		this._difficulty = difficulty;
		this.settingsManager = settingsManager;
		this.targetElement = targetElement;
		this.canvas = canvas;

		this.context = canvas.getContext('2d')!;
		this.sounds = sounds;
		this._life = BreakoutGame.defaultLife;
		fixCanvasDpi(this.canvas);
		this.initGame();
		this.handleResize();
		this.addListeners();
		window.requestAnimationFrame(this.loop);
	}

	get difficulty() {
		return this._difficulty;
	}

	get speedIncrement() {
		return speedIncrements[this.difficulty];
	}

	get platformCollisionSpeedFactor() {
		return platformCollisionSpeedFactor[this.difficulty];
	}

	get platformWidthFactor() {
		return platformWidthFactor[this.difficulty];
	}

	get maxBallSpeedFactor() {
		return maxBallSpeedFactor[this.difficulty];
	}

	get scoreFactor() {
		return scoreFactor[this.difficulty];
	}

	get life() {
		return this._life;
	}

	set life(life: number) {
		this._life = Math.max(0, life);

		if (this._life === 0) {
			this.isLost = true;
			this.timer.stop();
		} else {
			this.sounds.decreaseLife.play();
		}
	}

	get isLost() {
		return this._isLost;
	}

	set isLost(isLost: boolean) {
		if (isLost) {
			this.sounds.gameOver.play();
			this._onGameOver();
			this.timer.stop();
		}

		this._isLost = isLost;
	}

	get isOver() {
		return this.isLost;
	}

	get score() {
		return Math.floor(this._score);
	}

	set score(newScore: number) {
		if (newScore !== this._score) {
			this._score = newScore;
			const speedIncrement = this.speedIncrement;

			if (this.ball.running) {
				this.ball.speed.y += this.ball.speed.y > 0 ? speedIncrement : -speedIncrement;
				this.ball.speed.x += this.ball.speed.x > 0 ? speedIncrement : -speedIncrement;
			}
		}
	}

	get started() {
		return this.timer.started;
	}

	set started(started: boolean) {
		this.paused = !started;
	}

	get paused() {
		return this.timer.paused && this.timer.time !== 0;
	}

	set paused(paused: boolean) {
		if (this.disposed || this.isOver) {
			return;
		}

		if (paused) {
			this.timer.pause();
		} else {
			this.timer.start();
		}
	}

	get running() {
		return this._running;
	}

	set running(running: boolean) {
		if (this.disposed && running) {
			return;
		}

		this._running = running;
		this.lastTickTimeStamp = -1;

		if (running) {
			this.targetElement.style.setProperty('cursor', 'none');
		} else {
			this.targetElement.style.removeProperty('cursor');
		}

		if (this.supportsPointerLock) {
			try {
				if (running) {
					this.targetElement.requestPointerLock();
				} else {
					document.exitPointerLock();
				}
			} catch (_error) {
				// Ignore
			}
		}
	}

	get supportsPointerLock() {
		return !isTouchDevice();
	}

	get supportsMouseSensitivity() {
		return this.supportsPointerLock;
	}

	set mouseSensitivity(mouseSensitivity: number) {
		this.settings.mouseSensitivity = mouseSensitivity;
	}

	get mouseSensitivity() {
		if (this.supportsPointerLock) {
			return this.settings.mouseSensitivity;
		}

		return 1;
	}

	get settings(): BreakoutSettings {
		return this.settingsManager.settings;
	}

	private incrementScore() {
		this.score = this._score + this.scoreFactor;
	}

	resume() {
		this.paused = false;
	}

	start() {
		if (this.running || this.isOver) {
			return;
		}

		this.sounds.start.play();
		this.ball.reset();
		this.running = true;
		this.started = true;
		this._onStart();
	}

	stop() {
		this.running = false;
	}

	private initGame() {
		this.platform = new BreakoutPlatform(this.platformWidthFactor, this.canvas);
		this.tiles = new BreakoutTiles(this.canvas);
		this.ball = new BreakoutBall(this.maxBallSpeedFactor, this.canvas);
	}

	private checkIfShouldResetTiles() {
		if (!this.tiles.hasSomeTileAlive()) {
			this.tiles.reset();
		}
	}

	private async handleBallCollisionWithWalls() {
		if (this.ball.stopped) {
			return;
		}

		const { box, speed } = this.ball;

		if (box.position.x <= 0) {
			speed.x *= -1;
			box.position.x *= -1;
		}

		if (box.right >= this.canvas.width) {
			speed.x *= -1;
			box.position.x += box.right - this.canvas.width;
		}

		if (box.position.y <= 0) {
			speed.y *= -1;
			box.position.y *= -1;
		}

		if (box.y + box.h >= this.canvas.height) {
			if (this.ball.running) {
				this.life -= 1;
				this.stop();
				this.ball.stop();
				this.alignBallToPlatform();
				this.ball.reset();
				this.checkIfShouldResetTiles();
			}
		}

		this.ball.limitBoxPosition();
	}

	private handleBallCollisionWithTiles() {
		const ball = this.ball;

		if (ball.stopped) {
			return;
		}

		const collisions = this.tiles
			.getCollidingTiles(ball.box)
			.map((tile) => {
				// Calculate the distance between centers
				const diffX = ball.box.centerX - tile.box.centerX;
				const diffY = ball.box.centerY - tile.box.centerY;

				// Calculate the minimum distance to separate along X and Y
				const minXDist = ball.box.w / 2 + tile.box.w / 2;
				const minYDist = ball.box.h / 2 + tile.box.h / 2;

				// Calculate the depth of collision for both the X and Y axis
				const depthX = diffX > 0 ? minXDist - diffX : -minXDist - diffX;
				const depthY = diffY > 0 ? minYDist - diffY : -minYDist - diffY;

				return {
					tile,
					depthX,
					depthY,
				};
			})
			.sort((a, b) => Math.max(a.depthX, a.depthY) - Math.max(b.depthX, b.depthY));

		const { tile: firstTile, depthX, depthY } = collisions[0] ?? {};

		/** @see https://stackoverflow.com/questions/56606799/how-to-detect-the-side-on-which-collision-occured */
		if (firstTile) {
			firstTile.alive = false;
			this.incrementScore();
			this.sounds.tileHit.play();

			// Now that you have the depth, you can pick the smaller depth and move
			// along that axis.
			if (depthX != 0 && depthY != 0) {
				if (Math.abs(depthX) < Math.abs(depthY)) {
					// Collision along the X axis. React accordingly
					if (depthX > 0) {
						// Left side collision
						if (ball.speed.x < 0) {
							ball.box.position.x += Math.abs(firstTile.box.right - ball.box.left);
							ball.speed.x = Math.abs(ball.speed.x);
						}
					} else {
						// Right side collision
						if (ball.speed.x > 0) {
							ball.box.position.x -= Math.abs(ball.box.right - firstTile.box.left);
							ball.speed.x = -Math.abs(ball.speed.x);
						}
					}
				} else {
					// Collision along the Y axis.
					if (depthY > 0) {
						// Top side collision
						if (ball.speed.y < 0) {
							ball.box.position.y += Math.abs(firstTile.box.bottom - ball.box.top);
							ball.speed.y = Math.abs(ball.speed.y);
						}
					} else {
						// Bottom side collision
						if (ball.speed.y > 0) {
							ball.box.position.y -= Math.abs(ball.box.bottom - firstTile.box.top);
							ball.speed.y = -Math.abs(ball.speed.y);
						}
					}
				}
			}
		}
	}

	private handleBallCollisionWithPlatform() {
		if (this.ball.stopped) {
			return;
		}

		// Check platform
		const hasCollision = this.ball.box.collidesWith(this.platform.box);

		if (hasCollision) {
			this.ball.speed.y *= -1;
			const delta = Math.abs(this.ball.box.y + this.ball.box.h - this.platform.box.y);
			this.ball.box.position.y -= delta;
			this.ball.speed.x += this.platform.speedX * this.platformCollisionSpeedFactor;

			this.checkIfShouldResetTiles();
		}
	}

	private moveBallAndCheckCollisions(time: number) {
		if (this.ball.stopped) {
			return;
		}

		const { box, speed } = this.ball;

		box.position.x += (speed.x * time) / 5;
		box.position.y += (speed.y * time) / 5;

		this.handleBallCollisionWithPlatform();
		this.handleBallCollisionWithTiles();
		this.handleBallCollisionWithWalls();
	}

	private adjustBallSpeedAngle() {
		if (this.ball.stopped) {
			return;
		}

		if (Math.abs(this.ball.speed.y) < 0.01) {
			this.ball.speed.y = -1.1 * window.devicePixelRatio;
		}
		if (Math.abs(this.ball.speed.x) < 0.01) {
			this.ball.speed.x = -1.1 * window.devicePixelRatio;
		}
	}

	private tick(time: number) {
		this.moveBallAndCheckCollisions(time);
		this.adjustBallSpeedAngle();
	}

	private clearDraw() {
		const canvasSize = this.context.canvas.width;
		this.context.clearRect(0, 0, canvasSize, canvasSize);
	}

	draw() {
		fixCanvasDpi(this.canvas);
		this.clearDraw();
		this.platform.draw();
		this.tiles.draw();
		this.ball.draw();
	}

	private loop = (timestamp: number) => {
		if (document.hidden || this.disposed) {
			return;
		}

		if (this.running) {
			if (this.lastTickTimeStamp < 0) {
				this.lastTickTimeStamp = timestamp;
			}

			const timeSinceLastUpdate = timestamp - this.lastTickTimeStamp;

			this.tick(timeSinceLastUpdate);
			this.lastTickTimeStamp = timestamp;
		}

		this.draw();

		this.animationFrame = window.requestAnimationFrame(this.loop);
	};

	private alignBallToPlatform() {
		this.ball.box.position.y =
			this.platform.box.y - this.ball.box.h - 1 * window.devicePixelRatio;
		this.ball.box.position.x = this.platform.box.centerX - this.ball.box.w / 2;
	}

	private handleResize = () => {
		this.canvas.setAttribute('width', `${this.canvas.width}`);
		this.canvas.setAttribute('height', `${this.canvas.height}`);
		fixCanvasDpi(this.canvas);
		this.platform?.resize();
		this.tiles?.resize();
		this.ball?.resize();
		this.platform.centralizeY();

		if (!this.running) {
			if ((this.supportsPointerLock && !this.started) || !this.supportsPointerLock) {
				this.platform.centralize();
				this.alignBallToPlatform();
			}
		}

		this.draw();
	};

	private handlePointerMove = (event: PointerEvent) => {
		if (this.paused) {
			return;
		}

		const rect = this.canvas.getBoundingClientRect();

		if (this.isPointerLocked) {
			const x = event.movementX * window.devicePixelRatio * this.mouseSensitivity;

			this.platform.moveBy(x);
		} else {
			let x =
				Math.max(0, Math.min(rect.width, event.pageX - rect.x)) * window.devicePixelRatio;

			if (this.supportsMouseSensitivity) {
				x *= this.mouseSensitivity;
			}
			this.platform.moveToPointerX(x);
		}

		if (!this.running) {
			this.ball.box.position.x = this.platform.box.centerX - this.ball.box.w / 2;
		}
	};

	private handleClick = () => {
		if (!this.running) {
			this.start();
		}
	};

	private handleVisibilityChange = () => {
		this.lastTickTimeStamp = -1;
	};

	private handlePointerLockChange = () => {
		this.isPointerLocked =
			document.pointerLockElement === this.targetElement ||
			(document as any).mozPointerLockElement === this.targetElement;
	};

	private handleKeyUp = (event: KeyboardEvent) => {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		const code = event.code.toLocaleLowerCase();

		if (code === 'space') {
			this.paused = !this.paused;
		}
	};

	private handleOrientationChange = () => {
		this.paused = true;

		setTimeout(() => {
			this.handleResize();
		}, 300);
	};

	private addListeners() {
		window.addEventListener('resize', this.handleResize);
		window.addEventListener('orientationchange', this.handleOrientationChange);
		window.addEventListener('pointermove', this.handlePointerMove);
		this.targetElement.addEventListener('click', this.handleClick);
		window.addEventListener('visibilitychange', this.handleVisibilityChange);
		document.addEventListener('pointerlockchange', this.handlePointerLockChange, false);
		document.addEventListener('mozpointerlockchange', this.handlePointerLockChange, false);
		document.addEventListener('keyup', this.handleKeyUp);

		this._cleanUpTimerListener?.();
		this._cleanUpTimerListener = $effect.root(() => {
			$effect(() => {
				if (!this.disposed && !this.isOver) {
					this.running = !this.timer.paused;
				}
			});
		});

		this._cleanupAdsListener?.();
		this._cleanupAdsListener = $effect.root(() => {
			$effect(() => {
				// Track ads
				ads.effectiveSize;

				untrack(() => {
					this.handleResize();
				});
			});
		});
	}

	dispose() {
		this.disposed = true;
		this.running = false;
		this.tiles.dispose();
		window.cancelAnimationFrame(this.animationFrame);
		window.removeEventListener('resize', this.handleResize);
		window.removeEventListener('orientationchange', this.handleOrientationChange);
		window.removeEventListener('pointermove', this.handlePointerMove);
		this.targetElement.removeEventListener('click', this.handleClick);
		window.removeEventListener('visibilitychange', this.handleVisibilityChange);
		document.removeEventListener('pointerlockchange', this.handlePointerLockChange);
		document.removeEventListener('mozpointerlockchange', this.handlePointerLockChange);
		document.removeEventListener('keyup', this.handleKeyUp);
		this._cleanUpTimerListener?.();
		this._cleanupAdsListener?.();
	}

	resetSettings() {
		this.settingsManager.settings.mouseSensitivity =
			this.settingsManager.defaultSettings.mouseSensitivity;
	}

	static maxMouseSensitivity = 1.5;
	static minMouseSentitivity = 0.5;
	static defaultLife = 3;
}
