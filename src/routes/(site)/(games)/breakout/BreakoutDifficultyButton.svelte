<script lang="ts">
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import type { BreakoutDifficulty } from './BreakoutGame.svelte';

	interface Props {
		difficulty: BreakoutDifficulty;
		onChange: (difficulty: BreakoutDifficulty) => void;
	}

	let { difficulty, onChange }: Props = $props();

	const difficultyLabels: Record<BreakoutDifficulty, string> = {
		easy: 'Easy',
		normal: 'Normal',
		hard: 'Hard',
	};

	let open = $state(false);
</script>

<Dropdown bind:open>
	<DropdownButton class="btn-sm">
		{difficultyLabels[difficulty]}
	</DropdownButton>

	<DropdownContent menu>
		{#each Object.keys(difficultyLabels) as difficultyKey}
			<DropdownItem>
				<button
					class:menu-active={difficulty === difficultyKey}
					onclick={() => {
						onChange(difficultyKey as BreakoutDifficulty);
						open = false;
					}}
				>
					{difficultyLabels[difficultyKey as BreakoutDifficulty]}
				</button>
			</DropdownItem>
		{/each}
	</DropdownContent>
</Dropdown>
