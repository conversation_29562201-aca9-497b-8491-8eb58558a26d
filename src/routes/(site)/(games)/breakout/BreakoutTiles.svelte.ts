import { HitBox } from '$lib/util/HitBox';
import { BreakoutTile } from './BreakoutTile';
import { theme } from '$lib/stores/theme.svelte';

export class BreakoutTiles {
	private tiles: BreakoutTile[][] = [];
	private canvas: HTMLCanvasElement;
	private rows = 6;
	private tilesPerRow = 0;
	private rowToColorMap: Record<number, string> = {};
	private cleanupEffect: () => void;

	constructor(canvas: HTMLCanvasElement) {
		this.canvas = canvas;
		this.initTiles();

		this.cleanupEffect = $effect.root(() => {
			$effect(() => {
				if (theme.value) {
					const style = getComputedStyle(document.documentElement);
					this.rowToColorMap = {
						0: style.getPropertyValue('--color-game-breakout-tile-1'),
						1: style.getPropertyValue('--color-game-breakout-tile-2'),
						2: style.getPropertyValue('--color-game-breakout-tile-3'),
						3: style.getPropertyValue('--color-game-breakout-tile-4'),
						4: style.getPropertyValue('--color-game-breakout-tile-5'),
						5: style.getPropertyValue('--color-game-breakout-tile-6'),
					};
					this.tiles.forEach((tileRow, row) => {
						tileRow.forEach((tile) => {
							tile.color = this.rowToColorMap[row];
						});
					});
				}
			});
		});
	}

	private get minTileHeight() {
		return 16 * window.devicePixelRatio;
	}

	private get marginY() {
		if (window.matchMedia('(min-width: 640px)').matches) {
			return 80 * window.devicePixelRatio;
		}

		return 56 * window.devicePixelRatio;
	}

	private get marginX() {
		if (window.matchMedia('(min-width: 640px)').matches) {
			return 32 * window.devicePixelRatio;
		}

		return 24 * window.devicePixelRatio;
	}

	private get tilesPerRowBasedOnScreenSize() {
		if (window.matchMedia('(min-width: 1024px)').matches) {
			return 12;
		}

		if (window.matchMedia('(min-width: 768px)').matches) {
			return 10;
		}

		if (window.matchMedia('(min-width: 640px)').matches) {
			return 8;
		}

		return 6;
	}

	hasSomeTileAlive() {
		return this.tiles.some((row) => row.some((tile) => tile.alive));
	}

	reset() {
		this.tiles.forEach((row) => row.forEach((tile) => (tile.alive = true)));
	}

	getCollidingTiles(box: HitBox) {
		return this.tiles
			.flatMap((_) => _)
			.filter((tile) => tile.alive && tile.box.collidesWith(box));
	}

	draw() {
		this.tiles.forEach((row) => row.forEach((tile) => tile.draw()));
	}

	resize() {
		this.tiles.forEach((row, rowIndex) => {
			row.forEach((tile, columnIndex) => {
				const { w, h } = this.getTileSize();
				const { x, y } = this.getTilePosition(rowIndex, columnIndex);
				tile.box.position.x = x;
				tile.box.position.y = y;
				tile.box.rect.w = w;
				tile.box.rect.h = h;
				tile.resize();
			});
		});
	}

	private getTilePosition(row: number, column: number): Pick<HitBox, 'x' | 'y'> {
		const { w, h } = this.getTileSize();

		return {
			x: this.marginX + column * w,
			y: this.marginY + row * h,
		};
	}

	private getTileSize(): Pick<HitBox, 'w' | 'h'> {
		return {
			w: (this.canvas.width - 2 * this.marginX) / this.tilesPerRow,
			h: Math.max(this.minTileHeight, this.canvas.height / 5 / this.rows),
		};
	}

	private initTiles() {
		this.tilesPerRow = this.tilesPerRowBasedOnScreenSize;
		this.tiles = Array(this.rows)
			.fill(0)
			.map((_, row) =>
				Array(this.tilesPerRow)
					.fill(0)
					.map((_, column) => {
						return new BreakoutTile(
							new HitBox(
								{
									x: 0,
									y: 0,
									...this.getTileSize(),
								},
								this.getTilePosition(row, column),
							),
							this.rowToColorMap[row],
							this.canvas,
						);
					}),
			);
	}

	dispose() {
		this.cleanupEffect?.();
	}
}
