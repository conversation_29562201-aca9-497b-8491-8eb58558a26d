import { shuffle } from '$lib/functions/shuffle';
import { wait } from '$lib/functions/wait';
import { Grid } from '$lib/util/Grid.svelte';
import type { Timer } from '$lib/util/Timer.svelte';
import { pickRandom, Smush32 } from '@thi.ng/random';
import { emojis } from './emojis';
import type { GameSound } from '$lib/util/GameSound.svelte';
import type { memoryGameSoundResources } from './memoryGameSoundResources';

export class MemoryGameItem {
	value = $state('');
	flipped = $state(false);
	matched = $state(false);

	constructor(value: string) {
		this.value = value;
	}
}

const flipWaitTime = 800;

export const memoryGameDifficulties = ['easy', 'normal', 'hard'] as const;
export type MemoryGameDifficulty = (typeof memoryGameDifficulties)[number];

export const difficultyToGridSize: Record<MemoryGameDifficulty, { rows: number; cols: number }> = {
	easy: { rows: 4, cols: 4 },
	normal: { rows: 6, cols: 6 },
	hard: { rows: 8, cols: 8 },
};

const getGridSize = (difficulty: MemoryGameDifficulty) => {
	return difficultyToGridSize[difficulty];
};

export type MemoryGameSounds = Record<keyof typeof memoryGameSoundResources, GameSound>;

type MemoryGameProps = {
	timer: Timer;
	seed?: number;
	difficulty?: MemoryGameDifficulty;
	sounds?: MemoryGameSounds;
	onGameOver?: () => void;
};

export class MemoryGameGame {
	grid = $state<Grid<MemoryGameItem>>(new Grid({ rows: 0, columns: 0 }));
	flips = $state(0);
	difficulty = $state<MemoryGameDifficulty>('easy');
	timer: Timer;
	#sounds?: MemoryGameSounds;
	#random: Smush32;
	#itemsToUnflip: MemoryGameItem[] = [];
	#onGameOver?: () => void;

	constructor({ seed = 0, difficulty = 'easy', timer, onGameOver, sounds }: MemoryGameProps) {
		this.timer = timer;
		this.difficulty = difficulty;
		this.#onGameOver = onGameOver;
		this.#sounds = sounds;
		this.#random = new Smush32(seed);

		this.restart();
	}

	matched = $derived.by(() => {
		return this.grid.grid
			.map((row) => {
				return row.filter((item) => item?.matched).length;
			})
			.reduce((a, b) => a + b, 0);
	});

	matchesMissing = $derived.by(() => {
		return this.grid.rows * this.grid.columns - this.matched;
	});

	/** Smaller is better */
	get score() {
		return Math.floor(this.timer.time / 1000 + this.flips);
	}

	debugRestart() {
		if (!import.meta.env.DEV) {
			return;
		}

		const gridSize = getGridSize(this.difficulty);
		const numPairs = (gridSize.rows * gridSize.cols) / 2;
		const gameEmojis = shuffle(emojis, () => this.#random.float(1))
			.slice(0, numPairs)
			.flatMap((emoji) => [emoji, emoji]);

		this.grid = new Grid<MemoryGameItem>(
			{
				rows: gridSize.rows,
				columns: gridSize.cols,
				reactive: false,
			},
			(row, col) => {
				const index = row * gridSize.cols + col;
				const randomEmoji = gameEmojis[index];

				return new MemoryGameItem(randomEmoji);
			},
		);

		this.flips = 0;
	}

	debugPrint() {
		if (!import.meta.env.DEV) {
			return;
		}

		this.grid.grid.forEach((row) => {
			const printableRow = row.map((row) => $state.snapshot(row?.value));

			console.log(printableRow);
		});
	}

	restart() {
		const gridSize = getGridSize(this.difficulty);
		const numPairs = (gridSize.rows * gridSize.cols) / 2;
		const gameEmojis = shuffle(
			shuffle(emojis, () => this.#random.float(1))
				.slice(0, numPairs)
				.flatMap((emoji) => [emoji, emoji]),
			() => this.#random.float(1),
		);

		this.grid = new Grid<MemoryGameItem>(
			{
				rows: gridSize.rows,
				columns: gridSize.cols,
				reactive: false,
			},
			(row, col) => {
				const index = row * gridSize.cols + col;
				const randomEmoji = gameEmojis[index];

				return new MemoryGameItem(randomEmoji);
			},
		);

		this.flips = 0;
	}

	canFlip(item: MemoryGameItem) {
		if (!this.timer.started) {
			return true;
		}

		return this.timer.running && !item.flipped && !item.matched;
	}

	/**
	 * @returns true if item has matched, false if unmatched,
	 * and null if the flip didn't happen */
	async flip(item: MemoryGameItem): Promise<boolean | null> {
		if (!this.timer.started) {
			this.timer.start();
		}

		if (!this.canFlip(item)) {
			return null;
		}

		// Unflip previous items first
		if (this.#itemsToUnflip.length > 0) {
			this.#sounds?.flipBack.play();
		}

		this.#itemsToUnflip.forEach((item) => (item.flipped = false));
		this.#itemsToUnflip = [];

		item.flipped = true;
		this.flips++;

		this.#sounds?.flip.play();

		const flippedItems = this.grid
			.flatten()
			.filter((i): i is MemoryGameItem => i !== null && i.flipped && !i.matched);

		if (flippedItems.length === 2) {
			const [item1, item2] = flippedItems as [MemoryGameItem, MemoryGameItem];

			if (item1.value === item2.value) {
				item1.matched = true;
				item2.matched = true;
				item1.flipped = false;
				item2.flipped = false;
				this.#checkWin();
				this.#sounds?.matched.play();
			} else {
				this.#itemsToUnflip = flippedItems;

				await wait(flipWaitTime);

				if (this.#itemsToUnflip === flippedItems) {
					item1.flipped = false;
					item2.flipped = false;
					this.#sounds?.flipBack.play();
					this.#itemsToUnflip = [];
				}
			}
		}

		return !!flippedItems[0]?.matched;
	}

	#checkWin() {
		if (
			this.grid
				.flatten()
				.every((item): item is MemoryGameItem => item !== null && item.matched)
		) {
			this.#onGameOver?.();
		}
	}
}
