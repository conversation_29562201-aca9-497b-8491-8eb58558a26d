<script lang="ts" module>
	export const difficultyTexts: Record<MemoryGameDifficulty, string> = {
		easy: 'Easy',
		normal: 'Normal',
		hard: 'Hard',
	};
</script>

<script lang="ts">
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import { memoryGameDifficulties, type MemoryGameDifficulty } from './MemoryGameGame.svelte';

	interface Props {
		open?: boolean;
		difficulty: MemoryGameDifficulty;
		onChange: (newDifficulty: MemoryGameDifficulty) => void;
	}

	let { open = $bindable(false), difficulty, onChange }: Props = $props();
</script>

<Dropdown bind:open>
	<DropdownButton class="btn-sm">
		{difficultyTexts[difficulty]}
	</DropdownButton>

	<DropdownContent menu>
		{#each memoryGameDifficulties as _difficulty}
			<DropdownItem>
				<button
					class:menu-active={_difficulty === difficulty}
					onclick={() => {
						onChange(_difficulty);
					}}
				>
					{difficultyTexts[_difficulty]}
				</button>
			</DropdownItem>
		{/each}
	</DropdownContent>
</Dropdown>
