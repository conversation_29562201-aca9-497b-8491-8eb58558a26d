<script lang="ts">
	import type { MemoryGameItem } from './MemoryGameGame.svelte';
	import { cn } from '$lib/util/cn';
	import type { Grid } from '$lib/util/Grid.svelte';
	import { scale } from 'svelte/transition';
	import { quadInOut } from 'svelte/easing';

	interface Props {
		class?: string;
		grid: Grid<MemoryGameItem>;
		animated?: boolean;
		onclick?: (item: MemoryGameItem) => void;
		inert?: boolean;
	}

	let container = $state<HTMLDivElement>();
	let { class: className, grid, animated = true, onclick, ...props }: Props = $props();
</script>

<div
	class={cn('grid gap-1 p-1 w-full aspect-square bg-base-300', className)}
	style="grid-template-columns: repeat({grid.columns}, 1fr);"
	bind:this={container}
	{...props}
>
	{#each grid.grid as row, rowIndex}
		{#each row as cell, colIndex (`${grid.columns}, ${rowIndex}, ${colIndex}`)}
			{@const item = cell as MemoryGameItem}
			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<div
				role="button"
				aria-label="row {rowIndex + 1}, column {colIndex + 1}, card is {item.flipped ||
				item.matched
					? 'flipped'
					: 'unflipped'}"
				class={cn(
					'@container p-0 rounded-none size-full relative transition-transform duration-300 ease-in-out transform-3d',
					{
						'rotate-y-180': item.flipped || item.matched,
						'cursor-pointer': !item.flipped && !item.matched,
						'duration-0': !animated,
					},
				)}
				aria-disabled={item.flipped || item.matched}
				tabindex="0"
				onclick={() => {
					onclick?.(item);
				}}
			>
				<!-- Back -->
				<div class="size-full bg-base-content rotate-y-0 backface-hidden"></div>

				<!-- Front -->
				{#if !item.matched}
					<div
						transition:scale={{
							start: 0.6,
							opacity: 0,
							delay: animated ? 400 : 0,
							duration: animated ? 300 : 0,
							easing: quadInOut,
						}}
						class={cn(
							'absolute inset-0 flex items-center justify-center rotate-y-180 backface-hidden bg-base-100 dark:bg-base-content/60 text-[90cqw]',
						)}
					>
						{item.value}
					</div>
				{/if}
			</div>
		{/each}
	{/each}
</div>
