<script lang="ts">
	import MemoryGameGameRenderer from './MemoryGameGameRenderer.svelte';
	import { MemoryGameGame } from './MemoryGameGame.svelte';
	import { Timer } from '$lib/util/Timer.svelte';
	import { untrack } from 'svelte';
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();

	let game = $state<MemoryGameGame>();

	$effect.pre(() => {
		untrack(() => {
			const newGame = new MemoryGameGame({
				difficulty: 'easy',
				onGameOver: () => {},
				timer: new Timer(),
				seed: 0,
			});

			newGame.grid.at({ row: 0, column: 0 })!.flipped = true;
			newGame.grid.at({ row: 0, column: 0 })!.value = '🐶';
			newGame.grid.at({ row: 2, column: 2 })!.flipped = true;
			newGame.grid.at({ row: 2, column: 2 })!.value = '🦋';

			newGame.grid.at({ row: 0, column: 1 })!.matched = true;
			newGame.grid.at({ row: 2, column: 0 })!.matched = true;
			newGame.grid.at({ row: 0, column: 3 })!.matched = true;
			newGame.grid.at({ row: 1, column: 2 })!.matched = true;
			newGame.grid.at({ row: 2, column: 3 })!.matched = true;
			newGame.grid.at({ row: 3, column: 1 })!.matched = true;

			game = newGame;
		});
	});
</script>

<div class={cn('relative size-full p-4', className)} aria-hidden="true" {...props}>
	{#if game}
		<MemoryGameGameRenderer grid={game.grid} animated={false} inert />
	{/if}
</div>
