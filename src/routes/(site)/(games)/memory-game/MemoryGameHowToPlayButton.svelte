<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';
	import TouchTutorialFigure from '$lib/components/TouchTutorialFigure.svelte';
	import { Timer } from '$lib/util/Timer.svelte';
	import { untrack } from 'svelte';
	import { MemoryGameGame } from './MemoryGameGame.svelte';
	import MemoryGameGameRenderer from './MemoryGameGameRenderer.svelte';

	let isOpen = $state(false);
</script>

<button class="btn btn-sm" onclick={() => (isOpen = true)}>How to Play</button>

<Dialog bind:isOpen>
	<article>
		<h2>How to Play Memory Game</h2>

		<p>Click on a card to reveal it.</p>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 0,
				column: 1,
			}}
			rows={4}
			columns={4}
			fadeTouch
			figcaption="Revealing a card"
		>
			{#snippet gameBeforeTouch()}
				<MemoryGameGameRenderer
					animated={false}
					grid={untrack(() => {
						const game = new MemoryGameGame({
							difficulty: 'easy',
							timer: new Timer(),
							seed: 1,
						});

						return game.grid;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<MemoryGameGameRenderer
					animated={false}
					grid={untrack(() => {
						const game = new MemoryGameGame({
							difficulty: 'easy',
							timer: new Timer(),
							seed: 1,
						});

						const item1 = game.grid.at({
							row: 0,
							column: 1,
						})!;

						item1.value = '🐶';
						item1.flipped = true;

						return game.grid;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>Find matching pairs.</p>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 2,
				column: 2,
			}}
			rows={4}
			columns={4}
			fadeTouch
			figcaption="Matched pairs"
		>
			{#snippet gameBeforeTouch()}
				<MemoryGameGameRenderer
					animated={false}
					grid={untrack(() => {
						const game = new MemoryGameGame({
							difficulty: 'easy',
							timer: new Timer(),
							seed: 1,
						});

						const item1 = game.grid.at({
							row: 0,
							column: 1,
						})!;

						item1.value = '🐶';
						item1.flipped = true;

						return game.grid;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<MemoryGameGameRenderer
					animated={false}
					grid={untrack(() => {
						const game = new MemoryGameGame({
							difficulty: 'easy',
							timer: new Timer(),
							seed: 1,
						});

						const item1 = game.grid.at({
							row: 0,
							column: 1,
						})!;

						item1.value = '🐶';
						item1.flipped = true;

						const item2 = game.grid.at({
							row: 2,
							column: 2,
						})!;

						item2.value = '🐶';
						item2.flipped = true;

						return game.grid;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>Matched pairs will disappear.</p>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 2,
				column: 2,
			}}
			rows={4}
			columns={4}
			noTouch
			figcaption="Matched pairs disappearing"
		>
			{#snippet gameBeforeTouch()}
				<MemoryGameGameRenderer
					animated={false}
					grid={untrack(() => {
						const game = new MemoryGameGame({
							difficulty: 'easy',
							timer: new Timer(),
							seed: 1,
						});

						const item1 = game.grid.at({
							row: 0,
							column: 1,
						})!;

						item1.value = '🐶';
						item1.flipped = true;

						const item2 = game.grid.at({
							row: 2,
							column: 2,
						})!;

						item2.value = '🐶';
						item2.flipped = true;

						return game.grid;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<MemoryGameGameRenderer
					animated={false}
					grid={untrack(() => {
						const game = new MemoryGameGame({
							difficulty: 'easy',
							timer: new Timer(),
							seed: 1,
						});

						const item1 = game.grid.at({
							row: 0,
							column: 1,
						})!;

						item1.value = '🐶';
						item1.matched = true;

						const item2 = game.grid.at({
							row: 2,
							column: 2,
						})!;

						item2.value = '🐶';
						item2.matched = true;

						return game.grid;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>Match all pairs to win!</p>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 2,
				column: 2,
			}}
			rows={4}
			columns={4}
			noTouch
			figcaption="Winning the game"
		>
			{#snippet gameBeforeTouch()}
				<MemoryGameGameRenderer
					animated={false}
					grid={untrack(() => {
						const game = new MemoryGameGame({
							difficulty: 'easy',
							timer: new Timer(),
							seed: 1,
						});

						game.grid.grid.forEach((row) => {
							row.forEach((item) => {
								item!.matched = true;
							});
						});

						const item1 = game.grid.at({
							row: 1,
							column: 1,
						})!;

						item1.value = '🐱';
						item1.flipped = true;
						item1.matched = false;

						const item2 = game.grid.at({
							row: 1,
							column: 3,
						})!;

						item2.value = '🐱';
						item2.flipped = true;
						item2.matched = false;

						return game.grid;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<MemoryGameGameRenderer
					animated={false}
					grid={untrack(() => {
						const game = new MemoryGameGame({
							difficulty: 'easy',
							timer: new Timer(),
							seed: 1,
						});

						game.grid.grid.forEach((row) => {
							row.forEach((item) => {
								item!.matched = true;
							});
						});

						return game.grid;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>Have fun!</p>
	</article>
</Dialog>
