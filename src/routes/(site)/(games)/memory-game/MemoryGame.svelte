<script lang="ts">
	import { onMount, onDestroy, untrack } from 'svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import {
		MemoryGameGame,
		memoryGameDifficulties,
		type MemoryGameDifficulty,
	} from './MemoryGameGame.svelte';
	import MemoryGameGameRenderer from './MemoryGameGameRenderer.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import { Smush32 } from '@thi.ng/random';
	import { Stats } from '$lib/util/Stats.svelte';
	import { memoryGameSoundResources } from './memoryGameSoundResources';
	import InfoButton from '$lib/components/InfoButton.svelte';
	import MemoryGameInfoModal from './MemoryGameInfoModal.svelte';
	import MemoryGameHowToPlayButton from './MemoryGameHowToPlayButton.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';
	import MemoryGameDifficultyButton, {
		difficultyTexts,
	} from './MemoryGameDifficultyButton.svelte';
	import { Timer } from '$lib/util/Timer.svelte';
	import {
		elementTagsToIgnoreEvents,
		type ShortcutsTags,
	} from '$lib/util/Undoable/UndoableKeyboardListener';

	let isDifficultyDropdownOpen = $state(false);
	let isInfoModalOpen = $state(false);

	const context = new GameContext({
		gameName: 'Memory Game',
		gameKey: 'memory-game',
		GameClass: MemoryGameGame,
		sounds: {
			resources: memoryGameSoundResources,
			lifecycle: {
				createGame: memoryGameSoundResources.start,
				win: memoryGameSoundResources.win,
			},
		},
		settings: {
			defaultSettings: {
				difficulty: 'easy' as MemoryGameDifficulty,
			},
		},
		stats({ props, context }) {
			const game = context.game!;

			return {
				stats: new Stats({
					...props,
					liveStats: {
						flips: {
							name: 'Flips',
							unit: 'plain',
							value() {
								return game.flips;
							},
							metrics: {
								total: {
									key: 'totalFlips',
									name: 'Total Flips',
								},
								average: {
									key: 'averageFlips',
									name: 'Average Flips',
								},
								min: {
									key: 'fewestFlips',
									name: 'Fewest Flips',
									useAsBest: true,
								},
								max: {
									key: 'maxFlips',
									name: 'Max Flips',
								},
							},
						},
						matchedCards: {
							name: 'Matched Cards',
							unit: 'plain',
							value() {
								return context.game?.matched ?? 0;
							},
						},
						matchesMissing: {
							name: 'Matches Missing',
							unit: 'plain',
							value() {
								return context.game?.matchesMissing ?? 0;
							},
						},
					},
					initialPinnedStats: ['time', 'matchesMissing'],
				}),
				visibleStats: [
					'bestTime',
					'averageTime',
					'fewestFlips',
					'averageFlips',
					'wonGames',
					'totalGames',
				],
				canUpdateWithGameLost(game) {
					return game.flips > 0;
				},
			};
		},
		variants: {
			map: {
				difficulty: {
					allValues: memoryGameDifficulties,
					format: (difficulty) => difficultyTexts[difficulty],
				},
			},
			fromGame(game) {
				return {
					difficulty: game.difficulty,
				};
			},
			getStatsVariant(variants) {
				return variants.difficulty ?? 'easy';
			},
			getLeaderboardVariant(variants) {
				return variants.difficulty ?? 'easy';
			},
		},
		defaultGameProps(context) {
			const settings = context.settingsManager.settings;

			return {
				difficulty: settings.difficulty,
				seed: performance.now(),
				timer: context.timer,
				sounds: context.sounds,
				onGameOver: () => {
					context.handleGameOver('won');
				},
			};
		},
		leaderboard({ props }) {
			return {
				leaderboard: new Leaderboard({
					...props,
					firstAvailableDate: new Date('2025/09/26'),
					order: 'lower-first',
					hasLevel: false,
					hasMoves: true,
					hasTime: true,
				}),
				sendScoreOn: ['won'],
				getScore(game) {
					return {
						score: game.score,
						moves: game.flips,
					};
				},
			};
		},
		dailyGame(context) {
			return {
				type: 'seed',
				firstAvailableGameDate: new Date('2025/09/26'),
				toProps(seed: number) {
					const random = new Smush32(seed);
					const difficulty = random.int() % memoryGameDifficulties.length;

					return {
						difficulty: memoryGameDifficulties[difficulty],
						seed,
						timer: context.timer,
						sounds: context.sounds,
						onGameOver: () => {
							context.handleGameOver('won');
						},
					};
				},
			};
		},
		onWillCreateGame({ newGameOptions, context }) {
			if (!newGameOptions.isDaily && newGameOptions.difficulty) {
				context.settingsManager.settings.difficulty = newGameOptions.difficulty;
			}
		},
		onGameCreated() {
			isDifficultyDropdownOpen = false;
		},
	});

	let game = $derived.by(() => {
		if (context.game) {
			return context.game;
		}
		return untrack(
			() =>
				new MemoryGameGame({
					difficulty: 'easy',
					seed: performance.now(),
					timer: new Timer(),
					onGameOver: () => {},
				}),
		);
	});

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});

	function onkeypress(e: KeyboardEvent) {
		const tagName = (e.target as HTMLElement)?.tagName as unknown;

		if (elementTagsToIgnoreEvents.includes(tagName as ShortcutsTags)) {
			return;
		}

		if (import.meta.env.DEV) {
			if (e.code === 'Digit0' && e.shiftKey) {
				game.debugRestart();
				context.sounds.start.play();
			}
		}
	}
</script>

<svelte:window {onkeypress} />

<GameLayout>
	{#snippet Island()}
		<GameIsland {context} />
	{/snippet}

	<div class="max-h-screen-no-navbar w-full max-w-xl flex flex-col items-start gap-2">
		<div class="flex items-center gap-2">
			<MemoryGameHowToPlayButton />

			<MemoryGameDifficultyButton
				bind:open={isDifficultyDropdownOpen}
				difficulty={context.game?.difficulty ?? 'easy'}
				onChange={(difficulty) => {
					isDifficultyDropdownOpen = false;
					context.createGame({ difficulty });
				}}
			/>

			<InfoButton onclick={() => (isInfoModalOpen = true)} />

			<MoreGamesButton />
		</div>

		<MemoryGameGameRenderer grid={game.grid} onclick={(item) => game.flip(item)} />
	</div>
</GameLayout>

<MemoryGameInfoModal bind:isOpen={isInfoModalOpen} />
