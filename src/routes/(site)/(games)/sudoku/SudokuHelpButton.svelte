<script lang="ts">
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import type { SudokuGame } from './SudokuGame.svelte';

	interface Props {
		game: SudokuGame | undefined | null;
	}

	let { game }: Props = $props();

	let open = $state(false);

	let disabled = $derived(!game?.canPerformActions);
</script>

<Dropdown class="dropdown-top dropdown-end dropdown flex flex-nowrap md:dropdown-bottom" bind:open>
	<DropdownButton class="grow btn-sm" {disabled}>Help</DropdownButton>

	<DropdownContent menu class="w-52">
		<DropdownItem>
			<button
				class="gap-0"
				{disabled}
				onclick={() => {
					game?.showHint();
					open = false;
				}}
			>
				<span><u class="no-underline lg:underline">H</u>int</span>
			</button>
		</DropdownItem>

		<DropdownItem>
			<button
				class="gap-0"
				{disabled}
				onclick={() => {
					game?.revealSelectedItem();
					open = false;
				}}
			>
				<span><u class="no-underline lg:underline">R</u>eveal Cell</span>
			</button>
		</DropdownItem>

		<DropdownItem>
			<button
				class="gap-0"
				{disabled}
				onclick={() => {
					game?.revealSelectedItemNotes();
					open = false;
				}}
			>
				<span>Reveal Cell N<u class="no-underline lg:underline">o</u>tes</span>
			</button>
		</DropdownItem>

		<div class="divider"></div>

		<DropdownItem>
			<button
				{disabled}
				onclick={() => {
					game?.revealGame();
					open = false;
				}}
			>
				Reveal Game
			</button>
		</DropdownItem>

		<DropdownItem>
			<button
				{disabled}
				onclick={() => {
					game?.reset();
					open = false;
				}}
			>
				Reset Game
			</button>
		</DropdownItem>
	</DropdownContent>
</Dropdown>
