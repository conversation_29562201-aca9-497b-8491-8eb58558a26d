<script lang="ts">
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import { SudokuGame } from './SudokuGame.svelte';

	interface Props {
		game: SudokuGame;
		autoNotes: boolean;
		autoCheck: boolean;
	}

	let { game, autoNotes = $bindable(), autoCheck = $bindable() }: Props = $props();

	let open = $state(false);
	let disabled = $derived(!game?.canPerformActions);
</script>

<Dropdown
	class="dropdown-end dropdown dropdown-top flex flex-nowrap md:dropdown-bottom md:block"
	bind:open
>
	<DropdownButton {disabled} class="btn-sm grow" aria-label="Show settings">
		<SettingsIcon class="size-5" />
	</DropdownButton>

	<DropdownContent>
		<DropdownItem>
			<Toggle aria-label="toggle auto notes" bind:checked={autoNotes} {disabled}
				>Auto Notes</Toggle
			>
		</DropdownItem>

		<DropdownItem>
			<Toggle aria-label="toggle auto check" bind:checked={autoCheck} {disabled}
				>Auto Check</Toggle
			>
		</DropdownItem>
	</DropdownContent>
</Dropdown>
