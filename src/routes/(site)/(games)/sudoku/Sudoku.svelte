<script lang="ts">
	import { onDestroy, onMount } from 'svelte';
	import { SudokuGame, type SudokuDifficulty } from './SudokuGame.svelte';
	import { fly } from 'svelte/transition';
	import SudokuInfoModal from './SudokuInfoModal.svelte';
	import { wait } from '$lib/functions/wait';
	import SudokuButtons from './SudokuButtons.svelte';
	import { sudokuSoundResources } from './sudokuSoundResources';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import { cn } from '$lib/util/cn';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import capitalize from 'lodash/capitalize';
	import { Stats } from '$lib/util/Stats.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import InfoButton from '$lib/components/InfoButton.svelte';
	import { supabase } from '$lib/api/supabase';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import SudokuHowToPlayButton from './SudokuHowToPlayButton.svelte';
	import NewGameButton from '$lib/components/NewGameButton.svelte';
	import CheckIcon from '$lib/components/Icons/CheckIcon.svelte';
	import { quadInOut } from 'svelte/easing';
	import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	let gamePlayId: number | null = $state(null);
	let isHoldingControlOrCommand = $state(false);
	let isDifficultyDropdownOpen = $state(false);
	let isInfoModalOpen = $state(false);
	let gameIsland: GameIsland<any, any, any> | null = $state(null);

	const context = new GameContext({
		gameName: 'Sudoku',
		GameClass: SudokuGame,
		gameKey: 'sudoku',
		settings: {
			defaultSettings: {
				difficulty: 'easy' as SudokuDifficulty,
				autoNotes: false,
				autoCheck: false,
			},
		},
		sounds: {
			resources: sudokuSoundResources,
			lifecycle: {
				win: sudokuSoundResources.gameWin,
			},
		},
		variants: {
			map: {
				difficulty: {
					allValues: ['easy', 'medium', 'hard'] as SudokuDifficulty[],
					format: capitalize,
				},
			},
			fromGame(game) {
				return {
					difficulty: game?.difficulty ?? 'easy',
				};
			},
			getStatsVariant(variants) {
				return variants.difficulty ?? 'easy';
			},
			getLeaderboardVariant(variants) {
				return capitalize(variants.difficulty ?? 'easy');
			},
		},
		defaultGameProps(context) {
			return {
				difficulty: context.settingsManager.settings.difficulty,
				settingsManager: context.settingsManager,
				timer: context.timer,
				onWin: onGameWin,
			};
		},
		stats({ context, props }) {
			return {
				stats: new Stats({
					...props,
					gameVariant: context.game?.difficulty ?? ('easy' as SudokuDifficulty),
					liveStats: {},
					initialPinnedStats: ['time', null],
				}),
				canUpdateWithGameLost(game) {
					return game.timer.started && !game.isOver;
				},
				visibleStats: ['bestTime', 'averageTime', 'wonGames', 'totalGames'],
			};
		},
		leaderboard({ props }) {
			return {
				leaderboard: new Leaderboard({
					...props,
					order: 'lower-first',
					firstAvailableDate: new Date('2025/05/08'),
				}),
				sendScoreOn: ['won'],
				getScore(game) {
					return {
						score: game.score,
					};
				},
			};
		},
		dailyGame(context) {
			return {
				type: 'fetch',
				firstAvailableGameDate: new Date('2025/01/11'),
				toProps(fetched: { game: string; solution: string; difficulty: SudokuDifficulty }) {
					return {
						settingsManager: context.settingsManager,
						onWin: onGameWin,
						difficulty: fetched.difficulty,
						timer: context.timer,
						fromGame: {
							game: fetched.game,
							solvedGame: fetched.solution,
							difficulty: fetched.difficulty,
						},
					};
				},
				async fetchGame(date) {
					const games = (
						await supabase
							.rpc('daily_sudoku', {
								target_date: `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`,
							})
							.select('game,solution,difficulty')
							.throwOnError()
					).data as { game: string; solution: string; difficulty: SudokuDifficulty }[];

					if (!games || games.length === 0) {
						throw new Error('Game not found');
					}

					return games[0];
				},
			};
		},
		onWillCreateGame({ previousGame, context, newGameOptions }) {
			previousGame?.dispose();
			context.settingsManager.settings.difficulty = newGameOptions.difficulty;
			resetState();
		},
		async onGameCreated() {
			await wait(600);

			gamePlayId = Math.random() * 1e9;
		},
		isGameReady(game) {
			return game.ready;
		},
		onDispose(context) {
			context.game?.dispose();
		},
	});

	let game = $derived(context.game);

	async function onGameWin() {
		context.handleGameOver('won');
	}

	function resetState() {
		closeDropdownsAndModals();
		gamePlayId = null;
	}

	function toggleNotes(newValue?: boolean) {
		game?.toggleIsTakingNotes(newValue);
	}

	const replayCurrentGame = async () => {
		resetState();
		game?.reset();

		await wait(600);

		gamePlayId = Math.random() * 1e9;
	};

	function handleKeyDown(event: KeyboardEvent) {
		if (!game?.canPerformActions) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (key === 'h') {
			game?.showHint();
		}

		if (key === 'r') {
			game?.revealSelectedItem();
		}

		if (key === 'o') {
			game?.revealSelectedItemNotes();
		}

		if (key === 'escape') {
			game?.deselectItem();
		}

		if (key === 'shift' && !event.repeat) {
			toggleNotes(true);
		}

		if (key === 'n') {
			if (!event.shiftKey) {
				toggleNotes();
			}
		}

		isHoldingControlOrCommand = event.metaKey || event.ctrlKey;
	}

	function handleKeyUp(event: KeyboardEvent) {
		if (!game?.canPerformActions) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (key === 'shift') {
			toggleNotes(false);
		}

		isHoldingControlOrCommand = event.metaKey || event.ctrlKey;
	}

	function handleClick(event: MouseEvent) {
		if (!game?.canPerformActions) {
			return;
		}

		const ignoreTags = ['BUTTON', 'INPUT', 'TEXTAREA', 'SELECT', 'LABEL', 'SUMMARY'];
		let shouldHandle = true;
		let currentElement = event.target as HTMLElement | null;

		while (currentElement !== null) {
			if (ignoreTags.includes(currentElement.tagName)) {
				shouldHandle = false;
				break;
			}
			currentElement = currentElement.parentElement;
		}

		if (shouldHandle) {
			game?.deselectItem();
		}
	}

	function closeDropdownsAndModals() {
		isDifficultyDropdownOpen = false;
	}

	function showDailyGameIsland() {
		closeDropdownsAndModals();
		gameIsland?.changeVariant('daily-game');
	}

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<svelte:window onkeydown={handleKeyDown} onkeyup={handleKeyUp} onclick={handleClick} />

<SudokuInfoModal bind:isOpen={isInfoModalOpen} />

<GameLayout
	adsProps={{
		minHeightToShowHorizontalAd: 922,
	}}
>
	{#snippet Island()}
		<GameIsland bind:this={gameIsland} {context} />
	{/snippet}

	{#if game && gamePlayId}
		<!-- svelte-ignore a11y_no_static_element_interactions -->
		<section
			class="flex h-full w-full touch-manipulation select-none items-stretch"
			oncontextmenu={(event) => event.preventDefault()}
		>
			<div class="mx-auto flex w-full max-w-xl flex-col items-stretch justify-center gap-4">
				<div class="relative flex items-center justify-start gap-2">
					<SudokuHowToPlayButton />

					<NewGameButton
						variant="short"
						buttonClass="btn-sm"
						onReplayCurrentGame={replayCurrentGame}
						onPlayNewGame={() => context.createGame()}
						onPlayDailyGame={showDailyGameIsland}
					/>

					<Dropdown bind:open={isDifficultyDropdownOpen}>
						<DropdownButton class="btn-sm capitalize">
							{game.difficulty}
						</DropdownButton>

						<DropdownContent menu>
							<DropdownItem>
								<button
									class:menu-active={game?.difficulty === 'easy'}
									onclick={() => context.createGame({ difficulty: 'easy' })}
									>Easy</button
								>
							</DropdownItem>
							<DropdownItem>
								<button
									class:menu-active={game?.difficulty === 'medium'}
									onclick={() => context.createGame({ difficulty: 'medium' })}
									>Medium</button
								>
							</DropdownItem>
							<DropdownItem>
								<button
									class:menu-active={game?.difficulty === 'hard'}
									onclick={() => context.createGame({ difficulty: 'hard' })}
									>Hard</button
								>
							</DropdownItem>
						</DropdownContent>
					</Dropdown>

					<!-- Game buttons -->
					<SudokuButtons
						class="hidden md:flex"
						{game}
						bind:autoCheck={context.settingsManager.settings.autoCheck}
						bind:autoNotes={context.settingsManager.settings.autoNotes}
					/>

					<!-- Info button -->
					<div
						class="flex items-center justify-center gap-2 md:fixed md:bottom-4 md:right-4"
					>
						<MoreGamesButton class="btn-sm" />

						<InfoButton
							onclick={() => (isInfoModalOpen = true)}
							class="hidden md:flex"
						/>

						<button
							aria-label="Open info dialog"
							class="btn btn-sm md:hidden order-first"
						>
							<InfoSolidIcon
								class="size-5"
								onclick={() => (isInfoModalOpen = true)}
							/>
						</button>
					</div>
				</div>

				<!-- Game board -->
				<div
					transition:fly={{ duration: 300, y: 50 }}
					class="flex w-full grow items-center justify-center md:grow-0 md:items-start"
				>
					<div
						class="grid w-full cursor-pointer grid-cols-3 gap-1 bg-game-sudoku-board-outer-lines p-1 font-medium"
					>
						{#each game?.clusteredBoard ?? [] as cluster, clusterIndex}
							<div
								class="grid grid-cols-3 gap-[2px] bg-game-sudoku-board-inner-lines"
							>
								{#each cluster as row, rowIndex}
									{#each row as item, columnIndex}
										{@const clusterPosition = {
											row: rowIndex,
											column: columnIndex,
										}}
										{@const notes = game?.getNotes(
											clusterPosition,
											clusterIndex,
										)}
										{@const state = game?.getItemState(
											clusterPosition,
											clusterIndex,
										)}
										{@const hasError = game?.hasError(
											clusterPosition,
											clusterIndex,
										)}
										{@const isHighlighted = game?.isHighlighted(
											clusterPosition,
											clusterIndex,
										)}
										{@const isSelected = game?.isSelected(
											clusterPosition,
											clusterIndex,
										)}
										{@const matchesSolution = game?.matchesSolution(
											clusterPosition,
											clusterIndex,
										)}
										{@const matchesValue = game?.matchesSelectedValue(
											clusterPosition,
											clusterIndex,
										)}
										{@const position = {
											row: rowIndex,
											column: columnIndex,
										}}

										<!-- svelte-ignore a11y_click_events_have_key_events -->
										<div
											class={cn(
												'flex-center relative border-2 colorblind:border-4 border-transparent transition-colors aspect-square md:text-2xl',
												{
													'bg-game-sudoku-tile-normal':
														state === 'normal',
													'text-game-sudoku-number-normal':
														!hasError &&
														(state === 'normal' ||
															state === 'selected' ||
															state === 'highlighted'),
													'bg-game-sudoku-tile-highlighted':
														isHighlighted,
													'bg-game-sudoku-tile-selected':
														state === 'selected',
													'bg-game-sudoku-tile-immutable':
														!isHighlighted && state === 'immutable',
													'bg-game-sudoku-tile-hint text-game-sudoku-number-hint':
														state === 'hint',
													'bg-game-sudoku-tile-error': state === 'error',
													'border-game-sudoku-tile-border-match':
														matchesValue,
													'border-game-sudoku-tile-border-match-selected':
														isSelected,
													'text-game-sudoku-number-error': hasError,
												},
											)}
											onclick={(event) => {
												game?.selectItem(position, clusterIndex);
												event.stopPropagation();
											}}
										>
											<div
												class="absolute inset-0 grid grid-cols-3 text-center text-[11px] leading-[7px] sm:text-base sm:leading-[10px]"
											>
												{#each game?.possibleValues as value}
													{@const hasNote = !notes?.includes(value)}
													{@const hasItem = !!item}
													{@const shouldHover =
														isSelected &&
														state !== 'immutable' &&
														!hasItem &&
														game?.canEditNotes}

													<span
														class="flex-center transition-all aspect-square"
														class:hover:lg:opacity-60={shouldHover &&
															hasNote}
														class:hover:lg:scale-150={shouldHover}
														class:opacity-0={hasNote}
														onpointerup={(event) => {
															if (event.pointerType === 'touch') {
																return;
															}

															if (
																isSelected &&
																!hasItem &&
																game?.canEditNotes
															) {
																game?.handleValue(
																	value,
																	!isHoldingControlOrCommand,
																);
																event.stopImmediatePropagation();
															}
														}}
													>
														{value}
													</span>
												{/each}
											</div>

											<span>
												{state === 'hint'
													? game?.hint?.value
													: (item ?? '')}
											</span>

											{#if item && game?.settingsManager.settings.autoCheck && !matchesSolution}
												<div
													class="absolute h-[2px] w-3/5 -rotate-45 bg-game-sudoku-check-line"
												></div>
											{/if}

											{#if hasError}
												<WarningSolidIcon
													class="size-2 xs:size-3 sm:size-4 absolute top-0 right-0 text-game-sudoku-number-error rounded-full bg-base-100 hidden colorblind:block"
												/>
											{/if}
										</div>
									{/each}
								{/each}
							</div>
						{/each}
					</div>
				</div>

				<!-- Mobile -->

				<!-- Game buttons -->
				<SudokuButtons
					class="grid grid-cols-5 md:hidden"
					{game}
					bind:autoCheck={context.settingsManager.settings.autoCheck}
					bind:autoNotes={context.settingsManager.settings.autoNotes}
				/>

				<!-- Game numbers buttons -->
				<div class="mb-6 flex w-full justify-between md:gap-2">
					{#each [1, 2, 3, 4, 5, 6, 7, 8, 9] as number}
						{@const hasSelection =
							game?.selectedItemValue === number ||
							game?.selectedNotes?.includes(number)}
						{@const isComplete = game?.completionMap?.[number] === 9}

						<div class="indicator shrink-0">
							<button
								disabled={!game?.canPerformActions}
								class={cn(
									'btn btn-sm btn-square shrink-0 xs:btn-md sm:btn-lg md:grow',
									{
										'btn-primary': hasSelection,
									},
								)}
								onclick={() => game?.handleValue(number)}
							>
								{number}
							</button>

							{#if isComplete}
								<div
									transition:fly={{ y: 10, duration: 300, easing: quadInOut }}
									class="indicator-item badge badge-neutral p-0 size-4 sm:size-5 pointer-events-none"
								>
									<CheckIcon />
								</div>
							{/if}
						</div>
					{/each}
				</div>
			</div>
		</section>
	{/if}
</GameLayout>
