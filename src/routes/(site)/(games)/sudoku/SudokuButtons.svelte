<script lang="ts">
	import UndoIcon from '$lib/components/Icons/UndoIcon.svelte';
	import type { SudokuGame } from './SudokuGame.svelte';
	import SudokuHelpButton from './SudokuHelpButton.svelte';
	import SudokuSettingsButton from './SudokuSettingsButton.svelte';

	interface Props {
		game: SudokuGame;
		autoNotes: boolean;
		autoCheck: boolean;
		class?: string;
	}

	let {
		game,
		class: classFromProps = '',
		autoNotes = $bindable(),
		autoCheck = $bindable(),
	}: Props = $props();

	let canUndo = $derived((!game?.isOver && game?.canUndo()) ?? false);

	function toggleNotes() {
		game?.toggleIsTakingNotes();
	}

	function erase() {
		game?.deleteSelectedItem();
	}
</script>

<div class="justify-between gap-2 {classFromProps}">
	<button
		disabled={!game?.canPerformActions || !game?.canEditNotes}
		onclick={toggleNotes}
		class="btn-sm btn flex flex-nowrap"
		class:btn-primary={game?.isTakingNotes}
	>
		<span><u class="no-underline lg:underline">N</u>otes</span>
	</button>

	<button disabled={!game?.canPerformActions} onclick={erase} class="btn-sm btn">Erase</button>

	<SudokuHelpButton {game} />

	<SudokuSettingsButton {game} bind:autoNotes bind:autoCheck />

	<button
		disabled={!canUndo || !game?.canPerformActions}
		aria-label="undo"
		onclick={() => game?.undo()}
		class="btn-sm btn flex"
	>
		<UndoIcon />
	</button>
</div>
