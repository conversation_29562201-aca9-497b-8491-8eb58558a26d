<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full p-4', className)}
	fill="transparent"
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 498 498"
	{...props}
>
	<g clip-path="url(#sudoku-cover__a)">
		<path class="fill-game-sudoku-board-outer-lines" d="M23 23h453v453H23z" />
		<path class="fill-game-sudoku-board-inner-lines" d="M31 31h437v437H31z" />
		<path class="fill-game-sudoku-tile-normal" d="M31 31h143v143H31z" />
		<path
			class="fill-game-sudoku-number-normal"
			d="M102.594 127h9.344V81.906h-9.376L90.906 90v8.375l11.5-7.813h.188V127Z"
		/>
		<path class="fill-game-sudoku-tile-immutable" d="M178 31h143v143H178z" />
		<path
			class="fill-base-content"
			d="M233.844 95.625h8.75c0-4.344 3.406-7.375 7.906-7.375 4.062 0 6.844 2.75 6.844 6.375 0 3.125-1.282 5.281-7 10.781l-15.969 15.125V127h32.781v-7.5h-20.437v-.188l9.469-9.062c7.624-7.281 10.343-10.688 10.343-16.219 0-7.5-6.375-13.125-16.031-13.125-9.781 0-16.656 6.031-16.656 14.719Z"
		/>
		<path class="fill-game-sudoku-tile-normal" d="M325 31h143v143H325z" />
		<path class="fill-game-sudoku-tile-immutable" d="M31 178h143v143H31z" />
		<path
			class="fill-base-content"
			d="M106.969 274h8.969v-8.281h5.937v-7.531h-5.937v-29.282h-13.313c-9.25 13.594-14.156 21.469-18.094 28.938v7.875h22.438V274ZM92.75 258.188c3.594-6.719 7.688-13.126 14.219-22.532h.187v22.782H92.75v-.25Z"
		/>
		<path class="fill-game-sudoku-tile-normal" d="M31 325h143v143H31z" />
		<path
			class="fill-game-sudoku-number-normal"
			d="M90.719 421h9.906l18.813-37.5v-7.594h-32.25v7.5h22.937v.188L90.719 421Z"
		/>
		<path class="fill-game-sudoku-tile-normal" d="M178 178h143v143H178zm0 147h143v143H178z" />
		<path
			class="fill-game-sudoku-number-normal"
			d="M250 422c10.75 0 18.281-5.375 18.281-13.188 0-5.937-4.343-10.312-10.5-11.531v-.187c5.281-1.313 8.844-5.188 8.844-10.032 0-7.062-6.844-12.156-16.625-12.156-9.781 0-16.656 5.125-16.656 12.125 0 4.907 3.594 8.781 8.906 10.063v.187c-6.156 1.188-10.531 5.563-10.531 11.5 0 7.813 7.5 13.219 18.281 13.219Zm0-27.75c-4.281 0-7.312-2.562-7.312-6.25 0-3.719 3.031-6.281 7.312-6.281 4.25 0 7.312 2.562 7.312 6.281 0 3.688-3.062 6.25-7.312 6.25Zm0 20.875c-4.969 0-8.438-2.937-8.438-7.031 0-4.032 3.469-6.969 8.438-6.969 4.969 0 8.406 2.906 8.406 6.969 0 4.094-3.437 7.031-8.406 7.031Z"
		/>
		<path class="fill-game-sudoku-tile-immutable" d="M325 325h143v143H325z" />
		<path
			class="fill-base-content"
			d="M396.188 422c11.718 0 18.812-9 18.812-23.938 0-16.468-8.875-23.187-18.812-23.187-10.063 0-17.376 6.656-17.376 16 0 8.781 6.594 14.719 14.969 14.719 5.907 0 10.375-2.938 12-7.313h.188c.062 9.688-2.969 16.25-9.75 16.25-3.781 0-6.563-1.719-7.563-4.937h-9.312c1.187 7.312 8 12.406 16.844 12.406Zm.031-23.406c-4.719 0-8.219-3.375-8.219-8.094 0-4.562 3.656-8.094 8.312-8.094 4.626 0 8.282 3.594 8.282 8.25 0 4.625-3.625 7.938-8.375 7.938Z"
		/>
		<path class="fill-game-sudoku-tile-normal" d="M325 178h143v143H325z" />
		<path
			class="fill-game-sudoku-number-normal"
			d="M397.781 275c10.063 0 17.375-6.656 17.375-16 0-8.469-5.968-14.688-14.687-14.688-6.094 0-10.531 3.063-12.281 7.25H388c-.031-9.874 3.219-16.218 9.781-16.218 3.719 0 6.407 1.875 7.469 5.094h9.312c-1.25-7.438-8-12.563-16.75-12.563-11.718 0-18.843 9-18.843 23.937 0 15.563 8.031 23.188 18.812 23.188Zm-.125-7.5c-4.625 0-8.25-3.625-8.25-8.281 0-4.594 3.563-7.938 8.344-7.938 4.719 0 8.219 3.375 8.219 8.125-.031 4.532-3.688 8.094-8.313 8.094Z"
		/>
	</g>
	<defs>
		<clipPath id="sudoku-cover__a">
			<path fill="#fff" d="M0 0h498v498H0z" />
		</clipPath>
	</defs>
</svg>
