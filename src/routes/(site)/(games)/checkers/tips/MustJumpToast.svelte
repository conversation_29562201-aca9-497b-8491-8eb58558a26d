<script lang="ts">
	import { onMount } from 'svelte';
	import type { CheckersApp } from '../CheckersApp/CheckersApp.svelte';
	import { toast } from 'svelte-sonner';
	import ToastCard from '$lib/components/ToastCard.svelte';

	let storageKey = 'checkers-must-jump-dialog';
	let canShow = $state(false);

	interface Props {
		game?: CheckersApp;
	}

	let { game }: Props = $props();

	onMount(() => {
		canShow = localStorage.getItem(storageKey) !== 'true';
	});

	function showSuccessToast() {
		toast.custom(ToastCard, {
			id: 'checkers-must-jump-dialog-success',
			componentProps: {
				title: 'Success!',
				description: 'You can change this setting in the rules menu.',
				action: {
					label: 'Got it',
					onClick: () => {
						toast.dismiss('checkers-must-jump-dialog-success');
					},
				},
			},
			dismissable: true,
			duration: 5_000,
		});
	}

	function onMustJump() {
		if (game?.gameManager) {
			game.gameManager.context.settingsManager.settings.mandatoryCapture = true;

			if (game?.gameManager.context.game) {
				game.gameManager.context.game.rules.mandatoryCapture = true;
				game.gameManager.syncSettings();
			}
		}

		localStorage.setItem(storageKey, 'true');
		canShow = false;
		showSuccessToast();
	}

	function onCanChooseJump() {
		if (game?.gameManager) {
			game.gameManager.context.settingsManager.settings.mandatoryCapture = false;

			if (game?.gameManager.context.game) {
				game.gameManager.context.game.rules.mandatoryCapture = false;
				game.gameManager.syncSettings();
			}
		}

		localStorage.setItem(storageKey, 'true');
		canShow = false;
		showSuccessToast();
	}

	$effect(() => {
		if (canShow && game?.gameManager?.context.game?.playerHasCaptureMovesOnTurn) {
			toast.custom(ToastCard, {
				id: 'checkers-must-jump-dialog',
				componentProps: {
					title: 'Must you jump when you have the chance?',
					description:
						"If so, you must always jump, even if it's not the best move. You won't be able to stop in the middle of a jump sequence.",
					cancel: {
						label: 'No, I want to choose when to jump',
						onClick: onCanChooseJump,
					},
					action: {
						label: 'Yes, I must jump if I can',
						onClick: onMustJump,
					},
				},
				duration: Number.POSITIVE_INFINITY,
			});
		}
	});
</script>
