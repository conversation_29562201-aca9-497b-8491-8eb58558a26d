<script lang="ts">
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import type { CheckersApp } from './CheckersApp/CheckersApp.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import {
		checkerPieceColors,
		checkersAtlasData,
		type CheckerPieceColor,
	} from './assets/CheckersAssetsManager';
	import CheckIcon from '$lib/components/Icons/CheckIcon.svelte';
	import type { CheckerPieceOwner } from './model/CheckersGame.svelte';
	import { fly } from 'svelte/transition';
	import Collapse from '$lib/components/Collapse/Collapse.svelte';
	import CollapseContent from '$lib/components/Collapse/CollapseContent.svelte';

	interface Props {
		game?: CheckersApp;
		disabled?: boolean;
	}

	let { game, disabled = false }: Props = $props();

	let settings = $derived(game?.gameManager?.context.settingsManager.settings);

	function getSpriteStyle(color: CheckerPieceColor): string {
		const buttonSize = 48;
		const sprite = checkersAtlasData.frames[color];
		if (!sprite) {
			return '';
		}

		const { x, y, h } = sprite.frame;

		const ratio = buttonSize / h;
		const offsetX = x * ratio;
		const offsetY = y * ratio;

		return `
			background-image: url('${checkersAtlasData.meta.image}');
			background-position: -${offsetX}px -${offsetY}px;
			background-size: ${Object.keys(checkersAtlasData.frames).length * buttonSize}px ${buttonSize}px;
			background-repeat: no-repeat;
		`;
	}

	function changeColor(color: CheckerPieceColor, owner: CheckerPieceOwner) {
		if (!settings) {
			return;
		}

		if (owner === 'player') {
			settings.playerSprite = color;
		} else {
			settings.opponentSprite = color;
		}
	}
</script>

{#snippet IndicatorItem()}
	<span
		class="indicator-item badge badge-secondary pointer-events-none p-0 size-5"
		transition:fly={{ y: -10 }}
	>
		<CheckIcon class="size-5" />
	</span>
{/snippet}

{#snippet Button({
	color,
	owner,
	isSelected,
	disabled,
}: {
	color: CheckerPieceColor;
	owner: CheckerPieceOwner;
	isSelected: boolean;
	disabled: boolean;
})}
	<div class="indicator">
		{#if isSelected}
			{@render IndicatorItem()}
		{/if}

		<button
			class="size-12 transition-opacity cursor-pointer"
			class:opacity-30={disabled}
			aria-label="{owner} {color}"
			style={getSpriteStyle(color)}
			onclick={() => {
				changeColor(color, owner);
			}}
			{disabled}
		>
		</button>
	</div>
{/snippet}

<Dropdown class="grow">
	<DropdownButton class="btn-xs md:btn-sm" aria-label="Show settings" {disabled}>
		<SettingsIcon class="size-5" />
	</DropdownButton>

	<DropdownContent
		class="w-64 left-1/2 -translate-x-1/2 md:left-auto md:translate-x-0 px-0 *:px-6"
	>
		{#if settings}
			<div class="text-sm py-2 text-inherit mb-2">Opponent Pieces</div>

			<div class="flex flex-row justify-between gap-2 mb-2">
				{#each checkerPieceColors as color}
					{@render Button({
						color,
						owner: 'opponent',
						isSelected: settings?.opponentSprite === color,
						disabled: settings?.playerSprite === color,
					})}
				{/each}
			</div>

			<div class="text-sm py-2 text-inherit mb-2">Player Pieces</div>

			<div class="flex flex-row justify-between gap-2 mb-2">
				{#each checkerPieceColors as color}
					{@render Button({
						color,
						owner: 'player',
						isSelected: settings?.playerSprite === color,
						disabled: settings?.opponentSprite === color,
					})}
				{/each}
			</div>

			<Collapse open={settings.enableAnimations}>
				<DropdownItem>
					<Toggle
						aria-label="toggle animations"
						{disabled}
						bind:checked={settings.enableAnimations}
					>
						Animations
					</Toggle>
				</DropdownItem>

				<CollapseContent asSettings>
					<label class="label py-2 flex items-center justify-between gap-4">
						<DropdownItem class="text-sm text-base-content">Speed</DropdownItem>

						<div class="join grid grid-cols-2">
							<input
								class="join-item btn btn-sm"
								type="radio"
								name="animations-speed"
								aria-label="Normal"
								value="normal"
								bind:group={settings.animationsSpeed}
							/>
							<input
								class="join-item btn btn-sm"
								type="radio"
								name="animations-speed"
								aria-label="Fast"
								value="fast"
								bind:group={settings.animationsSpeed}
							/>
						</div>
					</label>
				</CollapseContent>
			</Collapse>
		{/if}
	</DropdownContent>
</Dropdown>
