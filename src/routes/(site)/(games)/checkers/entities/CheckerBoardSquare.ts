import { Container, Graphics } from 'pixi.js';
import type { AnimationSystem } from '../../solitaire/systems/AnimationSystem';
import { Group, Tween } from '@tweenjs/tween.js';

interface CheckerBoardSquareProps {
	row: number;
	column: number;
	animationSystem: AnimationSystem;
	highlightColor: string;
}

export class CheckerBoardSquare {
	view = new Container();
	base = new Graphics().rect(0, 0, 1, 1).fill(0);
	highlight: Graphics;
	row: number;
	column: number;
	animationSystem: AnimationSystem;
	private _isHighlighted = false;

	constructor({ row, column, animationSystem, highlightColor }: CheckerBoardSquareProps) {
		this.row = row;
		this.column = column;
		this.view.eventMode = 'static';
		this.view.cursor = 'pointer';
		this.animationSystem = animationSystem;
		this.highlight = new Graphics().rect(0, 0, 1, 1).fill(highlightColor);
		this.highlight.alpha = 0;

		this.view.addChild(this.base, this.highlight);
	}

	get id() {
		return `square-${this.row}-${this.column}`;
	}

	get isHighlighted() {
		return this._isHighlighted;
	}

	changeColor(newColor: string, highlightColor: string) {
		this.base.clear().rect(0, 0, 1, 1).fill(newColor);
		this.highlight.clear().rect(0, 0, 1, 1).fill(highlightColor);
	}

	startHighlight(props?: { duration?: number; oscillationDuration?: number }) {
		if (this._isHighlighted) {
			return;
		}

		this._isHighlighted = true;

		const group = new Group();

		const animation1 = new Tween({ alpha: this.highlight.alpha })
			.to({ alpha: 0.9 })
			.duration(props?.duration ?? 300)
			.onUpdate(({ alpha }) => {
				this.highlight.alpha = alpha;
			});

		const animation2 = new Tween({ alpha: 0.9 })
			.to({ alpha: 0.7 })
			.duration(props?.oscillationDuration ?? 2000)
			.onUpdate(({ alpha }) => {
				this.highlight.alpha = alpha;
			});

		const animation3 = new Tween({ alpha: 0.7 })
			.to({ alpha: 0.9 })
			.duration(props?.oscillationDuration ?? 2000)
			.onUpdate(({ alpha }) => {
				this.highlight.alpha = alpha;
			});

		animation1.chain(animation2);
		animation2.chain(animation3);
		animation3.chain(animation2);

		group.add(animation1, animation2, animation3);

		this.animationSystem.add({
			id: `highlight-${this.id}`,
			animation: group,
		});
	}

	stopHighlight(props?: { duration: number }) {
		this._isHighlighted = false;

		const animation = new Tween({ alpha: this.highlight.alpha })
			.to({ alpha: 0 })
			.duration(props?.duration ?? 300)
			.onUpdate(({ alpha }) => {
				this.highlight.alpha = alpha;
			});

		this.animationSystem.add({
			id: `highlight-${this.id}`,
			animation: animation,
		});
	}
}
