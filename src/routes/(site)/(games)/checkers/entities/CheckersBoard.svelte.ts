import type { GridItem } from '$lib/models/GridItem';
import { theme, type Theme } from '$lib/stores/theme.svelte';
import { Application, Container, Graphics } from 'pixi.js';
import type { AnimationSystem } from '../../solitaire/systems/AnimationSystem';
import { CheckerBoardSquare } from './CheckerBoardSquare';
import { ads } from '$lib/stores/ads.svelte';
import { untrack } from 'svelte';
import { MediaQuery } from '$lib/util/MediaQuery.svelte';

interface Props {
	app: Application;
	gridSize: number;
	animationSystem: AnimationSystem;
	onPointerUp: (gridItem: GridItem) => void;
}

const checkersBoardSettings = {
	maxSize: 576,
	border: 4,
	canvasPadding: 16,
	marginTop: 152,
	undoRedoButtonsHeight: 72,
};

const checkersBoardSettingsTabletUp = {
	...checkersBoardSettings,
	marginTop: 128,
	undoRedoButtonsHeight: 0,
};

const squareColors: Record<Theme, { light: string; dark: string; highlight: string }> = {
	light: {
		light: '#F9D8AD',
		dark: '#F7BB76',
		highlight: '#FFFFFF',
	},
	'light-classic': {
		light: '#F9D8AD',
		dark: '#F7BB76',
		highlight: '#FFFFFF',
	},
	dark: {
		light: '#ecf9ff',
		dark: '#15191e',
		highlight: '#ecf9ff',
	},
	'dark-classic': {
		light: '#9CA3B1',
		dark: '#1D2128',
		highlight: '#FFFFFF',
	},
};

export class CheckersBoard {
	app: Application;
	view = new Container();
	private _tabletUpMediaQuery = new MediaQuery('(min-width: 768px)');
	private _animationSystem: AnimationSystem;
	private _gridSize: number;
	private _outerBg: Graphics;
	private _innerBg: Graphics;
	private _squares: CheckerBoardSquare[] = [];
	private _clearThemeListener: () => void;
	private _onPointerUp: (gridItem: GridItem) => void;

	constructor({ app, gridSize, animationSystem, onPointerUp }: Props) {
		this.app = app;
		this._animationSystem = animationSystem;
		this._gridSize = gridSize;
		this._outerBg = new Graphics().rect(0, 0, 1, 1).fill(squareColors.light.dark);
		this._innerBg = new Graphics().rect(0, 0, 1, 1).fill(squareColors.light.light);
		this._onPointerUp = onPointerUp;

		this.view.addChild(this._outerBg);
		this.view.addChild(this._innerBg);

		this.createBoard();
		this.resize();

		this._clearThemeListener = $effect.root(() => {
			$effect(() => {
				// Track theme
				theme.value;

				untrack(() => {
					const colors = squareColors[theme.value];

					this._outerBg.clear().rect(0, 0, 1, 1).fill(colors.dark);
					this._innerBg.clear().rect(0, 0, 1, 1).fill(colors.light);

					this._squares.forEach((square) => {
						square.changeColor(colors.dark, colors.highlight);
					});

					this.resize();
				});
			});
		});
	}

	get squares() {
		return this._squares;
	}

	private createBoard() {
		this._squares.forEach((square) => square.view.destroy()); // Clear previous squares
		this._squares = [];

		// Create squares with correct placement
		for (let row = 0; row < this._gridSize; row++) {
			for (let column = 0; column < this._gridSize; column++) {
				if ((row + column) % 2 === 1) {
					const square = new CheckerBoardSquare({
						row,
						column,
						animationSystem: this._animationSystem,
						highlightColor: squareColors[theme.value].highlight,
					});

					square.view.on('pointerup', () => {
						this._onPointerUp({ row, column: column });
					});

					this._squares.push(square);
					this.view.addChild(square.view);
				}
			}
		}
	}

	getSquareAt({ row, column }: GridItem) {
		return (
			this._squares.find((square) => square.row === row && square.column === column) || null
		);
	}

	get checkerSize() {
		return this._squares[0]?.view.width ?? 0;
	}

	get checkerOffset() {
		return this.boardSettings.border;
	}

	get boardSettings() {
		return this._tabletUpMediaQuery.current
			? checkersBoardSettingsTabletUp
			: checkersBoardSettings;
	}

	resize() {
		const { width: adsWidth, height: adsHeight } = ads.effectiveSize;

		const availableHeight =
			this.app.renderer.height -
			this.boardSettings.marginTop -
			adsHeight -
			this.boardSettings.undoRedoButtonsHeight;
		const availableWidth = this.app.renderer.width - adsWidth;

		const boardSize =
			Math.min(availableWidth, availableHeight) - this.boardSettings.canvasPadding * 2;
		const newSize = Math.min(boardSize, this.boardSettings.maxSize);

		let offsetX = this.app.renderer.width / 2 - newSize / 2;

		const adsMargin = 30;
		const belowAdsOffsetX = ads.canShowGameAds
			? Math.max(0, -(this.app.screen.width - (offsetX + newSize) - adsWidth - adsMargin))
			: 0;

		const willBeBellowAds = belowAdsOffsetX > 0;

		if (willBeBellowAds) {
			offsetX -= adsWidth / 2;
		} else {
			offsetX -= belowAdsOffsetX;
		}

		const offsetY =
			this.boardSettings.marginTop + availableHeight / 2 - newSize / 2 + adsHeight;

		this.view.position.set(offsetX, offsetY);

		this._outerBg.setSize(newSize, newSize);
		this._innerBg.setSize(
			newSize - this.boardSettings.border * 2,
			newSize - this.boardSettings.border * 2,
		);
		this._innerBg.position.set(this.boardSettings.border, this.boardSettings.border);

		const squareSize = (newSize - this.boardSettings.border * 2) / this._gridSize;

		let squareIndex = 0;

		for (let row = 0; row < this._gridSize; row++) {
			for (let col = 0; col < this._gridSize; col++) {
				if ((row + col) % 2 === 1) {
					const square = this._squares[squareIndex++];

					square.view.setSize(squareSize, squareSize);
					square.view.position.set(
						this.boardSettings.border + col * squareSize,
						this.boardSettings.border + row * squareSize,
					);
				}
			}
		}
	}

	dispose() {
		this._clearThemeListener();
		this.view.destroy();
	}
}
