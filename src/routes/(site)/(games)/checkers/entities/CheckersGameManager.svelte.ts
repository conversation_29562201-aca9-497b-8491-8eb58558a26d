import { Container, type Application } from 'pixi.js';
import type { CheckerPieceColor, CheckersAssetsManager } from '../assets/CheckersAssetsManager';
import { UndoableKeyboardListener } from '$lib/util/Undoable/UndoableKeyboardListener';
import throttle from 'lodash/throttle';
import { Stats } from '$lib/util/Stats.svelte';
import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
import { checkersSoundResources } from '../assets/checkersSoundResources';
import {
	CheckersGame,
	type CheckersDifficulty,
	type CheckerRules,
} from '../model/CheckersGame.svelte';
import type { AnimationSystem } from '../../solitaire/systems/AnimationSystem';
import { CheckerPiecesManager } from './CheckerPiecesManager.svelte';
import { tick, untrack } from 'svelte';
import { defaultCheckersPreset } from '../model/checkersPresets';
import { checkerDifficulties } from '../model/checkerDifficulties';
import { ads } from '$lib/stores/ads.svelte';

export type CheckersSpeed = 'normal' | 'fast';

export type CheckersOpponent = 'cpu' | 'local-multiplayer';

interface GameManagerParams {
	app: Application;
	assetsManager: CheckersAssetsManager;
	animationSystem: AnimationSystem;
}

type Settings = {
	newGameThrottleDelay: number;
	undoAndRedoThrottleDelay: number;
	moveDuration: number;
	cpuTurnDelay: number;
	oscillationDuration: number;
};

const settingsMap: Record<CheckersSpeed | 'off', Settings> = {
	normal: {
		newGameThrottleDelay: 1000,
		undoAndRedoThrottleDelay: 150,
		moveDuration: 300,
		cpuTurnDelay: 500,
		oscillationDuration: 1200,
	},
	fast: {
		newGameThrottleDelay: 1000,
		undoAndRedoThrottleDelay: 50,
		moveDuration: 100,
		cpuTurnDelay: 150,
		oscillationDuration: 500,
	},
	off: {
		newGameThrottleDelay: 1000,
		undoAndRedoThrottleDelay: 0,
		moveDuration: 0,
		cpuTurnDelay: 0,
		oscillationDuration: 0,
	},
};

export class CheckersGameManager {
	view = new Container();
	private _piecesManager: CheckerPiecesManager;
	private app: Application;
	private animationSystem: AnimationSystem;
	private assetsManager: CheckersAssetsManager;
	throttledOnUndo = this.onUndo;
	throttledOnRedo = this.onRedo;
	throttledStartNewGame = this.startNewGame;
	private undoableKeyboardListener: UndoableKeyboardListener;
	private _settings: Settings = $state(settingsMap.normal);
	private cleanUpPiecesSpriteSettingsListener: () => void;
	private cleanUpSettingsListener: () => void;
	private cleanUpGameWinnerListener: () => void;
	private cleanUpAdsListener: () => void;
	private cleanUpGameStartListener: () => void;
	context = new GameContext({
		gameName: 'Checkers',
		gameKey: 'checkers',
		GameClass: CheckersGame,

		defaultGameProps(context) {
			const settings = context.settingsManager.settings;

			return {
				difficulty: settings.difficulty,
				rules: {
					size: settings.size,
					canCaptureBackwards: settings.canCaptureBackwards,
					allowLongKingMoves: settings.allowLongKingMoves,
					mandatoryCapture: settings.mandatoryCapture,
					allowLongKingMovesAfterCapture: settings.allowLongKingMovesAfterCapture,
					maximizeCaptureSequence: settings.maximizeCaptureSequence,
					promoteStrategy: settings.promoteStrategy,
					captureStrategy: settings.captureStrategy,
				},
				opponent: settings.opponent,
			};
		},
		sounds: {
			resources: checkersSoundResources,
			lifecycle: {
				createGame: checkersSoundResources.chessPieces,
			},
		},
		settings: {
			defaultSettings: {
				enableAnimations: true,
				animationsSpeed: 'normal' as CheckersSpeed,
				difficulty: 'easy' as CheckersDifficulty,
				opponent: 'cpu' as CheckersOpponent,
				playerSprite: 'red' as CheckerPieceColor,
				opponentSprite: 'black' as CheckerPieceColor,
				preset: defaultCheckersPreset.preset as string,
				...(defaultCheckersPreset.rules as CheckerRules),
			},
		},
		variants: {
			map: {
				opponent: {
					allValues: ['cpu', 'local-multiplayer'] as CheckersOpponent[],
					format: (opponent: CheckersOpponent) =>
						opponent === 'cpu' ? 'CPU' : 'Player 2',
				},
				difficulty: {
					allValues: checkerDifficulties.map((d) => d.value) as CheckersDifficulty[],
					format: (difficulty: CheckersDifficulty) =>
						checkerDifficulties.find((d) => d.value === difficulty)?.label ?? '',
				},
			},
			fromGame: (game) => {
				return {
					opponent: game.opponent,
					difficulty: game.difficulty,
				};
			},
			getStatsVariant(variants) {
				return variants.opponent === 'cpu'
					? (variants.difficulty ?? 'easy')
					: 'local-multiplayer';
			},
			getLeaderboardVariant(variants) {
				return variants.opponent === 'cpu'
					? (variants.difficulty ?? 'easy')
					: 'local-multiplayer';
			},
		},
		stats: ({ props, context }) => {
			return {
				stats: new Stats({
					...props,
					liveStats: {
						moves: {
							name: 'Moves',
							unit: 'plain',
							value: () => context.game?.moves ?? 0,
							metrics: {
								total: {
									key: 'totalMoves',
									name: 'Total Moves',
								},
								average: {
									key: 'averageMoves',
									name: 'Average Moves',
								},
								min: {
									key: 'fewestMoves',
									name: 'Fewest Moves',
									useAsBest: true,
								},
								max: {
									key: 'maxMoves',
									name: 'Max Moves',
								},
							},
						},
						totalMoves: {
							name: 'Total Moves',
							description: 'Includes undos, redos, and automatic finish moves',
							unit: 'plain',
							value: () => context.game?.totalMoves ?? 0,
							metrics: {
								total: {
									key: 'totalTotalMoves',
									name: 'Total Total Moves',
								},
								average: {
									key: 'averageTotalMoves',
									name: 'Average Total Moves',
								},
								min: {
									key: 'fewestTotalMoves',
									name: 'Fewest Total Moves',
									useAsBest: true,
								},
								max: {
									key: 'maxTotalMoves',
									name: 'Max Total Moves',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'moves'],
				}),
				canUpdateWithGameLost: (game) => {
					return game.winner === 'opponent';
				},
				visibleStats: [
					'bestTime',
					'averageTime',
					'fewestMoves',
					'averageMoves',
					'fewestTotalMoves',
					'averageTotalMoves',
					'wonGames',
					'totalGames',
				],
			};
		},
		onGameCreated: (props) => {
			if (this._piecesManager) {
				if (props.newGame.size !== props.previousGame?.size) {
					this._piecesManager.dispose();
					this._piecesManager = new CheckerPiecesManager({
						timer: props.context.timer,
						app: this.app,
						animationSystem: this.animationSystem,
						assetsManager: this.assetsManager,
						game: props.newGame,
						opponentSprite: props.context.settingsManager.settings.opponentSprite,
						playerSprite: props.context.settingsManager.settings.playerSprite,
						isOpponentCpu: props.context.settingsManager.settings.opponent === 'cpu',
						sounds: props.context.sounds,
					});
					this.view.addChild(this._piecesManager.view);
				} else {
					this._piecesManager.reset(props.newGame);
				}

				this._piecesManager.isOpponentCpu =
					props.context.settingsManager.settings.opponent === 'cpu';
			}
		},
	});

	constructor({ app, assetsManager, animationSystem }: GameManagerParams) {
		this.app = app;
		this.animationSystem = animationSystem;
		this.assetsManager = assetsManager;

		this.undoableKeyboardListener = new UndoableKeyboardListener(
			() => this.throttledOnUndo(),
			() => this.throttledOnRedo(),
		);

		this.context.load();

		this._piecesManager = new CheckerPiecesManager({
			timer: this.context.timer,
			assetsManager,
			app,
			game: this.context.game!,
			animationSystem,
			playerSprite: this.context.settingsManager.settings.playerSprite,
			opponentSprite: this.context.settingsManager.settings.opponentSprite,
			isOpponentCpu: this.context.settingsManager.settings.opponent === 'cpu',
			sounds: this.context.sounds,
		});

		this.view.addChild(this._piecesManager.view);

		this.addListeners();

		this.cleanUpPiecesSpriteSettingsListener = $effect.root(() => {
			$effect(() => {
				this._piecesManager.playerSprite =
					this.context.settingsManager.settings.playerSprite;
				this._piecesManager.opponentSprite =
					this.context.settingsManager.settings.opponentSprite;
			});
		});

		this.cleanUpSettingsListener = $effect.root(() => {
			$effect(() => {
				this.syncSettings();
			});
		});

		this.cleanUpGameWinnerListener = $effect.root(() => {
			$effect(() => {
				if (this.context.game?.isOver()) {
					const gameState =
						this.context.game.winner === 'player'
							? 'won'
							: this.context.game.winner === 'opponent'
								? 'lost'
								: 'draw';

					this.context.handleGameOver(gameState, {
						handleSound: false,
						handleConfetti: false,
					});

					if (
						gameState === 'won' ||
						(this.context.settingsManager.settings.opponent === 'local-multiplayer' &&
							gameState !== 'draw')
					) {
						this.context.addConfetti();
						this.context.sounds.gameWin.play();
					} else if (gameState === 'lost') {
						this.context.sounds.gameOver.play();
					} else {
						this.context.sounds.gameOver.play();
					}
				}
			});
		});

		this.cleanUpGameStartListener = $effect.root(() => {
			$effect(() => {
				if (
					(this.context.game?.history.timeline.past.length ?? 0) > 0 &&
					!this.context.timer.started
				) {
					this.context.timer.start();
				}
			});
		});

		this.cleanUpAdsListener = $effect.root(() => {
			$effect(() => {
				// Track ads
				ads.canShowGameAds;
				ads.effectiveSize;

				untrack(async () => {
					await tick();

					this.resize();
				});
			});
		});
	}

	get sounds() {
		return this.context.sounds;
	}

	get timer() {
		return this.context.timer;
	}

	get gameSettings() {
		return this.context.settingsManager;
	}

	changeOpponent(opponent: CheckersOpponent, difficulty?: CheckersDifficulty) {
		this.context.settingsManager.settings.opponent = opponent;

		if (difficulty) {
			this.context.settingsManager.settings.difficulty = difficulty;
		}

		this.context.createGame();
	}

	syncSettings() {
		if (!this.gameSettings.settings.enableAnimations) {
			this.settings = settingsMap.off;
		} else {
			this.settings = settingsMap[this.gameSettings.settings.animationsSpeed];
		}
	}

	get settings(): Settings {
		return this._settings;
	}

	set settings(newSettings: Settings) {
		this._settings = newSettings;
		this.throttledOnUndo = throttle(this.onUndo, this.settings.undoAndRedoThrottleDelay, {
			leading: true,
			trailing: false,
		});
		this.throttledOnRedo = throttle(this.onRedo, this.settings.undoAndRedoThrottleDelay, {
			leading: true,
			trailing: false,
		});
		this.throttledStartNewGame = throttle(
			this.startNewGame,
			this.settings.newGameThrottleDelay,
			{
				leading: true,
				trailing: false,
			},
		);
		untrack(() => {
			this._piecesManager.cpuTurnDelay = this.settings.cpuTurnDelay;
			this._piecesManager.moveDuration = this.settings.moveDuration;
			this._piecesManager.oscillationDuration = this.settings.oscillationDuration;
			this._piecesManager.sync({ duration: 0 });
		});
	}

	resize() {
		this._piecesManager.resize();
	}

	/** UI methods */

	canUndo(): boolean {
		if (!this.canHandleUserEvents) {
			return false;
		}
		return !!this.context.game?.canUndo();
	}

	canRedo(): boolean {
		if (!this.canHandleUserEvents) {
			return false;
		}

		return !!this.context.game?.canRedo();
	}

	isOver = $derived.by(() => {
		return !!this.context.game?.isOver();
	});

	canHandleUserEvents = $derived.by(() => {
		return (
			this._piecesManager.canHandleUserEvents &&
			((this.timer.running && !this.timer.paused) || !this.timer.started)
		);
	});

	private onUndo() {
		if (!this.canUndo()) {
			return;
		}

		this.sounds.swoosh.stopAndPlay();
		this.context.game?.undo({
			untilTurn: this.context.settingsManager.settings.opponent === 'cpu',
		});
		this._piecesManager.sync({ sparePiecesDelay: 0, capturedPiecesDelay: 0 });
	}

	private onRedo() {
		if (!this.canRedo()) {
			return;
		}

		this.sounds.swoosh.stopAndPlay();
		this.context.game?.redo({
			untilTurn: this.context.settingsManager.settings.opponent === 'cpu',
		});
		this._piecesManager.sync({ sparePiecesDelay: 0, capturedPiecesDelay: 0 });
	}

	private handleKeyDown = (event: KeyboardEvent) => {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		// const key = event.key.toLocaleLowerCase();

		if (import.meta.env.DEV) {
			if (event.code === 'Digit0' && event.shiftKey) {
				console.log('debug');
				// this.throttledStartNewGame();
			}
		}
	};

	private async startNewGame() {
		this.context.createGame();
	}

	private addListeners() {
		window.addEventListener('keydown', this.handleKeyDown);
		this.undoableKeyboardListener.listen();
	}

	dispose() {
		this._piecesManager.dispose();
		this.context.dispose();
		this.cleanUpAdsListener();
		this.cleanUpPiecesSpriteSettingsListener();
		this.cleanUpSettingsListener();
		this.cleanUpGameWinnerListener();
		this.cleanUpGameStartListener();
		window.removeEventListener('keydown', this.handleKeyDown);
		this.undoableKeyboardListener.dispose();
	}
}
