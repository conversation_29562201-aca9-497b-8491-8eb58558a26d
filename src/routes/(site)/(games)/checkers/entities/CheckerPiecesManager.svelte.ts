import { Container, type Application } from 'pixi.js';
import { attatchedOffsetPercentage, CheckerPiece } from './CheckerPiece';
import { CheckersBoard } from './CheckersBoard.svelte';
import type { CheckerPieceColor, CheckersAssetsManager } from '../assets/CheckersAssetsManager';
import type {
	CheckersGame,
	CheckerPiece as CheckerPieceModel,
	CheckerPieceOwner,
} from '../model/CheckersGame.svelte';
import type {
	AnimationEntity,
	AnimationSystem,
	CompleteAnimationEntity,
} from '../../solitaire/systems/AnimationSystem';
import { handleError } from '../../../../../hooks.client';
import type { CheckersSounds } from '../assets/checkersSoundResources';
import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import type { Timer } from '$lib/util/Timer.svelte';

interface Props {
	timer: Timer;
	app: Application;
	assetsManager: CheckersAssetsManager;
	game: CheckersGame;
	animationSystem: AnimationSystem;
	playerSprite: CheckerPieceColor;
	opponentSprite: CheckerPieceColor;
	isOpponentCpu: boolean;
	sounds: CheckersSounds;
}

const moveZIndex = 1000;
const capturedPiecesPileGapPercentage = 0.1;

export class CheckerPiecesManager {
	cpuTurnDelay = 500;
	moveDuration = 300;
	oscillationDuration = 2000;
	isOpponentCpu = false;

	private _timer: Timer;
	private _sounds: CheckersSounds;
	private _playerSprite: CheckerPieceColor;
	private _opponentSprite: CheckerPieceColor;
	private _app: Application;
	private _animationSystem: AnimationSystem;
	private _assetsManager: CheckersAssetsManager;
	private _board: CheckersBoard;
	private _pieces: CheckerPiece[];
	private _sparePieces: CheckerPiece[] = [];
	private _game = $state() as CheckersGame;
	private _view = new Container();
	private _isDragging = false;
	private _cpuWorker: Worker;

	constructor({
		app,
		assetsManager,
		game,
		animationSystem,
		playerSprite,
		opponentSprite,
		isOpponentCpu,
		sounds,
		timer,
	}: Props) {
		this._timer = timer;
		this.isOpponentCpu = isOpponentCpu;
		this._sounds = sounds;
		this._app = app;
		this._assetsManager = assetsManager;
		this._animationSystem = animationSystem;
		this._game = game;
		this._playerSprite = playerSprite;
		this._opponentSprite = opponentSprite;

		this._board = new CheckersBoard({
			app,
			gridSize: this._game.size,
			animationSystem,
			onPointerUp: (position) => {
				if (!this.canHandleUserEvents) {
					return;
				}

				// If we just finished dragging, don't process the board click
				if (this._isDragging) {
					this._isDragging = false;
					return;
				}

				if (this._game.move(position)) {
					this.playAnyMoveSound();
					this.sync();
				}
			},
		});

		this._pieces = this._game.grid.grid.flatMap((row) => {
			return row
				.map((piece) => {
					if (piece) {
						return new CheckerPiece({
							assetsManager,
							width: this._board.checkerSize,
							piece,
							color:
								piece.owner === 'player'
									? this._playerSprite
									: this._opponentSprite,
						});
					}
				})
				.filter(Boolean) as CheckerPiece[];
		});

		this._view.addChild(this._board.view);
		this._view.addChild(...this._pieces.map((piece) => piece.view));

		// Set up drag and drop for pieces
		this._pieces.forEach((piece) => {
			piece.view.interactive = true;
			piece.view.cursor = 'pointer';

			// Track if we're dragging to distinguish between drag and click
			let dragStarted = false;
			let dragStartX = 0;
			let dragStartY = 0;
			let originalX = 0;
			let originalY = 0;

			piece.view.on('pointerdown', (event) => {
				// Only allow dragging player's pieces when it's their turn
				if (
					!this.canHandleUserEvents ||
					!this._game.allowedPieceMovesForTurn.has(piece.piece.id)
				) {
					return;
				}

				// Store original position
				originalX = piece.view.x;
				originalY = piece.view.y;

				// Store drag start position
				dragStartX = event.global.x;
				dragStartY = event.global.y;

				// Mark that we're starting a potential drag
				dragStarted = true;

				piece.view.zIndex = moveZIndex;

				// Set up move and up handlers
				piece.view.on('globalpointermove', onMove);
				piece.view.on('pointerup', onEnd);
				piece.view.on('pointerupoutside', onEnd);
			});

			const onMove = (event: any) => {
				if (!dragStarted) return;

				// Calculate distance moved to distinguish drag from click
				const dx = event.global.x - dragStartX;
				const dy = event.global.y - dragStartY;

				// If we've moved enough, consider it a drag
				if (!this._isDragging && (Math.abs(dx) > 5 || Math.abs(dy) > 5)) {
					this._isDragging = true;

					// Select the piece for highlighting valid moves
					if (
						!this._game.selectedPiece ||
						this._game.selectedPiece.id !== piece.piece.id
					) {
						this._game.selectedPiece = piece.piece;
						this.sync();
					}
				}

				if (this._isDragging) {
					// Move the piece with the pointer
					piece.view.x = originalX + dx;
					piece.view.y = originalY + dy;
				}
			};

			const onEnd = (event: any) => {
				if (!dragStarted) {
					return;
				}

				// Clean up the event listeners
				piece.view.off('globalpointermove', onMove);
				piece.view.off('pointerup', onEnd);
				piece.view.off('pointerupoutside', onEnd);

				// If we were dragging (not just clicking)
				if (this._isDragging) {
					// Convert global position to board position
					const boardLocalPos = this._board.view.toLocal(event.global);
					const checkerSize = this._board.checkerSize;
					const row = Math.floor(
						(boardLocalPos.y - this._board.checkerOffset) / checkerSize,
					);
					const column = Math.floor(
						(boardLocalPos.x - this._board.checkerOffset) / checkerSize,
					);

					// Try to move to this position
					if (
						row >= 0 &&
						row < this._game.size &&
						column >= 0 &&
						column < this._game.size
					) {
						const moveSuccess = this._game.move({ row, column });

						if (moveSuccess) {
							// Move successful - sync will animate the piece to its new position
							this.playAnyMoveSound();
							this.sync();
						} else {
							// Move failed - return to original position
							this.sync();
						}
					} else {
						// Drop outside board - return to original position
						this.sync();
					}

					// Reset dragging state
					dragStarted = false;
					this._isDragging = false;
				} else {
					// This was just a click, not a drag
					// The piece has already been selected/deselected by the pointerdown handler
					piece.view.x = originalX;
					piece.view.y = originalY;
					dragStarted = false;

					this._game.toggleSelectedPiece(piece.piece);
					this.sync();
				}

				piece.view.zIndex = 2;
			};
		});

		this._view.sortableChildren = true;

		this._cpuWorker = new Worker(new URL('./worker.svelte.ts', import.meta.url), {
			type: 'module',
		});

		this.resize();
	}

	get canHandleUserEvents() {
		if (!this._timer.started) {
			return true;
		}

		if (!this._timer.running) {
			return false;
		}

		if (this.isCpuTurn) {
			return false;
		}

		return !this._game.isOver() && !this._game.hasPendingSequenceMoves;
	}

	private playAnyMoveSound() {
		const sound = getRandomItemAt([
			this._sounds.chess1,
			this._sounds.chess2,
			this._sounds.chess3,
			this._sounds.chess4,
		]);

		sound.stopAndPlay();
	}

	private runCpuTurnOnWorker() {
		const canRunCpuTurn =
			this._game.turn === 'opponent' &&
			this.isOpponentCpu &&
			!this._game.isOver() &&
			!this._game.hasPendingSequenceMoves;

		if (!canRunCpuTurn) {
			return;
		}

		const playCpuTurnAt = performance.now() + this.moveDuration + this.cpuTurnDelay;
		const gameState = this._game.toJson();

		this._cpuWorker.onmessage = (event) => {
			const { bestMove, error } = event.data;

			if (error) {
				handleError(error);
				return;
			}

			if (bestMove) {
				const deltaTime = Math.max(0, playCpuTurnAt - performance.now());

				// Schedule play
				setTimeout(() => {
					this._game.selectedPiece = this._game.getPieceById(bestMove.id);
					this._game.move(bestMove.move);
					this.playAnyMoveSound();
					this.sync();
				}, deltaTime);
			}
		};

		this._cpuWorker.onerror = (error) => {
			handleError(error.error);
		};

		this._cpuWorker.postMessage({ game: gameState });
	}

	get view() {
		return this._view;
	}

	get capturedPiecesGap() {
		const marginX = 16;
		const desiredGap = 16;
		const canFitBoardAndCaptured =
			this._app.renderer.width -
				this._board.view.width -
				this._pieces[0].view.width * 2 -
				desiredGap * 2 -
				marginX * 2 >=
			0;

		if (canFitBoardAndCaptured) {
			return desiredGap;
		}

		return (desiredGap + marginX + this._pieces[0].view.width) * 2; // push captured pieces outside the view
	}

	set playerSprite(color: CheckerPieceColor) {
		this._playerSprite = color;
		this._pieces.forEach((piece) => {
			if (piece.piece.owner === 'player') {
				piece.setSprite(color);
			}
		});
	}

	set opponentSprite(color: CheckerPieceColor) {
		this._opponentSprite = color;
		this._pieces.forEach((piece) => {
			if (piece.piece.owner === 'opponent') {
				piece.setSprite(color);
			}
		});
	}

	reset(
		game: CheckersGame,
		props?: { duration?: number; sparePiecesDelay?: number; capturedPiecesDelay?: number },
	) {
		this._game = game;
		this.sync(props);
	}

	resize() {
		this._board.resize();
		this._pieces.forEach((piece) => {
			piece.resize(this._board.checkerSize);
		});

		this.sync({ duration: 0 });
	}

	sync(props?: {
		arc?: boolean;
		duration?: number;
		sparePiecesDelay?: number;
		capturedPiecesDelay?: number;
	}) {
		if (!this._game) {
			return;
		}

		this.runCpuTurnOnWorker();

		const duration = props?.duration ?? this.moveDuration;

		this.syncAllAttatAndDettach({ duration });
		this.syncPieces({ arc: props?.arc, duration });
		this.syncAllCapturedPieces({ duration, delay: props?.capturedPiecesDelay ?? duration });
		this.syncBoardSquares({ duration, oscillationDuration: this.oscillationDuration });
		this.syncCaptureSequence({ duration });
		this.syncSparePieces({ duration, delay: props?.sparePiecesDelay ?? duration });

		if (this._game.hasPendingSequenceMoves) {
			setTimeout(() => {
				this._game.moveToNextOnPendingSequence();
				this.playAnyMoveSound();
				this.sync(props);
			}, duration);
		}
	}

	private syncPieces({ arc, duration }: { arc?: boolean; duration: number }) {
		const game = this._game;

		game.grid.grid.forEach((itemRow, row) => {
			itemRow.forEach((pieceModel, column) => {
				if (pieceModel) {
					const piece = this._pieces.find((piece) => piece.piece.isSame(pieceModel));

					if (piece) {
						if (!pieceModel.attachedBy) {
							const checkerSize = this._board.checkerSize;
							const x =
								this._board.view.position.x +
								this._board.checkerOffset +
								checkerSize * column +
								checkerSize / 2;
							const y =
								this._board.view.position.y +
								this._board.checkerOffset +
								checkerSize * row +
								checkerSize / 2;

							const mustLift =
								pieceModel.isSame(game.selectedPiece) &&
								!this._game.hasPendingSequenceMoves;

							const animations: AnimationEntity<any>[] = [];
							const moveAnimation = piece.move({ x, y, duration, arc });

							if (moveAnimation.length > 0) {
								piece.view.zIndex = moveZIndex;

								moveAnimation[0].animation.onComplete(() => {
									piece.view.zIndex = 2;
								});
							}

							animations.push(...moveAnimation);

							// Don't animate drop for the piece being dragged
							if (!mustLift) {
								animations.push(...piece.animateDrop({ duration }));
							}

							if (mustLift) {
								if (animations.length > 0) {
									animations[animations.length - 1].animation.onComplete(() => {
										this._animationSystem.add(
											...piece.animateLift({ duration }),
										);
									});
								} else {
									this._animationSystem.add(...piece.animateLift({ duration }));
								}
							}

							this._animationSystem.add(...animations);
						}
					}
				}
			});
		});
	}

	private syncAllCapturedPieces(props: { duration: number; delay?: number }) {
		this.syncCapturedPieces(this._game.capturedOpponentPieces, 'bottom-right', props);
		this.syncCapturedPieces(this._game.capturedPlayerPieces, 'top-left', props);
	}

	private syncCapturedPieces(
		pieces: CheckerPieceModel[],
		side: 'top-left' | 'bottom-right',
		{ duration, delay }: { duration: number; delay?: number },
	) {
		const gap = this.capturedPiecesGap;

		pieces.forEach((pieceModel, index) => {
			const piece = this._pieces.find((piece) => piece.piece.isSame(pieceModel));

			if (piece) {
				if (!pieceModel.attachedBy) {
					const direction = side === 'top-left' ? 1 : -1;
					const offsetOnPile =
						-(index / 3) * attatchedOffsetPercentage * piece.view.height;
					const pileOffset =
						Math.floor(index % 3) *
						piece.view.height *
						direction *
						(1 + capturedPiecesPileGapPercentage);

					const moveAnimation = piece.move({
						x:
							side === 'top-left'
								? this._board.view.position.x - piece.view.width / 2 - gap
								: this._board.view.position.x +
									this._board.view.width +
									piece.view.width / 2 +
									gap,
						y:
							offsetOnPile +
							pileOffset +
							(side === 'top-left'
								? this._board.view.position.y + piece.view.height / 2
								: this._board.view.position.y +
									this._board.view.height -
									piece.view.height / 2),
						duration,
						delay,
					});

					if (moveAnimation.length > 0) {
						moveAnimation[0].animation.onStart(() => {
							piece.view.zIndex = moveZIndex + index;
						});
						moveAnimation[0].animation.onComplete(() => {
							piece.view.zIndex = 0;
						});
						moveAnimation[0].resolveConflict = getPlayingAnimation;
					}

					this._animationSystem.add(...moveAnimation);
				}
			}
		});
	}

	get isCpuTurn() {
		return this._game.turn === 'opponent' && this.isOpponentCpu;
	}

	private syncBoardSquares({
		duration,
		oscillationDuration,
	}: {
		duration: number;
		oscillationDuration: number;
	}) {
		const game = this._game;
		this._board.squares.forEach((square) => square.stopHighlight({ duration: duration / 2 }));

		if (this.isCpuTurn) {
			return;
		}

		if (game.selectedPiece) {
			// Highlight selected piece moves
			game.selectedPieceMoves.forEach((move) => {
				const square = this._board.getSquareAt(move);
				square?.startHighlight({ duration, oscillationDuration });
			});
		} else {
			// Highlight allowed pieces
			Array.from(game.allowedPieceMovesForTurn.keys()).forEach((id) => {
				const piece = game.getPieceById(id)!;
				const position = game.getPiecePositionOnGrid(piece);

				if (position) {
					const square = this._board.getSquareAt(position);
					square?.startHighlight({ duration, oscillationDuration });
				}
			});
		}
	}

	private syncCaptureSequence({
		duration,
		oscillationDuration,
	}: {
		duration?: number;
		oscillationDuration?: number;
	}) {
		if (this.isCpuTurn) {
			return;
		}

		this._game.selectedPieceCaptureSequences.forEach((sequence) => {
			sequence.moves.forEach((move, index) => {
				if (index >= this._game.captureSequenceMoveIndex) {
					const square = this._board.getSquareAt(move);
					square?.startHighlight({ duration, oscillationDuration });
				}
			});
		});
	}

	private getSparePieceY(owner: CheckerPieceOwner) {
		return 2 * (owner === 'opponent' ? 1 : -1) * this._board.view.height;
	}

	private syncAllAttatAndDettach({ duration }: { duration: number }) {
		this._game.grid.grid.forEach((row) => {
			row.forEach((piece) => {
				if (piece) {
					this.syncAttatchAndDettach(piece, { duration });
				}
			});
		});
		this._game.capturedPlayerPieces.forEach((piece) => {
			this.syncAttatchAndDettach(piece, { duration });
		});
		this._game.capturedOpponentPieces.forEach((piece) => {
			this.syncAttatchAndDettach(piece, { duration });
		});
	}

	private syncAttatchAndDettach(
		pieceModel: CheckerPieceModel,
		{ duration }: { duration: number },
	) {
		const piece = this._pieces.find((piece) => piece.piece.isSame(pieceModel));

		if (!piece) {
			// Piece not found
			return;
		}

		if (!pieceModel.attached && piece.attatched) {
			this._animationSystem.add(...piece.dettach({ duration }));
		}
		if (!pieceModel.attachedBy && piece.attatchedBy) {
			this._animationSystem.add(...piece.attatchedBy.dettach({ duration }));
		}

		if (!pieceModel.attachedBy) {
			if (pieceModel.attached) {
				let attatched = this._pieces.find((piece) =>
					piece.piece.isSame(pieceModel.attached),
				);

				if (!attatched) {
					attatched = new CheckerPiece({
						assetsManager: this._assetsManager,
						piece: pieceModel.attached,
						width: piece.width,
						color:
							piece.piece.owner === 'player'
								? this._playerSprite
								: this._opponentSprite,
					});

					this._sparePieces.push(attatched);
					this._pieces.push(attatched);
					this._board.view.addChild(attatched.view);
					attatched.view.position.set(
						piece.view.x,
						this.getSparePieceY(pieceModel.owner),
					);
				}

				this._animationSystem.add(...piece.attatch(attatched, { duration }));
			}
		}
	}

	private syncSparePieces({ duration, delay }: { duration?: number; delay?: number }) {
		this._sparePieces.forEach((piece) => {
			if (!piece.attatchedBy) {
				const moveAnimation = piece.move({
					x: piece.view.x,
					y: this.getSparePieceY(piece.piece.owner),
					duration,
					delay,
				});

				if (moveAnimation[0]) {
					moveAnimation[0].animation.onStart(() => {
						piece.view.zIndex = moveZIndex;
					});
					moveAnimation[0].animation.onComplete(() => {
						piece.view.zIndex = 0;
						this._board.view.removeChild(piece.view);
						this._sparePieces = this._sparePieces.filter(
							(p) => !p.piece.isSame(piece.piece),
						);
						this._pieces = this._pieces.filter((p) => !p.piece.isSame(piece.piece));
					});
					moveAnimation[0].resolveConflict = getPlayingAnimation;
				}

				this._animationSystem.add(...moveAnimation);
			}
		});
	}

	dispose() {
		this._cpuWorker.terminate();
		this._board.dispose();
		this.view.destroy();
	}
}

function getPlayingAnimation(
	playingAnimation: CompleteAnimationEntity,
	_newAnimation: CompleteAnimationEntity,
) {
	return playingAnimation;
}
