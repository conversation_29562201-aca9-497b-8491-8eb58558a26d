import { Container, Sprite } from 'pixi.js';
import type { CheckerPieceColor, CheckersAssetsManager } from '../assets/CheckersAssetsManager';
import type { CheckerPiece as CheckerPieceModel } from '../model/CheckersGame.svelte';
import type { AnimationEntity } from '../../solitaire/systems/AnimationSystem';
import { Easing, Tween } from '@tweenjs/tween.js';

interface Props {
	assetsManager: CheckersAssetsManager;
	width: number;
	piece: CheckerPieceModel;
	color: CheckerPieceColor;
}

const checkerAspectRatio = 56 / 61;
const liftAmount = 0.35;
const shadowScaleAmount = 0.85;
const shadowAlpha = 0.5;

export const attatchedOffsetPercentage = 0.115;
const dettatchZIndex = 500;

export class CheckerPiece {
	view = new Container();
	piece: CheckerPieceModel;
	width: number = 0;
	private _isLifted = false;
	private assetsManager: CheckersAssetsManager;
	private sprite: Sprite;
	private pieceContainer = new Container();
	private shadow: Sprite;
	private crown: Sprite;
	private _attatched?: CheckerPiece;
	private _attatchedBy?: CheckerPiece;

	constructor({ assetsManager, width, piece, color }: Props) {
		this.assetsManager = assetsManager;
		this.piece = piece;

		this.sprite = Sprite.from(assetsManager.pieceTexture(color));
		this.shadow = Sprite.from(assetsManager.shadowTexture());
		this.crown = Sprite.from(assetsManager.crownTexture());
		this.crown.alpha = 0;

		this.shadow.anchor.set(0.5);
		this.crown.anchor.set(0.5);
		this.sprite.anchor.set(0.5);
		this.pieceContainer.addChild(this.sprite, this.crown);

		this.view.addChild(this.shadow);
		this.view.addChild(this.pieceContainer);

		this.view.eventMode = 'static';
		this.view.cursor = 'pointer';

		this.resize(width);
	}

	private get id() {
		return `piece-${this.piece.id}`;
	}

	get isLifted() {
		return this._isLifted;
	}

	get attatched() {
		return this._attatched;
	}

	get attatchedBy() {
		return this._attatchedBy;
	}

	setSprite(color: CheckerPieceColor) {
		this.sprite.texture = this.assetsManager.pieceTexture(color);
	}

	attatch(other: CheckerPiece, props?: { duration: number }): AnimationEntity<Tween>[] {
		const previousPosition = other.view.toLocal(this.view);

		this._attatched = other;
		this.pieceContainer.addChild(other.view);

		const newPosition = other.view.toLocal(this.view);
		const diffX = newPosition.x - previousPosition.x;
		const diffY = newPosition.y - previousPosition.y;

		other.view.x += diffX;
		other.view.y += diffY;
		other.view.eventMode = 'none';
		other._attatchedBy = this;

		return [
			...other.showCrown(props),
			...other.move({
				x: this.sprite.x,
				y: this.sprite.y - this.sprite.height * attatchedOffsetPercentage,
				duration: props?.duration,
			}),
			...other.animateDrop(props),
		].filter(Boolean) as AnimationEntity<Tween>[];
	}

	dettach(props?: { duration: number }): AnimationEntity<Tween>[] {
		const other = this._attatched;

		if (!other) {
			return [];
		}

		const previousPosition = other.view.toLocal(this.view);

		this._attatched = undefined;
		this.pieceContainer.removeChild(other.view);
		this.view.parent.addChild(other.view);
		this.view.zIndex = dettatchZIndex;
		other.view.zIndex = dettatchZIndex + 1;

		const newPosition = other.view.toLocal(this.view);
		const diffX = newPosition.x - previousPosition.x;
		const diffY = newPosition.y - previousPosition.y;

		other.view.x += diffX;
		other.view.y += diffY;
		other.view.eventMode = 'static';
		other._attatchedBy = undefined;

		return other.hideCrown(props);
	}

	move({
		x,
		y,
		duration,
		delay,
		arc = true,
	}: {
		x: number;
		y: number;
		duration?: number;
		delay?: number;
		arc?: boolean;
	}): AnimationEntity<Tween>[] {
		if (Math.abs(this.view.x - x) <= 0.001 && Math.abs(this.view.y - y) <= 0.001) {
			return [];
		}

		const arcHeight = arc ? this.view.height * 0.5 : 0;
		const animation = new Tween({
			x: this.view.x,
			y: this.view.y,
		})
			.to({
				x,
				y,
			})
			.delay(delay)
			.duration(duration ?? 300)
			.easing(Easing.Quadratic.InOut)
			.onUpdate(({ x, y }, progress) => {
				this.view.x = x;
				this.view.y = y - arcHeight * Math.sin(progress * Math.PI);
			});

		return [
			{
				id: `move-${this.id}`,
				animation,
			},
		];
	}

	showCrown(props?: { duration: number }): AnimationEntity<Tween>[] {
		const animation = new Tween({
			alpha: this.crown.alpha,
		})
			.to({ alpha: 1 })
			.duration((props?.duration ?? 300) / 2)
			.easing(Easing.Quadratic.InOut)
			.onUpdate(({ alpha }) => {
				this.crown.alpha = alpha;
			});

		return [
			{
				id: `crown-${this.id}`,
				animation,
			},
		];
	}

	hideCrown(props?: { duration: number }): AnimationEntity<Tween>[] {
		const animation = new Tween({
			alpha: this.crown.alpha,
		})
			.to({ alpha: 0 })
			.duration((props?.duration ?? 300) / 2)
			.easing(Easing.Quadratic.InOut)
			.onUpdate(({ alpha }) => {
				this.crown.alpha = alpha;
			});

		return [
			{
				id: `crown-${this.id}`,
				animation,
			},
		];
	}

	resize(width: number) {
		this.width = width;
		const checkerWidth = width * 0.75;
		const checkerHeight = checkerWidth / checkerAspectRatio;

		this.sprite.setSize(checkerWidth, checkerHeight);
		this.crown.setSize(checkerWidth, checkerHeight);
		this.shadow.setSize(checkerWidth, checkerWidth);

		this.shadow.position.set(0, checkerHeight * 0.08);
	}

	animateLift(props?: { duration: number }): AnimationEntity<Tween>[] {
		if (this._isLifted) {
			return [];
		}

		this._isLifted = true;

		const animation = new Tween({
			y: this.pieceContainer.y,
			scale: this.shadow.scale.x,
			alpha: this.shadow.alpha,
		})
			.to({
				y: this.pieceContainer.y - liftAmount * this.pieceContainer.height,
				scale: this.shadow.scale.x * shadowScaleAmount,
				alpha: shadowAlpha,
			})
			.duration(props?.duration ?? 300)
			.easing(Easing.Quadratic.InOut)
			.onUpdate(({ y, scale, alpha }) => {
				this.pieceContainer.y = y;
				this.shadow.scale.set(scale);
				this.shadow.alpha = alpha;
			});

		return [
			{
				id: `lift-${this.id}`,
				animation,
			},
		];
	}

	animateDrop(props?: { duration?: number }): AnimationEntity<Tween>[] {
		if (!this._isLifted) {
			return [];
		}

		this._isLifted = false;

		const animation = new Tween({
			y: this.pieceContainer.y,
			scale: this.shadow.scale.x,
			alpha: this.shadow.alpha,
		})
			.to({
				y: 0,
				scale: this.sprite.scale.x,
				alpha: 1,
			})
			.duration(props?.duration ?? 300)
			.easing(Easing.Quadratic.InOut)
			.onUpdate(({ y, scale, alpha }) => {
				this.pieceContainer.y = y;
				this.shadow.scale.set(scale);
				this.shadow.alpha = alpha;
			});

		return [
			{
				id: `lift-${this.id}`,
				animation,
			},
		];
	}
}
