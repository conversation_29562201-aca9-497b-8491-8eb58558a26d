import { CheckersGame, type Move } from './CheckersGame.svelte';
import { Node, Minimax } from 'minimaxer';
import { defaultCheckersPreset } from './checkersPresets';
import { shuffleMutate } from '$lib/functions/shuffle';

export class CheckersCpu {
	#minimax: Minimax<CheckersGame, { id: number; move: Move }, {}>;
	#startedAt = performance.now();
	#nodes = 0;

	constructor() {
		const root = new Node<CheckersGame, { id: number; move: Move }, {}>(
			0,
			new CheckersGame({
				difficulty: 'easy',
				rules: defaultCheckersPreset.rules,
				opponent: 'cpu',
			}),
			{ id: -1, move: { row: 0, column: 0 } },
			0,
			1,
		);
		this.#minimax = new Minimax<CheckersGame, { id: number; move: Move }, {}>(root);
		this.setMinimaxOptions(root.gamestate);

		this.#minimax.EvaluateNode = (node) => {
			const state = node.gamestate;
			const capturedPlayerPieces = state.capturedPlayerPieces.length;
			const capturedOpponentPieces = state.capturedOpponentPieces.length;
			const opponentKings = state.opponentPiecesOnGrid.reduce(
				(acc, piece) => acc + (piece.isKing ? 1 : 0),
				0,
			);
			const playerKings = state.playerPiecesOnGrid.reduce(
				(acc, piece) => acc + (piece.isKing ? 1 : 0),
				0,
			);

			// Cpu turn
			return (
				5 * (opponentKings - playerKings) + capturedPlayerPieces - capturedOpponentPieces
			);
		};

		this.#minimax.GetMoves = (node) => {
			if (this.#forceEnd) {
				return [];
			}

			const moves = shuffleMutate(
				Array.from(node.gamestate.allowedPieceMovesForTurn.entries()).flatMap(
					([id, moves]) => {
						// Get last move capture sequence
						const captureSequences =
							node.gamestate.captureSequenceAccordingToRulesMap.get(id);

						if (captureSequences && captureSequences.length > 0) {
							return captureSequences.map((sequence) => {
								return { id, move: sequence.moves[sequence.moves.length - 1] };
							});
						}

						// Get immediate moves
						return moves.map((move) => {
							return { id, move };
						});
					},
				),
			);

			return moves;
		};

		this.#minimax.CreateChildNode = (node, move) => {
			const nextState = node.gamestate.cloneWithoutFullHistory();
			nextState.selectedPiece = nextState.getPieceById(move.id);
			nextState.move(move.move);

			while (nextState.hasPendingSequenceMoves) {
				nextState.moveToNextOnPendingSequence();
			}

			const aim = nextState.turn === 'opponent' ? 1 : -1;

			this.#nodes += 1;

			if (this.#forceEnd) {
				return new Node(2, nextState, move, 0, aim);
			}

			if (nextState.isOver()) {
				return new Node(2, nextState, move, 0, aim);
			}

			return new Node(1, nextState, move, 0, aim);
		};
	}

	get #forceEnd() {
		return (
			performance.now() - this.#startedAt > this.#minimax.opts.timeout || this.#nodes > 12_000
		);
	}

	start(game: CheckersGame) {
		this.setMinimaxOptions(game);
		this.#minimax.root = new Node<CheckersGame, { id: number; move: Move }, {}>(
			0,
			game,
			{ id: -1, move: { row: 0, column: 0 } },
			0,
			1,
		);
		this.#minimax.activeRoot = this.#minimax.root;
	}

	getBestMove() {
		this.#minimax.evaluate();

		return this.#minimax.getOptimalMoves()[1];
	}

	private setMinimaxOptions(game: CheckersGame) {
		this.#minimax.opts.pruning = 1;
		this.#minimax.opts.genBased = true;

		if (game.difficulty === 'easy') {
			this.#minimax.opts.depth = 1;
			this.#minimax.opts.method = 0;
			this.#minimax.opts.timeout = 400;
		} else if (game.difficulty === 'medium') {
			this.#minimax.opts.depth = 2;
			this.#minimax.opts.method = 2;
			this.#minimax.opts.timeout = 400;
		} else if (game.difficulty === 'hard') {
			this.#minimax.opts.depth = 4;
			this.#minimax.opts.method = 2;
			this.#minimax.opts.timeout = 1000;
		} else {
			this.#minimax.opts.depth = 6;
			this.#minimax.opts.method = 2;
			this.#minimax.opts.timeout = 1200;
		}
	}
}
