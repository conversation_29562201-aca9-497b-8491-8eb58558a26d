import type { CheckerRules } from './CheckersGame.svelte';

export interface CheckersPreset {
	preset: string;
	formatted: string;
	flag: string;
	rules: CheckerRules;
}

export const checkersPresets = [
	{
		preset: 'american',
		formatted: 'American',
		flag: '🇺🇸',
		rules: {
			allowLongKingMoves: false,
			allowLongKingMovesAfterCapture: false,
			canCaptureBackwards: false,
			mandatoryCapture: true,
			maximizeCaptureSequence: false,
			promoteStrategy: 'promote-and-stop-capture-sequence',
			captureStrategy: 'remove-piece-at-capture',
			size: 8,
		},
	},
	{
		preset: 'brazilian',
		formatted: 'Brazilian',
		flag: '🇧🇷',
		rules: {
			allowLongKingMoves: true,
			allowLongKingMovesAfterCapture: true,
			mandatoryCapture: true,
			maximizeCaptureSequence: true,
			canCaptureBackwards: true,
			promoteStrategy: 'promote-if-capture-sequence-ended',
			captureStrategy: 'remove-piece-at-end-of-turn',
			size: 8,
		},
	},
	{
		preset: 'international',
		formatted: 'International',
		flag: '🌎',
		rules: {
			allowLongKingMoves: true,
			allowLongKingMovesAfterCapture: true,
			mandatoryCapture: true,
			maximizeCaptureSequence: true,
			canCaptureBackwards: true,
			promoteStrategy: 'promote-if-capture-sequence-ended',
			captureStrategy: 'remove-piece-at-end-of-turn',
			size: 10,
		},
	},
	{
		preset: 'russian',
		formatted: 'Russian',
		flag: '🇷🇺',
		rules: {
			allowLongKingMoves: true,
			allowLongKingMovesAfterCapture: true,
			mandatoryCapture: true,
			maximizeCaptureSequence: false,
			canCaptureBackwards: true,
			promoteStrategy: 'promote-and-continue-capture-sequence',
			captureStrategy: 'remove-piece-at-end-of-turn',
			size: 10,
		},
	},
	{
		preset: 'canadian',
		formatted: 'Canadian',
		flag: '🇨🇦',
		rules: {
			allowLongKingMoves: true,
			allowLongKingMovesAfterCapture: true,
			mandatoryCapture: true,
			maximizeCaptureSequence: true,
			canCaptureBackwards: true,
			promoteStrategy: 'promote-if-capture-sequence-ended',
			captureStrategy: 'remove-piece-at-end-of-turn',
			size: 12,
		},
	},
	{
		preset: 'custom',
		formatted: 'Custom',
		flag: '🛠',
		rules: {
			allowLongKingMoves: false,
			allowLongKingMovesAfterCapture: false,
			canCaptureBackwards: false,
			mandatoryCapture: true,
			maximizeCaptureSequence: false,
			promoteStrategy: 'promote-and-stop-capture-sequence',
			captureStrategy: 'remove-piece-at-capture',
			size: 8,
		},
	},
] as const satisfies CheckersPreset[];

export const defaultCheckersPreset = checkersPresets.find((p) => p.preset === 'american')!;

export const customPreset = checkersPresets.find((p) => p.preset === 'custom')!;
