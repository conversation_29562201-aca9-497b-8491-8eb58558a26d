<script lang="ts">
	import { theme } from '$lib/stores/theme.svelte';
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();

	let url = $derived.by(() => {
		if (theme.loaded) {
			if (theme.brightness === 'light') {
				return 'https://static.lofiandgames.com/covers/checkers-cover-light.svg';
			} else {
				if (theme.value === 'dark') {
					return 'https://static.lofiandgames.com/covers/checkers-cover-dark-new.svg';
				}
				return 'https://static.lofiandgames.com/covers/checkers-cover-dark.svg';
			}
		}
	});

	let style = $derived.by(() => {
		if (url) {
			return `background-image: url('${url}')`;
		}
	});
</script>

<div class={cn('p-4 size-full', className)} {...props}>
	<div class="bg-center bg-cover bg-no-repeat size-full" {style}></div>
</div>
