<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';
	import type { CheckersApp } from './CheckersApp/CheckersApp.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import {
		checkersPresets,
		customPreset,
		defaultCheckersPreset,
		type CheckersPreset,
	} from './model/checkersPresets';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import Collapse from '$lib/components/Collapse/Collapse.svelte';
	import CollapseContent from '$lib/components/Collapse/CollapseContent.svelte';

	let isOpen = $state(false);
	let isPresetsDropdownOpen = $state(false);
	let isSizeDropdownOpen = $state(false);

	interface Props {
		game?: CheckersApp;
	}

	let { game }: Props = $props();

	const sortedCheckersPresets = [...checkersPresets].sort((a, b) => {
		if (a.preset === 'custom') {
			return 1;
		}

		return a.formatted.localeCompare(b.formatted);
	});
	let settings = $derived(game?.gameManager?.context.settingsManager.settings);
	let currentPreset = $state<CheckersPreset>({
		...defaultCheckersPreset,
	});

	$effect(function initPreset() {
		// Track isOpen currentPreset resets on open/close
		isOpen;

		if (settings?.preset) {
			const preset =
				checkersPresets.find((p) => p.preset === settings.preset) ?? defaultCheckersPreset;
			currentPreset = {
				...preset,
				rules: {
					...preset.rules,
					allowLongKingMoves: settings.allowLongKingMoves,
					allowLongKingMovesAfterCapture: settings.allowLongKingMovesAfterCapture,
					canCaptureBackwards: settings.canCaptureBackwards,
					captureStrategy: settings.captureStrategy,
					mandatoryCapture: settings.mandatoryCapture,
					maximizeCaptureSequence: settings.maximizeCaptureSequence,
					promoteStrategy: settings.promoteStrategy,
					size: settings.size,
				},
			};
		}
	});

	function save() {
		if (settings) {
			settings.preset = currentPreset.preset;
			settings.allowLongKingMoves = currentPreset.rules.allowLongKingMoves;
			settings.allowLongKingMovesAfterCapture =
				currentPreset.rules.allowLongKingMovesAfterCapture;
			settings.mandatoryCapture = currentPreset.rules.mandatoryCapture;
			settings.maximizeCaptureSequence = currentPreset.rules.maximizeCaptureSequence;
			settings.canCaptureBackwards = currentPreset.rules.canCaptureBackwards;
			settings.promoteStrategy = currentPreset.rules.promoteStrategy;
			settings.size = currentPreset.rules.size;
			settings.captureStrategy = currentPreset.rules.captureStrategy;

			game?.gameManager?.context.createGame();
			isOpen = false;
			isPresetsDropdownOpen = false;
			isSizeDropdownOpen = false;
		}
	}

	function onchange() {
		currentPreset.preset = customPreset.preset;
		currentPreset.flag = customPreset.flag;
		currentPreset.formatted = customPreset.formatted;
	}
</script>

<button class="btn btn-xs md:btn-sm grow items-center flex" onclick={() => (isOpen = true)}>
	<span>
		{checkersPresets.find((p) => p.preset === settings?.preset)?.flag || currentPreset.flag}
	</span> Rules
</button>

<Dialog bind:isOpen modalBoxClass="flex flex-col relative pb-0 px-0 *:px-6">
	<div class="mt-6 flex flex-row gap-4 justify-between items-center w-full py-2">
		<span class="text-sm">Game Mode</span>

		<Dropdown bind:open={isPresetsDropdownOpen} class="dropdown-end">
			<DropdownButton class="gap-2 btn-sm">
				<span>{currentPreset.flag}</span>
				{currentPreset.formatted}
			</DropdownButton>

			<DropdownContent menu class="w-40">
				{#each sortedCheckersPresets as p (p.preset)}
					<DropdownItem>
						<button
							class="flex gap-2 flex-row"
							class:menu-active={p.preset === currentPreset.preset}
							onclick={() => {
								currentPreset = p;
								isPresetsDropdownOpen = false;
							}}
						>
							<span>{p.flag}</span>
							{p.formatted}
						</button>
					</DropdownItem>
				{/each}
			</DropdownContent>
		</Dropdown>
	</div>

	<div class="flex flex-row gap-4 justify-between items-center w-full py-2">
		<span class="text-sm">Board Size</span>

		<Dropdown class="dropdown-end" bind:open={isSizeDropdownOpen}>
			<DropdownButton class="btn-sm">
				{currentPreset.rules.size}x{currentPreset.rules.size}
			</DropdownButton>

			<DropdownContent menu class="w-24">
				{#each [8, 10, 12] as size}
					<DropdownItem>
						<button
							class="btn-sm"
							class:menu-active={currentPreset.rules.size === size}
							onclick={() => {
								currentPreset.rules.size = size;
								onchange();
								isSizeDropdownOpen = false;
							}}
						>
							{size}x{size}
						</button>
					</DropdownItem>
				{/each}
			</DropdownContent>
		</Dropdown>
	</div>

	<Collapse open={currentPreset.rules.mandatoryCapture}>
		<!-- Do not handle onchange here! -->
		<Toggle
			aria-label="toggle must jump when you can"
			bind:checked={currentPreset.rules.mandatoryCapture}
		>
			Must Jump When You Can

			{#snippet description()}
				You must jump (capture a piece) if you can.
			{/snippet}
		</Toggle>

		<CollapseContent asSettings>
			<!-- Do not handle onchange here! -->
			<Toggle
				aria-label="toggle must choose path with most jumps"
				bind:checked={currentPreset.rules.maximizeCaptureSequence}
				disabled={!currentPreset.rules.mandatoryCapture}
			>
				Must Choose Path With Most Jumps

				{#snippet description()}
					You must choose the path that allows you to jump the most pieces.
				{/snippet}
			</Toggle>
		</CollapseContent>
	</Collapse>

	<Toggle
		aria-label="toggle can jump backwards"
		bind:checked={currentPreset.rules.canCaptureBackwards}
		{onchange}
	>
		Can Jump Backwards

		{#snippet description()}
			Any piece can jump backwards, not only kings.
		{/snippet}
	</Toggle>

	<Toggle
		aria-label="toggle remove pieces right away"
		checked={currentPreset.rules.captureStrategy === 'remove-piece-at-capture'}
		onchange={(e) => {
			currentPreset.rules.captureStrategy = (e.target as HTMLInputElement).checked
				? 'remove-piece-at-capture'
				: 'remove-piece-at-end-of-turn';
			onchange();
		}}
	>
		Remove Pieces Right Away

		{#snippet description()}
			Pieces are removed from the board as soon as they are jumped. Otherwise, they are
			removed at the end of the turn.
		{/snippet}
	</Toggle>

	<Collapse open={currentPreset.rules.allowLongKingMoves}>
		<Toggle
			aria-label="toggle kings can move far"
			bind:checked={currentPreset.rules.allowLongKingMoves}
			{onchange}
		>
			Kings Can Move Far

			{#snippet description()}
				Kings can move any number of spaces.
			{/snippet}
		</Toggle>

		<CollapseContent asSettings>
			<Toggle
				aria-label="toggle kings must land next to jumped piece"
				checked={!currentPreset.rules.allowLongKingMovesAfterCapture}
				onchange={(e) => {
					currentPreset.rules.allowLongKingMovesAfterCapture = !(
						e.target as HTMLInputElement
					).checked;
					onchange();
				}}
				disabled={!currentPreset.rules.allowLongKingMoves}
			>
				Kings Must Land Next to Jumped Piece

				{#snippet description()}
					Kings can only move to the next empty space after jumping a piece.
				{/snippet}
			</Toggle>
		</CollapseContent>
	</Collapse>

	<Collapse open class="py-2 flex flex-col gap-2">
		<span class="text-sm">What Happens When a Piece Reaches the Other End?</span>

		<CollapseContent asSettings>
			<label class="cursor-pointer gap-4 w-full flex justify-between items-center py-2">
				<span class="text-sm">Become King and End Turn</span>
				<input
					type="radio"
					name="promote-strategy"
					class="radio"
					checked={currentPreset.rules.promoteStrategy ===
						'promote-and-stop-capture-sequence'}
					onchange={() => {
						currentPreset.rules.promoteStrategy = 'promote-and-stop-capture-sequence';
						onchange();
					}}
				/>
			</label>

			<label class="cursor-pointer gap-4 w-full flex justify-between items-center py-2">
				<span class="text-sm">Become King and Keep Jumping</span>
				<input
					type="radio"
					name="promote-strategy"
					class="radio"
					checked={currentPreset.rules.promoteStrategy ===
						'promote-and-continue-capture-sequence'}
					onchange={() => {
						currentPreset.rules.promoteStrategy =
							'promote-and-continue-capture-sequence';
						onchange();
					}}
				/>
			</label>

			<label class="cursor-pointer gap-4 w-full flex justify-between items-center py-2">
				<span class="text-sm">Become King Only If Jumping Ended</span>
				<input
					type="radio"
					name="promote-strategy"
					class="radio"
					checked={currentPreset.rules.promoteStrategy ===
						'promote-if-capture-sequence-ended'}
					onchange={() => {
						currentPreset.rules.promoteStrategy = 'promote-if-capture-sequence-ended';
						onchange();
					}}
				/>
			</label>
		</CollapseContent>
	</Collapse>

	<div
		class="sticky bottom-0 left-0 right-0 w-full flex justify-end py-6 from-base-100/0 via-base-100/80 to-base-100 bg-linear-to-b"
	>
		<button class="btn btn-ghost" onclick={() => (isOpen = false)}>Cancel</button>
		<button class="btn btn-primary ml-2" onclick={save}>Save and play a new game</button>
	</div>
</Dialog>
