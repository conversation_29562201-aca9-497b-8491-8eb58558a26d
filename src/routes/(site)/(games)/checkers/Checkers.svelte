<script lang="ts">
	import { fade, fly } from 'svelte/transition';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import { onDestroy, onMount } from 'svelte';
	import { browser } from '$app/environment';
	import UndoIcon from '$lib/components/Icons/UndoIcon.svelte';
	import RedoIcon from '$lib/components/Icons/RedoIcon.svelte';
	import { isMacLike } from '$lib/functions/isMacLike';
	import CheckersInfoModal from './CheckersInfoModal.svelte';
	import FeedbackModal from '$lib/components/FeedbackModal.svelte';
	import type { CheckersApp } from './CheckersApp/CheckersApp.svelte';
	import { page } from '$app/state';
	import CheckersHowToPlayButton from './CheckersHowToPlayButton.svelte';
	import FeedbackIcon from '$lib/components/Icons/FeedbackIcon.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import CheckersSettingsButton from './CheckersSettingsButton.svelte';
	import CheckersRulesButton from './CheckersRulesButton.svelte';
	import OpponentButton from '$lib/components/OpponentButton.svelte';
	import { checkerDifficulties } from './model/checkerDifficulties';
	import NewGameButton from '$lib/components/NewGameButton.svelte';
	import MustJumpToast from './tips/MustJumpToast.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';
	import { cn } from '$lib/util/cn';
	import { ads } from '$lib/stores/ads.svelte';

	let canvas = $state<HTMLCanvasElement>();
	let game = $state<CheckersApp | undefined>();
	let infoModalOpen = $state(false);
	let feedbackOpen = $state(false);
	let isMac = $state(false);
	let gameIsland: GameIsland<any, any, any> | null = $state(null);

	let context = $derived(game?.gameManager?.context);
	let settings = $derived(context?.settingsManager.settings);

	onMount(async () => {
		try {
			const { CheckersApp } = await import('./CheckersApp/CheckersApp.svelte');
			game = new CheckersApp({
				canvas: canvas!,
				showFPS: page.url.searchParams.get('fps') !== null,
			});
			isMac = isMacLike();
		} catch (error: any) {
			console.error(error);
		}
	});

	onDestroy(() => {
		if (browser) {
			try {
				game?.dispose();
			} catch (error) {
				// Ignore
				console.error(error);
			}
		}
	});

	$effect(() => {
		if (context?.game?.canRequestDraw && gameIsland) {
			gameIsland.changeVariant('declare-draw');
		}
	});
</script>

<MustJumpToast {game} />

<GameLayout
	mobileOrientation="all"
	noPadding
	navbarStyle="on-top"
	adsProps={{
		variant: 'on-top',
	}}
>
	{#snippet Island()}
		{#if context}
			<GameIsland
				bind:this={gameIsland}
				{context}
				isFetchingGame={game?.isLoading}
				draw={{
					onPostponeDraw() {
						context?.game?.postponeRequestDraw();
					},
					onDeclareDraw: () => {
						context?.game?.requestDraw();
					},
				}}
			/>
		{/if}
	{/snippet}

	{#if game?.isLoading}
		<span
			transition:fade={{ duration: 150 }}
			class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 loading loading-spinner loading-lg text-base-300"
		></span>
	{/if}

	<div
		class={cn(
			'absolute left-0 right-0 flex gap-2 max-w-4xl xl:max-w-5xl py-4 mx-auto w-full lg:px-0 md:px-8 px-4 limit-width-by-screen-height justify-between items-center top-[calc(var(--navbar-height))]',
		)}
		style="top: {ads.effectiveSizeWithNavbar?.height}px"
	>
		<div class="flex flex-col md:flex-row gap-2 w-full">
			<div class="flex flex-row gap-2 items-center justify-between">
				<CheckersHowToPlayButton />

				<NewGameButton
					class="grow"
					buttonClass="btn btn-xs md:btn-sm"
					autoBreakpoint="lg"
					onPlayNewGame={() => {
						game?.gameManager?.throttledStartNewGame();
					}}
				/>

				<CheckersRulesButton {game} />
			</div>

			<div class="flex flex-row gap-2 items-center justify-between">
				<OpponentButton
					class="grow"
					dropdownButtonClass="btn-xs md:btn-sm"
					opponent={settings?.opponent ?? 'cpu'}
					difficulty={settings?.difficulty ?? 'easy'}
					difficulties={checkerDifficulties}
					onOpponentChange={(opponent, difficulty) => {
						game?.gameManager?.changeOpponent(opponent, difficulty);
					}}
				/>

				<CheckersSettingsButton {game} />

				<button
					class="btn btn-xs md:btn-sm grow"
					onclick={() => {
						feedbackOpen = true;
					}}
					aria-label="send feedback"
				>
					<FeedbackIcon class="size-5" />
				</button>

				<button
					class="btn btn-xs md:btn-sm grow"
					onclick={() => (infoModalOpen = true)}
					aria-label="Open info dialog"
				>
					<InfoSolidIcon class="size-5" />
				</button>

				<MoreGamesButton class="grow btn-xs md:btn-sm" />
			</div>
		</div>

		<div class="flex-row gap-2 hidden md:flex">
			<div
				class="tooltip before:hidden after:hidden lg:before:block lg:after:block tooltip-bottom"
				data-tip="Undo ({isMac ? '⌘' : 'Ctrl'} + Z)"
			>
				<button
					class="btn btn-xs md:btn-sm"
					disabled={!game?.gameManager?.canUndo()}
					onclick={() => game?.gameManager?.throttledOnUndo()}
					aria-label="undo"
				>
					<UndoIcon class="size-5" />
				</button>
			</div>
			<div
				class="tooltip before:hidden after:hidden lg:before:block lg:after:block tooltip-left xl:tooltip-top"
				data-tip="Redo ({isMac ? '⌘' : 'Ctrl'} + Shift + Z)"
			>
				<button
					class="btn btn-xs md:btn-sm"
					disabled={!game?.gameManager?.canRedo()}
					onclick={() => game?.gameManager?.throttledOnRedo()}
					aria-label="redo"
				>
					<RedoIcon class="size-5" />
				</button>
			</div>
		</div>
	</div>

	<div class="w-full h-screen">
		<!-- TODO: extract these error states to components -->
		{#if game?.error === 'webgl-not-supported'}
			<div
				in:fly={{ y: 20, delay: 300 }}
				out:fly={{ y: 20 }}
				class="fixed p-4 top-1/2 -translate-y-1/2 w-full"
			>
				<div role="alert" class="card bg-base-100 shadow-lg w-full mx-auto max-w-[400px]">
					<div class="card-body">
						<h3 class="card-title">Oops!</h3>
						<p>
							It looks like your browser is not compatible with this game (WebGL is
							not supported).
						</p>

						<div class="card-actions justify-center xs:justify-end mt-2">
							<a href="/" class="btn btn-primary w-full xs:w-auto">Got it, go home</a>
						</div>
					</div>
				</div>
			</div>
		{/if}

		{#if game?.error === 'device-error'}
			<div
				in:fly={{ y: 20, delay: 300 }}
				out:fly={{ y: 20 }}
				class="fixed p-4 top-1/2 -translate-y-1/2 w-full"
			>
				<div role="alert" class="card bg-base-100 shadow-lg w-full mx-auto max-w-[400px]">
					<div class="card-body">
						<h3 class="card-title">Oops!</h3>
						<p>It looks like your device could not run the game correctly.</p>

						<div class="card-actions justify-center xs:justify-end mt-2">
							<a href="/" class="btn btn-ghost xs:w-auto">Go home</a>
							<button
								onclick={() => location.reload()}
								class="btn btn-primary xs:w-auto"
							>
								Reload page
							</button>
						</div>
					</div>
				</div>
			</div>
		{/if}

		<canvas bind:this={canvas}></canvas>
	</div>

	<div class="flex-row md:hidden flex absolute bottom-0 p-4 items-center justify-end w-full">
		<div class="flex flex-row gap-4">
			<button
				class="btn btn-circle"
				disabled={!game?.gameManager?.canUndo()}
				onclick={() => game?.gameManager?.throttledOnUndo()}
				aria-label="undo"
			>
				<UndoIcon class="size-8" />
			</button>
			<button
				class="btn btn-circle"
				disabled={!game?.gameManager?.canRedo()}
				onclick={() => game?.gameManager?.throttledOnRedo()}
				aria-label="redo"
			>
				<RedoIcon class="size-8" />
			</button>
		</div>
	</div>
</GameLayout>

<CheckersInfoModal bind:isOpen={infoModalOpen} />

<FeedbackModal context="Checkers" bind:isOpen={feedbackOpen} />

<style>
	@media screen and (max-height: 768px) {
		.limit-width-by-screen-height {
			max-width: 768px;
		}
	}
</style>
