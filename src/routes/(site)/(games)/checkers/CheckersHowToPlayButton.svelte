<script>
	import Dialog from '$lib/components/Dialog.svelte';
	import ThemeImage from '$lib/components/ThemeImage.svelte';
	import { isMacLike } from '$lib/functions/isMacLike';
	import { onMount } from 'svelte';

	let isOpen = $state(false);
	let isMac = $state(false);

	onMount(() => {
		isMac = isMacLike();
	});
</script>

<button class="btn btn-xs md:btn-sm grow" onclick={() => (isOpen = true)}>How to play</button>

<Dialog bind:isOpen modalBoxClass="sm:max-w-2xl">
	<article>
		<h2>How to Play Checkers</h2>

		<p>
			Checkers is a classic strategy game that is easy to learn and fun to play! Follow these
			simple rules to get started:
		</p>

		<h3>Game Rules</h3>
		<ul>
			<li>Each player starts with 12 pieces placed on the dark squares of an 8x8 board.</li>
			<li>Players take turns moving one piece diagonally forward to an empty square.</li>
			<li>
				If an opponent’s piece is adjacent and there is an empty space beyond it, you must
				jump over it, capturing the piece.
			</li>
			<li>Multiple jumps are allowed if possible.</li>
			<li>
				When a piece reaches the opponent’s last row, it is crowned a King and can move
				diagonally both forward and backward.
			</li>
		</ul>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-1.avif"
			lightPng="https://static.lofiandgames.com/images/checkers/tutorial/checkers-1.png"
			darkAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-1-dark.avif"
			darkPng="https://static.lofiandgames.com/images/checkers/tutorial/sudoku-1-dark.png"
			alt="Checkers setup"
			caption="Checkers game setup"
		/>

		<h3>Make Your Move</h3>
		<p>
			Tap on a piece to select it, then tap on a valid move to advance. You can also drag and
			drop the piece into place.
		</p>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-3.avif"
			lightPng="https://static.lofiandgames.com/images/checkers/tutorial/checkers-3.png"
			darkAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-3-dark.avif"
			darkPng="https://static.lofiandgames.com/images/checkers/tutorial/sudoku-3-dark.png"
			alt="Selecting a piece"
			caption="Selecting a piece"
		/>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-2.avif"
			lightPng="https://static.lofiandgames.com/images/checkers/tutorial/checkers-2.png"
			darkAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-2-dark.avif"
			darkPng="https://static.lofiandgames.com/images/checkers/tutorial/sudoku-2-dark.png"
			alt="Moving the selected piece"
			caption="Moving the selected piece"
		/>

		<h3>Capture Opponent’s Pieces</h3>
		<p>
			If an opponent’s piece is in your path and there is an open space beyond it, jump over
			the piece to capture it.
		</p>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-4.avif"
			lightPng="https://static.lofiandgames.com/images/checkers/tutorial/checkers-4.png"
			darkAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-4-dark.avif"
			darkPng="https://static.lofiandgames.com/images/checkers/tutorial/sudoku-4-dark.png"
			alt="Selecting a piece and preparing it for capture"
			caption="Selecting a piece and preparing it for capture"
		/>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-5.avif"
			lightPng="https://static.lofiandgames.com/images/checkers/tutorial/checkers-5.png"
			darkAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-5-dark.avif"
			darkPng="https://static.lofiandgames.com/images/checkers/tutorial/sudoku-5-dark.png"
			alt="Captured the piece of the opponent"
			caption="Captured the piece of the opponent"
		/>

		<h3>Get a King</h3>
		<p>
			When your piece reaches the opponent's last row, it becomes a King and can move
			diagonally both forward and backward.
		</p>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-6.avif"
			lightPng="https://static.lofiandgames.com/images/checkers/tutorial/checkers-6.png"
			darkAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-6-dark.avif"
			darkPng="https://static.lofiandgames.com/images/checkers/tutorial/sudoku-6-dark.png"
			alt="Getting a king"
			caption="Getting a king"
		/>

		<h3>Win the Game</h3>
		<p>Eliminate all your opponent's pieces or block them from making a move to win!</p>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-7.avif"
			lightPng="https://static.lofiandgames.com/images/checkers/tutorial/checkers-7.png"
			darkAvif="https://static.lofiandgames.com/images/checkers/tutorial/checkers-7-dark.avif"
			darkPng="https://static.lofiandgames.com/images/checkers/tutorial/sudoku-7-dark.png"
			alt="Game won! The opponent cannot perform any move"
			caption="Game won! The opponent cannot perform any move"
		/>

		<h3>Customize Your Experience</h3>
		<p>
			Checkers offers many rule variations from different countries. You can choose to play by
			American, International, Brazilian, Russian, or Canadian rules - or even create your own
			custom combination for a unique experience!
		</p>

		<section class="hidden lg:block">
			<h3>Keyboard Shortcuts</h3>

			<div class="overflow-x-auto">
				<table class="table">
					<thead>
						<tr>
							<th>Action</th>
							<th>Keybinding</th>
						</tr>
					</thead>

					<tbody>
						<tr>
							<td>Undo</td>
							<td>
								<div class="kbd dark:text-base-content">{isMac ? '⌘' : 'Ctrl'}</div>
								+
								<div class="kbd dark:text-base-content">Z</div>
							</td>
						</tr>
						<tr>
							<td>Redo</td>
							<td>
								<kbd class="kbd dark:text-base-content">{isMac ? '⌘' : 'Ctrl'}</kbd>
								+
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">Z</kbd>
							</td>
						</tr>
						<tr>
							<td>Toggle Pause</td>
							<td>
								<kbd class="kbd dark:text-base-content">P</kbd>
							</td>
						</tr>
						<tr>
							<td>Play a New Game</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">N</kbd>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</section>
	</article>
</Dialog>
