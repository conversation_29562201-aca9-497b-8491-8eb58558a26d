<script lang="ts">
	interface Props {
		svg: SVGElement;
	}

	let { svg = $bindable() }: Props = $props();
</script>

<svg
	bind:this={svg}
	class="hidden"
	width="9"
	height="9"
	viewBox="0 0 9 9"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="star">
		<path
			id="star-1"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M4 0H5V3H6V4H9V5H6V6H5V9H4V6H3V5H0V4H3V3H4V0Z"
			class="fill-transparent dark:fill-base-content"
		/>
		<path
			id="star-2"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M4 0H5V3H6V4H9V5H6V6H5V9H4V6H3V5H0V4H3V3H4V0ZM1 1H2V2H1V1ZM2 7H1V8H2V7ZM7 7H8V8H7V7ZM8 1H7V2H8V1Z"
			class="fill-transparent dark:fill-base-content"
		/>
		<path
			id="star-3"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M4 0H5V3H6V4H9V5H6V6H5V9H4V6H3V5H0V4H3V3H4V0ZM3 3H2V2H3V3ZM3 6V7H2V6H3ZM6 6H7V7H6V6ZM6 3V2H7V3H6Z"
			class="fill-transparent dark:fill-base-content"
		/>
	</g>
</svg>
