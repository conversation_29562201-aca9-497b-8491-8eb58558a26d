<script lang="ts">
	interface Props {
		svg: SVGElement;
	}

	let { svg = $bindable() }: Props = $props();
</script>

<svg
	bind:this={svg}
	class="hidden"
	width="46"
	height="13"
	viewBox="0 0 46 13"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M29 0H25V1H20V2H19V3H18H17V6H14V7H13V8H7H6V9H5V11H4V12H6V11V10H7V9H14V8H15V7H18V6V4H20V3H21V2H26V1H28V2H30V3H31V6H30V7H31V6H32V5H34V6H39V8H40H42V10H43H44V11H45V12H10V11H9V12H10V13H46V12V11H45V10V9H43V8V7H40V6V5H35V4H32V3V2H31V1H29V0ZM1 11H3V12H2V13H0V12H1V11Z"
		fill="currentColor"
	/>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M28 1H26V2H21V3H20V4H18V7H15V8H14V9H7V10H6V11V12H4V11H3V12H2V13H10V12H45V11H44V10H42V9V8H39V7V6H34V5H32V6H31V4V3H30V2H28V1ZM31 6H30V7H31V6ZM10 12V11H9V12H10Z"
		class="fill-base-100"
	/>
</svg>
