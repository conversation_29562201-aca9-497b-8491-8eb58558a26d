<script lang="ts">
	interface Props {
		svg: SVGElement;
	}

	let { svg = $bindable() }: Props = $props();
</script>

<svg
	bind:this={svg}
	class="hidden"
	width="51"
	height="35"
	viewBox="0 0 51 35"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="cactus-small">
		<g id="cactus-small-1">
			<path
				id="cactus-small-1-body"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M10 1H7V2H6V20H4V10H3V9H2V10H1V21H2V22H3V23H6V34H11V19H14V18H15V17H16V6H15V5H14V6H13V16H11V2H10V1Z"
				fill="currentColor"
			/>
			<path
				id="cactus-small-1-border"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M10 0H7V1H6V2H5V10H4V9H3V8H2V9H1V10H0V21H1V22H2V23H3V24H5V34H6V35H11V34H12V20H14V19H15V18H16V17H17V6H16V5H15V4H14V5H13V6H12V2H11V1H10V0ZM10 1V2H11V6V16H13V6H14V5H15V6H16V17H15V18H14V19H12H11V34H6V24V23H3V22H2V21H1V10H2V9H3V10H4V20H6V10V2H7V1H10Z"
				fill="currentColor"
				class="fill-base-100"
			/>
		</g>
		<g id="cactus-small-2">
			<path
				id="cactus-small-2-body"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M10 1H7V2H6V20H4V10H3V9H2V10H1V21H2V22H3V23H6V34H11V19H14V18H15V17H16V6H15V5H14V6H13V16H11V2H10V1ZM24 2H23V13H22V6H21V5H19V6H18V15H19V16H20V17H23V34H28V21H30H31V20H32V19H33V6H32V5H31V6H30V18H28V2H27V1H24V2Z"
				fill="currentColor"
			/>
			<path
				id="cactus-small-2-border"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M7 0H10V1H7V0ZM6 2V1H7V2H6ZM4 10H5V2H6V10V20H4V10ZM3 9H4V10H3V9ZM2 9V8H3V9H2ZM1 10V9H2V10H1ZM1 21H0V10H1V21ZM2 22H1V21H2V22ZM3 23H2V22H3V23ZM6 34H5V24H3V23H6V24V34ZM11 34V35H6V34H11ZM14 19V20H12V34H11V19H12H14ZM15 18V19H14V18H15ZM16 17V18H15V17H16ZM16 6V17H17V15H18V16H19V17H20V18H22V34H23V35H28V34H29V22H31V21H32V20H33V19H34V6H33V5H32V4H31V5H30V6H29V2H28V1H27V0H24V1H23V2H22V5H21V4H19V5H18V6H17H16ZM15 5H16V6H15V5ZM14 5V4H15V5H14ZM13 6V5H14V6H13ZM11 2H12V6H13V16H11V6V2ZM11 2V1H10V2H11ZM18 6H19V5H21V6H22V13H23V2H24V1H27V2H28V6V18H30V6H31V5H32V6H33V19H32V20H31V21H29H28V34H23V18V17H20V16H19V15H18V6Z"
				fill="currentColor"
				class="fill-base-100"
			/>
		</g>
		<g id="cactus-small-3">
			<path
				id="cactus-small-3-body"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M10 1H7V2H6V20H4V10H3V9H2V10H1V21H2V22H3V23H6V34H11V19H14V18H15V17H16V6H15V5H14V6H13V16H11V2H10V1ZM24 2H23V21H21V5H20V4H19V5H18V21H19V22H20V23H21V24H23V34H28V26H31V25H32V24H33V9H32V8H31V9H30V23H28V2H27V1H24V2ZM36 4H37V5H38V16H40V2H41V1H44V2H45V18H47V6H48V5H49V6H50V19H49V20H48V21H45V34H40V19H37V18H36V17H35V5H36V4Z"
				fill="currentColor"
			/>
			<path
				id="cactus-small-3-border"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M7 0H10V1H7V0ZM6 2V1H7V2H6ZM4 10H5V2H6V10V20H4V10ZM3 9H4V10H3V9ZM2 9V8H3V9H2ZM1 10V9H2V10H1ZM1 21H0V10H1V21ZM2 22H1V21H2V22ZM3 23H2V22H3V23ZM6 34H5V24H3V23H6V24V34ZM11 34V35H6V34H11ZM14 19V20H12V34H11V19H12H14ZM15 18V19H14V18H15ZM16 17V18H15V17H16ZM16 6V17H17V21H18V22H19V23H20V24H21V23H20V22H19V21H18V6V5H19V4H20V5H21V21H23V5V2H24V1H27V2H28V9V23H30V9H31V8H32V9H33V24H32V25H31V26H29H28V34H23V24H22V34H23V35H28V34H29V27H31V26H32V25H33V24H34V18H35H36V19H37V20H39V34H40V35H45V34H46V22H48V21H49V20H50V19H51V6H50V5H49V4H48V5H47V6H46V2H45V1H44V0H41V1H40V2H39V5H38V4H37V3H36V4H35V5H34V9H33V8H32V7H31V8H30V9H29V2H28V1H27V0H24V1H23V2H22V5H21V4H20V3H19V4H18V5H17V6H16ZM15 5H16V6H15V5ZM14 5V4H15V5H14ZM13 6V5H14V6H13ZM11 2H12V6H13V16H11V6V2ZM11 2V1H10V2H11ZM35 5H36V4H37V5H38V16H40V5V2H41V1H44V2H45V6V18H47V6H48V5H49V6H50V19H49V20H48V21H46H45V34H40V20V19H37V18H36V17H35V5Z"
				fill="currentColor"
				class="fill-base-100"
			/>
		</g>
	</g>
</svg>
