<script lang="ts">
	interface Props {
		svg: SVGElement;
	}

	let { svg = $bindable() }: Props = $props();
</script>

<svg
	bind:this={svg}
	class="hidden"
	width="46"
	height="40"
	viewBox="0 0 46 40"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="bird-spritesheet">
		<g id="bird-flying">
			<g id="bird-flying-2">
				<path
					id="bird-flying-2-border"
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M18 0H16V2H14V6H10V8H8V10H6V12H4V14H2V16H0V18H2V20H14V22H16V24H18V26H20V28H22V30H38V28H42V26H44V24V22H46V20H44V18H32V14H30V12H28V10H26V8H24V6H22V4H20V2H18V0ZM18 2V4H20V6H22V8H24V10H26V12H28V14H30V18H32V20H44V22H38V24H42V26H36V28H22V26H20V24H18V22H16V20H14V18H2V16H4V14H6V12H8V10H10V8H14V12H16V16H18V12V8H16V2H18Z"
					fill="currentColor"
					class="fill-base-100"
				/>
				<path
					id="bird-flying-2-body"
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M18 2H16V4V6V8H18V10V12V14V16H16V12H14V8H10V10H8V12H6V14H4V16H2V18H4H6H8H10H14V20H16V22H18V24H20V26H22V28H32H36V26H38H42V24H38V22H44V20H38H36H32V18H30V14H28V12H26V10H24V8H22V6H20V4H18V2Z"
					fill="currentColor"
				/>
			</g>
			<g id="bird-flying-1">
				<path
					id="bird-flying-1-border"
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M10 6H14V8H10V6ZM8 10V8H10V10H8ZM6 12V10H8V12H6ZM4 14V12H6V14H4ZM2 16V14H4V16H2ZM14 20H0V18V16H2V18H14V20ZM16 22H14V20H16V22ZM18 38H16V22H18V38ZM20 38V40H18V38H20ZM22 36V38H20V36H22ZM24 32V36H22V32H24ZM26 30V32H24V30H26ZM36 28V30H26V28H36ZM42 26V28H36V26H42ZM44 22V24V26H42V24H38V22H44ZM32 18H46V20V22H44V20H32V18ZM30 16H32V18H30V16ZM16 12H18V14H30V16H18H16V12ZM16 12V8H14V12H16Z"
					fill="currentColor"
					class="fill-base-100"
				/>
				<path
					id="bird-flying-1-body"
					fill-rule="evenodd"
					clip-rule="evenodd"
					d="M14 8H10V10H8V12H6V14H4V16H2V18H4H6H8H10H14V20H16V22H18V28V38H20V36H22V32H24V30H26V28H30H32H36V26H38H42V24H38V22H44V20H38H36H32V18H30V16H18H16V12H14V8Z"
					fill="currentColor"
				/>
			</g>
		</g>
	</g>
</svg>
