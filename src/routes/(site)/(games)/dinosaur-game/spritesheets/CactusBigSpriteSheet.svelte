<script lang="ts">
	interface Props {
		svg: SVGElement;
	}

	let { svg = $bindable() }: Props = $props();
</script>

<svg
	bind:this={svg}
	class="hidden"
	width="75"
	height="50"
	viewBox="0 0 75 50"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="cactus-big">
		<g id="cactus-big-1">
			<path
				id="cactus-big-1-body"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M10 1H15V2H16V27H19V12H20V11H23V12H24V27H23V28H22V29H21V30H20V31H16V47H9H7V46H9V32H4V31H3V30H2V29H1V14H2V13H5V14H6V27H9V2H10V1ZM5 46H6V47H5V46ZM17 48H16V49H17V48ZM19 45H20V46H19V45Z"
				fill="currentColor"
			/>
			<path
				id="cactus-big-1-border"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M15 0H10V1H9V2H8V26H7V14H6V13H5V12H2V13H1V14H0V29H1V30H2V31H3V32H4V33H8V45H7V46H6V45H5V46H4V47H5V48H6V47H7V48H15V49H16V50H17V49H18V48H17V47V32H20V31H21V30H22V29H23V28H24V27H25V12H24V11H23V10H20V11H19V12H18V26H17V2H16V1H15V0ZM15 1V2H16V27H17H18H19V12H20V11H23V12H24V27H23V28H22V29H21V30H20V31H17H16V47H7V46H8H9V33V32H4V31H3V30H2V29H1V14H2V13H5V14H6V26V27H8H9V2H10V1H15ZM17 48V49H16V48H17ZM6 47V46H5V47H6ZM19 45H18V46H19V47H20V46H21V45H20V44H19V45ZM19 45H20V46H19V45Z"
				fill="currentColor"
				class="fill-base-100"
			/>
		</g>
		<g id="cactus-big-2">
			<path
				id="cactus-big-2-body"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M10 1H15V2H16V27H19V12H20V11H22V12H23V27H22V28H21V29H20V30H16V47H9H7V46H9V32H4V31H3V30H2V29H1V14H2V13H5V14H6V27H9V2H10V1ZM5 46H6V47H5V46ZM17 48H16V49H17V48ZM19 45H20V46H19V45ZM35 2H34V21H31V7H30V6H27V7H26V22H27V23H28V24H29V25H30V26H31H34V46H32V47H34H41V31H45V30H46V29H47V28H48V27H49V12H48V11H45V12H44V27H41V2H40V1H35V2ZM31 46H30V47H31V46ZM44 45H45V46H44V45ZM42 48H41V49H42V48Z"
				fill="currentColor"
			/>
			<path
				id="cactus-big-2-border"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M15 0H10V1H9V2H8V26H7V14H6V13H5V12H2V13H1V14H0V29H1V30H2V31H3V32H4V33H8V45H7V46H6V45H5V46H4V47H5V48H6V47H7V48H15V49H16V50H17V49H18V48H17V47V31H20V30H21V29H22V28H23V27H24V12V11H22V10H20V11H19V12H18V26H17V2H16V1H15V0ZM15 1V2H16V27H17H18H19V12H20V11H22V12H23V27H22V28H21V29H20V30H17H16V47H7V46H8H9V33V32H4V31H3V30H2V29H1V14H2V13H5V14H6V26V27H8H9V2H10V1H15ZM17 48H16V49H17V48ZM6 47H5V46H6V47ZM18 45H19V46H18V45ZM20 46V47H19V46H20ZM20 45H21V46H20V45ZM20 45V44H19V45H20ZM27 5H30V6H27V5ZM26 7V6H27V7H26ZM26 22H25V7H26V22ZM27 23H26V22H27V23ZM28 24H27V23H28V24ZM29 25H28V24H29V25ZM30 26H29V25H30V26ZM32 46V45H33V27H30V26H34V27V46H33H32ZM32 47V46H31V45H30V46H29V47H30V48H31V47H32ZM45 31H42H41V47H32V48H40V49H41V50H42V49H43V48H42V47V32H45V31ZM46 30V31H45V30H46ZM47 29V30H46V29H47ZM48 28V29H47V28H48ZM49 27V28H48V27H49ZM49 12H50V27H49V12ZM48 11H49V12H48V11ZM45 11V10H48V11H45ZM44 12V11H45V12H44ZM41 2H42V26H43V12H44V27H43H42H41V2ZM40 1H41V2H40V1ZM35 1V0H40V1H35ZM34 2H35V1H34V2ZM33 21H34V2H33V20H32V7H31V6H30V7H31V21H32H33ZM42 48V49H41V48H42ZM31 47V46H30V47H31ZM45 46H44V45H45V46ZM45 46H46V45H45V44H44V45H43V46H44V47H45V46Z"
				fill="currentColor"
				class="fill-base-100"
			/>
		</g>
		<g id="cactus-big-3">
			<path
				id="cactus-big-3-body"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M15 1H10V2H9V28H6V14H5V13H2V14H1V28V29H2V30H3V31H4V32H5V33H9V47H7V48H9H16V49H17V48H16V31H20V30H21V29H22V28H23V13H22V12H20V13H19V28H16V2H15V1ZM5 47H6V48H5V47ZM19 45H20V46H19V45ZM35 4H36V18H39V9H40V8H41V9H42V19H41V20H40V21H39H36V48H31V34H28V33H27V32H26V31H25V30V18H26V17H28V18H29V30H31V4H32V3H35V4ZM41 25H40V26H39V35H40V36H41V37H42V38H44V48H47V37H50V36H51V35H52V26H51V25H50V26H49V35H47V21H46V20H45V21H44V35H42V26H41V25ZM40 48H41V49H40V48ZM25 47H24V48H25V47ZM65 2H66V28H69V27V13H70V12H73V13H74V27H73V28H72V29H71V30H70V31H66V48H59H57V47H59V26H56H54V25H53V24H52V23H51V8H52V7H55V8H56V21H59V2H60V1H65V2ZM66 48H67V49H66V48ZM56 47H55V48H56V47ZM70 45H69V46H70V45Z"
				fill="currentColor"
			/>
			<path
				id="cactus-big-3-border"
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M15 0H10V1H9V2H8V27H7V14H6V13H5V12H2V13H1V14H0V30H1H2V31H3V32H4V33H5V34H8V46H7V47H6V46H5V47H4V48H5V49H6V48H7V49H16V50H17V49H18V48H17V32H20V31H21V30H22V29H23V28H24V31H25V32H26V33H27V34H28V35H30V48H31V49H36V48H37V22H40V21H41V20H42V19H43V9H42V8H41V7H40V8H39V9H38V17H37V4H36V3H35V2H32V3H31V4H30V18H29V17H28V16H26V17H25V18H24V13H23V12H22V11H20V12H19V13H18V27H17V2H16V1H15V0ZM15 1V2H16V28H17H18H19V13H20V12H22V13H23V28H22V29H21V30H20V31H17H16V48H7V47H8H9V34V33H5V32H4V31H3V30H2V29H1V14H2V13H5V14H6V28H7H8H9V2H10V1H15ZM25 18V31H26V32H27V33H28V34H30H31V48H36V22V21H40V20H41V19H42V9H41V8H40V9H39V18H38H37H36V4H35V3H32V4H31V18V30H29V18H28V17H26V18H25ZM16 48V49H17V48H16ZM6 48V47H5V48H6ZM19 46H20V47H19V46ZM19 45V46H18V45H19ZM19 45H20V46H21V45H20V44H19V45ZM24 46H25V47H24V46ZM24 48H23V47H24V48ZM25 48V49H24V48H25ZM25 48H26V47H25V48ZM40 48H39V49H40V50H41V49H42V48H41V47H40V48ZM40 48H41V49H40V48ZM46 19H45V20H44V21H43V26H42V25H41V24H40V25H39V26H38V35H39V36H40V37H41V38H42V39H43V48H44V49H47V48H48V38H50V37H51V36H52V35H53V26H54V27H58V46H57V47H56V46H55V47H54V48H55V49H56V48H57V49H66V50H67V49H68V48H67V32H70V31H71V30H72V29H73V28H74V27H75V13H74V12H73V11H70V12H69V13H68V27H67V2H66V1H65V0H60V1H59V2H58V20H57V8H56V7H55V6H52V7H51V8H50V23H51V24H50V25H49V26H48V21H47V20H46V19ZM46 20V21H47V26V35H49V26H50V25H51V26H52V35H51V36H50V37H48H47V48H44V38H43H42V37H41V36H40V35H39V26H40V25H41V26H42V35H43H44V21H45V20H46ZM52 25V26H53V25H54V26H59V27V47H58H57V48H66V49H67V48H66V31H67H70V30H71V29H72V28H73V27H74V13H73V12H70V13H69V28H68H67H66V2H65V1H60V2H59V21H58H57H56V8H55V7H52V8H51V23H52V24H51V25H52ZM52 25V24H53V25H52ZM56 48H55V47H56V48ZM68 45H69V46H68V45ZM70 46V47H69V46H70ZM70 45H71V46H70V45ZM70 45V44H69V45H70Z"
				fill="currentColor"
				class="fill-base-100"
			/>
		</g>
	</g>
</svg>
