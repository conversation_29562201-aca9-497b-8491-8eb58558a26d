<script lang="ts">
	interface Props {
		svg: SVGElement;
	}

	let { svg = $bindable() }: Props = $props();
</script>

<svg
	bind:this={svg}
	class="hidden"
	width="1200"
	height="12"
	viewBox="0 0 1200 12"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M740 0H734V1H733V2H731V3H730V4H0V5H731V4H732V3H734V2H735V1H739V2H740V3H742V4H743V5H761V6H762V7H771V6H772V5H1052V4H1053V3H1055V2H1056V1H1060V2H1061V3H1063V4H1064V5H1075V4H1076V3H1079V2H1080V1H1084V2H1085V3H1087V4H1088V5H1200V4H1089V3H1087V2H1085V1V0H1079V1H1078V2H1076V3H1074V4H1065V3H1063V2H1061V1V0H1055V1H1054V2H1052V3H1051V4H771V5H770V6H763V5H762V4H744V3H742V2H741V1H740V0ZM0 11H4V12H0V11ZM10 8H6V9H10V8ZM14 11H16V12H14V11ZM42 10H41V11H42V10ZM48 11H52V12H48V11ZM58 7H55V8H58V7ZM70 10H72V11H70V10ZM86 8H85V9H86V8ZM90 10H94V11H90V10ZM104 8H102V9H104V8ZM122 11H123V12H122V11ZM139 10H137V11H139V10ZM151 7H155V8H151V7ZM179 10H178V11H179V10ZM182 8H184V9H182V8ZM191 10H188V11H191V10ZM215 8H216V9H215V8ZM229 7H226V8H229V7ZM231 9H232V10H231V9ZM247 8H246V9H247V8ZM262 10H263V11H262V10ZM268 10H266V11H268V10ZM282 10H285V11H282V10ZM300 9H299V10H300V9ZM321 8H323V9H321V8ZM338 11H335V12H338V11ZM353 7H357V8H353V7ZM363 8H362V9H363V8ZM381 8H384V9H381V8ZM412 9H410V10H412V9ZM416 11H420V12H416V11ZM429 11H428V12H429V11ZM438 8H441V9H438V8ZM466 10H464V11H466V10ZM491 9H492V10H491V9ZM500 8H497V9H500V8ZM515 9H518V10H515V9ZM526 11H524V12H526V11ZM545 8H546V9H545V8ZM551 8H549V9H551V8ZM563 8H566V9H563V8ZM573 10H572V11H573V10ZM575 7H579V8H575V7ZM589 11H588V12H589V11ZM600 11H604V12H600V11ZM610 8H606V9H610V8ZM614 11H616V12H614V11ZM642 10H641V11H642V10ZM648 11H652V12H648V11ZM658 7H655V8H658V7ZM670 10H672V11H670V10ZM686 8H685V9H686V8ZM690 10H694V11H690V10ZM704 8H702V9H704V8ZM722 11H723V12H722V11ZM739 10H737V11H739V10ZM751 7H755V8H751V7ZM779 10H778V11H779V10ZM782 8H784V9H782V8ZM791 10H788V11H791V10ZM815 8H816V9H815V8ZM829 7H826V8H829V7ZM831 9H832V10H831V9ZM847 8H846V9H847V8ZM862 10H863V11H862V10ZM868 10H866V11H868V10ZM882 10H885V11H882V10ZM900 9H899V10H900V9ZM921 8H923V9H921V8ZM938 11H935V12H938V11ZM953 7H957V8H953V7ZM963 8H962V9H963V8ZM981 8H984V9H981V8ZM1012 9H1010V10H1012V9ZM1016 11H1020V12H1016V11ZM1029 11H1028V12H1029V11ZM1038 8H1041V9H1038V8ZM1066 11V10H1064V11H1066ZM1091 9H1092V10H1091V9ZM1100 8H1097V9H1100V8ZM1115 9H1118V10H1115V9ZM1126 11H1124V12H1126V11ZM1145 8H1146V9H1145V8ZM1151 8H1149V9H1151V8ZM1163 8H1166V9H1163V8ZM1173 10H1172V11H1173V10ZM1175 7H1179V8H1175V7ZM1189 11H1188V12H1189V11Z"
		fill="currentColor"
	/>
</svg>
