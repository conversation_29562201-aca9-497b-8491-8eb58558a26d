import { SVGSprite } from '../util/AnimatedSVGSprite';
import type { HitBox } from '$lib/util/HitBox';
import { WorldObject } from './WorldObject';
import { getFloorPosition } from './getFloorPosition';

export class Floor extends WorldObject {
	hitBoxes: HitBox[] = [];
	private _sprite: SVGSprite;

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement) {
		super(context);
		this._context = context;
		this._sprite = new SVGSprite(spriteSheet);
	}

	draw() {
		const context = this._context;
		context.save();

		context.translate(this.position.x, this.position.y);
		this._sprite.draw(this._context);

		context.restore();
	}

	tick() {
		this.position.y = getFloorPosition(this._context, this._sprite.height);
	}

	get width() {
		return this._sprite.width;
	}
}
