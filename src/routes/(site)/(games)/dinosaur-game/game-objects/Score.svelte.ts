const maxScore = 99999;

const getScoreString = (score: number) => {
	return Math.floor(score).toString().padStart(5, '0');
};

export class Score {
	private _context: CanvasRenderingContext2D;
	private _getBestScore: () => number | null;
	private _totalGameTime = $state(0);

	constructor(context: CanvasRenderingContext2D, getBestScore: () => number | null) {
		this._context = context;
		this._getBestScore = getBestScore;
	}

	get score() {
		// 10 seconds = 100 score
		const timeInSeconds = this._totalGameTime / 1000;

		return Math.min(maxScore, timeInSeconds * 10);
	}

	get scoreString() {
		return getScoreString(this.score);
	}

	get highestScoreString() {
		const best = this._getBestScore();

		if (best === null || best === 0) {
			return '';
		}

		return getScoreString(best ?? 0);
	}

	get highScoreText() {
		if (!this.highestScoreString) {
			return '';
		}

		return `HI   ${this.highestScoreString}`;
	}

	draw() {
		const context = this._context;
		context.save();
		context.fillStyle = window.getComputedStyle(context.canvas).color;
		context.font = `bold ${15 * devicePixelRatio}px ${
			window.getComputedStyle(context.canvas).fontFamily
		}`;
		context.textBaseline = 'top';
		context.textAlign = 'start';

		const y = 8 * devicePixelRatio;
		context.fillText(
			this.highScoreText,
			this._context.canvas.width - 150 * devicePixelRatio,
			y,
		);
		context.fillText(this.scoreString, this._context.canvas.width - 65 * devicePixelRatio, y);
		context.restore();
	}

	tick(timeSinceLastUpdate: number) {
		this._totalGameTime += timeSinceLastUpdate;
	}

	reset() {
		this._totalGameTime = 0;
	}
}
