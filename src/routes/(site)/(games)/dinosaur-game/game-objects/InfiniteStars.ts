import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import { Star, type StarVariant } from './Star';

const starAmount = 2;
const starVariants: StarVariant[] = [1, 2, 3];

export class InfiniteStars {
	private _context: CanvasRenderingContext2D;
	private _stars: Star[];

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement) {
		this._context = context;
		this._stars = Array(starAmount)
			.fill(0)
			.map(() => new Star(context, spriteSheet, getRandomItemAt(starVariants)));

		this._stars.forEach((star, index) => {
			star.position.x =
				this._context.canvas.width +
				this.baseStarSpacing * index +
				this.baseStarSpacing * 0.2 * Math.random();
		});
	}

	get baseStarSpacing() {
		return this._context.canvas.width * 0.8;
	}

	draw() {
		this._stars.forEach((star) => star.draw());
	}

	tick() {
		this._stars.forEach((star) => star.tick());
		this._stars.forEach((star) => {
			if (star.state === 'dead') {
				star.position.x += this.baseStarSpacing * starAmount;
			}
		});
	}

	incrementPositionXBy(dx: number) {
		this._stars.forEach((star) => (star.position.x += dx));
	}
}
