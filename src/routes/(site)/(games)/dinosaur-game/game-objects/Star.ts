import { SVGPathSprite } from '../util/AnimatedSVGSprite';
import type { HitBox } from '$lib/util/HitBox';
import { WorldObject } from './WorldObject';

export type StarVariant = 1 | 2 | 3;

export class Star extends WorldObject {
	hitBoxes: HitBox[] = [];
	private _sprite: SVGPathSprite;
	private _yFactor = Math.random();

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement, variant: StarVariant) {
		super(context);
		this._context = context;

		this._sprite = new SVGPathSprite(spriteSheet.querySelector(`[id=star-${variant}]`)!);
		this.reset();
	}

	draw() {
		const context = this._context;
		context.save();

		context.translate(this.position.x, this.position.y);
		context.globalAlpha = 0.5;
		this._sprite.draw(this._context);

		context.restore();
	}

	tick() {
		const maxY = this._context.canvas.height / 2 - this._sprite.height;
		this.position.y = maxY * this._yFactor;
	}

	get width() {
		return this._sprite.width;
	}
}
