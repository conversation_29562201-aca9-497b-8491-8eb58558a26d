import { Moon, type MoonVariant } from './Moon';

const moonsAmount: MoonVariant = 7;

export class InfiniteMoon {
	private _moons: Moon[];
	private _currentMoonIndex = 0;

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement) {
		this._moons = Array(moonsAmount)
			.fill(0)
			.map((_, index) => new Moon(context, spriteSheet, (index + 1) as MoonVariant));
	}

	private get _currentMoon() {
		return this._moons[this._currentMoonIndex];
	}

	private _goToNextMoon() {
		let nextIndex = this._currentMoonIndex + 1;

		if (nextIndex >= this._moons.length) {
			nextIndex = 0;
		}

		this._currentMoonIndex = nextIndex;
		this._currentMoon.reset();
	}

	draw() {
		this._currentMoon.draw();
	}

	tick() {
		this._currentMoon.tick();
		if (this._currentMoon.state === 'dead') {
			this._goToNextMoon();
		}
	}

	incrementPositionXBy(dx: number) {
		this._currentMoon.position.x += dx;
	}
}
