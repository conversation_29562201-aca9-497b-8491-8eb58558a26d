import { AnimatedSVGSprite } from '../util/AnimatedSVGSprite';
import { HitBox } from '$lib/util/HitBox';
import { WorldObject } from './WorldObject';
import { getFloorPosition } from './getFloorPosition';

export type BirdPosition = 'top' | 'center' | 'bottom';

export class Bird extends WorldObject {
	hitBoxes = [
		new HitBox(
			{
				x: 15,
				y: 18,
				w: 22,
				h: 12,
			},
			this.position,
			true,
		),
		new HitBox(
			{
				x: 6,
				y: 11,
				w: 10,
				h: 7,
			},
			this.position,
			true,
		),
	];
	private _animation: AnimatedSVGSprite;
	private _birdPosition: BirdPosition;

	constructor(
		context: CanvasRenderingContext2D,
		spriteSheet: SVGElement,
		position: BirdPosition,
	) {
		super(context);
		this._birdPosition = position;
		this._animation = new AnimatedSVGSprite(
			[
				spriteSheet.querySelector('[id=bird-flying-1]')!,
				spriteSheet.querySelector('[id=bird-flying-2]')!,
			],
			150,
		);
		this.position.x = context.canvas.width;
		this.position.y = this.getTranslateY();
	}

	get width() {
		return this._animation.width;
	}

	getTranslateY() {
		const floor = getFloorPosition(this._context, this._animation.height);

		if (this._birdPosition === 'bottom') {
			return floor;
		}

		if (this._birdPosition === 'center') {
			return floor - 27 * window.devicePixelRatio;
		}

		return floor - 48 * window.devicePixelRatio;
	}

	draw() {
		const context = this._context;
		context.save();
		context.translate(this.position.x, this.position.y);
		this._animation.draw(context);
		context.restore();
	}

	tick(time: number) {
		this._animation.tick(time);
		this.position.y = this.getTranslateY();
	}
}
