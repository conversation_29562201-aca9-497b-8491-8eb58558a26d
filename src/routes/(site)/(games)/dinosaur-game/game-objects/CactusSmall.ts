import { SVGSprite } from '../util/AnimatedSVGSprite';
import { HitBox } from '$lib/util/HitBox';
import { WorldObject } from './WorldObject';
import { getFloorPosition } from './getFloorPosition';

export type CactusSmallVariant = 1 | 2 | 3;

export class CactusSmall extends WorldObject {
	private _sprite: SVGSprite;
	private _variant: CactusSmallVariant;
	private _hitBoxes1: HitBox[] = [
		new HitBox(
			{
				x: 5,
				y: 1,
				w: 6,
				h: 32,
			},
			this.position,
			true,
		),
	];
	private _hitBoxes2: HitBox[] = [
		new HitBox(
			{
				x: 5,
				y: 1,
				w: 24,
				h: 33,
			},
			this.position,
			true,
		),
	];
	private _hitBoxes3: HitBox[] = [
		new HitBox(
			{
				x: 5,
				y: 1,
				w: 42,
				h: 33,
			},
			this.position,
			true,
		),
	];

	constructor(
		context: CanvasRenderingContext2D,
		spriteSheet: SVGElement,
		amount: CactusSmallVariant,
	) {
		super(context);
		this._variant = amount;

		this._sprite = new SVGSprite(spriteSheet.querySelector(`[id=cactus-small-${amount}]`)!);
	}

	get hitBoxes(): HitBox[] {
		if (this._variant === 1) {
			return this._hitBoxes1;
		}

		if (this._variant === 2) {
			return this._hitBoxes2;
		}

		return this._hitBoxes3;
	}

	get width() {
		return this._sprite.width;
	}

	draw() {
		const context = this._context;
		context.save();
		context.translate(this.position.x, this.position.y);
		this._sprite.draw(this._context);
		context.restore();
	}

	tick() {
		this.position.y = getFloorPosition(this._context, this._sprite.height);
	}
}
