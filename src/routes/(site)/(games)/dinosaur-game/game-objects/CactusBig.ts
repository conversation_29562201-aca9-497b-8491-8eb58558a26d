import { SVGSprite } from '../util/AnimatedSVGSprite';
import { HitBox } from '$lib/util/HitBox';
import { WorldObject } from './WorldObject';
import { getFloorPosition } from './getFloorPosition';

export type CactusBigVariant = 1 | 2 | 3;

export class CactusBig extends WorldObject {
	private _sprite: SVGSprite;
	private _variant: CactusBigVariant;
	private _hitBoxes1: HitBox[] = [
		new HitBox(
			{
				x: 8,
				y: 12,
				w: 10,
				h: 34,
			},
			this.position,
			true,
		),
	];
	private _hitBoxes2: HitBox[] = [
		new HitBox(
			{
				x: 8,
				y: 12,
				w: 36,
				h: 34,
			},
			this.position,
			true,
		),
	];
	private _hitBoxes3: HitBox[] = [
		new HitBox(
			{
				x: 8,
				y: 12,
				w: 60,
				h: 35,
			},
			this.position,
			true,
		),
	];

	constructor(
		context: CanvasRenderingContext2D,
		spriteSheet: SVGElement,
		amount: CactusBigVariant,
	) {
		super(context);
		this._variant = amount;

		this._sprite = new SVGSprite(spriteSheet.querySelector(`[id=cactus-big-${amount}]`)!);
	}

	get variant() {
		return this._variant;
	}

	get hitBoxes(): HitBox[] {
		if (this._variant === 1) {
			return this._hitBoxes1;
		}

		if (this._variant === 2) {
			return this._hitBoxes2;
		}

		return this._hitBoxes3;
	}

	get width() {
		return this._sprite.width;
	}

	draw() {
		const context = this._context;
		context.save();
		context.translate(this.position.x, this.position.y);
		this._sprite.draw(this._context);
		context.restore();
	}

	tick() {
		this.position.y = getFloorPosition(this._context, this._sprite.height);
	}
}
