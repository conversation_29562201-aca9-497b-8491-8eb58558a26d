import type { Point2D } from '$lib/models/Point2D';
import { AnimatedSVGSprite } from '../util/AnimatedSVGSprite';
import { HitBox } from '$lib/util/HitBox';
import type { Timer } from '$lib/util/Timer.svelte';
import { getFloorPosition } from './getFloorPosition';

export type DinoState = 'idle' | 'walk' | 'duck' | 'dead';

export const gravity = 0.75;
export const maxSpeedY = 19;
export const positionX = 20;
export const friction = 0.9;

type onValidUserInput = (repeat: boolean) => void;
export class Dino {
	onValidUserInput: onValidUserInput = () => {
		// Ignore
	};
	onJump: () => void = () => {
		// Ignore
	};
	private _timer: Timer;
	private _state: DinoState = 'idle';
	private _animations: Record<DinoState, AnimatedSVGSprite>;
	private _context: CanvasRenderingContext2D;
	private _speedY = 0;
	readonly position: Point2D = {
		x: 0,
		y: 0,
	};
	private _walkHitBoxes = [
		new HitBox(
			{
				x: 22,
				y: 3,
				w: 20,
				h: 13,
			},
			this.position,
			true,
		),
		new HitBox(
			{
				x: 4,
				y: 20,
				w: 26,
				h: 12,
			},
			this.position,
			true,
		),
		new HitBox(
			{
				x: 8,
				y: 32,
				w: 18,
				h: 6,
			},
			this.position,
			true,
		),
		new HitBox(
			{
				x: 12,
				y: 38,
				w: 12,
				h: 7,
			},
			this.position,
			true,
		),
		new HitBox(
			{
				x: 2,
				y: 17,
				w: 2,
				h: 12,
			},
			this.position,
			true,
		),
	];
	private _duckHitBoxes = [
		new HitBox(
			{
				x: 10,
				y: 20,
				w: 40,
				h: 18,
			},
			this.position,
			true,
		),
	];
	private _touchEventsTarget: HTMLElement;

	constructor(
		context: CanvasRenderingContext2D,
		spriteSheet: SVGElement,
		touchEventsTarget: HTMLElement,
		timer: Timer,
	) {
		this._timer = timer;
		this._context = context;
		this._animations = {
			idle: new AnimatedSVGSprite([spriteSheet.querySelector('[id=idle]')!]),
			dead: new AnimatedSVGSprite([spriteSheet.querySelector('[id=dino-dead]')!]),
			duck: new AnimatedSVGSprite([
				spriteSheet.querySelector('[id=dino-duck-1]')!,
				spriteSheet.querySelector('[id=dino-duck-2]')!,
			]),
			walk: new AnimatedSVGSprite([
				spriteSheet.querySelector('[id=dino-walk-1]')!,
				spriteSheet.querySelector('[id=dino-walk-2]')!,
			]),
		};
		this._touchEventsTarget = touchEventsTarget;
		this.addListeners();
		this.position.y = this._maxY;
	}

	set state(newState: DinoState) {
		if (this._state === 'dead') {
			return;
		}
		this._state = newState;
	}

	get state() {
		return this._state;
	}

	get hitBoxes() {
		if (this.state === 'duck') {
			return this._duckHitBoxes;
		}

		return this._walkHitBoxes;
	}

	private get _currentAnimation() {
		return this._animations[this._state];
	}

	private get _isGrounded() {
		return Math.abs(this.position.y - this._maxY) < 0.01;
	}

	private get _maxY() {
		return getFloorPosition(this._context, this._animations.idle.height) + devicePixelRatio;
	}

	draw() {
		const context = this._context;
		context.save();
		context.translate(this.position.x, this.position.y);
		this._currentAnimation.draw(context);
		context.restore();
	}

	tick(time: number) {
		this._currentAnimation.tick(time);
		this._speedY -= gravity * devicePixelRatio;
		this.position.x = positionX * devicePixelRatio;
		this.position.y -= this._speedY;
		this._speedY *= friction; // friction

		if (this.position.y > this._maxY) {
			this.position.y = this._maxY;
			this._speedY = 0;
		}

		if (this._isGrounded && this._state !== 'duck') {
			this.state = 'walk';
		}
	}

	collidesWith(hitBox: HitBox | HitBox[]): boolean {
		if (Array.isArray(hitBox)) {
			return hitBox.some((box) => this.collidesWith(box));
		}

		return this.hitBoxes.some((box) => box.collidesWith(hitBox));
	}

	private _jump() {
		if (this._state === 'dead') {
			return;
		}

		if (this._isGrounded) {
			this.state = 'idle';
			this._speedY = maxSpeedY * devicePixelRatio;
			this.onJump();
		}
	}

	private _duck() {
		if (this._state === 'dead') {
			return;
		}

		this.state = 'duck';
		this._speedY = -maxSpeedY * 0.7 * devicePixelRatio;
	}

	private _walk() {
		if (this._state === 'dead') {
			return;
		}

		if (this._isGrounded) {
			this.state = 'walk';
		} else {
			this._state = 'idle';
		}
	}

	private _handleTouchStart = () => {
		this._jump();
		this.onValidUserInput(false);
	};

	private _handleKeyDown = (event: KeyboardEvent) => {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		if (this._timer.paused && this._timer.started && !this._timer.stopped) {
			return;
		}

		const key = event.key.toLocaleLowerCase();
		if (['w', 'arrowup', ' '].includes(key)) {
			this._jump();
			this.onValidUserInput(event.repeat);
			event.preventDefault();
		}

		if (['s', 'arrowdown'].includes(key)) {
			this._duck();
			this.onValidUserInput(event.repeat);
			event.preventDefault();
		}
	};

	private _handleKeyUp = (event: KeyboardEvent) => {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		if (this._timer.paused && this._timer.started && !this._timer.stopped) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (['w', 'arrowup', 's', 'arrowdown'].includes(key)) {
			this._walk();
			this.onValidUserInput(event.repeat);
			event.preventDefault();
		}
	};

	private addListeners() {
		this._touchEventsTarget.addEventListener('touchstart', this._handleTouchStart);
		window.addEventListener('keydown', this._handleKeyDown);
		window.addEventListener('keyup', this._handleKeyUp);
	}

	dispose() {
		this._touchEventsTarget.removeEventListener('touchstart', this._handleTouchStart);
		window.removeEventListener('keydown', this._handleKeyDown);
		window.removeEventListener('keyup', this._handleKeyUp);
	}

	reset() {
		this._state = 'walk';
		this._speedY = 0;
		this.position.y = this._maxY;
	}
}
