import { Floor } from './Floor';

export class InfiniteFloor {
	private _floor1: Floor;
	private _floor2: Floor;

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement) {
		this._floor1 = new Floor(context, spriteSheet);
		this._floor2 = new Floor(context, spriteSheet);
		this._floor2.position.x = this._floor1.width * window.devicePixelRatio;
	}

	draw() {
		this._floor1.draw();
		this._floor2.draw();
	}

	tick() {
		this._floor1.tick();
		this._floor2.tick();

		if (this._floor1.state === 'dead') {
			this._floor1.position.x =
				this._floor2.position.x + this._floor2.width * window.devicePixelRatio;
		}
		if (this._floor2.state === 'dead') {
			this._floor2.position.x =
				this._floor1.position.x + this._floor1.width * window.devicePixelRatio;
		}
	}

	updatePositionX(x: number) {
		this._floor1.position.x = x;
		this._floor2.position.x = x;
	}

	incrementPositionXBy(dx: number) {
		this._floor1.position.x += dx;
		this._floor2.position.x += dx;
	}
}
