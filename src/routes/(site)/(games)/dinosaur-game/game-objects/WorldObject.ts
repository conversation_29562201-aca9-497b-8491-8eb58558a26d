import type { Point2D } from '$lib/models/Point2D';
import type { HitBox } from '$lib/util/HitBox';

export abstract class WorldObject {
	protected _context: CanvasRenderingContext2D;
	readonly position: Point2D = {
		x: 0,
		y: 0,
	};

	abstract get hitBoxes(): HitBox[];
	abstract get width(): number;

	constructor(context: CanvasRenderingContext2D) {
		this._context = context;
	}

	get outsideOnLeftSideBy() {
		return this.position.x + this.width * window.devicePixelRatio;
	}

	get state() {
		if (this.outsideOnLeftSideBy < 0) {
			return 'dead';
		}

		return 'alive';
	}

	abstract draw(): void;

	tick(_time: number) {
		//
	}

	reset() {
		this.position.x = this._context.canvas.width;
	}
}
