import { SVGPathSprite } from '../util/AnimatedSVGSprite';
import type { HitBox } from '$lib/util/HitBox';
import { WorldObject } from './WorldObject';

export type MoonVariant = 1 | 2 | 3 | 4 | 5 | 6 | 7;

export class Moon extends WorldObject {
	hitBoxes: HitBox[] = [];
	private _sprite: SVGPathSprite;
	private _yFactor = 0.9;

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement, variant: MoonVariant) {
		super(context);
		this._context = context;

		this._sprite = new SVGPathSprite(spriteSheet.querySelector(`[id=moon-${variant}]`)!);
		this.reset();
	}

	draw() {
		const context = this._context;
		context.save();

		context.translate(this.position.x, this.position.y);
		context.globalAlpha = 0.1;
		this._sprite.draw(this._context);

		context.restore();
	}

	tick() {
		const maxY = this._context.canvas.height / 2 - this._sprite.height * devicePixelRatio;
		this.position.y = maxY * this._yFactor;
	}

	get width() {
		return this._sprite.width;
	}
}
