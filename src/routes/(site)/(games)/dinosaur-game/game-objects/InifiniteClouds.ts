import { Cloud } from './Cloud';

const cloudAmount = 6;

export class InfiniteClouds {
	private _context: CanvasRenderingContext2D;
	private _clouds: Cloud[];

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement) {
		this._context = context;
		this._clouds = Array(cloudAmount)
			.fill(0)
			.map(() => new Cloud(context, spriteSheet));

		this._clouds.forEach((cloud, index) => {
			cloud.position.x =
				this._context.canvas.width +
				this.baseCloudsSpacing * index +
				this.baseCloudsSpacing * 0.5 * Math.random();
		});
	}

	get baseCloudsSpacing() {
		return this._context.canvas.width / 2;
	}

	draw() {
		this._clouds.forEach((cloud) => cloud.draw());
	}

	tick() {
		this._clouds.forEach((cloud) => cloud.tick());
		this._clouds.forEach((cloud) => {
			if (cloud.state === 'dead') {
				cloud.resetYFactor();
				cloud.position.x += this.baseCloudsSpacing * cloudAmount;
			}
		});
	}

	incrementPositionXBy(dx: number) {
		this._clouds.forEach((cloud) => (cloud.position.x += dx));
	}
}
