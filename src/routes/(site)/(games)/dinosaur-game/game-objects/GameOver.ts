import { SVGSprite } from '../util/AnimatedSVGSprite';

const yFactor = 0.3;

export class GameOver {
	private _context: CanvasRenderingContext2D;
	private _sprite: SVGSprite;

	constructor(context: CanvasRenderingContext2D, retrySpriteSheet: SVGElement) {
		this._context = context;
		this._sprite = new SVGSprite(retrySpriteSheet);
	}

	private drawGameOver() {
		const context = this._context;
		context.save();
		context.fillStyle = window.getComputedStyle(context.canvas).color;
		context.font = `bold ${17 * devicePixelRatio}px ${
			window.getComputedStyle(context.canvas).fontFamily
		}`;
		context.textBaseline = 'top';
		context.textAlign = 'center';

		const y = context.canvas.height * yFactor;
		context.fillText('G  A  M  E      O  V  E  R', context.canvas.width / 2, y);
		context.restore();
	}

	private drawRetry() {
		const context = this._context;
		context.save();
		context.translate(
			context.canvas.width / 2 - (this._sprite.width / 2) * devicePixelRatio,
			context.canvas.height * yFactor + 30 * devicePixelRatio,
		);
		this._sprite.draw(context);
		context.restore();
	}

	draw() {
		this.drawGameOver();
		this.drawRetry();
	}

	tick() {
		//
	}
}
