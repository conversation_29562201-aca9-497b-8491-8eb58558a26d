import { get2DGrid } from '$lib/functions/get2DGrid';
import { getGridItemsAround } from '$lib/functions/getGridItemsAround';
import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import type { GridItem } from '$lib/models/GridItem';
import { DirectionListener, type Direction } from '$lib/util/DirectionListener';
import type { GameSound } from '$lib/util/GameSound.svelte';
import type { Timer } from '$lib/util/Timer.svelte';
import { Smush32 } from '@thi.ng/random';

export type TileSlidePuzzleAudios = {
	replay: GameSound;
	gameWin: GameSound;
	move1: GameSound;
	move2: GameSound;
	move3: GameSound;
};
type EmptyItem = null;
type TileSlidePuzzleBoard = (number | EmptyItem)[][];
type Callback = () => void;

export interface TileSlidePuzzleGameProps {
	targetElement: HTMLElement;
	size: number;
	slideDuration: number;
	audios: TileSlidePuzzleAudios;
	timer: Timer;
	seed?: number;
	onGameOver: Callback;
}

export class TileSlidePuzzleGame {
	id: number = Math.floor(Math.random() * 1e9);
	size: number;
	board: TileSlidePuzzleBoard = $state([]);
	directionListener: DirectionListener;
	private _slideDuration = 0;
	_started = $state(false);
	_isWon = $state(false);
	audios: TileSlidePuzzleAudios | null = null;
	moves = $state(0);
	moveAudios: GameSound[];
	timer: Timer;
	onGameOver: Callback;

	static emptyItem: EmptyItem = null;

	constructor({
		audios,
		onGameOver,
		size,
		slideDuration,
		targetElement,
		timer,
		seed,
	}: TileSlidePuzzleGameProps) {
		this.size = size;
		this.directionListener = new DirectionListener(targetElement, this.handleDirectionChange, {
			emit: 'once',
			throttle: slideDuration,
		});
		this.audios = audios;
		this._slideDuration = slideDuration;
		this.moveAudios = [audios.move1, audios.move2, audios.move3];
		this.timer = timer;
		this.initBoard(seed);
		this.addListeners();
		this.onGameOver = onGameOver;
	}

	get score() {
		return this.moves + Math.floor(this.timer.time / 1000);
	}

	set slideDuration(slideDuration: number) {
		this._slideDuration = slideDuration;
		this.directionListener.options.throttle = slideDuration;
	}

	get started() {
		return this._started;
	}

	set started(started: boolean) {
		this._started = started;
	}

	get isWon() {
		return this._isWon;
	}

	set isWon(isWon: boolean) {
		this._isWon = isWon;

		if (isWon) {
			this.audios?.gameWin.play();
			this.onGameOver();
		}
	}

	get isOver() {
		return this.isWon;
	}

	get emptyItemPosition(): GridItem {
		for (let row = 0; row < this.size; row += 1) {
			for (let column = 0; column < this.size; column += 1) {
				if (this.board[row][column] === TileSlidePuzzleGame.emptyItem) {
					return { row, column };
				}
			}
		}

		throw new Error('Board does not have an empty position!');
	}

	private checkIfGameIsWon(): boolean {
		return this.board
			.flatMap((row) => row)
			.every((item, index) => {
				if (item === TileSlidePuzzleGame.emptyItem) {
					return true;
				}
				return item === index + 1;
			});
	}

	private initBoard = (seed?: number) => {
		const numbers = Array(Math.pow(this.size, 2) - 1)
			.fill(0)
			.map((_, index) => index + 1);

		// Start with a finished game
		this.board = get2DGrid(this.size).map((row, rowIndex) =>
			row.map(
				(_, columnIndex) =>
					numbers[rowIndex * this.size + columnIndex] ?? TileSlidePuzzleGame.emptyItem,
			),
		);

		// Shuffle board
		this.shuffleBoard(seed);

		// Move empty position to last row and column
		this.moveEmptyPositionToLastRowAndColumn();

		// If game is won, init the board again
		if (this.checkIfGameIsWon()) {
			this.initBoard(seed ? seed + 1 : undefined);
		}
	};

	private shuffleBoard(seed?: number) {
		const random = new Smush32(seed);
		const randomFunc = seed !== undefined ? () => random.float() : Math.random;
		const movesToMake = 10 * this.size * this.size;
		let moves = 0;

		while (moves < movesToMake) {
			const itemOnSameRow: GridItem = {
				row: this.emptyItemPosition.row,
				column: Math.floor(randomFunc() * this.size),
			};

			if (this.moveItemOnSameRow(itemOnSameRow)) {
				moves += 1;
			}

			const itemOnSameColumn: GridItem = {
				column: this.emptyItemPosition.column,
				row: Math.floor(randomFunc() * this.size),
			};

			if (this.moveItemOnSameColumn(itemOnSameColumn)) {
				moves += 1;
			}
		}
	}

	private moveEmptyPositionToLastRowAndColumn() {
		const emptyItemPosition = this.emptyItemPosition;

		for (let row = emptyItemPosition.row + 1; row < this.size; row += 1) {
			const nextPosition: GridItem = { row, column: emptyItemPosition.column };

			this.moveItemToEmptyItemNearby(nextPosition);
		}

		for (let column = emptyItemPosition.column + 1; column < this.size; column += 1) {
			const nextPosition: GridItem = { column, row: this.size - 1 };

			this.moveItemToEmptyItemNearby(nextPosition);
		}
	}

	private moveItemToEmptyItemNearby(position: GridItem) {
		const { row, column } = this.emptyItemPosition;

		if (position.row === row && position.column === column) {
			return false;
		}

		const itemsAroundEmptyItem = getGridItemsAround(
			{ row, column },
			{ row: this.size, column: this.size },
			{
				diagonal: false,
			},
		);

		if (
			itemsAroundEmptyItem.some(
				(item) => item.row === position.row && item.column === position.column,
			)
		) {
			this.board[row][column] = this.board[position.row][position.column];
			this.board[position.row][position.column] = TileSlidePuzzleGame.emptyItem;

			return true;
		}

		return false;
	}

	private moveItemOnSameRow(position: GridItem): boolean {
		const { row, column } = this.emptyItemPosition;

		if (position.row === row && position.column === column) {
			return false;
		}

		if (position.row !== row) {
			return false;
		}

		// Move all tiles on same row from the position to the empty item
		const positionsToMove = Array(Math.abs(column - position.column))
			.fill(0)
			.map((_, i) => {
				return {
					row,
					column: position.column < column ? column - i - 1 : column + i + 1,
				} as GridItem;
			});

		for (let index = 0; index < positionsToMove.length; index += 1) {
			const positionToMove = positionsToMove[index];

			this.moveItemToEmptyItemNearby(positionToMove);
		}

		return true;
	}

	private moveItemOnSameColumn(position: GridItem): boolean {
		const { row, column } = this.emptyItemPosition;

		if (position.row === row && position.column === column) {
			return false;
		}

		if (position.column !== column) {
			return false;
		}

		const positionsToMove = Array(Math.abs(row - position.row))
			.fill(0)
			.map((_, i) => {
				return {
					column,
					row: position.row < row ? row - i - 1 : row + i + 1,
				} as GridItem;
			});

		for (let index = 0; index < positionsToMove.length; index += 1) {
			const positionToMove = positionsToMove[index];

			this.moveItemToEmptyItemNearby(positionToMove);
		}

		return true;
	}

	private _moveItem = (position: GridItem): boolean => {
		if (this.isOver) {
			return false;
		}

		if (this.started && this.timer.paused) {
			return false;
		}

		const hasMoved =
			this.moveItemToEmptyItemNearby(position) ||
			this.moveItemOnSameRow(position) ||
			this.moveItemOnSameColumn(position);

		if (hasMoved) {
			this.moves += 1;

			if (this.checkIfGameIsWon()) {
				this.isWon = true;
			}

			if (this.timer.paused) {
				this.timer.start();
			}
		}

		return hasMoved;
	};

	moveItem(position: GridItem) {
		if (this._moveItem(position)) {
			if (this.audios) {
				getRandomItemAt(this.moveAudios).play();
			}
			this.started = true;
		}
	}

	private handleDirectionChange = async (direction: Direction) => {
		const { row, column } = this.emptyItemPosition;
		let positionToMove: GridItem = { row: -1, column: -1 };

		if (direction === 'left') {
			positionToMove = { row, column: column + 1 };
		} else if (direction === 'right') {
			positionToMove = { row, column: column - 1 };
		} else if (direction === 'down') {
			positionToMove = { row: row - 1, column };
		} else if (direction === 'up') {
			positionToMove = { row: row + 1, column };
		}

		if (
			positionToMove?.row < 0 ||
			positionToMove.column < 0 ||
			positionToMove.row >= this.size ||
			positionToMove.column >= this.size
		) {
			return;
		}

		this.moveItem(positionToMove);
	};

	private addListeners() {
		this.directionListener.listen();
	}

	dispose() {
		this.directionListener.dispose();
	}
}
