<script lang="ts">
	import DirectionKeys from '$lib/components/DirectionKeys.svelte';
	import MouseLeftIcon from '$lib/components/Icons/MouseLeftIcon.svelte';
	import TouchIcon from '$lib/components/Icons/TouchIcon.svelte';
	import InstructionsIslandBase from '$lib/components/GameIsland/islands/InstructionsIslandBase.svelte';
</script>

{#snippet direction()}
	<DirectionKeys />
{/snippet}

{#snippet mouseLeft()}
	<MouseLeftIcon class="size-16" />
{/snippet}

{#snippet touch()}
	<TouchIcon class="size-16" />
{/snippet}

<InstructionsIslandBase
	goal="Put the numbers in sequence"
	desktopControls={[
		{
			bindings: [mouseLeft, direction],
		},
	]}
	mobileControls={[
		{
			bindings: [touch],
		},
	]}
/>
