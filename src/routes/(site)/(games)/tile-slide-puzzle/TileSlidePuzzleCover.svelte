<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full p-4', className)}
	fill="transparent"
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 498 498"
	{...props}
>
	<g clip-path="url(#tile-slide-puzzle-cover__a)">
		<path class="fill-base-300" d="M23 23h453v453H23z" />
		<path fill="currentColor" d="M31 31h143v143H31z" />
		<path
			class="fill-base-300"
			d="M102.594 127h9.344V81.906h-9.376L90.906 90v8.375l11.5-7.813h.188V127Z"
		/>
		<path fill="currentColor" d="M178 31h143v143H178z" />
		<path
			class="fill-base-300"
			d="M233.844 95.625h8.75c0-4.344 3.406-7.375 7.906-7.375 4.062 0 6.844 2.75 6.844 6.375 0 3.125-1.282 5.281-7 10.781l-15.969 15.125V127h32.781v-7.5h-20.437v-.188l9.469-9.062c7.624-7.281 10.343-10.688 10.343-16.219 0-7.5-6.375-13.125-16.031-13.125-9.781 0-16.656 6.031-16.656 14.719Z"
		/>
		<path fill="currentColor" d="M325 31h143v143H325z" />
		<path
			class="fill-base-300"
			d="M391.438 107.156h5.156c5.468 0 8.687 2.438 8.656 6.813 0 3.812-3.25 6.437-7.969 6.437-4.969 0-8.219-2.5-8.531-6.187h-9c.406 8.281 7.344 13.781 17.531 13.781 10.469 0 17.563-5.438 17.563-13.625 0-6.187-4.375-10.156-11.063-10.781v-.188c5.313-1.094 9.407-4.812 9.407-10.562 0-7.219-6.438-11.938-15.969-11.938-9.438 0-16 5.281-16.469 13.531h8.656c.282-3.906 3.375-6.28 7.813-6.28 4.5 0 7.031 2.343 7.031 6.03 0 3.688-3.062 6.251-7.781 6.251h-5.031v6.718Z"
		/>
		<path fill="currentColor" d="M31 178h143v143H31z" />
		<path
			class="fill-base-300"
			d="M106.969 274h8.969v-8.281h5.937v-7.531h-5.937v-29.282h-13.313c-9.25 13.594-14.156 21.469-18.094 28.938v7.875h22.438V274ZM92.75 258.188c3.594-6.719 7.688-13.126 14.219-22.532h.187v22.782H92.75v-.25Z"
		/>
		<path fill="currentColor" d="M31 325h143v143H31z" />
		<path
			class="fill-base-300"
			d="M90.719 421h9.906l18.813-37.5v-7.594h-32.25v7.5h22.937v.188L90.719 421Z"
		/>
		<path fill="currentColor" d="M178 178h143v143H178z" />
		<path
			class="fill-base-300"
			d="M250.5 275c10.344 0 17.438-6.469 17.438-16 0-8.594-6.157-14.781-14.75-14.781-4.563 0-8.282 1.843-10.157 4.875h-.187l1.094-12.688h21.281v-7.5h-28.781l-2.063 26.469h8.219c1.531-2.719 4.344-4.406 7.968-4.406 4.938 0 8.376 3.406 8.376 8.25 0 4.906-3.438 8.312-8.407 8.312-4.343 0-7.687-2.562-8.219-6.469h-8.718c.25 8.188 7.25 13.938 16.906 13.938Z"
		/>
		<path fill="currentColor" d="M178 325h143v143H178z" />
		<path
			class="fill-base-300"
			d="M250 422c10.75 0 18.281-5.375 18.281-13.188 0-5.937-4.343-10.312-10.5-11.531v-.187c5.281-1.313 8.844-5.188 8.844-10.032 0-7.062-6.844-12.156-16.625-12.156-9.781 0-16.656 5.125-16.656 12.125 0 4.907 3.594 8.781 8.906 10.063v.187c-6.156 1.188-10.531 5.563-10.531 11.5 0 7.813 7.5 13.219 18.281 13.219Zm0-27.75c-4.281 0-7.312-2.562-7.312-6.25 0-3.719 3.031-6.281 7.312-6.281 4.25 0 7.312 2.562 7.312 6.281 0 3.688-3.062 6.25-7.312 6.25Zm0 20.875c-4.969 0-8.438-2.937-8.438-7.031 0-4.032 3.469-6.969 8.438-6.969 4.969 0 8.406 2.906 8.406 6.969 0 4.094-3.437 7.031-8.406 7.031Z"
		/>
		<path fill="currentColor" d="M325 178h143v143H325z" />
		<path
			class="fill-base-300"
			d="M397.781 275c10.063 0 17.375-6.656 17.375-16 0-8.469-5.968-14.688-14.687-14.688-6.094 0-10.531 3.063-12.281 7.25H388c-.031-9.874 3.219-16.218 9.781-16.218 3.719 0 6.407 1.875 7.469 5.094h9.312c-1.25-7.438-8-12.563-16.75-12.563-11.718 0-18.843 9-18.843 23.937 0 15.563 8.031 23.188 18.812 23.188Zm-.125-7.5c-4.625 0-8.25-3.625-8.25-8.281 0-4.594 3.563-7.938 8.344-7.938 4.719 0 8.219 3.375 8.219 8.125-.031 4.532-3.688 8.094-8.313 8.094Z"
		/>
	</g>
	<defs>
		<clipPath id="tile-slide-puzzle-cover__a">
			<path fill="#fff" d="M0 0h498v498H0z" />
		</clipPath>
	</defs>
</svg>
