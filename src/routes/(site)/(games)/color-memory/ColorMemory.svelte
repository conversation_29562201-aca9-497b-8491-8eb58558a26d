<script lang="ts">
	import { onMount } from 'svelte';
	import { onDestroy } from 'svelte';
	import { ColorMemoryGame, type MemoryGameColor } from './ColorMemoryGame.svelte';
	import { colorMemorySoundResources } from './colorMemorySoundResources';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import { wait } from '$lib/functions/wait';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import ColorMemoryInfoModal from './ColorMemoryInfoModal.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	let gameOverDuration = $derived.by(() => {
		if (
			islandSettings.settings.leaderboards &&
			islandSettings.settings.showLeaderboardsOnGameOver
		) {
			return 4000;
		}

		return 2500;
	});

	let isInfoModalOpen = $state(false);

	let context = new GameContext({
		gameName: 'Color Memory',
		GameClass: ColorMemoryGame,
		gameKey: 'color-memory',
		settings: {
			defaultSettings: {},
		},
		variants: {
			map: {},
			fromGame: () => {
				return {};
			},
			getStatsVariant() {
				return 'default';
			},
			getLeaderboardVariant() {
				return 'default';
			},
		},
		sounds: {
			resources: colorMemorySoundResources,
		},
		defaultGameProps(context) {
			return {
				audios: context.sounds,
				timer: context.timer,
				onGameOver,
			};
		},
		onWillCreateGame({ previousGame }) {
			previousGame?.dispose();
		},
		dailyGame(context) {
			return {
				type: 'seed',
				firstAvailableGameDate: new Date('2025/01/11'),
				toProps(seed) {
					return {
						audios: context.sounds,
						seed,
						timer: context.timer,
						onGameOver,
					};
				},
			};
		},
		stats({ context, props }) {
			return {
				stats: new Stats({
					...props,
					liveStats: {
						score: {
							name: 'Score',
							unit: 'plain',
							value() {
								return context.game?.score ?? 0;
							},
							metrics: {
								total: {
									key: 'totalScore',
									name: 'Total Score',
								},
								average: {
									key: 'averageScore',
									name: 'Average Score',
								},
								max: {
									key: 'bestScore',
									name: 'Best Score',
									useAsBest: true,
								},
								min: {
									key: 'worstScore',
									name: 'Worst Score',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'score'],
				}),
				canUpdateWithGameLost(game) {
					return game.isInProgress && game.score > 0;
				},
				visibleStats: ['bestScore', 'averageScore', 'totalGames'],
			};
		},
		leaderboard({ props }) {
			return {
				leaderboard: new Leaderboard({
					...props,
					firstAvailableDate: new Date('2025/05/08'),
					order: 'higher-first',
				}),
				sendScoreOn: ['lost'],
				getScore(game) {
					return {
						score: game.score,
					};
				},
			};
		},
		onDispose(context) {
			context.game?.dispose();
		},
	});

	let game = $derived(context.game);

	function playColor(color: MemoryGameColor) {
		game?.play(color);
	}

	async function onGameOver() {
		context.handleGameOver('lost');

		if (!context.isPlayingDailyGame) {
			const gameInstance = game;

			await wait(gameOverDuration);

			if (gameInstance === game && gameInstance?.isOver) {
				context.createGame();
			}
		}
	}

	async function startGame() {
		if (!game || game.isOver) {
			await context.createGame();
		}

		context.game?.start();
	}

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<ColorMemoryInfoModal bind:isOpen={isInfoModalOpen} />

<GameLayout>
	{#snippet Island()}
		<GameIsland {context} gameOverIslandDelay={0} gameOverStrategy="best-stats-update" />
	{/snippet}

	<section
		class="flex flex-col size-full touch-pinch-zoom select-none items-center justify-center"
	>
		<div class="flex flex-row gap-2 absolute bottom-0 right-0">
			<MoreGamesButton class="btn-sm" />

			<button
				aria-label="Open info dialog"
				class="btn btn-sm"
				onclick={() => (isInfoModalOpen = true)}
			>
				<InfoSolidIcon class="size-5" />
			</button>
		</div>

		<div class="flex items-center justify-center w-full flex-col gap-8 relative">
			<svg
				class="max-w-md aspect-square"
				xmlns="http://www.w3.org/2000/svg"
				viewBox="0 0 432 432"
			>
				<g filter={game?.color === 'blue' ? 'url(#genius__a)' : undefined}>
					<!-- svelte-ignore a11y_click_events_have_key_events -->
					<!-- svelte-ignore a11y_no_static_element_interactions -->
					<path
						class="fill-game-color-memory-1 transition-colors"
						class:cursor-pointer={game?.isInProgress}
						class:opacity-60={game?.color !== 'blue'}
						onclick={() => playColor('blue')}
						fill-rule="evenodd"
						d="M224 407.522c0 4.542 3.785 8.188 8.312 7.822 97.448-7.865 175.167-85.584 183.032-183.032.366-4.527-3.28-8.312-7.822-8.312H300.821c-4.104 0-7.498 3.12-8.291 7.147-6.086 30.928-30.455 55.297-61.383 61.383-4.027.793-7.147 4.187-7.147 8.291v106.701Z"
						clip-rule="evenodd"
					/>
				</g>
				<g filter={game?.color === 'green' ? 'url(#genius__b)' : undefined}>
					<!-- svelte-ignore a11y_click_events_have_key_events -->
					<!-- svelte-ignore a11y_no_static_element_interactions -->
					<path
						class="fill-game-color-memory-2 transition-colors"
						class:cursor-pointer={game?.isInProgress}
						class:opacity-60={game?.color !== 'green'}
						onclick={() => playColor('green')}
						d="M199.689 16.656C102.241 24.52 24.522 102.24 16.656 199.688c-.365 4.527 3.28 8.312 7.823 8.312h106.7c4.105 0 7.499-3.12 8.291-7.147 6.087-30.928 30.455-55.297 61.384-61.383 4.027-.793 7.147-4.187 7.147-8.291V24.478c0-4.542-3.786-8.188-8.312-7.822Z"
					/>
				</g>
				<g filter={game?.color === 'red' ? 'url(#genius__c)' : undefined}>
					<!-- svelte-ignore a11y_click_events_have_key_events -->
					<!-- svelte-ignore a11y_no_static_element_interactions -->
					<path
						class="fill-game-color-memory-3 transition-colors"
						class:cursor-pointer={game?.isInProgress}
						class:opacity-60={game?.color !== 'red'}
						onclick={() => playColor('red')}
						d="M415.345 199.688C407.48 102.24 329.761 24.521 232.313 16.656c-4.527-.366-8.312 3.28-8.312 7.822v106.701c0 4.104 3.119 7.498 7.147 8.291 30.928 6.086 55.297 30.455 61.383 61.383.793 4.027 4.187 7.147 8.291 7.147h106.701c4.542 0 8.188-3.785 7.822-8.312Z"
					/>
				</g>
				<g filter={game?.color === 'yellow' ? 'url(#genius__d)' : undefined}>
					<!-- svelte-ignore a11y_click_events_have_key_events -->
					<!-- svelte-ignore a11y_no_static_element_interactions -->
					<path
						class="fill-game-color-memory-4 transition-colors"
						class:cursor-pointer={game?.isInProgress}
						class:opacity-60={game?.color !== 'yellow'}
						onclick={() => playColor('yellow')}
						d="M16.656 232.312c7.866 97.448 85.585 175.167 183.033 183.032 4.526.366 8.312-3.28 8.312-7.822V300.821c0-4.104-3.12-7.498-7.147-8.291-30.929-6.086-55.297-30.455-61.384-61.383-.792-4.027-4.186-7.147-8.291-7.147h-106.7c-4.542 0-8.188 3.785-7.823 8.312Z"
					/>
				</g>
				<defs>
					<filter
						id="genius__a"
						width="223.37"
						height="223.37"
						x="208"
						y="208"
						color-interpolation-filters="sRGB"
						filterUnits="userSpaceOnUse"
					>
						<feFlood flood-opacity="0" result="BackgroundImageFix" />
						<feColorMatrix
							in="SourceAlpha"
							result="hardAlpha"
							values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
						/>
						<feOffset />
						<feGaussianBlur stdDeviation="8" />
						<feComposite in2="hardAlpha" operator="out" />
						<feColorMatrix
							values="0 0 0 0 0.333333 0 0 0 0 0.607843 0 0 0 0 0.976471 0 0 0 0.25 0"
						/>
						<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_280_1328" />
						<feBlend
							in="SourceGraphic"
							in2="effect1_dropShadow_280_1328"
							result="shape"
						/>
					</filter>
					<filter
						id="genius__b"
						width="223.37"
						height="223.37"
						x=".631"
						y=".63"
						color-interpolation-filters="sRGB"
						filterUnits="userSpaceOnUse"
					>
						<feFlood flood-opacity="0" result="BackgroundImageFix" />
						<feColorMatrix
							in="SourceAlpha"
							result="hardAlpha"
							values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
						/>
						<feOffset />
						<feGaussianBlur stdDeviation="8" />
						<feComposite in2="hardAlpha" operator="out" />
						<feColorMatrix
							values="0 0 0 0 0.254902 0 0 0 0 0.85098 0 0 0 0 0.458824 0 0 0 0.25 0"
						/>
						<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_280_1328" />
						<feBlend
							in="SourceGraphic"
							in2="effect1_dropShadow_280_1328"
							result="shape"
						/>
					</filter>
					<filter
						id="genius__c"
						width="223.37"
						height="223.37"
						x="208.001"
						y=".63"
						color-interpolation-filters="sRGB"
						filterUnits="userSpaceOnUse"
					>
						<feFlood flood-opacity="0" result="BackgroundImageFix" />
						<feColorMatrix
							in="SourceAlpha"
							result="hardAlpha"
							values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
						/>
						<feOffset />
						<feGaussianBlur stdDeviation="8" />
						<feComposite in2="hardAlpha" operator="out" />
						<feColorMatrix
							values="0 0 0 0 0.968627 0 0 0 0 0.4 0 0 0 0 0.4 0 0 0 0.25 0"
						/>
						<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_280_1328" />
						<feBlend
							in="SourceGraphic"
							in2="effect1_dropShadow_280_1328"
							result="shape"
						/>
					</filter>
					<filter
						id="genius__d"
						width="223.37"
						height="223.37"
						x=".631"
						y="208"
						color-interpolation-filters="sRGB"
						filterUnits="userSpaceOnUse"
					>
						<feFlood flood-opacity="0" result="BackgroundImageFix" />
						<feColorMatrix
							in="SourceAlpha"
							result="hardAlpha"
							values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
						/>
						<feOffset />
						<feGaussianBlur stdDeviation="8" />
						<feComposite in2="hardAlpha" operator="out" />
						<feColorMatrix
							values="0 0 0 0 0.976471 0 0 0 0 0.772549 0 0 0 0 0.0823529 0 0 0 0.25 0"
						/>
						<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_280_1328" />
						<feBlend
							in="SourceGraphic"
							in2="effect1_dropShadow_280_1328"
							result="shape"
						/>
					</filter>
				</defs>
			</svg>

			<button
				disabled={game?.isInProgress}
				class="absolute btn-outline btn-circle btn-lg btn size-24"
				onclick={() => startGame()}
			>
				Start
			</button>
		</div>
	</section>
</GameLayout>
