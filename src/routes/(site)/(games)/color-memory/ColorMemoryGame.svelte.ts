import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import { wait } from '$lib/functions/wait';
import type { GameSound } from '$lib/util/GameSound.svelte';
import { Timer } from '$lib/util/Timer.svelte';
import { Smush32 } from '@thi.ng/random';

export type MemoryGameColor = 'yellow' | 'red' | 'blue' | 'green';
type State = 'idle' | 'showing-colors' | 'listening' | 'over';

export type ColorMemoryAudios = {
	red: GameSound;
	yellow: GameSound;
	blue: GameSound;
	green: GameSound;
	start: GameSound;
	gameOver: GameSound;
};

const colors: MemoryGameColor[] = ['yellow', 'red', 'blue', 'green'];

interface Props {
	audios: ColorMemoryAudios;
	onGameOver: () => void;
	seed?: number;
	timer: Timer;
}

export class ColorMemoryGame {
	color: MemoryGameColor | null = $state(null);
	state: State = $state('idle');
	colorDuration = 1000;
	timer: Timer;

	private colorValidationIndex = 0;
	private colorSequence: MemoryGameColor[] = $state([]);
	private highlightColorId = -1;
	private bestScoreLocalStorageKey = 'color-memory-best-score';
	private _bestScore = $state(0);
	private audios: ColorMemoryAudios;
	private colorSequenceId = Symbol();
	private cleanupTimerListener: () => void;
	private onGameOver: () => void;
	private randomFunc = Math.random;

	constructor({ seed, audios, timer, onGameOver }: Props) {
		if (seed !== undefined) {
			const random = new Smush32(seed);
			this.randomFunc = () => random.float();
		}

		this.timer = timer;
		this.onGameOver = onGameOver;
		this.audios = audios;
		this._bestScore = +(localStorage.getItem(this.bestScoreLocalStorageKey) ?? 0);
		this.cleanupTimerListener = $effect.root(() => {
			$effect(() => {
				if (
					this.timer.started &&
					this.timer.paused &&
					!this.timer.stopped &&
					this.state !== 'over'
				) {
					this.state = 'idle';
					this.colorSequenceId = Symbol();
				} else if (this.timer.started && !this.timer.paused && !this.timer.stopped) {
					if (this.state === 'idle') {
						setTimeout(() => {
							this.showColorSequence();
						}, 1000);
					}
				}
			});
		});
	}

	get isOver() {
		return this.state === 'over';
	}

	private setIsOver(isOver: boolean, playSound = true) {
		this.state = isOver ? 'over' : 'idle';

		if (isOver) {
			this.timer.stop();
			this.bestScore = Math.max(this.score, this.bestScore);
			localStorage.setItem(this.bestScoreLocalStorageKey, `${this.bestScore}`);

			if (playSound) {
				this.audios.gameOver.play();
			}
			this.onGameOver();
		}
	}

	get isInProgress() {
		return this.timer.started && !this.isOver;
	}

	get bestScore() {
		return this._bestScore;
	}

	set bestScore(bestScore: number) {
		this._bestScore = bestScore;
	}

	get score() {
		return Math.max(0, this.colorSequence.length - 1);
	}

	private async showColorSequence() {
		if (this.isOver || this.timer.paused) {
			return;
		}

		this.state = 'showing-colors';

		const currentColorSequenceId = Symbol();
		this.colorSequenceId = currentColorSequenceId;

		for (let i = 0; i < this.colorSequence.length; i += 1) {
			if (this.isOver || this.timer.paused) {
				return;
			}

			const isLast = i === this.colorSequence.length - 1;

			await this.highlightColor(this.colorSequence[i]);

			// Cancel sequence
			if (currentColorSequenceId !== this.colorSequenceId) {
				return;
			}

			/**
			 * When playing the last color, immediately start listening,
			 * otherwise the user can experience a delay when selecting the color
			 **/
			if (!isLast) {
				await wait(this.colorDuration / 2);

				// Cancel sequence
				if (currentColorSequenceId !== this.colorSequenceId) {
					return;
				}
			}
		}

		this.state = 'listening';
		this.colorValidationIndex = 0;
	}

	private addColorToSequence() {
		if (this.isOver) {
			return;
		}
		this.colorSequence.push(getRandomItemAt(colors, this.randomFunc));
	}

	private playColorAudio(color: MemoryGameColor) {
		if (this.isOver || this.timer.paused) {
			return;
		}
		this.audios.blue.stop();
		this.audios.red.stop();
		this.audios.green.stop();
		this.audios.yellow.stop();

		switch (color) {
			case 'blue':
				return this.audios.blue.play();
			case 'red':
				return this.audios.red.play();
			case 'green':
				return this.audios.green.play();
			case 'yellow':
				return this.audios.yellow.play();
		}
	}

	private async highlightColor(color: MemoryGameColor, playAudio = true): Promise<void> {
		if (this.isOver || this.timer.paused) {
			return;
		}

		const currentHighlightColorId = Math.random() * 1e9;
		this.highlightColorId = currentHighlightColorId;
		this.color = color;

		if (playAudio) {
			this.playColorAudio(color);
		}

		await wait(this.colorDuration);

		if (currentHighlightColorId === this.highlightColorId) {
			this.color = null;
		}
	}

	async play(color: MemoryGameColor): Promise<boolean> {
		if (this.isOver || !this.timer.started || this.state !== 'listening' || this.timer.paused) {
			return false;
		}

		if (this.colorSequence[this.colorValidationIndex] !== color) {
			this.highlightColor(color, false);
			this.setIsOver(true);

			return true;
		}

		if (this.colorValidationIndex === this.colorSequence.length - 1) {
			this.addColorToSequence();
			this.state = 'showing-colors';
			await this.highlightColor(color, true);
			await wait(500);
			await this.showColorSequence();

			return true;
		}

		this.colorValidationIndex += 1;
		this.highlightColor(color, true);
		return true;
	}

	async start() {
		this.timer.start();
		this.colorSequence = [];
		this.state = 'showing-colors';

		if (!this.audios.start.playing()) {
			this.audios.start.play();
		}

		await wait((this.audios.start.duration() ?? 0) * 1000 + 800);

		this.addColorToSequence();
		this.showColorSequence();
	}

	dispose() {
		this.cleanupTimerListener();
		this.state = 'over';
	}
}
