<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full p-4', className)}
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 498 498"
	fill="transparent"
	{...props}
>
	<g clip-path="url(#snake-cover__a)">
		<rect
			width="444"
			height="444"
			x="27"
			y="27"
			stroke="currentColor"
			stroke-width="4"
			rx="6"
		/>
		<path
			stroke="currentColor"
			stroke-width="13"
			d="M298 137H155v56.5h207.5V242H129v55.5h197.5v64H370"
		/>
		<path fill="currentColor" d="M396 355h13v13h-13z" />
	</g>
	<defs>
		<clipPath id="snake-cover__a">
			<path fill="#fff" d="M0 0h498v498H0z" />
		</clipPath>
	</defs>
</svg>
