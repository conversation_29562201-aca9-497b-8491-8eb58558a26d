import { fixCanvasDpi } from '$lib/functions/fixCanvasDpi';
import type { Point2D } from '$lib/models/Point2D';
import { DirectionListener, type Direction } from '$lib/util/DirectionListener';
import { Timer } from '$lib/util/Timer.svelte';

export type SnakeSpeed = 'slow' | 'normal' | 'fast' | 'ultra';

const movePointInDirection = (point: Point2D, direction: Direction) => {
	switch (direction) {
		case 'up':
			return { x: point.x, y: point.y - 1 };
		case 'down':
			return { x: point.x, y: point.y + 1 };
		case 'left':
			return { x: point.x - 1, y: point.y };
		case 'right':
			return { x: point.x + 1, y: point.y };
		default:
			return point;
	}
};

const speedToUpdateInterval: Record<SnakeSpeed, number> = {
	slow: 400,
	normal: 200,
	fast: 80,
	ultra: 30,
};

const speedToSpecialFoodDuration: Record<SnakeSpeed, number> = {
	slow: 14000,
	normal: 7000,
	fast: 3000,
	ultra: 1000,
};

const speedToScoreBoost: Record<SnakeSpeed, number> = {
	slow: 0.5,
	normal: 1,
	fast: 2,
	ultra: 4,
};

interface Props {
	targetElement: HTMLElement;
	canvas: HTMLCanvasElement;
	speed: SnakeSpeed;
	gridRows: number;
	timer: Timer;
	onEnd?: () => void;
	onScoreChange?: (newScore: number) => void;
}

export class SnakeGame {
	canvas: HTMLCanvasElement;
	context: CanvasRenderingContext2D;
	lastTickTimeStamp = -1;
	animationFrame = -1;
	snake: Point2D[] = [];
	gridRows: number;
	direction: Direction = 'right';
	nextDirection: Direction = 'right';
	food: Point2D | null = null;
	specialFood: Point2D | null = null;
	specialFoodAddedTime = 0;
	specialFoodSpawnProbability = 0.15;
	speed: SnakeSpeed = $state('normal');
	gridMargin = 2;
	isLost = $state(false);
	isWon = $state(false);
	_score = $state(0);
	directionListener: DirectionListener;
	timer: Timer;
	onEnd?: () => void;
	onScoreChange?: (newScore: number) => void;

	constructor({
		targetElement,
		canvas,
		speed = 'normal',
		gridRows = 24,
		timer,
		onEnd,
		onScoreChange,
	}: Props) {
		this.timer = timer;
		this.onEnd = onEnd;
		this.onScoreChange = onScoreChange;
		this.canvas = canvas;
		this.context = canvas.getContext('2d')!;
		this.gridRows = gridRows;
		this.speed = speed;
		this.directionListener = new DirectionListener(targetElement, this.handleDirectionChange);
		this.placeSnakeOnInitialPosition();
		this.spawnFood();
		this.resizeCanvas();
	}

	get running() {
		return this.timer.running;
	}

	get itemSize() {
		const width = this.canvas.width;

		return Math.floor(width / this.gridRows);
	}

	get gridSize() {
		return this.itemSize * this.gridRows;
	}

	get gridStart(): Point2D {
		return {
			x: (this.canvas.width - this.gridSize) / 2,
			y: (this.canvas.height - this.gridSize) / 2,
		};
	}

	checkGameWin() {
		const isWon = this.snake.length === this.gridRows * this.gridRows;

		if (isWon) {
			this.isWon = isWon;
		}
	}

	get updateIntervalInMs() {
		return speedToUpdateInterval[this.speed];
	}

	get isOver() {
		return this.isLost || this.isWon;
	}

	get score() {
		return Math.floor(this._score);
	}

	set score(newScore: number) {
		if (newScore !== this._score) {
			this._score = newScore;
			this.onScoreChange?.(this.score);
		}
	}

	start() {
		this.addListeners();

		this.timer.start();

		window.requestAnimationFrame(this.loop);
	}

	placeSnakeOnInitialPosition() {
		const headIndex = Math.floor(this.gridRows / 2);

		this.snake = [
			{
				x: headIndex,
				y: headIndex,
			},
			{
				x: headIndex - 1,
				y: headIndex,
			},
			{
				x: headIndex - 2,
				y: headIndex,
			},
		];

		this.direction = 'right';
	}

	spawnFood() {
		this.food = this.getNextFoodPosition();
		this.spawnSpecialFood();
	}

	spawnSpecialFood() {
		if (!this.specialFood && Math.random() <= this.specialFoodSpawnProbability) {
			this.specialFood = this.getNextFoodPosition();
			this.specialFoodAddedTime = 0;
		}
	}

	getNextFoodPosition(): Point2D | null {
		if (this.isWon) {
			return null;
		}

		const x = Math.floor(Math.random() * this.gridRows);
		const y = Math.floor(Math.random() * this.gridRows);
		const hasSpawnedOnSnake = this.snake.some((point) => point.x === x && point.y === y);
		const hasSpawnedOnFood = this.food?.x === x && this.food?.y === y;
		const hasSpawnedOnSpecialFood = this.specialFood?.x === x && this.specialFood?.y === y;

		if (hasSpawnedOnSnake || hasSpawnedOnFood || hasSpawnedOnSpecialFood) {
			return this.getNextFoodPosition();
		}

		return { x, y };
	}

	isOutOfBounds(point: Point2D) {
		return point.x < 0 || point.y < 0 || point.x >= this.gridRows || point.y >= this.gridRows;
	}

	isSnakeEatingItself() {
		const [head, ...body] = this.snake;

		return body.some(({ x, y }) => x === head.x && y === head.y);
	}

	tick() {
		if (this.timer.paused) {
			return;
		}

		if (this.lastTickTimeStamp !== -1) {
			const dt = performance.now() - this.lastTickTimeStamp;

			if (this.specialFood) {
				this.specialFoodAddedTime += dt;
			}
		}

		const head = this.snake[0];
		const newHead = movePointInDirection(head, this.nextDirection);

		this.direction = this.nextDirection;
		this.snake.unshift(newHead);

		const tail = this.snake.pop();

		// Consume food
		if (tail && newHead.x === this.food?.x && newHead.y === this.food?.y) {
			this.snake.push(tail);
			this.spawnFood();
			this.score = this._score + speedToScoreBoost[this.speed];
		}

		// Consume special food
		if (tail && newHead.x === this.specialFood?.x && newHead.y === this.specialFood?.y) {
			this.snake.push(tail);
			this.score = this._score + 3 * speedToScoreBoost[this.speed];
			this.specialFood = null;
		}

		// Expire special food
		if (this.specialFoodAddedTime > speedToSpecialFoodDuration[this.speed]) {
			this.specialFood = null;
		}

		// Check collision
		if (this.isOutOfBounds(newHead) || this.isSnakeEatingItself()) {
			this.isLost = true;
			this.timer.stop();
		}

		this.checkGameWin();

		if (this.isOver) {
			this.onEnd?.();
		}
	}

	clearDraw() {
		const canvasSize = this.context.canvas.width;
		this.context.clearRect(0, 0, canvasSize, canvasSize);
	}

	draw() {
		fixCanvasDpi(this.canvas);
		this.clearDraw();
		this.drawSnake();
		this.drawFood();
		this.drawSpecialFood();
	}

	drawSnake() {
		const snakeColor = window.getComputedStyle(this.canvas).color;
		const itemSize = this.itemSize;
		const context = this.context;
		const gridMargin = this.gridMargin;
		const gridStart = this.gridStart;

		context.strokeStyle = snakeColor;
		context.lineWidth = this.itemSize - gridMargin;
		context.beginPath();

		this.snake.forEach(({ x, y }) => {
			context.lineTo(gridStart.x + (x + 0.5) * itemSize, gridStart.y + (y + 0.5) * itemSize);
		});

		context.stroke();
	}

	drawRect(x: number, y: number, color: string) {
		const context = this.context;
		const itemSize = this.itemSize;
		const gridMargin = this.gridMargin;
		const gridStart = this.gridStart;

		context.fillStyle = color;
		context.fillRect(
			gridStart.x + x * itemSize + gridMargin,
			gridStart.y + y * itemSize + gridMargin,
			itemSize - 2 * gridMargin,
			itemSize - 2 * gridMargin,
		);
	}

	drawFood() {
		if (!this.food) {
			return;
		}

		this.drawRect(this.food.x, this.food.y, window.getComputedStyle(this.canvas).color);
	}

	drawSpecialFood() {
		if (!this.specialFood) {
			return;
		}

		this.drawRect(this.specialFood.x, this.specialFood.y, '#EB5757');
	}

	loop = (timestamp: number) => {
		const timeSinceLastUpdate = timestamp - this.lastTickTimeStamp;

		if (this.running && timeSinceLastUpdate >= this.updateIntervalInMs) {
			this.tick();
			this.lastTickTimeStamp = timestamp;
		}

		this.draw();

		this.animationFrame = window.requestAnimationFrame(this.loop);
	};

	resizeCanvas = () => {
		const canvasSize = this.canvas.width;

		this.canvas.setAttribute('width', `${canvasSize}`);
		this.canvas.setAttribute('height', `${canvasSize}`);
	};

	handleDirectionChange = (direction: Direction) => {
		if (!this.running) {
			return;
		}

		const canChangeDirection =
			(direction === 'right' && this.direction !== 'left') ||
			(direction === 'left' && this.direction !== 'right') ||
			(direction === 'up' && this.direction !== 'down') ||
			(direction === 'down' && this.direction !== 'up');

		if (canChangeDirection) {
			this.nextDirection = direction;
		}
	};

	addListeners() {
		window.addEventListener('resize', this.resizeCanvas);
		this.directionListener.listen();
	}

	dispose() {
		this.timer.reset();
		window.cancelAnimationFrame(this.animationFrame);
		window.removeEventListener('resize', this.resizeCanvas);
		this.directionListener.dispose();
	}
}
