<svg class="size-full" viewBox="0 0 60 13" fill="none" xmlns="http://www.w3.org/2000/svg">
	<path class="letter-s" d="M11.5 2.5H2V6.5H10V10.5H0.5" stroke="currentColor" stroke-width="3" />
	<path class="letter-n" d="M14 12V2.5H22V12" stroke="currentColor" stroke-width="3" />
	<g class="letter-a">
		<path d="M26 12V6.5V2.5H34V7.25V12" stroke="currentColor" stroke-width="3" />
		<path d="M26 7.5H34" stroke="currentColor" stroke-width="3" />
	</g>
	<g class="letter-k">
		<path d="M39 1C39 5.29577 39 7.70423 39 12" stroke="currentColor" stroke-width="3" />
		<path d="M46 2L40 6.5L46 11" stroke="currentColor" stroke-width="3" />
	</g>
	<g class="letter-e">
		<path d="M59.5 2.5H50V10.5H59.5" stroke="currentColor" stroke-width="3" />
		<path d="M50 6.5H59.5" stroke="currentColor" stroke-width="3" />
	</g>
</svg>

<style>
	@keyframes dash {
		to {
			stroke-dashoffset: 0;
		}
	}

	path {
		--duration: 400ms;

		stroke-dasharray: 30;
		stroke-dashoffset: 30;
		animation: dash var(--duration) ease-in forwards;
	}
	.letter-s {
		stroke-dasharray: 40;
		stroke-dashoffset: 40;
		animation-delay: 0ms;
	}
	.letter-n {
		animation-delay: var(--duration);
	}
	.letter-a path:nth-child(1) {
		animation-delay: calc(1.5 * var(--duration));
	}
	.letter-a path:nth-child(2) {
		stroke-dasharray: 10;
		stroke-dashoffset: 10;
		animation-delay: calc(2 * var(--duration));
	}
	.letter-k path:nth-child(1) {
		animation-delay: calc(2.5 * var(--duration));
	}
	.letter-k path:nth-child(2) {
		stroke-dasharray: 20;
		stroke-dashoffset: 20;
		animation-delay: calc(3 * var(--duration));
	}
	.letter-e path:nth-child(1) {
		animation-delay: calc(3.5 * var(--duration));
	}
	.letter-e path:nth-child(2) {
		stroke-dasharray: 10;
		stroke-dashoffset: 10;
		animation-delay: calc(4 * var(--duration));
	}
</style>
