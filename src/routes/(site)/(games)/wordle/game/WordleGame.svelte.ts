import { wait } from '$lib/functions/wait';
import { countCharacters } from '$lib/util/countCharacters';
import { normalize } from '$lib/util/normalize';
import { Timer } from '$lib/util/Timer.svelte';

export type WordleDifficulty = 'normal' | 'hard';

export type WordleAttemptError = 'word-not-found' | 'too-short';

export type WordSize = 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11;

export const wordleLanguages = ['en', 'pt'] as const;

export type WordleLanguage = (typeof wordleLanguages)[number];

const wordsCache: Partial<Record<WordleLanguage, string[]>> = {};
const dictionaryCache: Partial<Record<WordleLanguage, string[]>> = {};

type WordleGameOptions = {
	language: WordleLanguage;
	size: WordSize;
	// challenge?: string | null;
	timer: Timer;
	onRenderUi: () => void;
	onGameWon: () => void;
	onGameLost: () => void;
};

const onlyLettersRegex = /[a-zA-Z]/;

export const maxWordleAttempts = 6;

export type WordleState = 'in-progress' | 'won' | 'lost';

export type WordleLetterValidation = 'correct' | 'misplaced' | 'not-present';

export class WordleGame {
	private onRenderUi: () => void;
	private onGameWon: () => void;
	private onGameLost: () => void;
	private _difficulty: WordleDifficulty = $state('normal');
	private _errorOnCurrentAttempt: WordleAttemptError | null = $state(null);
	private words: string[] = [];
	private dictionary: string[] = [];
	private normalizedWords: string[] = [];
	private normalizedDictionary: string[] = [];
	private _state: WordleState = $state('in-progress');
	private _word = $state('');
	private normalizedWord: string = $derived.by(() => normalize(this._word));
	private _isReady = $state(false);
	language: WordleLanguage = $state('en');
	id = Symbol();
	currentAttemptIndex = $state(0);
	attempts: string[] = $state([]);
	validations: WordleLetterValidation[][] = $state([]);
	keyboardValidations: Record<string, WordleLetterValidation> = $state({});
	size: WordSize;
	// challenge: string = $state('');
	attemptsLeft = $derived(
		this.state === 'lost'
			? 0
			: this.state === 'won'
				? maxWordleAttempts - this.currentAttemptIndex - 1
				: maxWordleAttempts - this.currentAttemptIndex,
	);
	timer: Timer;

	constructor({
		language,
		size,
		// challenge,
		timer,
		onRenderUi,
		onGameLost,
		onGameWon,
	}: WordleGameOptions) {
		this.timer = timer;
		this.size = size;
		this.language = language;
		this.attempts = Array(maxWordleAttempts).fill(Array(size).fill(' ').join(''));
		this.onGameWon = onGameWon;
		this.onGameLost = onGameLost;
		this.onRenderUi = onRenderUi;

		// if (challenge) {
		// 	this.challenge = challenge;
		// }
		this.start();
	}

	private async start() {
		this._isReady = false;
		this.words = await this.fetchWords();
		this.normalizedWords = this.words.map(normalize);
		this.dictionary = await this.fetchDictionary();
		this.normalizedDictionary = this.dictionary.map(normalize);

		// if (!this.pickWordFromChallenge()) {
		this.pickRandomWord();
		// }

		this._isReady = true;
	}

	// private pickWordFromChallenge(): boolean {
	// 	if (this.challenge) {
	// 		const index = DeterministicIdGenerator.getNumberFromRandomString(this.challenge);

	// 		if (index !== null) {
	// 			this.word = this.words[index];

	// 			if (this.word) {
	// 				return true;
	// 			}
	// 		}
	// 	}
	// 	return false;
	// }

	private pickRandomWord() {
		const randomIndex = Math.floor(Math.random() * this.words.length);
		this.word = this.words[randomIndex];
		// this.challenge = DeterministicIdGenerator.generateRandomStringFromNumber(randomIndex);
	}

	get isReady() {
		return this._isReady;
	}

	get word() {
		return this._word;
	}

	set word(newWord: string) {
		if (!newWord) {
			return;
		}

		this._word = newWord;
	}

	/** Smaller is better */
	get score() {
		return this.currentAttemptIndex * 10 + Math.floor(this.timer.time / 100);
	}

	get isOver() {
		return this.state !== 'in-progress';
	}

	get state() {
		return this._state;
	}

	giveUp() {
		this.state = 'lost';
	}

	private set state(newState: WordleState) {
		this._state = newState;
		this.onRenderUi();

		if (newState === 'won') {
			this.onGameWon();
			this.timer.stop();
		} else if (newState === 'lost') {
			this.onGameLost();
			this.timer.stop();
		}
	}

	private get currentWord() {
		return this.currentAttempt.trim();
	}

	private validateLetters() {
		const availableLettersCount: Record<string, number> = countCharacters(this.normalizedWord);
		const currentWordLetters = [...this.currentWord];

		this.validations[this.currentAttemptIndex] = currentWordLetters.map(() => 'not-present');

		// Correct letters
		currentWordLetters.forEach((letter, index) => {
			if (letter === this.normalizedWord[index]) {
				availableLettersCount[letter] -= 1;
				this.validations[this.currentAttemptIndex][index] = 'correct';
			}
		});

		// Misplaced letters
		currentWordLetters.forEach((letter, index) => {
			if (
				letter !== this.normalizedWord[index] &&
				this.normalizedWord.includes(letter) &&
				availableLettersCount[letter] > 0
			) {
				availableLettersCount[letter] -= 1;
				this.validations[this.currentAttemptIndex][index] = 'misplaced';
			}
		});
	}

	private updateKeyboardValidation() {
		this.validations[this.currentAttemptIndex].forEach((validation, index) => {
			const letter = this.currentWord[index];
			const currentValidation = this.keyboardValidations[letter];

			if (currentValidation === 'correct') {
				return;
			}

			this.keyboardValidations[letter] = validation;
		});
	}

	private replaceNormalizedLetterWithActualLetter() {
		const correspondingWordDictionaryIndex = this.normalizedDictionary.findIndex(
			(word) => word === this.currentWord,
		);
		const correspondingWordIndex = this.normalizedWords.findIndex(
			(word) => word === this.currentWord,
		);
		const correspondingWord =
			correspondingWordIndex !== -1
				? this.words[correspondingWordIndex]
				: this.dictionary[correspondingWordDictionaryIndex];

		this.attempts[this.currentAttemptIndex] = [...this.currentAttempt]
			.map((_, letterIndex) => {
				const wordLetter = correspondingWord[letterIndex];

				return wordLetter;
			})
			.join('');
	}

	private async validate() {
		if (this.currentWord.length < this.size) {
			this.errorOnCurrentAttempt = 'too-short';
		} else if (
			this.normalizedWords.includes(this.currentWord) ||
			this.normalizedDictionary.includes(this.currentWord)
		) {
			this.validateLetters();
			this.updateKeyboardValidation();
			this.replaceNormalizedLetterWithActualLetter();

			if (this.currentWord === this.word) {
				this.state = 'won';
			} else {
				if (this.currentAttemptIndex + 1 === maxWordleAttempts) {
					this.state = 'lost';
				} else {
					this.currentAttemptIndex += 1;
				}
			}
		} else {
			this.errorOnCurrentAttempt = 'word-not-found';
		}

		this.onRenderUi();

		if (this.errorOnCurrentAttempt) {
			const errorBefore = this.errorOnCurrentAttempt;
			await wait(2000);
			const errorAfter = this._errorOnCurrentAttempt;

			if (errorBefore === errorAfter) {
				this.errorOnCurrentAttempt = null;
				this.onRenderUi();
			}
		}
	}

	handleKey(key: string) {
		if (this.isOver || (this.timer.paused && this.timer.started)) {
			return;
		}

		this.timer.start();

		key = key.toLocaleLowerCase();

		if (key.length === 1 && onlyLettersRegex.test(key)) {
			this.currentAttempt = `${this.currentAttempt.trim()}${key}`;
		}

		if (key === 'enter') {
			this.validate();
		}

		if (key === 'backspace') {
			const wordOnly = this.currentAttempt.trim();
			this.currentAttempt = wordOnly.slice(0, wordOnly.length - 1);
		}
	}

	get errorOnCurrentAttempt() {
		return this._errorOnCurrentAttempt;
	}

	set errorOnCurrentAttempt(error: WordleAttemptError | null) {
		this._errorOnCurrentAttempt = error;
		this.onRenderUi();
	}

	private get currentAttempt() {
		return this.attempts[this.currentAttemptIndex];
	}

	private set currentAttempt(attempt: string) {
		this.attempts[this.currentAttemptIndex] = attempt
			.trim()
			.padEnd(this.size)
			.slice(0, this.size)
			.toLocaleLowerCase();

		this.onRenderUi();
	}

	get difficulty(): WordleDifficulty {
		return this._difficulty;
	}

	set difficulty(newDifficulty: WordleDifficulty) {
		this._difficulty = newDifficulty;
		this.onRenderUi();
	}

	dispose() {
		//
	}

	private async fetchWords() {
		if (!wordsCache[this.language]) {
			let allWords: string;

			switch (this.language) {
				case 'en': {
					allWords = await fetch(
						'https://static.lofiandgames.com/generated-games/wordle/5/words-en.txt',
					).then((res) => res.text());
					break;
				}
				case 'pt': {
					allWords = await fetch(
						'https://static.lofiandgames.com/generated-games/wordle/5/words-pt.txt',
					).then((res) => res.text());

					break;
				}
			}

			const words = allWords
				.split('\n')
				.map((line) => line.replace('\r', ''))
				.map((word) => word.toLocaleLowerCase());
			wordsCache[this.language] = words;
		}

		return wordsCache[this.language] ?? [];
	}

	private async fetchDictionary() {
		if (!dictionaryCache[this.language]) {
			let allWords: string;

			switch (this.language) {
				case 'en': {
					allWords = await fetch(
						'https://static.lofiandgames.com/generated-games/wordle/5/en.txt',
					).then((res) => res.text());
					break;
				}
				case 'pt': {
					allWords = await fetch(
						'https://static.lofiandgames.com/generated-games/wordle/5/pt.txt',
					).then((res) => res.text());
					break;
				}
			}

			const words = allWords
				.split('\n')
				.map((line) => line.replace('\r', ''))
				.map((word) => word.toLocaleLowerCase());
			dictionaryCache[this.language] = words;
		}

		return dictionaryCache[this.language] ?? [];
	}
}
