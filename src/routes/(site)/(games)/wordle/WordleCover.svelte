<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full p-4', className)}
	fill="transparent"
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 498 498"
	{...props}
>
	<path
		class="fill-game-wordle-not-present"
		d="M31 31h143v143H31zm147 0h143v143H178zm147 0h143v143H325z"
	/>
	<path class="fill-game-wordle-correct" d="M31 178h143v143H31zm0 147h143v143H31z" />
	<path class="fill-game-wordle-not-present" d="M178 178h143v143H178z" />
	<path class="fill-game-wordle-correct" d="M178 325h143v143H178zm148 0h143v143H326z" />
	<path class="fill-game-wordle-misplaced" d="M325 178h143v143H325z" />
</svg>
