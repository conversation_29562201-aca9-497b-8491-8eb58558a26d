<script lang="ts">
	import { fly } from 'svelte/transition';
	import { onMount, onDestroy } from 'svelte';
	import {
		WordleGame,
		type WordSize,
		type WordleAttemptError,
		type WordleLetterValidation,
		type WordleLanguage,
		wordleLanguages,
	} from './game/WordleGame.svelte';
	import { wordleSoundResources } from './wordleSoundResources';
	import Keyboard from '$lib/components/Keyboard.svelte';
	import { wait } from '$lib/functions/wait';
	import WordleHowToPlayModal from './WordleHowToPlayModal.svelte';
	import WordleInfoModal from './WordleInfoModal.svelte';
	import { languageToFlag } from '$lib/util/languages';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import InfoButton from '$lib/components/InfoButton.svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	const attemptErrorMessages: Record<WordleAttemptError, string> = {
		'too-short': 'The word is too short',
		'word-not-found': 'Word not found',
	};

	const validationToClasses: Record<WordleLetterValidation, string> = {
		'not-present':
			'border-transparent bg-game-wordle-not-present text-game-wordle-validated-text hover:bg-game-wordle-not-present-hover',
		correct:
			'border-transparent bg-game-wordle-correct text-game-wordle-validated-text hover:bg-game-wordle-correct-hover',
		misplaced:
			'border-transparent bg-game-wordle-misplaced text-game-wordle-validated-text hover:bg-game-wordle-misplaced-hover',
	};
	const size = 5 as WordSize;
	let keysClasses: Record<string, string> = $state({});
	let isLanguageDropdownOpen = $state(false);
	let isInfoModalOpen = $state(false);
	let isHowToPlayModalOpen = $state(false);

	const context = new GameContext({
		gameName: 'Wordle',
		GameClass: WordleGame,
		gameKey: 'wordle',
		settings: {
			defaultSettings: {
				language: 'en' as WordleLanguage,
			},
		},
		sounds: {
			resources: wordleSoundResources,
			lifecycle: {
				createGame: wordleSoundResources.start,
				win: wordleSoundResources.gameWin,
			},
		},
		variants: {
			map: {
				language: {
					allValues: wordleLanguages,
					format: (language: string) => language?.toLocaleUpperCase(),
				},
			},
			fromGame(game) {
				return {
					language: game?.language ?? 'en',
				};
			},
			getStatsVariant(variants) {
				return `5-${variants.language}`;
			},
			getLeaderboardVariant(variants) {
				return (variants.language ?? 'en').toLocaleUpperCase();
			},
		},
		defaultGameProps(context) {
			return {
				language: context.settingsManager.settings.language,
				size,
				// challenge: page.url.searchParams.get('challenge'),
				timer: context.timer,
				onRenderUi,
				onGameWon,
				onGameLost,
			};
		},
		stats({ context, props }) {
			return {
				stats: new Stats({
					...props,
					liveStats: {
						wordsLeft: {
							name: 'Words left',
							unit: 'plain',
							value() {
								return context.game?.attemptsLeft ?? 6;
							},
						},
					},
					initialPinnedStats: ['time', 'wordsLeft'],
				}),
				canUpdateWithGameLost(game) {
					return game.currentAttemptIndex > 0 && game.state === 'in-progress';
				},
				visibleStats: ['bestTime', 'averageTime', 'wonGames', 'totalGames'],
			};
		},
		leaderboard({ props }) {
			return {
				leaderboard: new Leaderboard({
					...props,
					order: 'lower-first',
					firstAvailableDate: new Date('2025/05/08'),
				}),
				sendScoreOn: ['won'],
				getScore(game) {
					return {
						score: game.score,
					};
				},
			};
		},
		onWillCreateGame({ previousGame, context, newGameOptions }) {
			previousGame?.dispose();
			context.settingsManager.settings.language = newGameOptions.language;
			isLanguageDropdownOpen = false;
			keysClasses = {};
			(document.activeElement as HTMLElement)?.blur();
		},
		isGameReady(game) {
			return game.isReady;
		},
		onDispose(context) {
			context.game?.dispose();
		},
	});

	let game = $derived(context.game);

	async function onRenderUi() {
		if (!game) {
			return;
		}

		let keysWillChange = false;
		const totalWaitTime = 500 + game.size * 100;

		Object.entries(game.keyboardValidations).forEach(([letter, validation]) => {
			if (keysClasses[letter] !== validationToClasses[validation]) {
				keysWillChange = true;
			}
		});

		if (keysWillChange) {
			await wait(totalWaitTime);

			Object.entries(game.keyboardValidations).forEach(([letter, validation]) => {
				if (keysClasses[letter] !== validationToClasses[validation]) {
					keysClasses[letter] = validationToClasses[validation];
				}
			});
		}
	}

	async function onGameWon() {
		context.handleGameOver('won');
	}

	async function onGameLost() {
		context.handleGameOver('lost');
	}

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<svelte:window {onresize} />

{#snippet GameFinishedIsland(onClose: () => void)}
	<div class="w-full p-8">
		<div class="flex items-center justify-center w-full">
			<div class="stats bg-transparent">
				<div class="stat py-0 px-4 text-center">
					<div class="stat-title text-lg text-gray-300 opacity-60">The word was</div>
					{#if game?.state === 'lost'}
						<div class="stat-value capitalize py-[9px]">{game.word}</div>
					{/if}
				</div>
			</div>
		</div>

		<button
			class="btn btn-primary rounded-full mt-5 w-full"
			onclick={(event) => {
				event.stopPropagation();
				context.createGame();
				onClose();
			}}
		>
			Play a new game
		</button>
	</div>
{/snippet}

<GameLayout
	noPadding
	adsProps={{
		minHeightToShowHorizontalAd: 822,
	}}
>
	{#snippet Island()}
		<GameIsland
			{context}
			gameOverIslandDelay={context.isLost ? 1000 : undefined}
			GameOverIsland={context.isLost ? GameFinishedIsland : undefined}
			pauseKey={null}
		/>
	{/snippet}

	<div
		class="flex size-full flex-col items-center justify-between gap-5 max-w-xl select-none py-4 md:pb-4 pb-10 px-2 md:px-0"
	>
		<div class="flex w-full items-center justify-between gap-2 relative">
			<div class="flex w-full items-center justify-between">
				<div class="flex items-center gap-2">
					<button class="btn btn-sm" onclick={() => (isHowToPlayModalOpen = true)}>
						How to Play
					</button>

					<Dropdown bind:open={isLanguageDropdownOpen}>
						<DropdownButton class="btn-sm whitespace-nowrap uppercase">
							{languageToFlag[context.settingsManager.settings.language]}&nbsp;
							{context.settingsManager.settings.language}
						</DropdownButton>

						<DropdownContent menu>
							{#each wordleLanguages as wordleLanguage}
								<DropdownItem>
									<button
										class="uppercase"
										class:menu-active={context.settingsManager.settings
											.language === wordleLanguage}
										onclick={() => {
											context.createGame({ language: wordleLanguage });
										}}
									>
										{languageToFlag[wordleLanguage]}&nbsp;
										{wordleLanguage}
									</button>
								</DropdownItem>
							{/each}
						</DropdownContent>
					</Dropdown>

					<button
						disabled={!game || game?.currentAttemptIndex === 0 || game?.isOver}
						class="btn btn-sm"
						onclick={() => game?.giveUp()}
					>
						Give up
					</button>

					<InfoButton onclick={() => (isInfoModalOpen = true)} />

					<MoreGamesButton class="btn-sm" />
				</div>
			</div>
		</div>

		<div class="flex w-full max-w-xs flex-col gap-1 grow justify-center">
			{#each game?.attempts ?? [] as attempt, attemptIndex}
				<div
					class:error={attemptIndex === game?.currentAttemptIndex &&
						game?.errorOnCurrentAttempt}
					class="flex justify-center gap-1"
				>
					{#each attempt as letter, letterIndex}
						{@const hasLetter = letter !== ' '}
						{@const validation = game?.validations?.[attemptIndex]?.[letterIndex]}
						{@const hasValidation = !!validation}
						<div
							class="flex-center w-12 border-2 text-center text-lg font-semibold uppercase transition-colors aspect-square xs:w-full md:text-xl"
							class:bounce={hasLetter}
							class:flip={hasValidation}
							class:border-transparent={hasValidation}
							class:border-current={hasLetter && !hasValidation}
							class:border-game-wordle-border={!hasLetter && !hasValidation}
							class:text-game-wordle-validated-text={hasValidation}
							class:bg-game-wordle-correct={validation === 'correct'}
							class:bg-game-wordle-misplaced={validation === 'misplaced'}
							class:bg-game-wordle-not-present={validation === 'not-present'}
							style="--index:{letterIndex}"
						>
							{letter}
						</div>
					{/each}
				</div>
			{/each}
		</div>

		<Keyboard
			{keysClasses}
			on:keypress={({ detail }) => {
				game?.handleKey(detail);
			}}
			class="w-full"
		/>
	</div>

	<WordleHowToPlayModal bind:isOpen={isHowToPlayModalOpen} />

	{#if game?.errorOnCurrentAttempt}
		<div
			transition:fly={{ y: -50, duration: 500 }}
			role="alert"
			class="alert alert-warning absolute left-1/2 top-2 mx-auto flex w-72 -translate-x-1/2 flex-nowrap items-center shadow-lg md:top-20"
		>
			<svg
				xmlns="http://www.w3.org/2000/svg"
				class="size-6 shrink-0 stroke-current"
				fill="none"
				viewBox="0 0 24 24"
			>
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
				/>
			</svg>
			<span>{attemptErrorMessages[game.errorOnCurrentAttempt]}</span>
		</div>
	{/if}
</GameLayout>

<WordleInfoModal bind:isOpen={isInfoModalOpen} />

<style>
	@keyframes bounceAnimation {
		from {
			scale: 1;
		}

		25% {
			scale: 0.8;
		}

		75% {
			scale: 1.1;
		}

		to {
			scale: 1;
		}
	}

	.bounce {
		animation: bounceAnimation 150ms ease-in-out;
	}

	@keyframes errorAnimation {
		from {
			transform: translateX(0);
		}

		25% {
			transform: translateX(-5px);
		}

		75% {
			transform: translateX(5px);
		}

		to {
			transform: translateX(0);
		}
	}

	.error {
		animation: errorAnimation 150ms ease-in-out;
	}

	@keyframes flipAnimation {
		from {
			transform: rotateX(0);
		}

		50% {
			transform: rotateX(90deg);
		}

		to {
			transform: rotateX(0);
		}
	}

	.flip {
		--duration: 500ms;
		--base-delay: 100ms;
		animation: flipAnimation var(--duration) ease-in-out;
		animation-delay: calc(var(--base-delay) * var(--index));
		transition-delay: calc(var(--duration) / 2 + var(--base-delay) * var(--index));
	}
</style>
