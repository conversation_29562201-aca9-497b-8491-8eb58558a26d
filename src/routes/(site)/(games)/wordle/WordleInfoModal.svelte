<script lang="ts">
	import { wordleSoundResources as sounds } from './wordleSoundResources';
	import InfoModal from '$lib/components/InfoModal.svelte';

	interface Props {
		isOpen?: boolean;
	}

	let { isOpen = $bindable(false) }: Props = $props();
</script>

<InfoModal bind:isOpen {sounds}>
	<h1>Wordle</h1>

	<p>
		Wordle is a simple game: You need to guess a 5-letter hidden word in 6 tries. To get
		started, enter a word on the first line. The letters will change their colors to match the
		target word:
	</p>

	<ul>
		<li>
			If the letter is in the correct position, it will be highlighted in <strong
				class="text-green-500">green</strong
			>
		</li>
		<li>
			If it's present in the word, but it's in the wrong spot, it will be highlighted in <strong
				class="text-yellow-400">yellow</strong
			>
		</li>
		<li>
			If the letter is not in the word, it will be highlighted in <strong
				class="text-gray-400">gray</strong
			>
		</li>
	</ul>

	<p>
		Wordle is a simple yet addictive game that has captivated millions of people around the
		world. Whether you're a word enthusiast or just looking for a fun and challenging game,
		<PERSON><PERSON> is worth giving a try.
	</p>

	<p>Can you guess the word in 6 tries?</p>
</InfoModal>
