<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';

	interface Props {
		isOpen: boolean;
	}

	let { isOpen = $bindable(false) }: Props = $props();
</script>

<Dialog bind:isOpen>
	<article>
		<h2>How to Play Wordle</h2>

		<p>
			You have to guess the word in 6 tries. Each word must be a valid word. The color of the
			letter tiles will represent how close you are to the hidden word.
		</p>

		<p>To start the game, enter a word, for example:</p>

		<div class="flex-center my-8 w-full gap-1">
			<div
				class="flex-center h-12 w-12 bg-game-wordle-not-present text-lg text-game-wordle-validated-text"
			>
				F
			</div>
			<div
				class="flex-center h-12 w-12 bg-game-wordle-misplaced text-lg text-game-wordle-validated-text"
			>
				L
			</div>
			<div
				class="flex-center h-12 w-12 bg-game-wordle-misplaced text-lg text-game-wordle-validated-text"
			>
				A
			</div>
			<div
				class="flex-center h-12 w-12 bg-game-wordle-not-present text-lg text-game-wordle-validated-text"
			>
				M
			</div>
			<div
				class="flex-center h-12 w-12 bg-game-wordle-correct text-lg text-game-wordle-validated-text"
			>
				E
			</div>
		</div>

		<div class="card w-full bg-base-200 dark:bg-base-300">
			<div class="card-body items-center p-4">
				<ul class="m-0 w-full text-left">
					<li>
						<span
							class="inline-flex size-8 items-center justify-center bg-game-wordle-not-present text-sm text-game-wordle-validated-text"
						>
							F
						</span>
						and
						<span
							class="inline-flex size-8 items-center justify-center bg-game-wordle-not-present text-sm text-game-wordle-validated-text"
						>
							L
						</span> aren't in the hidden word at all
					</li>
					<li>
						<span
							class="inline-flex size-8 items-center justify-center bg-game-wordle-misplaced text-sm text-game-wordle-validated-text"
						>
							L
						</span>
						and
						<span
							class="inline-flex size-8 items-center justify-center bg-game-wordle-misplaced text-sm text-game-wordle-validated-text"
						>
							A
						</span> are in the word but in the wrong spot
					</li>
					<li>
						<span
							class="inline-flex size-8 items-center justify-center bg-game-wordle-correct text-sm text-game-wordle-validated-text"
						>
							E
						</span> is in the word and correct spot
					</li>
				</ul>
			</div>
		</div>

		<p>Try again with another word:</p>

		<div class="flex-center my-8 w-full gap-1">
			<div
				class="flex-center h-12 w-12 bg-game-wordle-correct text-lg text-game-wordle-validated-text"
			>
				L
			</div>
			<div
				class="flex-center h-12 w-12 bg-game-wordle-correct text-lg text-game-wordle-validated-text"
			>
				A
			</div>
			<div
				class="flex-center h-12 w-12 bg-game-wordle-correct text-lg text-game-wordle-validated-text"
			>
				T
			</div>
			<div
				class="flex-center h-12 w-12 bg-game-wordle-correct text-lg text-game-wordle-validated-text"
			>
				T
			</div>
			<div
				class="flex-center h-12 w-12 bg-game-wordle-correct text-lg text-game-wordle-validated-text"
			>
				E
			</div>
		</div>

		<p>Perfect!</p>
	</article>
</Dialog>
