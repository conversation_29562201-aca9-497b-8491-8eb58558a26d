<script lang="ts">
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import type { MinesweeperDifficulty } from './MinesweeperGame.svelte';

	const difficultyLabels: Record<MinesweeperDifficulty, string> = {
		easy: 'Easy',
		normal: 'Normal',
		hard: 'Hard',
	};

	interface Props {
		difficulty: MinesweeperDifficulty;
		onChange: (difficulty: MinesweeperDifficulty) => void;
		open?: boolean;
	}

	let { difficulty, onChange, open = $bindable(false) }: Props = $props();

	let label = $derived(difficultyLabels[difficulty]);
</script>

<Dropdown bind:open>
	<DropdownButton class="btn-sm">
		{label}
	</DropdownButton>

	<DropdownContent menu>
		<DropdownItem>
			<button class:menu-active={difficulty === 'easy'} onclick={() => onChange('easy')}
				>{difficultyLabels.easy}</button
			>
		</DropdownItem>
		<DropdownItem>
			<button class:menu-active={difficulty === 'normal'} onclick={() => onChange('normal')}
				>{difficultyLabels.normal}</button
			>
		</DropdownItem>
		<DropdownItem>
			<button class:menu-active={difficulty === 'hard'} onclick={() => onChange('hard')}
				>{difficultyLabels.hard}</button
			>
		</DropdownItem>
	</DropdownContent>
</Dropdown>
