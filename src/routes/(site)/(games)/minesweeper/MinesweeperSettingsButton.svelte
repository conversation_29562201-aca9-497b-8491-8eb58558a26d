<script lang="ts">
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';

	interface Props {
		animated: boolean;
	}

	let { animated = $bindable() }: Props = $props();
</script>

<Dropdown>
	<DropdownButton class="btn-sm" aria-label="Show settings">
		<SettingsIcon class="size-5" />
	</DropdownButton>

	<DropdownContent>
		<DropdownItem>
			<Toggle aria-label="toggle animations" bind:checked={animated}>Animations</Toggle>
		</DropdownItem>
	</DropdownContent>
</Dropdown>
