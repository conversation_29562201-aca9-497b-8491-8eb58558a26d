import type { Point2D } from '$lib/models/Point2D';
import type { Size } from '$lib/models/Size';

type MinesweeperBackgroundOptions = {
	startPoint?: Point2D;
	size?: Size;
	context: CanvasRenderingContext2D;
	color: string;
};

export class MinesweeperBackground {
	context: CanvasRenderingContext2D;
	startPoint: Point2D;
	size: Size;
	elementWithGridItemColor!: HTMLDivElement;
	color = '#000';

	constructor({
		context,
		startPoint = { x: 0, y: 0 },
		size = { width: 0, height: 0 },
		color,
	}: MinesweeperBackgroundOptions) {
		this.context = context;
		this.startPoint = startPoint;
		this.size = size;
		this.color = color;
	}

	draw() {
		this.context.fillStyle = this.color;
		this.context.fillRect(
			this.startPoint.x,
			this.startPoint.y,
			this.size.width,
			this.size.height,
		);
	}
}
