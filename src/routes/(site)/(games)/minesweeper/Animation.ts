export class Animation {
	playedAt: number | null = null;
	delay = 0;
	enabled = true;
	progressWhenDisabled = 0;
	duration = 0;

	constructor(duration: number, delay = 0) {
		this.duration = duration;
		this.delay = delay;
	}

	get effectiveDuration() {
		return this.enabled ? this.duration : 0;
	}

	get progress() {
		if (!this.enabled) {
			return this.progressWhenDisabled;
		}

		if (this.playedAt === null) {
			return 0;
		}

		const progress = (performance.now() - this.playedAt - this.delay) / this.duration;

		if (progress < 0) {
			return 0;
		}

		if (progress > 1) {
			return 1;
		}

		return progress;
	}

	get started() {
		return this.playedAt !== null;
	}

	get finished() {
		return this.progress === 1;
	}

	get running() {
		return this.started && !this.finished;
	}

	play() {
		this.playedAt = performance.now();
	}

	reset() {
		this.playedAt = null;
	}
}
