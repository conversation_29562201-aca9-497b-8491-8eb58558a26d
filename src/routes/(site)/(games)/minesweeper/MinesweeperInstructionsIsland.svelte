<script lang="ts">
	import MouseLeftIcon from '$lib/components/Icons/MouseLeftIcon.svelte';
	import MouseRightIcon from '$lib/components/Icons/MouseRightIcon.svelte';
	import TouchIcon from '$lib/components/Icons/TouchIcon.svelte';
	import InstructionsIslandBase from '$lib/components/GameIsland/islands/InstructionsIslandBase.svelte';
</script>

{#snippet mouseSquare()}
	<div class="flex items-start justify-center">
		<span>■</span>
		<MouseLeftIcon class="size-16" />
	</div>
{/snippet}

{#snippet touchSquare()}
	<div class="flex items-start justify-center">
		<span>■</span>
		<TouchIcon class="size-16" />
	</div>
{/snippet}

{#snippet mouseFlag()}
	<div class="flex items-start justify-center">
		<MouseRightIcon class="size-16" />
		<span>🚩</span>
	</div>
{/snippet}

{#snippet touchFlag()}
	<div class="flex items-start justify-center">
		<TouchIcon class="size-16" variant="hold" />
		<span>🚩</span>
	</div>
{/snippet}

<InstructionsIslandBase
	goal="Reveal all safe squares!"
	desktopControls={[
		{
			name: 'Left-click to reveal a square',
			bindings: [mouseSquare],
		},
		{
			name: 'Right-click to place a flag',
			bindings: [mouseFlag],
		},
	]}
	mobileControls={[
		{
			name: 'Tap to reveal a square',
			bindings: [touchSquare],
		},
		{
			name: 'Press and hold to place a flag',
			bindings: [touchFlag],
		},
	]}
/>
