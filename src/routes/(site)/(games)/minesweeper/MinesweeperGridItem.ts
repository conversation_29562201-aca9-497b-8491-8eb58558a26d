import { drawGridRect } from '$lib/functions/drawGridRect';
import type { GridItem } from '$lib/models/GridItem';
import type { Point2D } from '$lib/models/Point2D';

export type MinesweeperGridItemOptions = {
	context: CanvasRenderingContext2D;
	gridStart: Point2D;
	size: number;
	margin: number;
	row: number;
	column: number;
	color?: string;
	opacity?: number;
};

export class MinesweeperGridItem {
	context: CanvasRenderingContext2D;
	size: number;
	gridStart: Point2D;
	row: number;
	column: number;
	margin: number;
	color: string | undefined;
	opacity: number;
	translate: Point2D | undefined;
	scale: Point2D | undefined;
	rotateAngle: number | undefined;

	constructor({
		color,
		context,
		size,
		gridStart,
		row,
		column,
		margin,
		opacity = 1,
	}: MinesweeperGridItemOptions) {
		this.context = context;
		this.row = row;
		this.column = column;
		this.margin = margin;
		this.color = color;
		this.gridStart = gridStart;
		this.size = size;
		this.opacity = opacity;
	}

	get position(): GridItem {
		return { column: this.column, row: this.row };
	}

	draw() {
		drawGridRect({
			context: this.context,
			gridItemSize: this.size,
			gridItemMargin: this.margin ?? 1,
			row: this.row,
			column: this.column,
			color: this.color ?? '#000',
			gridStart: this.gridStart,
			scale: this.scale,
			translate: this.translate,
			rotateAngle: this.rotateAngle,
			opacity: this.opacity,
		});
	}
}
