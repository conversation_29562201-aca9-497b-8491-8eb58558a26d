import { MinesweeperButton } from './MinesweeperButton';

export class MinesweeperFlag extends MinesweeperButton {
	draw() {
		const context = this.context;
		const itemSize = this.size;
		const fontAdjustment = itemSize / 2;

		this.runHideAnimation();
		this.runExplosionAnimation();

		context.save();

		context.translate(
			this.gridStart.x + this.column * itemSize + fontAdjustment,
			this.gridStart.y + this.row * itemSize + fontAdjustment,
		);

		if (this.translate) {
			context.translate(this.translate.x, this.translate.y);
		}
		if (this.scale) {
			context.scale(this.scale.x, this.scale.y);
		}

		context.font = `bold ${0.5 * itemSize}px ${
			window.getComputedStyle(this.context.canvas).fontFamily
		}`;
		context.textBaseline = 'middle';
		context.textAlign = 'center';
		context.fillText('🚩', 0, 0);

		context.restore();
	}
}
