import { fixCanvasDpi } from '$lib/functions/fixCanvasDpi';
import { get2DGrid } from '$lib/functions/get2DGrid';
import { getGridItemsAround } from '$lib/functions/getGridItemsAround';
import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import type { GridItem } from '$lib/models/GridItem';
import type { Point2D } from '$lib/models/Point2D';
import type { Size } from '$lib/models/Size';
import { theme } from '$lib/stores/theme.svelte';
import { Animation } from './Animation';
import { GameSound } from '$lib/util/GameSound.svelte';
import { Timer } from '$lib/util/Timer.svelte';
import { MinesweeperBackground } from './MinesweeperBackground';
import { MinesweeperBomb } from './MinesweeperBomb';
import { MinesweeperButton } from './MinesweeperButton';
import { MinesweeperFlag } from './MinesweeperFlag';
import type { MinesweeperGridItemOptions } from './MinesweeperGridItem';
import { MinesweeperNumber, type MinesweeperNumberType } from './MinesweeperNumber';
import type { SettingsManager } from '$lib/util/SettingsManager.svelte';
import { ads } from '$lib/stores/ads.svelte';

export type MinesweeperDifficulty = 'easy' | 'normal' | 'hard';

type BoardConfig = {
	maxWidth?: number;
	maxColumns?: number;
	maxRows?: number;
	itemSizePercentage?: number;
};

type MinesweeperTopBoard = MinesweeperButton[][];
type MinesweeperBottomBoard = (MinesweeperNumber | MinesweeperBomb | null)[][];
type MinesweeperFlagsBoard = (MinesweeperFlag | null)[][];

export type MinesweeperSounds = {
	gameWin: GameSound;
	explosion: GameSound;
	dig: GameSound;
	digSuccess: GameSound;
	addFlag: GameSound;
	removeFlag: GameSound;
	replay: GameSound;
	highlight: GameSound;
};

const difficultyToShakeDuration: Record<MinesweeperDifficulty, number> = {
	easy: 1500,
	normal: 3000,
	hard: 4000,
};

const difficultyToBombsPercentage: Record<MinesweeperDifficulty, number> = {
	easy: 10,
	normal: 14,
	hard: 22,
};

const difficultyToScoreFactor: Record<MinesweeperDifficulty, number> = {
	easy: 1,
	normal: 2,
	hard: 3,
};

const longPressMovementThreshold = 30;

const getRandomShakePoint = (): Point2D => {
	const min = -1 * window.devicePixelRatio;
	const max = 1 * window.devicePixelRatio;
	const zero = 0;
	const all = [zero, min, max];

	return {
		x: getRandomItemAt(all),
		y: getRandomItemAt(all),
	};
};

const getShakeTranslate = (progress: number): Point2D => {
	if (progress === 0 || progress === 1) {
		return { x: 0, y: 0 };
	}

	return getRandomShakePoint();
};

export type MinesweeperGameOptions = {
	canvas: HTMLCanvasElement;
	offsetY: number;
	offsetBottom: number;
	audios?: MinesweeperSounds;
	settingsManager: SettingsManager<{
		difficulty: MinesweeperDifficulty;
		animated: boolean;
	}>;
	difficulty: MinesweeperDifficulty;
	timer: Timer;
	onGameWin: () => void;
	onGameLoseAndAnimationFinished: () => void;
};

export class MinesweeperGame {
	id: number = Math.floor(Math.random() * 1e9);
	isGameOverAnimationFinished = $state(false);
	settingsManager: SettingsManager<{
		difficulty: MinesweeperDifficulty;
		animated: boolean;
	}>;
	private _timer: Timer;
	private _difficulty: MinesweeperDifficulty;
	private topBoard: MinesweeperTopBoard = [];
	private flagsBoard: MinesweeperFlagsBoard = [];
	private bottomBoard: MinesweeperBottomBoard = [];
	private background: MinesweeperBackground;
	private config!: BoardConfig;
	private firstClickPosition: GridItem | null = null;
	private shakeAnimation = new Animation(500);
	private _isWon = $state(false);
	private _isLost = $state(false);
	private _availableFlags = $state(10);
	private onGameWin: () => void;
	private onGameLoseAndAnimationFinished: () => void;

	// Render related
	private canvas: HTMLCanvasElement;
	private context: CanvasRenderingContext2D;
	private animationFrame = -1;
	private gridItemMargin = 1;
	private gridMargin = 16;
	private offsetY: number;
	private offsetBottom: number;
	private gridRows!: number;
	private gridColumns!: number;
	private longPressThreshold = 400;
	private longPressTimeoutId = -1;
	private longPressEvent: MouseEvent | null = null;
	private consumedLongPressEvent = false;
	private pointerDownPosition: GridItem | null = null;
	private colors = {
		grid: '#000',
		background: '#000',
		highlightColor: '#000',
		highlightBorderColor: '#000',
		1: '#000',
		2: '#000',
		3: '#000',
		4: '#000',
		5: '#000',
		6: '#000',
		7: '#000',
		8: '#000',
	};
	private cleanupEffect: () => void;
	private cleanupSettingsEffect: () => void;

	// Audio
	private audios: MinesweeperSounds | undefined;
	private onGameOverAnimationFinishedTimeoutId = -1;

	constructor({
		canvas,
		offsetBottom,
		offsetY,
		audios,
		settingsManager,
		difficulty,
		timer,
		onGameWin,
		onGameLoseAndAnimationFinished,
	}: MinesweeperGameOptions) {
		this._timer = timer;
		this._difficulty = difficulty;
		this.settingsManager = settingsManager;
		this.canvas = canvas;
		this.context = canvas.getContext('2d')!;
		this.offsetY = offsetY;
		this.offsetBottom = offsetBottom;
		this.background = new MinesweeperBackground({
			context: this.context,
			color: '#000',
		});
		this.audios = audios;
		this.onGameLoseAndAnimationFinished = onGameLoseAndAnimationFinished;
		this.onGameWin = onGameWin;
		this.initBoardConfig();
		this.initBoards();
		this.resetAudios();
		this.start();

		this.cleanupEffect = $effect.root(() => {
			$effect(() => {
				if (theme.value) {
					const style = getComputedStyle(document.documentElement);
					this.colors = {
						grid: style.getPropertyValue('--color-game-minesweeper-grid'),
						background: style.getPropertyValue('--color-game-minesweeper-background'),
						highlightColor: style.getPropertyValue(
							'--color-game-minesweeper-highlight',
						),
						highlightBorderColor: style.getPropertyValue(
							'--color-game-minesweeper-highlight-border',
						),
						1: style.getPropertyValue('--color-game-minesweeper-1'),
						2: style.getPropertyValue('--color-game-minesweeper-2'),
						3: style.getPropertyValue('--color-game-minesweeper-3'),
						4: style.getPropertyValue('--color-game-minesweeper-4'),
						5: style.getPropertyValue('--color-game-minesweeper-5'),
						6: style.getPropertyValue('--color-game-minesweeper-6'),
						7: style.getPropertyValue('--color-game-minesweeper-7'),
						8: style.getPropertyValue('--color-game-minesweeper-8'),
					};
					this.background.color = this.colors.background;
					this.bottomBoard.forEach((row) => {
						row.forEach((item) => {
							if (item instanceof MinesweeperNumber) {
								item.color = this.colors[item.number];
							}
						});
					});
					this.topBoard.forEach((row) => {
						row.forEach((item) => {
							item.color = this.colors.grid;
							item.highlightColor = this.colors.highlightColor;
							item.highlightBorderColor = this.colors.highlightBorderColor;
						});
					});
				}
			});
		});

		this.cleanupSettingsEffect = $effect.root(() => {
			$effect(() => {
				const animated = this.settingsManager.settings.animated;

				this.shakeAnimation.enabled = animated;
				this.topBoard.forEach((row) =>
					row.forEach((item) => item.setAnimationsEnabled(animated)),
				);
				this.flagsBoard.forEach((row) =>
					row.forEach((item) => item?.setAnimationsEnabled(animated)),
				);
			});
		});
	}

	/** Smaller is better */
	get score() {
		return Math.max(
			0,
			Math.floor(this.timer.time / 100) -
				this.amountOfBombs * difficultyToScoreFactor[this.difficulty],
		);
	}

	get timer() {
		return this._timer;
	}

	get difficulty() {
		return this._difficulty;
	}

	get availableFlags() {
		return this._availableFlags;
	}

	set availableFlags(flags: number) {
		this._availableFlags = flags;
	}

	get isWon() {
		return this._isWon;
	}

	set isWon(newIsWon: boolean) {
		this._isWon = newIsWon;

		if (newIsWon) {
			this.audios?.gameWin?.play();
			this._timer.stop();
			this.onGameWin();
		}
	}

	get isLost() {
		return this._isLost;
	}

	set isLost(newIsLost: boolean) {
		this._isLost = newIsLost;

		if (newIsLost) {
			this._timer.stop();
		}
	}

	get isOver() {
		return this.isWon || this.isLost;
	}

	get availableBoardSize() {
		return {
			width:
				Math.min(
					(this.config.maxWidth ?? Infinity) * window.devicePixelRatio,
					this.canvas.width - ads.effectiveSize.width * window.devicePixelRatio,
				) -
				2 * this.gridMargin,
			height:
				this.canvas.height -
				(this.offsetY + this.offsetBottom + ads.effectiveSize.height) *
					window.devicePixelRatio -
				2 * this.gridMargin,
		};
	}

	get itemSize() {
		const { width, height } = this.availableBoardSize;
		return Math.min(
			// this.config.itemSizePercentage ?? Infinity,
			Math.floor(width / this.gridColumns),
			Math.floor(height / this.gridRows),
		);
	}

	get gridSize(): GridItem {
		return {
			row: this.gridRows,
			column: this.gridColumns,
		};
	}

	get gridWidth() {
		return this.itemSize * this.gridColumns;
	}

	get gridHeight() {
		return this.itemSize * this.gridRows;
	}

	get gridSizeInPx(): Size {
		return {
			width: this.gridWidth,
			height: this.gridHeight,
		};
	}

	get gridStart(): Point2D {
		return {
			x:
				(this.canvas.width -
					ads.effectiveSize.width * window.devicePixelRatio -
					this.gridWidth) /
				2,
			y: window.devicePixelRatio * (this.offsetY + ads.effectiveSize.height),
		};
	}

	get isMdUp(): boolean {
		return window.matchMedia('(min-width: 768px)').matches;
	}

	get isDesktop(): boolean {
		return window.matchMedia('(min-width: 1024px)').matches;
	}

	checkIfGameIsWon() {
		const isWon = this.topBoard.every((buttonRow, row) =>
			buttonRow.every((button, column) => {
				const bottomItem = this.bottomBoard[row][column];

				if (bottomItem instanceof MinesweeperBomb === false) {
					return button.isHidden;
				}

				return true;
			}),
		);

		if (isWon) {
			this.isWon = isWon;
		}
	}

	getConfig(): BoardConfig {
		if (this.isDesktop) {
			switch (this.difficulty) {
				case 'easy':
					return {
						maxWidth: 448,
						maxColumns: 10,
						maxRows: 10,
					};

				case 'normal':
					return {
						maxRows: 17,
						maxColumns: 17,
					};
				case 'hard':
					return {
						maxWidth: 1024,
						maxRows: 17,
						maxColumns: 27,
					};
			}
		}

		if (this.isMdUp) {
			switch (this.difficulty) {
				case 'easy':
					return { maxRows: 10, maxColumns: 10 };
				case 'normal':
					return {
						itemSizePercentage: 6.5,
					};
				case 'hard':
					return {
						itemSizePercentage: 4.5,
					};
			}
		}

		switch (this.difficulty) {
			case 'easy':
				return { maxRows: 10, maxColumns: 10 };
			case 'normal':
				return {
					itemSizePercentage: 9,
				};
			case 'hard':
				return {
					itemSizePercentage: 7,
				};
		}
	}

	get amountOfBombs() {
		const boardSize = this.gridRows * this.gridColumns;

		return Math.floor((boardSize * difficultyToBombsPercentage[this.difficulty]) / 100);
	}

	initBoardConfig() {
		fixCanvasDpi(this.canvas);
		this.config = this.getConfig();

		const { width, height } = this.availableBoardSize;

		let itemSize: number | undefined;

		if (this.config.itemSizePercentage) {
			itemSize = (this.config.itemSizePercentage * Math.min(width, height)) / 100;
		}

		if (itemSize) {
			this.gridRows = Math.floor(height / itemSize);
			this.gridColumns = Math.floor(width / itemSize);
		} else {
			this.gridRows = Math.min(
				this.config.maxRows ?? Infinity,
				Math.floor(height / (this.config.maxRows ?? 1)),
			);
			this.gridColumns = Math.min(
				this.config.maxColumns ?? Infinity,
				Math.min(
					this.config.maxColumns ?? Infinity,
					Math.floor(width / (this.config.maxColumns ?? 1)),
				),
			);
		}

		// Make it a square
		if (this.isMdUp && this.difficulty !== 'hard') {
			if (this.gridRows > this.gridColumns) {
				this.gridRows = this.gridColumns;
			} else {
				this.gridColumns = this.gridRows;
			}
		}

		this.gridColumns = Math.max(0, this.gridColumns);
		this.gridRows = Math.max(0, this.gridRows);
		this._availableFlags = this.amountOfBombs;
	}

	shake() {
		this.shakeAnimation.play();
	}

	initBoards() {
		this.topBoard = get2DGrid(this.gridRows, this.gridColumns).map((row, rowIndex) =>
			row.map((_, colIndex) => {
				return new MinesweeperButton({
					color: '#000',
					column: colIndex,
					row: rowIndex,
					context: this.context,
					margin: this.gridItemMargin,
					size: this.itemSize,
					gridStart: this.gridStart,
					animationsEnabled: this.settingsManager.settings.animated,
				});
			}),
		);
		this.bottomBoard = get2DGrid(this.gridRows, this.gridColumns);
		this.flagsBoard = get2DGrid(this.gridRows, this.gridColumns);
	}

	resetAudios() {
		if (!this.audios) {
			return;
		}

		Object.values(this.audios).forEach((audio) => audio.stop());
	}

	start() {
		this.addListeners();

		window.requestAnimationFrame(this.loop);
	}

	getGridItemOptions(row: number, column: number) {
		const options: MinesweeperGridItemOptions = {
			context: this.context,
			size: this.itemSize,
			gridStart: this.gridStart,
			margin: this.gridItemMargin,
			row,
			column,
		};

		return options;
	}

	spawnBomb() {
		const position = this.getNewBombPosition();

		if (position) {
			this.bottomBoard[position.row][position.column] = new MinesweeperBomb(
				this.getGridItemOptions(position.row, position.column),
			);
		}
	}

	getNewBombPosition(): GridItem | null {
		if (this.isWon || !this.firstClickPosition) {
			return null;
		}

		const column = Math.floor(Math.random() * this.gridColumns);
		const row = Math.floor(Math.random() * this.gridRows);
		const position: GridItem = { row, column };

		const hasSpawnedOnAnotherBomb = !!this.bottomBoard[row][column];
		const hasSpawnedOnFirstClickSafeArea = [
			this.firstClickPosition,
			...getGridItemsAround(this.firstClickPosition, this.gridSize),
		].some((item) => {
			return item.row === row && item.column === column;
		});

		if (hasSpawnedOnAnotherBomb || hasSpawnedOnFirstClickSafeArea) {
			return this.getNewBombPosition();
		}

		return position;
	}

	spawnAllBombs() {
		Array(this.amountOfBombs)
			.fill(0)
			.forEach(() => this.spawnBomb());
	}

	incrementNumberAt(row: number, column: number) {
		if (row < 0 || column < 0 || row >= this.gridRows || column >= this.gridColumns) {
			return;
		}

		const item = this.bottomBoard[row][column];

		if (item === null) {
			const newItem = new MinesweeperNumber({
				...this.getGridItemOptions(row, column),
				number: 1,
				color: this.colors[1],
			});

			this.bottomBoard[row][column] = newItem;
		} else if (item instanceof MinesweeperNumber) {
			item.number += 1;
			item.color = this.colors[item.number as MinesweeperNumberType];
		}
	}

	incrementNumbersAround(row: number, column: number) {
		getGridItemsAround({ row, column }, this.gridSize).forEach((item) =>
			this.incrementNumberAt(item.row, item.column),
		);
	}

	spawnAllNumbers() {
		for (let row = 0; row < this.bottomBoard.length; row += 1) {
			for (let column = 0; column < this.bottomBoard[row].length; column += 1) {
				const item = this.bottomBoard[row][column];
				if (item instanceof MinesweeperBomb) {
					this.incrementNumbersAround(row, column);
				}
			}
		}
	}

	fillBottomBoard() {
		this.spawnAllBombs();
		this.spawnAllNumbers();
	}

	clearDraw() {
		const canvasSize = this.context.canvas.width;
		this.context.clearRect(0, 0, canvasSize, canvasSize);
	}

	draw() {
		this.context.save();

		fixCanvasDpi(this.canvas);
		this.clearDraw();
		this.drawShake();
		this.drawBackground();
		this.drawBoards();

		this.context.restore();
	}

	drawShake() {
		const translate = getShakeTranslate(this.shakeAnimation.progress);

		this.context.translate(translate.x, translate.y);
	}

	drawBackground() {
		const start = this.gridStart;
		this.background.startPoint = start;
		this.background.size = {
			width: this.gridWidth,
			height: this.gridHeight,
		};
		this.background.draw();
	}

	drawBoards() {
		const itemSize = this.itemSize;
		const gridStart = this.gridStart;

		this.bottomBoard.forEach((row) =>
			row.forEach((item) => {
				if (item) {
					item.size = itemSize;
					item.gridStart = gridStart;
					item.draw();
				}
			}),
		);

		this.topBoard.forEach((row) =>
			row.forEach((item) => {
				if (item) {
					item.size = itemSize;
					item.gridStart = gridStart;
					item.draw();
				}
			}),
		);

		this.flagsBoard.forEach((row) =>
			row.forEach((item) => {
				if (item) {
					item.size = itemSize;
					item.gridStart = gridStart;
					item.draw();
				}
			}),
		);
	}

	loop = () => {
		this.draw();

		this.animationFrame = window.requestAnimationFrame(this.loop);
	};

	revealRegion(row: number, column: number) {
		if (this.isOver) {
			return;
		}
		if (row < 0 || column < 0 || row >= this.gridRows || column >= this.gridColumns) {
			return;
		}

		const visitedMatrix = get2DGrid(this.gridRows, this.gridColumns) as boolean[][];
		const nextItems: GridItem[] = [];

		const firstItem = this.bottomBoard[row][column];

		if (firstItem === null) {
			nextItems.push({ row, column });

			while (nextItems.length > 0) {
				const next = nextItems.shift()!;
				const nextItem = this.bottomBoard[next.row][next.column];
				const nextFlag = this.flagsBoard[next.row][next.column];

				if (!visitedMatrix[next.row][next.column]) {
					visitedMatrix[next.row][next.column] = true;

					if (nextItem === null || nextItem instanceof MinesweeperNumber) {
						const topItem = this.topBoard[next.row][next.column];
						topItem?.hide();
					}

					if (nextFlag instanceof MinesweeperFlag) {
						this.removeFlag(next.row, next.column, false);
					}
					if (nextItem === null) {
						nextItems.push(...getGridItemsAround(next, this.gridSize));
					}
				}
			}
		}
	}

	revealItem(row: number, column: number) {
		if (this.isOver) {
			return;
		}
		const flag = this.flagsBoard[row][column];

		if (!flag) {
			const item = this.topBoard[row][column];
			if (item && !item.isHidden) {
				const bottomItem = this.bottomBoard[row][column];

				this.audios?.dig?.playIfNotPlaying();

				if (bottomItem instanceof MinesweeperBomb) {
					this.explode({ row, column });
				} else if (bottomItem instanceof MinesweeperNumber) {
					this.audios?.digSuccess?.playIfNotPlaying();
					item.hide();
				} else if (bottomItem === null) {
					this.audios?.digSuccess?.playIfNotPlaying();
					this.revealRegion(row, column);
				}
			}
		}

		this.checkIfGameIsWon();
	}

	highlightButtonsAroundAndRevealIfNeeded(row: number, column: number) {
		if (this.isOver) {
			return;
		}

		const buttonsToHighlight = getGridItemsAround({ row, column }, this.gridSize)
			.map((item) => {
				const button = this.topBoard[item.row][item.column];

				return button;
			})
			.filter((button) => button.canHighlight);

		buttonsToHighlight.forEach((button) => button.highlight());

		if (buttonsToHighlight.length > 0) {
			this.audios?.highlight?.play();
		}

		// Reveal buttons if the neighbours are flagged with the same amount
		// of bombs

		const buttonsWithoutFlags = buttonsToHighlight.filter((button) => {
			const flag = this.flagsBoard[button.row][button.column];

			return flag === null || flag.isHidden;
		});

		const amountOfFlags = buttonsToHighlight.filter((button) => {
			const flag = this.flagsBoard[button.row][button.column];

			return flag !== null && !flag.isHidden;
		}).length;

		const amountOfBombs = buttonsToHighlight.filter((button) => {
			const bottomItem = this.bottomBoard[button.row][button.column];

			return bottomItem instanceof MinesweeperBomb;
		}).length;

		const canReveal = amountOfFlags === amountOfBombs;

		if (canReveal) {
			buttonsWithoutFlags.forEach((button) => {
				this.revealItem(button.row, button.column);
			});
		}
	}

	explode(epicenter: GridItem) {
		if (this.isOver) {
			return;
		}
		const explosionDuration = this.settingsManager.settings.animated
			? difficultyToShakeDuration[this.difficulty]
			: 1000;

		// Audio
		this.audios?.explosion?.fade(GameSound.volume, 0, explosionDuration + 300);
		this.audios?.explosion?.playIfNotPlaying();

		this.isLost = true;

		// Shake
		this.shakeAnimation.duration = explosionDuration;
		this.shakeAnimation.play();

		// Epicenter item
		const epicenterItem = this.topBoard[epicenter.row][epicenter.column];

		if (epicenterItem) {
			epicenterItem.explosionCircleAnimation.duration = this.shakeAnimation.duration;
		}

		// Bomb
		const explodedBomb = this.bottomBoard[epicenter.row][epicenter.column];

		if (explodedBomb instanceof MinesweeperBomb) {
			explodedBomb.exploded = true;
		}

		// Items
		this.topBoard.forEach((row) =>
			row.forEach((item) => {
				item?.explode(epicenter);
			}),
		);
		this.flagsBoard.forEach((row) =>
			row.forEach((item) => {
				item?.explode(epicenter);
			}),
		);

		this.onGameOverAnimationFinishedTimeoutId = setTimeout(() => {
			this.isGameOverAnimationFinished = true;
			this.onGameLoseAndAnimationFinished();
		}, explosionDuration) as unknown as number;
	}

	async removeFlag(row: number, column: number, withAudio = true) {
		const flag = this.flagsBoard[row][column];

		if (flag?.hideAnimation?.running) {
			return;
		}

		if (flag) {
			if (withAudio) {
				this.audios?.removeFlag?.play();
			}
			this.availableFlags += 1;
			await flag.hide();
			this.flagsBoard[row][column] = null;
		}
	}

	async toggleFlag(row: number, column: number, withAudio = true) {
		const topItem = this.topBoard[row][column];

		if (topItem && !topItem.isHidden) {
			const flag = this.flagsBoard[row][column];

			if (flag?.hideAnimation?.running) {
				return;
			}

			if (flag) {
				this.removeFlag(row, column, withAudio);
			} else if (this.availableFlags > 0) {
				if (withAudio) {
					this.audios?.addFlag?.play();
				}
				this.flagsBoard[row][column] = new MinesweeperFlag({
					...this.getGridItemOptions(row, column),
					animationsEnabled: this.settingsManager.settings.animated,
				});
				this.availableFlags -= 1;
			}
		}
	}

	getGridPositionFromEvent(event: PointerEvent | MouseEvent): GridItem | null {
		const rect = this.canvas.getBoundingClientRect();
		const x = window.devicePixelRatio * Math.round(event.clientX - rect.left);
		const y = window.devicePixelRatio * Math.round(event.clientY - rect.top);

		const gridStart = this.gridStart;
		const gridSize = this.gridSizeInPx;
		const gridX = x - gridStart.x;
		const gridY = y - gridStart.y;

		if (gridX >= 0 && gridY >= 0 && gridX <= gridSize.width && gridY <= gridSize.height) {
			const row = Math.min(
				this.gridRows - 1,
				Math.floor((this.gridRows * gridY) / gridSize.height),
			);
			const column = Math.min(
				this.gridColumns - 1,
				Math.floor((this.gridColumns * gridX) / gridSize.width),
			);

			return { row, column };
		}

		return null;
	}

	handlePointerDown = (event: PointerEvent) => {
		if (this.isOver) {
			return;
		}
		this.consumedLongPressEvent = false;
		const position = this.getGridPositionFromEvent(event);
		this.pointerDownPosition = position;

		if (position && event.pointerType === 'touch') {
			this.longPressEvent = event;
			this.longPressTimeoutId = setTimeout(() => {
				this.longPressEvent = null;
				this.toggleFlag(position.row, position.column);
				this.longPressTimeoutId = -1;
				this.consumedLongPressEvent = true;
			}, this.longPressThreshold) as unknown as number;
		}
	};

	handlePointerUp = (event: PointerEvent) => {
		clearTimeout(this.longPressTimeoutId);
		this.longPressTimeoutId = -1;
		this.longPressEvent = null;

		if (this.isOver || (this._timer.paused && this._timer.started)) {
			return;
		}

		if (this.consumedLongPressEvent) {
			this.consumedLongPressEvent = false;
			return;
		}

		const position = this.getGridPositionFromEvent(event);

		if (position) {
			// Prevent click when moving the cursor
			if (
				this.pointerDownPosition?.row !== position.row ||
				this.pointerDownPosition?.column !== position.column
			) {
				return;
			}

			// Left click
			if (event.button === 0) {
				const flag = this.flagsBoard[position.row][position.column];
				const topItem = this.topBoard[position.row][position.column];
				const bottomItem = this.bottomBoard[position.row][position.column];

				if (this.firstClickPosition === null) {
					if (!flag) {
						this.firstClickPosition = position;
						this.fillBottomBoard();
						this.shake();
						this._timer.start();
					}
				}

				if (!flag && topItem.isHidden && bottomItem instanceof MinesweeperNumber) {
					this.highlightButtonsAroundAndRevealIfNeeded(position.row, position.column);
				} else {
					this.revealItem(position.row, position.column);
				}
				return;
			}
			// Right click
			if (event.button === 2) {
				this.toggleFlag(position.row, position.column);
				return;
			}
		}
	};

	handlePointerMove = (event: PointerEvent) => {
		if (this.longPressEvent) {
			const dx = Math.abs(event.screenX - this.longPressEvent.screenX);
			const dy = Math.abs(event.screenY - this.longPressEvent.screenY);

			if (dx >= longPressMovementThreshold || dy >= longPressMovementThreshold) {
				clearTimeout(this.longPressTimeoutId);
				this.longPressTimeoutId = -1;
				this.longPressEvent = null;
			}
		}
	};

	handleContextMenu = (event: MouseEvent) => {
		event.preventDefault();

		return false;
	};

	addListeners() {
		this.canvas.addEventListener('pointermove', this.handlePointerMove);
		this.canvas.addEventListener('pointerdown', this.handlePointerDown);
		this.canvas.addEventListener('pointerup', this.handlePointerUp);
		this.canvas.addEventListener('contextmenu', this.handleContextMenu);
	}

	dispose() {
		this.isGameOverAnimationFinished = false;
		this._timer.reset();
		this.cleanupEffect?.();
		this.cleanupSettingsEffect?.();
		window.cancelAnimationFrame(this.animationFrame);
		this.canvas.removeEventListener('pointermove', this.handlePointerMove);
		this.canvas.removeEventListener('pointerdown', this.handlePointerDown);
		this.canvas.removeEventListener('pointerup', this.handlePointerUp);
		this.canvas.removeEventListener('contextmenu', this.handleContextMenu);
		clearTimeout(this.onGameOverAnimationFinishedTimeoutId);
	}
}
