import { drawCircle } from '$lib/functions/drawCircle';
import { drawGridRect } from '$lib/functions/drawGridRect';
import { wait } from '$lib/functions/wait';
import type { GridItem } from '$lib/models/GridItem';
import type { Point2D } from '$lib/models/Point2D';
import { Animation } from './Animation';
import { MinesweeperGridItem, type MinesweeperGridItemOptions } from './MinesweeperGridItem';

const getHideTranslate = (progress: number): Point2D => {
	const x = 80 * progress;
	const y = Math.pow(x, 2) / 60;

	return {
		x,
		y,
	};
};

const getHideScale = (progress: number): Point2D => {
	return {
		x: 1 - progress,
		y: 1 - progress,
	};
};

const getHideRotate = (progress: number): number => {
	return progress / 1.5;
};

const getExplosionScale = (progress: number): Point2D => {
	const scale = 1 + Math.sin((3 / 2) * progress * Math.PI);

	return {
		x: scale,
		y: scale,
	};
};

const getHighlightOpacity = (progress: number): number => {
	const fadeAmount = 0.15;
	const fadeOutStart = 1 - fadeAmount;

	if (progress === 1) {
		return 0;
	}

	if (progress < fadeAmount) {
		return progress / fadeAmount;
	}

	if (progress > fadeOutStart) {
		return 1 - (progress - fadeOutStart) / fadeAmount;
	}

	return 1;
};

const getExplosionTranslate = (
	progress: number,
	epicenter: Point2D,
	position: Point2D,
): Point2D => {
	const opposite = position.y - epicenter.y;
	const adjacent = position.x - epicenter.x;
	const hypotenuse = getDistance(position, epicenter);
	const sin = opposite / hypotenuse;
	const cos = adjacent / hypotenuse;
	const intensity = 500 * Math.exp((-hypotenuse * progress) / 5);
	const x = progress * intensity * cos;
	const y = progress * intensity * sin;

	return {
		x,
		y,
	};
};

const getExplosionCircleScale = (progress: number) => {
	return progress * 1.05;
};

const getExplosionCircleOpacity = (progress: number) => {
	return Math.max(0, 0.5 - progress);
};

function getDistance(a: Point2D, b: Point2D) {
	const dx = a.x - b.x;
	const dy = a.y - b.y;

	return Math.sqrt(Math.pow(dy, 2) + Math.pow(dx, 2));
}

export class MinesweeperButton extends MinesweeperGridItem {
	highlightColor = '#000';
	highlightBorderColor = '#000';

	// Hide
	hideRotateFactor = Math.random() > 0.5 ? 1 : -1;
	hideTranslateXFactor = Math.random() * this.hideRotateFactor;
	hideAnimation = new Animation(1000);

	// Explosion
	explosionEpicenter: GridItem | undefined;
	explosionAnimation = new Animation(500, 0);

	// Highlight
	highlightAnimation = new Animation(800, 0);

	explosionCircleAnimation = new Animation(500);
	isHidden = false;
	isExploded = false;

	get canHighlight() {
		return !this.isExploded && !this.isHidden;
	}

	constructor(options: MinesweeperGridItemOptions & { animationsEnabled?: boolean }) {
		super(options);

		this.explosionAnimation.progressWhenDisabled = 1;
		this.explosionCircleAnimation.progressWhenDisabled = 1;
		this.hideAnimation.progressWhenDisabled = 1;

		this.setAnimationsEnabled(options.animationsEnabled ?? true);
	}

	setAnimationsEnabled(enabled: boolean) {
		this.explosionAnimation.enabled = enabled;
		this.explosionCircleAnimation.enabled = enabled;
		this.hideAnimation.enabled = enabled;
		this.highlightAnimation.enabled = enabled;
	}

	async hide() {
		if (this.isExploded) {
			return;
		}

		this.hideAnimation.play();
		this.isHidden = true;

		return wait(this.hideAnimation.effectiveDuration);
	}

	explode(epicenter: GridItem) {
		this.explosionEpicenter = epicenter;
		this.explosionAnimation.delay =
			150 *
			getDistance(
				{ x: epicenter.column, y: epicenter.row },
				{
					x: this.position.column,
					y: this.position.row,
				},
			);
		this.explosionAnimation.play();
		this.explosionCircleAnimation.play();
		this.isExploded = true;
	}

	highlight() {
		if (!this.canHighlight) {
			return;
		}

		this.highlightAnimation.reset();
		this.highlightAnimation.play();
	}

	protected runExplosionAnimation() {
		if (!this.isExploded || !this.explosionEpicenter || this.isHidden) {
			return;
		}

		const progress = this.explosionAnimation.progress;

		this.scale = getExplosionScale(progress);
		this.translate = getExplosionTranslate(
			progress,
			{
				x: this.explosionEpicenter.column,
				y: this.explosionEpicenter.row,
			},
			{
				x: this.position.column,
				y: this.position.row,
			},
		);
	}

	protected runHideAnimation() {
		if (!this.isHidden) {
			return;
		}

		const progress = this.hideAnimation.progress;
		const translate = getHideTranslate(progress);

		this.translate = {
			x: this.hideTranslateXFactor * translate.x,
			y: translate.y,
		};
		this.scale = getHideScale(progress);
		this.rotateAngle = this.hideRotateFactor * getHideRotate(progress);
	}

	draw() {
		this.color = window.getComputedStyle(this.context.canvas).color;
		this.runHideAnimation();
		this.runExplosionAnimation();
		super.draw();
		this.drawHighlight();
		this.drawExplosionCircle();
	}

	drawHighlight() {
		if (this.highlightAnimation.running) {
			const borderOptions = {
				context: this.context,
				gridItemSize: this.size,
				gridItemMargin: this.margin ?? 1,
				row: this.row,
				column: this.column,
				color: this.highlightBorderColor,
				gridStart: this.gridStart,
				scale: this.scale,
				translate: this.translate,
				rotateAngle: this.rotateAngle,
				opacity: getHighlightOpacity(this.highlightAnimation.progress),
			};

			drawGridRect(borderOptions);
			drawGridRect({
				...borderOptions,
				gridItemMargin: borderOptions.gridItemMargin + 2,
				color: this.highlightColor,
			});
		}
	}

	drawExplosionCircle() {
		if (
			this.explosionEpicenter?.row === this.row &&
			this.explosionEpicenter?.column === this.column
		) {
			const scale = getExplosionCircleScale(this.explosionCircleAnimation.progress);
			const opacity = getExplosionCircleOpacity(this.explosionCircleAnimation.progress);
			const size = 2000 * scale;
			const halfSize = size / 2;

			this.context.save();

			this.context.translate(
				this.gridStart.x + this.column * this.size + this.margin + halfSize,
				this.gridStart.y + this.row * this.size + this.margin + halfSize,
			);

			this.context.globalAlpha = opacity;

			drawCircle(this.context, -halfSize, -halfSize, halfSize, this.color);

			this.context.restore();
		}
	}
}
