<script lang="ts">
	import { minesweeperSoundResources as sounds } from './minesweeperSoundResources';
	import InfoModal from '$lib/components/InfoModal.svelte';

	interface Props {
		isOpen?: boolean;
	}

	let { isOpen = $bindable(false) }: Props = $props();
</script>

<InfoModal {sounds} bind:isOpen>
	<h1>Minesweeper</h1>

	<p>
		Minesweeper is a classic puzzle game that has been around for decades. The goal of the game
		is to locate all the mines on a grid without detonating any of them.
	</p>

	<h2>How to Play Minesweeper: The Game Rules</h2>

	<ul>
		<li>The game takes place on a grid, with each square representing a potential mine.</li>

		<li>
			The player must clear all the squares that do not contain a mine, while avoiding the
			ones that do.
		</li>

		<li>The player can mark squares that they suspect contain a mine with a flag.</li>

		<li>If a square is cleared and contains a mine, the game is over.</li>
	</ul>

	<h2>Useful Minesweeper Game Tips</h2>

	<ul>
		<li>
			Pay attention to the numbers on the squares. These indicate how many mines are in the
			surrounding squares.
		</li>

		<li>
			Use logic to deduce where mines are likely to be located. If a square has a number of 3,
			for example, and there are only 2 flagged mines in the surrounding squares, then the
			remaining mine is likely to be located in one of the remaining squares.
		</li>

		<li>Don't rush. Take your time and think before making a move.</li>

		<li>
			When you first start playing, try to clear the edges of the grid first. This will give
			you a better idea of the layout of the mines.
		</li>

		<li>
			If you are stuck, try clicking on a square that you think might contain a mine. If it
			doesn't, you will learn something new about the location of the mines.
		</li>
	</ul>

	<h2>Conclusion</h2>

	<p>
		Minesweeper is a game that requires patience and logical thinking. By following these tips
		and rules, you will be able to improve your skills and increase your chances of winning.
		It's a great way to pass the time and put your mind to the test.
	</p>
</InfoModal>
