<script lang="ts">
	import { onDestroy, onMount } from 'svelte';
	import MinesweeperDifficultyButton from './MinesweeperDifficultyButton.svelte';
	import { MinesweeperGame, type MinesweeperDifficulty } from './MinesweeperGame.svelte';
	import { minesweeperSoundResources } from './minesweeperSoundResources';
	import MinesweeperSettingsButton from './MinesweeperSettingsButton.svelte';
	import MinesweeperInfoModal from './MinesweeperInfoModal.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import capitalize from 'lodash/capitalize';
	import MinesweeperInstructionsIsland from './MinesweeperInstructionsIsland.svelte';
	import InfoButton from '$lib/components/InfoButton.svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';
	import { ads } from '$lib/stores/ads.svelte';

	let canvas = $state<HTMLCanvasElement>();
	let header = $state<HTMLDivElement>();
	let isDifficultyDropdownOpen = $state(false);
	let isInfoModalOpen = $state(false);
	let headerHeight = $derived(header?.clientHeight ?? 0);

	let context = new GameContext({
		gameName: 'Minesweeper',
		GameClass: MinesweeperGame,
		gameKey: 'minesweeper',
		settings: {
			defaultSettings: {
				difficulty: 'easy' as MinesweeperDifficulty,
				animated: true,
			},
		},
		sounds: {
			resources: minesweeperSoundResources,
			lifecycle: {
				createGame: minesweeperSoundResources.replay,
			},
		},
		variants: {
			map: {
				difficulty: {
					allValues: ['easy', 'hard'] as MinesweeperDifficulty[],
					format: capitalize,
				},
			},
			fromGame(game) {
				return {
					difficulty: game?.difficulty ?? 'easy',
				};
			},
			getStatsVariant(variants) {
				return variants.difficulty ?? 'easy';
			},
			getLeaderboardVariant(variants) {
				return variants.difficulty ?? 'easy';
			},
		},
		defaultGameProps(context) {
			return {
				canvas: canvas!,
				offsetY: headerHeight,
				offsetBottom: 40,
				audios: context.sounds,
				settingsManager: context.settingsManager,
				difficulty: context.settingsManager.settings.difficulty,
				timer: context.timer,
				onGameWin,
				onGameLoseAndAnimationFinished,
			};
		},
		stats({ props, context }) {
			return {
				stats: new Stats({
					...props,
					liveStats: {
						flags: {
							name: 'Flags',
							unit: 'plain',
							value() {
								return context.game?.availableFlags ?? 0;
							},
						},
					},
					initialPinnedStats: ['time', 'flags'],
				}),
				canUpdateWithGameLost(game) {
					return game.timer.started && !game.isWon;
				},
				visibleStats: ['bestTime', 'averageTime', 'wonGames', 'totalGames'],
			};
		},
		leaderboard({ props }) {
			return {
				leaderboard: new Leaderboard({
					...props,
					firstAvailableDate: new Date('2025/05/08'),
					order: 'lower-first',
				}),
				sendScoreOn: ['won'],
				getScore(game) {
					return {
						score: game.score,
					};
				},
			};
		},
		onWillCreateGame({ previousGame, newGameOptions, context }) {
			previousGame?.dispose();
			context.settingsManager.settings.difficulty = newGameOptions.difficulty;
			isDifficultyDropdownOpen = false;
		},
		onDispose(context) {
			context.game?.dispose();
		},
	});

	let game = $derived(context.game);

	function onGameWin() {
		context.handleGameOver('won');
	}

	function onGameLoseAndAnimationFinished() {
		context.handleGameOver('lost');
	}

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<MinesweeperInfoModal bind:isOpen={isInfoModalOpen} />

<GameLayout
	noPadding
	navbarStyle="on-top"
	adsProps={{
		variant: 'on-top',
	}}
>
	<!-- TODO: Derive hasSpaceForAds according to viewport size and game difficulty -->
	{#snippet Island()}
		<GameIsland {context} gameOverIslandDelay={game?.isWon ? undefined : 0}>
			{#snippet InstructionsIsland()}
				<MinesweeperInstructionsIsland />
			{/snippet}
		</GameIsland>
	{/snippet}

	<section class="size-full relative touch-pinch-zoom select-none">
		<div class="flex size-full flex-col items-start justify-center">
			<!-- Header -->
			<div
				class="absolute top-0 left-0 right-0 mx-auto max-w-3xl w-full p-4 pt-20 flex flex-col gap-2"
				style="right: {ads.effectiveSize.width}px; top: {ads.effectiveSize.height}px;"
				bind:this={header}
			>
				<div class="flex gap-2 items-center justify-start w-full">
					<MinesweeperDifficultyButton
						bind:open={isDifficultyDropdownOpen}
						difficulty={context.settingsManager.settings.difficulty}
						onChange={(difficulty) => context.createGame({ difficulty })}
					/>

					<MinesweeperSettingsButton
						bind:animated={context.settingsManager.settings.animated}
					/>

					<InfoButton onclick={() => (isInfoModalOpen = true)} />

					<MoreGamesButton class="btn-sm" />
				</div>

				<div class="grid grid-cols-3 place-items-center gap-4">
					<div></div>

					<button
						aria-label="New Game"
						class="btn-ghost btn btn-circle btn-lg text-5xl"
						onclick={() => context.createGame()}
					>
						{#if !game?.isOver}
							🙂
						{/if}
						{#if game?.isWon}
							🥳
						{/if}
						{#if game?.isLost}
							🤯
						{/if}
					</button>

					<div class="flex-center text-2xl justify-self-start">
						🚩 {game?.availableFlags ?? 10}
					</div>
				</div>
			</div>

			<!-- Game -->

			<canvas
				bind:this={canvas}
				class="size-full grow cursor-pointer landscape:hidden landscape:lg:block"
			></canvas>
		</div>
	</section>
</GameLayout>
