<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';
	import { onDestroy, onMount, untrack } from 'svelte';
	import { LightsOutGame } from './LightsOutGame.svelte';
	import LightsOutGameRenderer from './LightsOutGameRenderer.svelte';
	import { fade } from 'svelte/transition';
	import { SettingsManager } from '$lib/util/SettingsManager.svelte';
	import { toast } from 'svelte-sonner';
	import ToastCard from '$lib/components/ToastCard.svelte';
	import { wait } from '$lib/functions/wait';
	import LightsOutPlayground from './LightsOutPlayground.svelte';
	import TouchTutorialFigure from '$lib/components/TouchTutorialFigure.svelte';

	interface Props {
		canShowToast?: boolean;
	}

	let { canShowToast }: Props = $props();
	let isOpen = $state(false);

	let settings = new SettingsManager({
		key: 'lights-out-how-to-play-settings',
		defaultSettings: {
			showIndicator: true,
		},
	});

	$effect(() => {
		untrack(() => {
			settings.load();
		});

		if (canShowToast && untrack(() => settings.settings.showIndicator)) {
			wait(1000).then(() => {
				toast.custom(ToastCard, {
					id: 'lights-out-how-to-play-toast',
					duration: Number.POSITIVE_INFINITY,
					dismissable: true,
					componentProps: {
						title: 'Want to learn how to play Lights Out?',
						description:
							"We'll show you the rules and mechanics with animated examples and a playground",
						action: {
							label: 'Yes, show me',
							onClick: () => {
								isOpen = true;
								settings.settings.showIndicator = false;
							},
						},
						cancel: {
							label: 'No, thanks',
							onClick: () => {
								toast.dismiss('lights-out-how-to-play-toast');

								toast.custom(ToastCard, {
									id: 'lights-out-how-to-play-toast-2',
									dismissable: true,
									duration: Number.POSITIVE_INFINITY,
									componentProps: {
										title: 'You can find the tutorial anytime in the "How to play" button',
										action: {
											label: 'Got it',
											onClick() {
												toast.dismiss('lights-out-how-to-play-toast-2');
											},
										},
									},
								});
							},
						},
					},
				});
			});
		}
	});

	onDestroy(() => {
		settings.dispose();
	});
</script>

<div class="indicator">
	<button
		onclick={() => {
			isOpen = true;
			settings.settings.showIndicator = false;
		}}
		class="btn btn-sm">How <span class="hidden md:inline">to play</span></button
	>

	{#if settings.settings.showIndicator}
		<div
			class="inline-grid *:[grid-area:1/1] indicator-item indicator-top indicator-end"
			transition:fade={{ duration: 300 }}
		>
			<div class="status status-primary animate-ping"></div>
			<div class="status status-primary"></div>
		</div>
	{/if}
</div>

<Dialog bind:isOpen>
	<article>
		<h2>How to Play Lights Out</h2>

		<p>
			Turn all the lights off to level up! When you click a light, it toggles along with its
			neighbors (up, down, left, and right).
		</p>

		<TouchTutorialFigure
			figcaption="Light toggle along with their neighbors"
			touchIconPosition={{ row: 1, column: 1 }}
		>
			{#snippet gameBeforeTouch()}
				<LightsOutGameRenderer
					disabled
					grid={untrack(() => {
						const game = new LightsOutGame({ difficulty: 'easy', empty: true });
						game.toggleLight({ row: 1, column: 1 });
						return game.grid;
					})}
				/>
			{/snippet}

			{#snippet gameAfterTouch()}
				<LightsOutGameRenderer
					disabled
					grid={untrack(() => {
						const game = new LightsOutGame({ difficulty: 'easy', empty: true });
						return game.grid;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<h3>Gameplay</h3>

		<p>
			The lights always form patterns. The more you play, the easier it becomes to spot and
			solve them!
		</p>

		<figure class="flex flex-col items-center text-center w-full mx-auto">
			<LightsOutGameRenderer
				class="max-w-48 mx-auto"
				disabled
				grid={untrack(() => {
					const game = new LightsOutGame({ difficulty: 'easy', empty: true });
					game.toggleLight({ row: 0, column: 0 });
					game.toggleLight({ row: 2, column: 2 });
					return game.grid;
				})}
			/>
			<figcaption>
				A pattern created by clicking the top-left and bottom-right lights
			</figcaption>
		</figure>

		<p>
			Click on the correct lights to turn them off. The adjacent lights will toggle as well.
		</p>

		<TouchTutorialFigure
			figcaption="Game after clicking on the bottom-right light"
			touchIconPosition={{ row: 2, column: 2 }}
		>
			{#snippet gameBeforeTouch()}
				<LightsOutGameRenderer
					disabled
					grid={untrack(() => {
						const game = new LightsOutGame({ difficulty: 'easy', empty: true });
						game.toggleLight({ row: 0, column: 0 });
						game.toggleLight({ row: 2, column: 2 });
						return game.grid;
					})}
				/>
			{/snippet}

			{#snippet gameAfterTouch()}
				<LightsOutGameRenderer
					disabled
					grid={untrack(() => {
						const game = new LightsOutGame({ difficulty: 'easy', empty: true });
						game.toggleLight({ row: 0, column: 0 });
						return game.grid;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>Turn off all lights to level up!</p>

		<TouchTutorialFigure
			figcaption="Game after clicking on the top-left light"
			touchIconPosition={{ row: 0, column: 0 }}
		>
			{#snippet gameBeforeTouch()}
				<LightsOutGameRenderer
					disabled
					grid={untrack(() => {
						const game = new LightsOutGame({ difficulty: 'easy', empty: true });
						game.toggleLight({ row: 0, column: 0 });
						return game.grid;
					})}
				/>
			{/snippet}

			{#snippet gameAfterTouch()}
				<LightsOutGameRenderer
					disabled
					grid={untrack(() => {
						const game = new LightsOutGame({ difficulty: 'easy', empty: true });
						return game.grid;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>After completing a level, the next one starts automatically.</p>

		<h2>Game Modes</h2>

		<p>
			<strong>Time:</strong> You have 2 minutes to complete as many levels as possible. Race against
			the clock to maximize your score.
		</p>

		<p>
			<strong>Endless:</strong> Progress through levels at your own pace with no time limit. A
			stop button lets you end the game whenever you want. Perfect for a relaxed experience or
			practicing patterns.
		</p>

		<h2>Playground</h2>

		<p>Use this playground to test the different patterns and strategies.</p>

		<LightsOutPlayground />

		<p>Have fun!</p>
	</article>
</Dialog>
