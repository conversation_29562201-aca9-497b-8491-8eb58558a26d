import { Grid } from '$lib/util/Grid.svelte';
import type { GridSize } from '$lib/models/GridSize';
import { Smush32 } from '@thi.ng/random';
import type { GridItem } from '$lib/models/GridItem';

export const lightsOutModes = ['time', 'infinite'] as const;
export type LightsOutMode = (typeof lightsOutModes)[number];

export const lightsOutDifficulties = ['easy', 'normal', 'hard', 'very-hard'] as const;
export type LightsOutDifficulty = (typeof lightsOutDifficulties)[number];

export const difficultyToGridSize: Record<LightsOutDifficulty, GridSize> = {
	easy: { rows: 3, columns: 3 },
	normal: { rows: 5, columns: 5 },
	hard: { rows: 7, columns: 7 },
	'very-hard': { rows: 10, columns: 10 },
};

const baseTogglesByLevel: Record<LightsOutDifficulty, number> = {
	easy: 0,
	normal: 3,
	hard: 7,
	'very-hard': 15,
};

interface LightsOutGameProps {
	mode?: LightsOutMode;
	difficulty: LightsOutDifficulty;
	seed?: number;
	empty?: boolean;
	goToNextLevelAutomatically?: boolean;
	onNextLevel?: () => void;
}

export class LightsOutGame {
	grid: Grid<boolean>;
	level = $state(1);
	clicks = $state(0);
	gameOver = $state(false);
	difficulty: LightsOutDifficulty;
	goToNextLevelAutomatically = $state(true);
	mode: LightsOutMode;
	onNextLevel?: () => void;
	private random: Smush32;

	constructor({
		mode = 'time',
		difficulty,
		seed,
		empty = false,
		goToNextLevelAutomatically = true,
		onNextLevel,
	}: LightsOutGameProps) {
		this.difficulty = difficulty;
		this.mode = mode;
		const size = difficultyToGridSize[this.difficulty];
		this.grid = new Grid(size, () => false);
		this.random = new Smush32(seed);
		this.goToNextLevelAutomatically = goToNextLevelAutomatically;
		this.onNextLevel = onNextLevel;

		if (!empty) {
			this.generateLevel();
		}
	}

	get score() {
		const lights = this.grid.flatten().filter((light) => !!light).length;

		return Math.max(0, this.level * 100 - this.clicks - lights);
	}

	generateLevel() {
		this.grid.grid.forEach((row) => row.fill(false));
		let previousRow = -1;
		let previousColumn = -1;
		const solution: GridItem[] = [];

		if (import.meta.env.DEV) {
			console.log(`=== Level ${this.level} ===`);
		}

		for (let i = 0; i < this.level + baseTogglesByLevel[this.difficulty]; i++) {
			let row = this.random.int() % this.grid.rows;
			let column = this.random.int() % this.grid.columns;

			while (row === previousRow && column === previousColumn) {
				row = this.random.int() % this.grid.rows;
				column = this.random.int() % this.grid.columns;
			}

			if (import.meta.env.DEV) {
				solution.unshift({ row, column });
			}

			previousRow = row;
			previousColumn = column;

			this.toggleLight({ row, column });
		}

		if (import.meta.env.DEV) {
			solution.forEach(({ row, column }) =>
				console.log(`row ${row + 1} column ${column + 1}`),
			);
		}

		if (this.checkWin()) {
			this.generateLevel();
		}
	}

	toggleLight(item: GridItem) {
		this._toggle(item);
		const neighbors = this.grid.getItemsAround(item, { diagonal: false });
		neighbors.forEach((neighbor) => this._toggle(neighbor));
	}

	handleUserClick(item: GridItem) {
		if (this.gameOver) {
			return;
		}

		this.clicks++;
		this.toggleLight(item);

		if (this.checkWin() && this.goToNextLevelAutomatically) {
			this.nextLevel();
		}
	}

	private _toggle(item: GridItem) {
		const currentValue = this.grid.at(item);
		if (currentValue !== null) {
			this.grid.set(item, !currentValue);
		}
	}

	checkWin(): boolean {
		return this.grid.flatten().every((light) => !light);
	}

	nextLevel() {
		this.level++;
		this.generateLevel();
		this.onNextLevel?.();
	}
}
