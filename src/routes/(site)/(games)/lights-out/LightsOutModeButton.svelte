<script lang="ts" module>
	export const modeTexts: Record<LightsOutMode, string> = {
		time: 'Time',
		infinite: 'Endless',
	};
</script>

<script lang="ts">
	import GameModeDialog from '$lib/components/GameModeDialog.svelte';
	import InfinityIcon from '$lib/components/Icons/InfinityIcon.svelte';
	import TimeIcon from '$lib/components/Icons/TimeIcon.svelte';
	import type { LightsOutMode } from './LightsOutGame.svelte';

	interface Props {
		mode: LightsOutMode;
		onSave: (mode: LightsOutMode) => void;
	}

	let { mode, onSave }: Props = $props();

	let isOpen = $state(false);
</script>

<button class="btn btn-sm" onclick={() => (isOpen = true)}>
	{modeTexts[mode]}
</button>

{#snippet TwoMinutesImage({ selected }: { selected: boolean })}
	<div class="size-full flex items-center justify-center">
		<TimeIcon class="size-4/5" />
	</div>
{/snippet}

{#snippet InfiniteImage({ selected }: { selected: boolean })}
	<InfinityIcon />
{/snippet}

<GameModeDialog
	bind:isOpen
	{mode}
	gameModes={[
		{
			title: modeTexts.time,
			description: 'Complete as many levels as you can in 2 minutes',
			mode: 'time',
			image: TwoMinutesImage,
		},
		{
			title: modeTexts.infinite,
			description: 'Progress through levels at your own pace, no time limit',
			mode: 'infinite',
			image: InfiniteImage,
		},
	]}
	{onSave}
/>
