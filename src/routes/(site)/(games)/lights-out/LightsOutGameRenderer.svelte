<script lang="ts">
	import { switchSoundAttachment } from '$lib/attachments/switchSoundAttachment.svelte';
	import type { GridItem } from '$lib/models/GridItem';
	import { cn } from '$lib/util/cn';
	import type { Grid } from '$lib/util/Grid.svelte';

	interface Props {
		onCellClick?: (gridItem: GridItem) => void;
		onPlayOnSound?: () => void;
		onPlayOffSound?: () => void;
		disabled?: boolean;
		grid: Grid<boolean>;
		class?: string;
		inert?: boolean;
	}

	let {
		onCellClick,
		grid,
		onPlayOnSound = () => {},
		onPlayOffSound = () => {},
		disabled,
		class: classFromProps = '',
		...props
	}: Props = $props();
</script>

<div
	class={cn('grid gap-1 w-full aspect-square', classFromProps)}
	style="grid-template-columns: repeat({grid.columns}, 1fr);"
	{...props}
>
	{#each grid.grid as row, rowIndex}
		{#each row as cell, colIndex (`${grid.size.columns}, ${rowIndex}, ${colIndex}`)}
			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<div
				role="button"
				aria-disabled={disabled}
				aria-label="row {rowIndex + 1}, column {colIndex + 1}, light is {cell
					? 'on'
					: 'off'}"
				class={cn(
					'btn p-0 rounded-none btn-ghost cursor-pointer size-full transition-colors duration-150 bg-game-lights-out-grid',
					{
						'bg-game-lights-out-highlight': !!cell,
					},
				)}
				tabindex="0"
				onclick={() => {
					if (disabled) {
						return;
					}

					onCellClick?.({ row: rowIndex, column: colIndex });
				}}
				{@attach switchSoundAttachment({
					action: cell ? 'off' : 'on',
					disabled,
					onPlayOnSound,
					onPlayOffSound,
				})}
			></div>
		{/each}
	{/each}
</div>
