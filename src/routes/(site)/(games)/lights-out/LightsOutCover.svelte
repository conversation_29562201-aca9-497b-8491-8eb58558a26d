<script lang="ts">
	import { LightsOutGame } from './LightsOutGame.svelte';
	import LightsOutGameRenderer from './LightsOutGameRenderer.svelte';
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();

	const game = new LightsOutGame({
		difficulty: 'easy',
		empty: true,
	});

	game.toggleLight({ row: 1, column: 1 });
</script>

<div
	class={cn('flex items-center justify-center p-6 size-full', className)}
	aria-hidden="true"
	{...props}
>
	<LightsOutGameRenderer grid={game.grid} disabled inert />
</div>
