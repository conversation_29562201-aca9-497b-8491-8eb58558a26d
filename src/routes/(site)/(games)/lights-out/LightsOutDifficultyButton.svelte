<script lang="ts" module>
	export const difficultyTexts: Record<LightsOutDifficulty, string> = {
		easy: 'Easy',
		normal: 'Normal',
		hard: 'Hard',
		'very-hard': 'Very Hard',
	};
</script>

<script lang="ts">
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import { lightsOutDifficulties, type LightsOutDifficulty } from './LightsOutGame.svelte';

	interface Props {
		open?: boolean;
		difficulty: LightsOutDifficulty;
		onChange: (newDifficulty: LightsOutDifficulty) => void;
	}

	let { open = $bindable(false), difficulty, onChange }: Props = $props();
</script>

<Dropdown bind:open>
	<DropdownButton class="btn-sm">
		{difficultyTexts[difficulty]}
	</DropdownButton>

	<DropdownContent menu>
		{#each lightsOutDifficulties as _difficulty}
			<DropdownItem>
				<button
					class:menu-active={_difficulty === difficulty}
					onclick={() => onChange(_difficulty)}
				>
					{difficultyTexts[_difficulty]}
				</button>
			</DropdownItem>
		{/each}
	</DropdownContent>
</Dropdown>
