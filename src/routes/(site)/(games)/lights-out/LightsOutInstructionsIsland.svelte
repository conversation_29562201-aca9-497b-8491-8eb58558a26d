<script lang="ts">
	import MouseLeftIcon from '$lib/components/Icons/MouseLeftIcon.svelte';
	import TouchIcon from '$lib/components/Icons/TouchIcon.svelte';
	import InstructionsIslandBase from '$lib/components/GameIsland/islands/InstructionsIslandBase.svelte';
</script>

{#snippet mouseLeft()}
	<MouseLeftIcon class="size-16" />
{/snippet}

{#snippet touch()}
	<TouchIcon class="size-16" />
{/snippet}

<InstructionsIslandBase
	goal="Turn off all lights to level up. Complete as many levels as possible in 2 minutes. Clicking toggles lights in a + pattern"
	desktopControls={[
		{
			bindings: [mouseLeft],
		},
	]}
	mobileControls={[
		{
			bindings: [touch],
		},
	]}
/>
