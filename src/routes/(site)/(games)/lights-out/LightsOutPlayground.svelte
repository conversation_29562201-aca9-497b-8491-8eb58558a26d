<script lang="ts">
	import RefreshIcon from '$lib/components/Icons/RefreshIcon.svelte';
	import { cn } from '$lib/util/cn';
	import { difficultyTexts } from './LightsOutDifficultyButton.svelte';
	import { lightsOutDifficulties, LightsOutGame } from './LightsOutGame.svelte';
	import LightsOutGameRenderer from './LightsOutGameRenderer.svelte';

	let game = $state(
		new LightsOutGame({
			difficulty: 'easy',
			empty: true,
			goToNextLevelAutomatically: false,
		}),
	);
</script>

<div class="flex flex-col gap-4 prose-">
	<div class="grid grid-cols-2 md:grid-cols-4 gap-2">
		{#each lightsOutDifficulties as difficulty}
			<button
				class={cn('btn btn-sm', {
					'btn-primary': difficulty === game.difficulty,
				})}
				onclick={() => {
					game = new LightsOutGame({
						difficulty,
						empty: true,
						goToNextLevelAutomatically: false,
					});
				}}
			>
				{difficultyTexts[difficulty]}
			</button>
		{/each}
	</div>

	<LightsOutGameRenderer grid={game.grid} onCellClick={(cell) => game.handleUserClick(cell)} />

	<div class="flex items-center justify-center">
		<button
			class="btn"
			onclick={() => {
				game.grid.grid.forEach((row, rowIndex) =>
					row.forEach((_, colIndex) => {
						game.grid.set({ row: rowIndex, column: colIndex }, false);
					}),
				);
			}}
		>
			<RefreshIcon class="size-5" /> Reset
		</button>
	</div>
</div>
