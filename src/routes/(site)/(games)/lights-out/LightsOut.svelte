<script lang="ts">
	import { onMount, onDestroy, untrack } from 'svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import {
		LightsOutGame,
		lightsOutDifficulties,
		lightsOutModes,
		type LightsOutDifficulty,
		type LightsOutMode,
	} from './LightsOutGame.svelte';
	import LightsOutGameRenderer from './LightsOutGameRenderer.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import { Smush32 } from '@thi.ng/random';
	import { Stats } from '$lib/util/Stats.svelte';
	import { lightsOutSoundResources } from './lightsOutSoundResources';
	import type { GridItem } from '$lib/models/GridItem';
	import InfoButton from '$lib/components/InfoButton.svelte';
	import LightsOutInfoModal from './LightsOutInfoModal.svelte';
	import LightsOutHowToPlayButton from './LightsOutHowToPlayButton.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';
	import LightsOutDifficultyButton, { difficultyTexts } from './LightsOutDifficultyButton.svelte';
	import LightsOutInstructionsIsland from './LightsOutInstructionsIsland.svelte';
	import LightsOutModeButton, { modeTexts } from './LightsOutModeButton.svelte';

	let isDifficultyDropdownOpen = $state(false);
	let isInfoModalOpen = $state(false);
	let canShowToast = $state(false);

	const context = new GameContext({
		gameName: 'Lights Out',
		gameKey: 'lights-out',
		GameClass: LightsOutGame,
		sounds: {
			resources: lightsOutSoundResources,
			lifecycle: {
				createGame: lightsOutSoundResources.play,
				win: lightsOutSoundResources.gameWin,
			},
		},
		settings: {
			defaultSettings: {
				mode: 'time' as LightsOutMode,
				difficulty: 'easy' as LightsOutDifficulty,
			},
		},
		stats({ props, context }) {
			const game = context.game!;

			return {
				stats: new Stats({
					...props,
					liveStats: {
						level: {
							name: 'Level',
							unit: 'plain',
							value() {
								return game.level;
							},
							metrics: {
								total: {
									key: 'totalLevel',
									name: 'Total Level',
								},
								average: {
									key: 'averageLevel',
									name: 'Average Level',
								},
								min: {
									key: 'worstLevel',
									name: 'Worst Level',
								},
								max: {
									key: 'highestLevel',
									name: 'Highest Level',
								},
							},
						},
						clicks: {
							name: 'Clicks',
							unit: 'plain',
							value() {
								return game.clicks;
							},
							metrics: {
								total: {
									key: 'totalClicks',
									name: 'Total Clicks',
								},
								average: {
									key: 'averageClicks',
									name: 'Average Clicks',
								},
								min: {
									key: 'fewestClicks',
									name: 'Fewest Clicks',
									useAsBest: true,
								},
								max: {
									key: 'maxClicks',
									name: 'Max Clicks',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'level'],
				}),
				visibleStats: ['highestLevel', 'averageLevel', 'wonGames', 'totalGames'],
				autoUpdateStatsGameState: (game) => (game.mode === 'time' ? 'lost' : 'won'),
				canUpdateWithGameLost(game) {
					return !game.gameOver && game.clicks > 0;
				},
			};
		},
		variants: {
			map: {
				difficulty: {
					allValues: lightsOutDifficulties,
					format: (difficulty) => difficultyTexts[difficulty],
				},
				mode: {
					allValues: lightsOutModes,
					format: (mode) => modeTexts[mode],
				},
			},
			fromGame(game) {
				return {
					difficulty: game.difficulty,
					mode: game.mode,
				};
			},
			getStatsVariant(variants) {
				if (variants.mode === 'time') {
					return `${variants.difficulty ?? 'normal'}`;
				}

				return `${variants.mode}-${variants.difficulty ?? 'normal'}`;
			},
			getLeaderboardVariant(variants) {
				if (variants.mode === 'time') {
					return `${variants.difficulty ?? 'normal'}`;
				}

				return `${variants.mode}-${variants.difficulty ?? 'normal'}`;
			},
		},
		defaultGameProps(context) {
			const settings = context.settingsManager.settings;

			return {
				mode: settings.mode,
				difficulty: settings.difficulty,
				seed: performance.now(),
				onNextLevel() {
					context.sounds.levelUp.play();
				},
			};
		},
		leaderboard({ props }) {
			return {
				leaderboard: new Leaderboard({
					...props,
					firstAvailableDate: new Date('2025/09/19'),
					order: 'higher-first',
					hasLevel: true,
					hasMoves: true,
					hasTime: false,
				}),
				sendScoreOn: ['won'],
				getScore(game) {
					return {
						score: game.score,
						level: game.level,
						moves: game.clicks,
					};
				},
			};
		},
		dailyGame() {
			return {
				type: 'seed',
				firstAvailableGameDate: new Date('2025/09/19'),
				toProps(seed: number) {
					const random = new Smush32(seed);
					const difficulty = random.int() % lightsOutDifficulties.length;

					return {
						difficulty: lightsOutDifficulties[difficulty],
						seed,
						mode: 'time' as LightsOutMode,
					};
				},
			};
		},
		onWillCreateGame({ newGameOptions, context }) {
			if (!newGameOptions.isDaily) {
				context.settingsManager.settings.mode = newGameOptions.mode ?? 'time';
				context.settingsManager.settings.difficulty = newGameOptions.difficulty;
			}

			if (newGameOptions.mode === 'time') {
				context.timer.countdownDuration = 120_000; // 2 minutes
			} else {
				context.timer.countdownDuration = null;
			}

			clearInterval(timerId);
		},
		onGameCreated({ context, newGameOptions }) {
			isDifficultyDropdownOpen = false;

			if (newGameOptions.mode === 'time') {
				timerId = setInterval(() => {
					if (context.timer.time <= 0) {
						game.gameOver = true;
						context.handleGameOver('won');
						clearInterval(timerId);
					}
				}, 100) as unknown as number;
			}
		},
		onDispose() {
			clearInterval(timerId);
		},
	});

	let game = $derived.by(() => {
		if (context.game) {
			return context.game;
		}
		return untrack(() => new LightsOutGame({ empty: true, difficulty: 'easy', mode: 'time' }));
	});
	let timerId = -1;

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});

	function onkeydown(e: KeyboardEvent) {
		if (e.key === ' ') {
			context.sounds?.switch1.play();
		}
	}

	function onkeyup(e: KeyboardEvent) {
		if (e.key === ' ') {
			context.sounds?.switch2.play();
		}
	}

	function onCellClick(gridItem: GridItem) {
		if (!context.timer.running) {
			context.timer.start();
		}

		game.handleUserClick(gridItem);
	}
</script>

<svelte:window {onkeydown} {onkeyup} />

<GameLayout>
	{#snippet Island()}
		<GameIsland
			{context}
			onVariantChange={(previous, current) => {
				if (previous === 'instructions' && current === 'live-stats') {
					canShowToast = true;
				}
			}}
		>
			{#snippet InstructionsIsland()}
				<LightsOutInstructionsIsland />
			{/snippet}
		</GameIsland>
	{/snippet}

	<div class="max-h-screen-no-navbar w-full max-w-xl flex flex-col items-start gap-2">
		<div class="flex items-center gap-2 w-full">
			<LightsOutHowToPlayButton {canShowToast} />

			<LightsOutDifficultyButton
				bind:open={isDifficultyDropdownOpen}
				difficulty={context.game?.difficulty ?? 'easy'}
				onChange={(difficulty) => context.createGame({ difficulty })}
			/>

			<LightsOutModeButton
				mode={context.game?.mode ?? 'time'}
				onSave={(mode) => context.createGame({ mode })}
			/>

			{#if context.game?.mode === 'infinite'}
				<button
					class="btn btn-sm btn-primary hidden xs:flex grow-[0.5]"
					disabled={!context.timer.running}
					onclick={() => context.handleGameOver('won')}
				>
					Stop
				</button>
			{/if}

			<InfoButton onclick={() => (isInfoModalOpen = true)} />

			<MoreGamesButton class="btn-sm" />
		</div>

		<LightsOutGameRenderer
			grid={game.grid}
			disabled={game.gameOver}
			{onCellClick}
			onPlayOnSound={() => context.sounds.switch2.play()}
			onPlayOffSound={() => context.sounds.switch1.play()}
		/>

		{#if context.game?.mode === 'infinite'}
			<button
				class="btn btn-md w-full mx-auto mt-4 btn-primary xs:hidden"
				disabled={!context.timer.running}
				onclick={() => context.handleGameOver('won')}
			>
				Stop
			</button>
		{/if}
	</div>
</GameLayout>

<LightsOutInfoModal bind:isOpen={isInfoModalOpen} />
