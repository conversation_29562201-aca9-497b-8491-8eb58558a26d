<script lang="ts">
	import PageTransition from '$lib/components/PageTransition.svelte';
	import Game from './2048.svelte';
	import { MetaTags } from 'svelte-meta-tags';
</script>

<MetaTags
	title="Play 2048 Game Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play 2048 game online for free. Beautiful 2048 game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/2048"
	openGraph={{
		url: 'https://www.lofiandgames.com/2048',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-2048.png',
				width: 1200,
				height: 630,
				alt: '2048 Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play 2048 Game on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-2048.png',
		site: 'https://www.lofiandgames.com/2048',
	}}
/>

<PageTransition>
	<Game />
</PageTransition>
