<script lang="ts">
	import { _2048SoundResources as sounds } from './_2048SoundResources';
	import InfoModal from '$lib/components/InfoModal.svelte';

	interface Props {
		isOpen?: boolean;
	}

	let { isOpen = $bindable(false) }: Props = $props();
</script>

<InfoModal {sounds} bind:isOpen>
	<h1>2048 Game</h1>

	<p>
		2048 is a popular puzzle game that was created by <PERSON><PERSON> in 2014. The game is
		played on a 4x4 grid and the goal is to merge tiles together to create the tile with the
		number 2048.
	</p>

	<h2>Rules</h2>

	<ul>
		<li>The game starts with two tiles on the grid.</li>

		<li>
			The player can move all tiles in the grid in four different directions (up, down, left,
			and right) by swiping their finger or using the arrow keys on a keyboard.
		</li>

		<li>
			When two tiles with the same number touch, they merge into one tile with the sum of
			their values. For example, two 2's will merge to create a 4.
		</li>

		<li>The player's goal is to keep merging tiles until they reach the 2048 tile.</li>
	</ul>
</InfoModal>
