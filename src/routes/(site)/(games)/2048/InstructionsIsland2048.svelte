<script lang="ts">
	import DirectionKeys from '$lib/components/DirectionKeys.svelte';
	import TouchIcon from '$lib/components/Icons/TouchIcon.svelte';
	import InstructionsIslandBase from '$lib/components/GameIsland/islands/InstructionsIslandBase.svelte';
</script>

{#snippet direction()}
	<DirectionKeys />
{/snippet}

{#snippet touch()}
	<TouchIcon class="size-16" />
{/snippet}

<InstructionsIslandBase
	goal="Join the numbers, get to 2048!"
	desktopControls={[
		{
			bindings: [direction],
		},
	]}
	mobileControls={[
		{
			bindings: [touch],
		},
	]}
/>
