<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full p-4', className)}
	fill="transparent"
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 498 498"
	{...props}
>
	<path class="fill-game-tents-floor" d="M56.831 82.417h92.528v92.528H56.831z" />
	<path class="fill-game-tents-grass" d="M153.985 82.418h92.528v92.528h-92.528z" />
	<path
		d="M218.412 102.08H182.9v52.047h54.361l-18.849-52.047Z"
		class="fill-game-tents-tent-100"
	/>
	<path d="m182.9 102.08 18.506 52.047h-37.011L182.9 102.08Z" class="fill-game-tents-tent-200" />
	<path d="m182.901 125.212 6.939 28.915h-13.879l6.94-28.915Z" class="fill-game-tents-tent-300" />
	<path
		class="fill-game-tents-grass"
		d="M251.14 82.417h92.528v92.528H251.14zm97.154.001h92.528v92.528h-92.528z"
	/>
	<path
		d="M412.719 102.08h-35.511v52.047h54.36l-18.849-52.047Z"
		class="fill-game-tents-tent-100"
	/>
	<path
		d="m377.209 102.08 18.505 52.047h-37.011l18.506-52.047Z"
		class="fill-game-tents-tent-200"
	/>
	<path d="m377.209 125.212 6.94 28.915H370.27l6.939-28.915Z" class="fill-game-tents-tent-300" />
	<path class="fill-game-tents-floor" d="M56.831 179.572h92.528V272.1H56.831z" />
	<path class="fill-game-tents-grass" d="M154 179.572h92.528V272.1H154z" />
	<path
		class="fill-game-tents-floor"
		d="M251.14 179.572h92.528V272.1H251.14zm97.154 0h92.528V272.1h-92.528zM56.831 276.726h92.528v92.528H56.831zm97.154 0h92.528v92.528h-92.528zm97.155 0h92.528v92.528H251.14z"
	/>
	<path class="fill-game-tents-grass" d="M348.294 276.726h92.528v92.528h-92.528z" />
	<path
		d="M412.719 296.389h-35.511v52.047h54.36l-18.849-52.047Z"
		class="fill-game-tents-tent-100"
	/>
	<path
		d="m377.209 296.389 18.505 52.047h-37.011l18.506-52.047Z"
		class="fill-game-tents-tent-200"
	/>
	<path d="m377.209 319.521 6.94 28.915H370.27l6.939-28.915Z" class="fill-game-tents-tent-300" />
	<path class="fill-game-tents-grass" d="M56.831 373.881h92.528v92.528H56.831z" />
	<path
		d="M121.257 393.543h-35.51v52.047h54.359l-18.849-52.047Z"
		class="fill-game-tents-tent-100"
	/>
	<path
		d="m85.746 393.543 18.505 52.047h-37.01l18.505-52.047Z"
		class="fill-game-tents-tent-200"
	/>
	<path d="m85.746 416.675 6.94 28.915h-13.88l6.94-28.915Z" class="fill-game-tents-tent-300" />
	<path class="fill-game-tents-grass" d="M153.985 373.881h92.528v92.528h-92.528z" />
	<path class="fill-game-tents-floor" d="M251.14 373.881h92.528v92.528H251.14z" />
	<path class="fill-game-tents-grass" d="M348.294 373.881h92.528v92.528h-92.528z" />
	<rect
		x="290.467"
		y="90.514"
		width="13.879"
		height="63.613"
		rx="2"
		class="fill-game-tents-tree-wood"
	/>
	<circle cx="297.984" cy="87.622" r="19.084" class="fill-game-tents-tree-leaves" />
	<circle cx="310.707" cy="106.128" r="19.084" class="fill-game-tents-tree-leaves" />
	<circle cx="284.105" cy="106.128" r="19.084" class="fill-game-tents-tree-leaves" />
	<rect
		x="193.312"
		y="381.977"
		width="13.879"
		height="63.613"
		rx="2"
		class="fill-game-tents-tree-wood"
	/>
	<circle cx="200.829" cy="379.086" r="19.084" class="fill-game-tents-tree-leaves" />
	<circle cx="213.552" cy="397.591" r="19.084" class="fill-game-tents-tree-leaves" />
	<circle cx="186.95" cy="397.591" r="19.084" class="fill-game-tents-tree-leaves" />
	<rect
		x="193.325"
		y="187.666"
		width="13.879"
		height="63.613"
		rx="2"
		class="fill-game-tents-tree-wood"
	/>
	<circle cx="200.843" cy="184.774" r="19.084" class="fill-game-tents-tree-leaves" />
	<circle cx="213.565" cy="203.279" r="19.084" class="fill-game-tents-tree-leaves" />
	<circle cx="186.964" cy="203.279" r="19.084" class="fill-game-tents-tree-leaves" />
	<rect
		x="387.619"
		y="381.977"
		width="13.879"
		height="63.613"
		rx="2"
		class="fill-game-tents-tree-wood"
	/>
	<circle cx="395.137" cy="379.086" r="19.084" class="fill-game-tents-tree-leaves" />
	<circle cx="407.859" cy="397.591" r="19.084" class="fill-game-tents-tree-leaves" />
	<circle cx="381.258" cy="397.591" r="19.084" class="fill-game-tents-tree-leaves" />
	<path
		d="M385.798 59.285v-2.944l9.108-9.437a50.3 50.3 0 0 0 2.406-2.694c.639-.78 1.117-1.52 1.432-2.221a5.374 5.374 0 0 0 .473-2.234c0-.894-.21-1.665-.63-2.314a4.128 4.128 0 0 0-1.722-1.511c-.727-.36-1.547-.54-2.458-.54-.964 0-1.805.198-2.523.592a4.055 4.055 0 0 0-1.657 1.67c-.385.718-.578 1.56-.578 2.523h-3.877c0-1.638.377-3.071 1.13-4.298.754-1.227 1.788-2.177 3.102-2.852 1.314-.683 2.808-1.025 4.482-1.025 1.691 0 3.181.337 4.469 1.012 1.296.666 2.308 1.577 3.036 2.734.727 1.148 1.091 2.444 1.091 3.89 0 1-.189 1.976-.566 2.931-.368.955-1.012 2.02-1.932 3.194-.92 1.165-2.199 2.58-3.837 4.245l-5.35 5.6v.196h12.118v3.483h-17.717Zm-87.801.447c-2.077-.009-3.851-.556-5.323-1.643-1.472-1.086-2.598-2.668-3.378-4.744-.78-2.077-1.17-4.579-1.17-7.505 0-2.918.39-5.41 1.17-7.479.788-2.068 1.919-3.645 3.391-4.731 1.481-1.087 3.251-1.63 5.31-1.63 2.059 0 3.824.548 5.296 1.643 1.472 1.086 2.598 2.664 3.378 4.731.789 2.06 1.183 4.548 1.183 7.466 0 2.935-.39 5.441-1.17 7.518-.779 2.068-1.905 3.65-3.377 4.744-1.472 1.087-3.242 1.63-5.31 1.63Zm0-3.51c1.822 0 3.246-.888 4.271-2.667 1.034-1.779 1.551-4.35 1.551-7.715 0-2.235-.236-4.123-.71-5.665-.464-1.55-1.134-2.725-2.011-3.522-.867-.806-1.901-1.21-3.101-1.21-1.814 0-3.238.894-4.272 2.682-1.034 1.787-1.555 4.359-1.564 7.715 0 2.243.232 4.14.697 5.69.473 1.543 1.143 2.713 2.011 3.51.867.789 1.91 1.183 3.128 1.183Zm-93.693-23.854v26.917h-4.075V36.442h-.157l-6.441 4.206v-3.89l6.717-4.39h3.956Zm-97.155 0v26.917h-4.074V36.442h-.158l-6.44 4.206v-3.89l6.716-4.39h3.956ZM22.625 142.561v-2.944l9.108-9.437a50.279 50.279 0 0 0 2.406-2.695c.64-.779 1.117-1.52 1.432-2.221a5.382 5.382 0 0 0 .474-2.234c0-.894-.21-1.665-.631-2.313a4.123 4.123 0 0 0-1.722-1.512c-.727-.359-1.547-.539-2.458-.539-.964 0-1.805.197-2.523.592-.719.394-1.27.95-1.656 1.669-.386.718-.579 1.56-.579 2.523H22.6c0-1.638.377-3.071 1.13-4.297.754-1.227 1.788-2.178 3.102-2.853 1.314-.683 2.808-1.025 4.482-1.025 1.691 0 3.18.338 4.469 1.012 1.296.666 2.309 1.578 3.036 2.734.727 1.148 1.09 2.445 1.09 3.891a7.92 7.92 0 0 1-.565 2.93c-.368.956-1.012 2.02-1.932 3.194-.92 1.166-2.199 2.581-3.837 4.245l-5.35 5.599v.198h12.118v3.483H22.625Zm8.197 97.601c-2.077-.009-3.851-.556-5.323-1.643-1.472-1.086-2.598-2.668-3.378-4.745-.78-2.076-1.17-4.578-1.17-7.504 0-2.918.39-5.411 1.17-7.479.789-2.068 1.919-3.645 3.39-4.731 1.482-1.087 3.252-1.63 5.31-1.63 2.06 0 3.825.547 5.297 1.643 1.472 1.086 2.598 2.663 3.378 4.731.789 2.059 1.183 4.548 1.183 7.466 0 2.935-.39 5.441-1.17 7.518-.78 2.067-1.905 3.649-3.377 4.744-1.472 1.087-3.242 1.63-5.31 1.63Zm0-3.509c1.822 0 3.246-.89 4.271-2.668 1.034-1.779 1.551-4.351 1.551-7.715 0-2.235-.236-4.123-.71-5.665-.464-1.551-1.134-2.725-2.01-3.522-.868-.807-1.902-1.21-3.102-1.21-1.814 0-3.238.894-4.272 2.682-1.034 1.787-1.555 4.359-1.564 7.715 0 2.243.232 4.14.697 5.691.473 1.542 1.143 2.712 2.01 3.509.868.788 1.91 1.183 3.129 1.183Zm4.618 73.299v26.918h-4.074v-22.843h-.158l-6.44 4.206v-3.891l6.716-4.39h3.956Zm0 97.155v26.917h-4.074v-22.843h-.158l-6.44 4.206v-3.89l6.716-4.39h3.956Z"
		fill="#8C919D"
	/>
</svg>
