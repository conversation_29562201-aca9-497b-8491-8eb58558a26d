import { get2DGrid } from '$lib/functions/get2DGrid';
import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import type { GridItem } from '$lib/models/GridItem';
import type { GridSize } from '$lib/models/GridSize';
import type { GameSound } from '$lib/util/GameSound.svelte';
import { Grid } from '$lib/util/Grid.svelte';
import { Timer } from '$lib/util/Timer.svelte';
import { Undoable } from '$lib/util/Undoable/Undoable.svelte';
import { Smush32 } from '@thi.ng/random';

export type TentsSounds = {
	replay: GameSound;
	gameWin: GameSound;
	placeTent: GameSound;
	removeTent: GameSound;
};

export const tentsDifficulties = ['easy', 'hard'] as const;

export type TentsDifficulty = (typeof tentsDifficulties)[number];

export enum TentsItem {
	Tent,
	Tree,
	Terrain,
}

const tentsPercentageMap: Record<TentsDifficulty, number> = {
	easy: 0.15,
	hard: 0.22,
};

const scoreFactorByDifficulty: Record<TentsDifficulty, number> = {
	easy: 1,
	hard: 2,
};

const autoFillPenalty = 10;
const zeroesPenalty = 5;

interface ValidateRowsAndColumnsParams {
	validateAmount: boolean;
}

const defaultValidationParams: ValidateRowsAndColumnsParams = {
	validateAmount: true,
};

interface TentsGameSettings {
	size: GridSize;
	difficulty: TentsDifficulty;
	empty?: boolean;
	seed?: number;
	runtimeSettings?: TentsRuntimeSettings;
	timer: Timer;
}

interface TentAmount {
	amount: number;
	hasError: boolean;
	hasReachedAmount: boolean;
}

interface TentsRuntimeSettings {
	autoFillZeros: boolean;
	autoFillNotAdjacentToTrees: boolean;
	autoFillTentsAmountReached: boolean;
	autoFillAroundTents: boolean;
	autoFill: boolean;
}

export class TentsGame {
	id = Symbol();
	board = $state() as Grid<TentsItem | null>;
	difficulty: TentsDifficulty = $state('easy');
	tentsAmountsOnRows: TentAmount[] = $state([]);
	tentsAmountsOnColumns: TentAmount[] = $state([]);
	isWon = $state(false);
	private _timer: Timer;
	private _startOverPenalty = 0;
	private _runtimeSettings: TentsRuntimeSettings = $state({
		autoFillZeros: false,
		autoFillNotAdjacentToTrees: false,
		autoFillTentsAmountReached: false,
		autoFillAroundTents: false,
		autoFill: false,
	});
	private placingItemStart: TentsItem | null = null;
	private history = $state() as Undoable<Grid<TentsItem | null>>;

	constructor({
		size,
		difficulty,
		empty = false,
		seed,
		runtimeSettings,
		timer,
	}: TentsGameSettings) {
		if (runtimeSettings) {
			this._runtimeSettings = runtimeSettings;
		}
		this._timer = timer;
		this.difficulty = difficulty;
		this.board = new Grid(size);
		this.history = new Undoable(this.board.clone());
		this.tentsAmountsOnRows = get2DGrid(1, size.rows, () => ({
			amount: 0,
			hasError: false,
			hasReachedAmount: false,
		}))[0];
		this.tentsAmountsOnColumns = get2DGrid(1, size.columns, () => ({
			amount: 0,
			hasError: false,
			hasReachedAmount: false,
		}))[0];
		if (!empty) {
			this.generateGame(seed);
		}
	}

	private checkGameWin() {
		if (this.tentsAmountsOnColumns.some(({ hasError }) => hasError)) {
			return false;
		}
		if (this.tentsAmountsOnRows.some(({ hasError }) => hasError)) {
			return false;
		}

		const hasTentError = this.board.grid.some((itemRow, row) => {
			return itemRow.some((item, column) => {
				if (item === TentsItem.Tent) {
					return this.hasError({ row, column });
				}
			});
		});

		if (hasTentError) {
			return false;
		}

		// Check tents count
		const hasAllTentsOnRows = this.tentsAmountsOnRows.every(
			({ amount }, row) => this.countTentsOnRow(row) === amount,
		);
		const hasAllTentsColumns = this.tentsAmountsOnColumns.every(
			({ amount }, column) => this.countTentsOnColumn(column) === amount,
		);

		if (!hasAllTentsOnRows || !hasAllTentsColumns) {
			return false;
		}

		// Check if all tents match all trees
		return this.recursiveCheckGameWin();
	}

	/** Check tree to tent pairity, given that all placed tents are valid */
	private recursiveCheckGameWin(
		trees: GridItem[] = this.board.grid
			.flatMap((rowItem, row) => {
				return rowItem.flatMap((item, column) => {
					return item === TentsItem.Tree ? { row, column } : null;
				});
			})
			.filter(Boolean) as GridItem[],
		pickedTents: GridItem[] = [],
		treeIndex = 0,
	): boolean {
		if (treeIndex === trees.length) {
			return pickedTents.length === trees.length;
		}

		const tree = trees[treeIndex];
		const tentCanditates = this.board
			.getItemsAround(tree, { diagonal: false })
			.filter(({ row, column }) => this.board.grid[row][column] === TentsItem.Tent)
			.filter((tent) => {
				return !pickedTents.some(
					(pickedTent) =>
						pickedTent.row === tent.row && pickedTent.column === tent.column,
				);
			});

		if (tentCanditates.length === 0) {
			return false;
		}

		for (let i = 0; i < tentCanditates.length; i += 1) {
			if (
				this.recursiveCheckGameWin(
					trees,
					[...pickedTents, tentCanditates[i]],
					treeIndex + 1,
				)
			) {
				return true;
			}
		}
		return false;
	}

	private generateGame(seed?: number): void {
		const random = new Smush32(seed);
		const randomFunc = seed !== undefined ? () => random.float() : Math.random;
		const tentsPercentage = tentsPercentageMap[this.difficulty];
		const boardSize = this.board.size.rows * this.board.size.columns;
		const tentsToPlace = Math.ceil(tentsPercentage * boardSize);
		const maxAttempts = 100;
		const rowsToAvoidPlacingTents = [];
		const columnsToAvoidPlacingTents = [];

		// Adjust generated game to the specified difficulty
		if (this.difficulty === 'easy') {
			rowsToAvoidPlacingTents.push(Math.floor(randomFunc() * this.board.rows));
			columnsToAvoidPlacingTents.push(Math.floor(randomFunc() * this.board.columns));
		}

		let placedTents = 0;
		let attempts = 0;

		while (placedTents < tentsToPlace && attempts < maxAttempts) {
			const randomItem = this.board.random(randomFunc);

			if (
				rowsToAvoidPlacingTents.some((row) => row === randomItem.at.row) ||
				columnsToAvoidPlacingTents.some((column) => column === randomItem.at.column)
			) {
				attempts += 1;
				continue;
			}

			if (this.placeTentAndTree(randomItem.at, randomFunc)) {
				placedTents += 1;
			}

			attempts += 1;
		}

		// Adjust hard game to the difficulty by trying
		// to fill empty rows and columns
		if (this.difficulty === 'hard') {
			this.updateRowsAndColumnsCount();

			this.tentsAmountsOnRows.forEach(({ amount }, row) => {
				if (amount === 0) {
					Array.from({ length: this.board.columns })
						.fill(0)
						.forEach((_, column) => {
							this.placeTentAndTree({ row, column }, randomFunc);
						});
				}
			});
			this.tentsAmountsOnColumns.forEach(({ amount }, column) => {
				if (amount === 0) {
					Array.from({ length: this.board.rows })
						.fill(0)
						.forEach((_, row) => {
							this.placeTentAndTree({ row, column }, randomFunc);
						});
				}
			});

			this.updateRowsAndColumnsCount();

			if (
				this.tentsAmountsOnRows.filter(({ amount }) => amount === 0).length +
					this.tentsAmountsOnColumns.filter(({ amount }) => amount === 0).length >
				2
			) {
				// Throw it away, the game is too easy
				this.reset();
				return this.generateGame(seed !== undefined ? seed + 1 : undefined);
			}
		}

		this.updateRowsAndColumnsCount();

		// Remove tents
		this.removeTents();
		this.history.reset(this.board.clone());
	}

	get runtimeSettings() {
		return this._runtimeSettings;
	}

	set runtimeSettings(newRuntimeSettings: TentsRuntimeSettings) {
		this._runtimeSettings = newRuntimeSettings;
	}

	/** Lower is better */
	getScore() {
		const scoreFactor = scoreFactorByDifficulty[this.difficulty];
		const tents = this.itemsAmounts.tents;
		const fillPenalty =
			autoFillPenalty *
			(this.runtimeSettings.autoFill
				? [
						this.runtimeSettings.autoFillZeros ? 1 : 0,
						this.runtimeSettings.autoFillNotAdjacentToTrees ? 1 : 0,
						this.runtimeSettings.autoFillTentsAmountReached ? 1 : 0,
						this.runtimeSettings.autoFillAroundTents ? 1 : 0,
					]
				: []
			).filter(Boolean).length;

		const zeroes =
			this.tentsAmountsOnRows.filter((amount) => amount.amount === 0).length +
			this.tentsAmountsOnColumns.filter((amount) => amount.amount === 0).length;

		return Math.floor(
			Math.max(
				0,
				(Math.floor(this._timer.elapsedTime / 100) -
					tents * 3 +
					fillPenalty +
					zeroes * zeroesPenalty) /
					scoreFactor +
					this._startOverPenalty,
			),
		);
	}

	get itemsAmounts() {
		let tentsAmount = 0;
		let treesAmount = 0;

		this.board.grid.forEach((rowItems) => {
			rowItems.forEach((item) => {
				if (item === TentsItem.Tent) {
					tentsAmount += 1;
				} else if (item === TentsItem.Tree) {
					treesAmount += 1;
				}
			});
		});

		return {
			trees: treesAmount,
			tents: tentsAmount,
		};
	}

	hasAutoGrass(at: GridItem): boolean {
		if (!this._runtimeSettings.autoFill) {
			return false;
		}

		const item = this.board.at(at);

		if (item !== null && item !== TentsItem.Terrain) {
			return false;
		}

		if (
			this.runtimeSettings.autoFillZeros &&
			(this.tentsAmountsOnRows[at.row].amount === 0 ||
				this.tentsAmountsOnColumns[at.column].amount === 0)
		) {
			return true;
		}

		if (
			this.runtimeSettings.autoFillTentsAmountReached &&
			(this.tentsAmountsOnRows[at.row].hasReachedAmount ||
				this.tentsAmountsOnColumns[at.column].hasReachedAmount)
		) {
			return true;
		}

		if (this.runtimeSettings.autoFillNotAdjacentToTrees) {
			if (
				this.board
					.getItemsAround(at, { diagonal: false })
					.every((gridItem) => this.board.at(gridItem) !== TentsItem.Tree)
			) {
				return true;
			}
		}

		if (this.runtimeSettings.autoFillAroundTents) {
			if (
				this.board
					.getItemsAround(at, { diagonal: true })
					.some((gridItem) => this.board.at(gridItem) === TentsItem.Tent)
			) {
				return true;
			}
		}

		return false;
	}

	hasError(at: GridItem): boolean {
		// Check tents around
		const allItemsAround = this.board.getItemsAround(at, { diagonal: true });

		const hasTentsAround = allItemsAround.some((at) => {
			return this.board.at(at) === TentsItem.Tent;
		});

		if (hasTentsAround) {
			return true;
		}

		// Check trees around

		const itemsAroundOnAxis = this.board.getItemsAround(at, { diagonal: false });

		const hasTreesAround = itemsAroundOnAxis.some((at) => {
			return this.board.at(at) === TentsItem.Tree;
		});

		return !hasTreesAround;
	}

	placeExpectedItem(at: GridItem): {
		item: TentsItem | null;
		previousItem: TentsItem | null;
		success: boolean;
	} {
		const tentsItem = this.board.at(at);
		const previousItem = tentsItem;

		if (tentsItem === TentsItem.Tree) {
			this.placingItemStart = TentsItem.Tree;
			return {
				previousItem,
				item: null,
				success: false,
			};
		}

		let itemToPlace: TentsItem | null = null;

		if (tentsItem === null) {
			itemToPlace = TentsItem.Tent;
		} else if (tentsItem === TentsItem.Tent) {
			itemToPlace = TentsItem.Terrain;
		} else if (tentsItem === TentsItem.Terrain) {
			itemToPlace = null;
		}

		if (this.place(itemToPlace, at)) {
			this.placingItemStart = itemToPlace;
			this.isWon = this.checkGameWin();
			return {
				previousItem,
				item: itemToPlace,
				success: true,
			};
		}

		return {
			previousItem,
			item: null,
			success: false,
		};
	}

	continuePlacing(at: GridItem): boolean {
		const tentsItem = this.board.at(at);

		if (tentsItem === TentsItem.Tree || tentsItem === TentsItem.Tent) {
			return false;
		}

		if (
			this.placingItemStart === TentsItem.Tent ||
			this.placingItemStart === TentsItem.Terrain
		) {
			if (tentsItem === null) {
				return this.place(TentsItem.Terrain, at);
			}
		}

		if (this.placingItemStart === null && tentsItem === TentsItem.Terrain) {
			return this.place(null, at);
		}

		return false;
	}

	startOver() {
		this._startOverPenalty = this.getScore();
		this.board.grid.forEach((rowItems, row) => {
			rowItems.forEach((item, column) => {
				if (item !== TentsItem.Tree) {
					this.board.grid[row][column] = null;
				}
			});
		});
		this.checkAllRowsAndColumns();
		this.history.reset(this.board.clone());
		this.isWon = false;
	}

	canUndo() {
		if (this.isWon) {
			return false;
		}

		return this.history.canUndo();
	}

	canRedo() {
		if (this.isWon) {
			return false;
		}
		return this.history.canRedo();
	}

	undo() {
		if (this.canUndo()) {
			this.history.undo();
			this.board = this.history.state.clone();
			this.checkAllRowsAndColumns();
		}
	}

	redo() {
		if (this.canRedo()) {
			this.history.redo();
			this.board = this.history.state.clone();
			this.checkAllRowsAndColumns();
		}
	}

	commitToHistory() {
		this.history.add(this.board.clone());
		this.checkGameWin();
	}

	private reset() {
		this.board.reset();
		this.tentsAmountsOnRows.forEach((tents) => {
			tents.amount = 0;
			tents.hasError = false;
		});
		this.tentsAmountsOnColumns.forEach((tents) => {
			tents.amount = 0;
			tents.hasError = false;
		});
		this.isWon = false;
		this.placingItemStart = null;
		this.history.reset(this.board.clone());
	}

	private removeTents() {
		this.board.grid.map((itemRow, row) => {
			itemRow.map((item, column) => {
				if (item === TentsItem.Tent) {
					this.board.grid[row][column] = null;
				}
			});
		});
	}

	private updateRowsAndColumnsCount(
		{ validateAmount }: ValidateRowsAndColumnsParams = {
			validateAmount: true,
		},
	) {
		this.tentsAmountsOnRows = this.board.grid.map((_, row) => {
			const tentCount = this.countTentsOnRow(row);

			return {
				amount: tentCount,
				hasError: false,
				hasReachedAmount: validateAmount ? tentCount === 0 : false,
			};
		});

		this.tentsAmountsOnColumns = Array.from({ length: this.board.columns })
			.fill(0)
			.map((_, column) => {
				const tentCount = this.countTentsOnColumn(column);

				return {
					amount: tentCount,
					hasError: false,
					hasReachedAmount: validateAmount ? tentCount === 0 : false,
				};
			});
	}

	private placeTentAndTree(at?: GridItem, randomFunc?: () => number): boolean {
		const tentPosition = at ? { at, value: this.board.at(at) } : this.board.random(randomFunc);

		if (tentPosition.value !== null) {
			return false;
		}

		const hasTentsAround = this.board
			.getItemsAround(tentPosition.at, { diagonal: true })
			.some((position) => this.board.at(position) === TentsItem.Tent);

		if (hasTentsAround) {
			return false;
		}

		const treePositions = this.board
			.getItemsAround(tentPosition.at, { diagonal: false })
			.filter((position) => {
				return this.board.at(position) === null;
			});

		if (treePositions.length === 0) {
			return false;
		}

		// Place tent and tree
		this.place(TentsItem.Tent, tentPosition.at);
		this.place(TentsItem.Tree, getRandomItemAt(treePositions, randomFunc));

		return true;
	}

	private place(value: TentsItem | null, at: GridItem): boolean {
		const result = this.board.set(at, value);

		if (result) {
			this.checkRowAndColumn(at);
		}
		return result;
	}

	private checkAllRowsAndColumns(params: ValidateRowsAndColumnsParams = defaultValidationParams) {
		this.board.grid.forEach((_, row) => {
			this.checkRow(row, params);
		});
		this.board.grid[0].forEach((_, column) => {
			this.checkColumn(column, params);
		});
	}

	private checkRowAndColumn(
		at: GridItem,
		params: ValidateRowsAndColumnsParams = defaultValidationParams,
	) {
		this.checkRow(at.row, params);
		this.checkColumn(at.column, params);
	}

	private checkRow(row: number, params: ValidateRowsAndColumnsParams = defaultValidationParams) {
		if (this.board.isOutOfBounds({ row })) {
			return;
		}

		const tentsOnRow = this.countTentsOnRow(row);

		this.tentsAmountsOnRows[row].hasError = tentsOnRow > this.tentsAmountsOnRows[row].amount;

		if (params?.validateAmount) {
			this.tentsAmountsOnRows[row].hasReachedAmount =
				tentsOnRow === this.tentsAmountsOnRows[row].amount;
		}
	}

	private checkColumn(
		column: number,
		params: ValidateRowsAndColumnsParams = defaultValidationParams,
	) {
		if (this.board.isOutOfBounds({ column })) {
			return;
		}

		const tentsOnColumn = this.countTentsOnColumn(column);

		this.tentsAmountsOnColumns[column].hasError =
			tentsOnColumn > this.tentsAmountsOnColumns[column].amount;
		if (params?.validateAmount) {
			this.tentsAmountsOnColumns[column].hasReachedAmount =
				tentsOnColumn === this.tentsAmountsOnColumns[column].amount;
		}
	}

	private countTentsOnRow(row: number) {
		return this.board.grid[row].reduce((acc, item) => {
			if (item === TentsItem.Tent) {
				return acc + 1;
			}

			return acc;
		}, 0);
	}

	private countTentsOnColumn(column: number) {
		return this.board.grid.reduce((acc, row) => {
			const item = row[column];

			if (item === TentsItem.Tent) {
				return acc + 1;
			}

			return acc;
		}, 0);
	}

	static from(grid: (TentsItem | null)[][], params?: ValidateRowsAndColumnsParams) {
		const game = new TentsGame({
			size: {
				rows: grid.length,
				columns: grid[0]?.length ?? 0,
			},
			difficulty: 'easy',
			empty: true,
			timer: new Timer(),
		});

		game.board.grid = grid;
		game.updateRowsAndColumnsCount(params);
		game.checkAllRowsAndColumns(params);
		game.history.reset(game.board);

		return game;
	}
}
