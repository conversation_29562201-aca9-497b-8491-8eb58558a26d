<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import type { TentsGame } from './TentsGame.svelte';
	import { TentsItem } from './TentsGame.svelte';
	import type { GridItem } from '$lib/models/GridItem';

	interface Props {
		class?: string;
		game: TentsGame;
		onpointermove?: (event: PointerEvent) => void;
		onCellClick?: (item: GridItem) => void;
	}

	let { class: classFromProps = '', game, onpointermove, onCellClick }: Props = $props();

	const gap = 0.5;
	let size = $derived(Math.min(100 / (game.board.rows + 1), 100 / (game.board.columns + 1)));
	let treeDy = $derived(size * 0.2);
	let width = $derived(size - 2 * gap);
	let height = $derived(width);
	let fontSize = $derived(8 * (6 / Math.max(game.board.rows + 1, game.board.columns + 1)));
	let isA11yEnabled = $state(true);
</script>

<svelte:document
	onmousemove={() => (isA11yEnabled = false)}
	onkeydown={(e) => {
		if (e.key === 'Tab') {
			isA11yEnabled = true;
		}
	}}
/>

<svg
	class={twMerge('size-full aspect-square select-none', classFromProps)}
	viewBox="{size / 4} 0 {game.board.columns * size + size} {(game.board.rows + 1) * size}"
	{onpointermove}
>
	<!-- Tent column amounts -->
	{#each game.tentsAmountsOnColumns as tentsOnColumn, i}
		{@const x = (i + 1) * size + size / 2}
		{@const y = size / 2}
		<text
			{x}
			{y}
			dominant-baseline="middle"
			text-anchor="middle"
			class={twMerge(
				'fill-current',
				tentsOnColumn.hasError
					? 'fill-game-tents-number-error'
					: tentsOnColumn.hasReachedAmount
						? 'fill-game-tents-number-success'
						: '',
			)}
			font-size={fontSize}
		>
			{tentsOnColumn.amount}
		</text>
	{/each}

	<!-- Tent row amounts -->
	{#each game.tentsAmountsOnRows as tentsOnRow, j}
		{@const x = size / 2}
		{@const y = (j + 1) * size + size / 2}

		<text
			{x}
			{y}
			font-size={fontSize}
			alignment-baseline="middle"
			text-anchor="middle"
			class={twMerge(
				'fill-current',
				tentsOnRow.hasError
					? 'fill-game-tents-number-error'
					: tentsOnRow.hasReachedAmount
						? 'fill-game-tents-number-success'
						: '',
			)}
		>
			{tentsOnRow.amount}
		</text>
	{/each}

	<!-- Game -->
	{#each game.board.grid as row, j}
		{#each row as item, i}
			{@const x = (i + 1) * size}
			{@const y = (j + 1) * size}

			<!-- svelte-ignore a11y_no_noninteractive_tabindex -->
			<g
				role={isA11yEnabled ? 'button' : 'none'}
				tabindex={isA11yEnabled ? 0 : null}
				aria-label={`Cell (${j + 1}, ${i + 1}) ${item === TentsItem.Tent ? 'with a tent' : item === TentsItem.Tree ? 'with a tree' : 'is empty'}`}
				onkeydown={(e) => {
					if (e.key === 'Enter' || e.key === ' ') {
						e.stopPropagation();
						onCellClick?.({ row: j, column: i });
					}
				}}
				class="focus-within:outline-yellow-400 rounded-md"
			>
				{#if item === TentsItem.Tent || item === TentsItem.Tree || item === TentsItem.Terrain}
					<rect
						{x}
						{y}
						{width}
						{height}
						class="fill-game-tents-grass"
						data-row={j}
						data-column={i}
					/>
				{:else if game.runtimeSettings.autoFill && game.hasAutoGrass({ row: j, column: i })}
					<rect
						{x}
						{y}
						{width}
						{height}
						class="fill-game-tents-grass-autofill"
						data-row={j}
						data-column={i}
					/>
				{:else}
					<rect
						{x}
						{y}
						{width}
						{height}
						data-row={j}
						data-column={i}
						class="fill-game-tents-floor"
					/>
				{/if}
			</g>

			{#if item === TentsItem.Tent}
				<use
					xlink:href={game.hasError({ row: j, column: i }) ? '#tent-error' : '#tent'}
					{x}
					{y}
					{width}
					{height}
					data-row={j}
					data-column={i}
				/>
			{/if}

			{#if item === TentsItem.Tree}
				<use
					class="pointer-events-none"
					xlink:href="#tree"
					{x}
					y={y - treeDy}
					{width}
					{height}
				/>
			{/if}
		{/each}
	{/each}

	<symbol id="tent" viewBox="0 0 80 80">
		<path d="M55.703 17H25v45h47L55.703 17Z" class="fill-game-tents-tent-100" />
		<path d="m25 17 16 45H9l16-45Z" class="fill-game-tents-tent-200" />
		<path d="m25 37 6 25H19l6-25Z" class="fill-game-tents-tent-300" />
	</symbol>

	<symbol id="tent-error" viewBox="0 0 80 80">
		<path d="M55.703 17H25v45h47L55.703 17Z" class="fill-game-tents-tent-error-100" />
		<path d="m25 17 16 45H9l16-45Z" class="fill-game-tents-tent-error-200" />
		<path d="m25 37 6 25H19l6-25Z" class="fill-game-tents-tent-error-300" />
		<circle cx="63" cy="17" r="13" class="fill-game-tents-tent-error-indicator-stroke" />
		<path
			d="M73.5 17c0 5.799-4.701 10.5-10.5 10.5S52.5 22.799 52.5 17 57.201 6.5 63 6.5 73.5 11.201 73.5 17ZM63 20.167a1.722 1.722 0 1 0 0 3.444 1.722 1.722 0 0 0 0-3.444Zm0-.223c.951 0 1.722-.77 1.722-1.722v-6.11a1.722 1.722 0 0 0-3.444 0v6.11c0 .951.77 1.723 1.722 1.723Z"
			class="fill-game-tents-tent-error-indicator"
		/>
	</symbol>

	<symbol id="tree" viewBox="0 0 80 80">
		<rect x="34" y="22" width="12" height="55" rx="2" class="fill-game-tents-tree-wood" />
		<circle cx="40.5" cy="19.5" r="16.5" class="fill-game-tents-tree-leaves" />
		<circle cx="51.5" cy="35.5" r="16.5" class="fill-game-tents-tree-leaves" />
		<circle cx="28.5" cy="35.5" r="16.5" class="fill-game-tents-tree-leaves" />
	</symbol>
</svg>
