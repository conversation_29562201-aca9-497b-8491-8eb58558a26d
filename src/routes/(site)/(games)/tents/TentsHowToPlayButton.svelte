<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';
	import { untrack } from 'svelte';
	import { TentsGame, TentsItem } from './TentsGame.svelte';
	import TentsGameRenderer from './TentsGameRenderer.svelte';

	let isOpen = $state(false);
</script>

<button onclick={() => (isOpen = true)} class="btn btn-sm">
	How <span class="hidden md:inline">to play</span>
</button>

<Dialog bind:isOpen>
	<article>
		<h2>How to Play Tents</h2>

		<p>
			Each tent must be placed adjacent to a tree (either above, below, left, or right of the
			tree). It should be a 1 to 1 relation, so one tent can be adjacent to many trees, but it
			will be related to just one of them.
		</p>

		<div class="grid grid-cols-2 gap-2 max-w-xs mx-auto">
			<TentsGameRenderer
				game={untrack(() => {
					return TentsGame.from(
						[
							[null, TentsItem.Tent, null],
							[null, TentsItem.Tree, null],
							[null, null, null],
						],
						{ validateAmount: false },
					);
				})}
			/>
			<TentsGameRenderer
				game={untrack(() =>
					TentsGame.from(
						[
							[null, null, null],
							[null, TentsItem.Tree, TentsItem.Tent],
							[null, null, null],
						],
						{ validateAmount: false },
					),
				)}
			/>

			<TentsGameRenderer
				game={untrack(() =>
					TentsGame.from(
						[
							[null, null, null],
							[null, TentsItem.Tree, null],
							[null, TentsItem.Tent, null],
						],
						{ validateAmount: false },
					),
				)}
			/>
			<TentsGameRenderer
				game={untrack(() =>
					TentsGame.from(
						[
							[null, null, null],
							[TentsItem.Tent, TentsItem.Tree, null],
							[null, null, null],
						],
						{ validateAmount: false },
					),
				)}
			/>
		</div>

		<p>No two tents can be adjacent to each other, not even diagonally.</p>

		<div class="grid grid-cols-3 gap-2 max-w-sm mx-auto">
			<TentsGameRenderer
				game={untrack(() =>
					TentsGame.from(
						[
							[TentsItem.Tent, null],
							[TentsItem.Tent, null],
						],
						{ validateAmount: false },
					),
				)}
			/>
			<TentsGameRenderer
				game={untrack(() =>
					TentsGame.from(
						[
							[null, TentsItem.Tent],
							[TentsItem.Tent, null],
						],
						{ validateAmount: false },
					),
				)}
			/>
			<TentsGameRenderer
				game={untrack(() =>
					TentsGame.from(
						[
							[null, null],
							[TentsItem.Tent, TentsItem.Tent],
						],
						{ validateAmount: false },
					),
				)}
			/>
		</div>

		<p>
			The number of tents placed in each row and column must match the numbers given at the
			edges of the grid.
		</p>

		<TentsGameRenderer
			class="max-w-52 mx-auto"
			game={untrack(() =>
				TentsGame.from(
					[
						[null, TentsItem.Tent, TentsItem.Tree, TentsItem.Tent],
						[null, TentsItem.Tree, null, null],
						[null, null, null, TentsItem.Tent],
						[TentsItem.Tent, TentsItem.Tree, null, TentsItem.Tree],
					],
					{ validateAmount: true },
				),
			)}
		/>

		<h3>Tip</h3>

		<p>
			Mark cells that cannot contain tents as grass. This helps you narrow down possible tent
			placements and prevents mistakes.
		</p>

		<TentsGameRenderer
			class="max-w-52 mx-auto"
			game={untrack(() =>
				TentsGame.from(
					[
						[TentsItem.Tree, TentsItem.Tent, TentsItem.Terrain],
						[TentsItem.Terrain, TentsItem.Terrain, TentsItem.Terrain],
						[null, null, null],
					],
					{ validateAmount: false },
				),
			)}
		/>

		<p>Have fun!</p>
	</article>
</Dialog>
