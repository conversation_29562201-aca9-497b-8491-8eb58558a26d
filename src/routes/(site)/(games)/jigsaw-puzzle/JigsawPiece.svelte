<script lang="ts">
	import type { Context, SceneContext } from 'konva/lib/Context';
	import { Group, Image, Shape, type KonvaDragTransformEvent } from 'svelte-konva';
	import type { Size } from '$lib/models/Size';
	import type { Box2D } from '$lib/models/Box2D';
	import { theme } from '$lib/stores/theme.svelte';

	interface Props {
		image: HTMLImageElement;
		imageDimensions: Box2D;
		rows: number;
		cols: number;
		row: number;
		col: number;
		objectFit?: 'cover' | 'contain';
		seed: number;
		container: Size;
		visible?: boolean;
		draggable?: boolean;
		listening?: boolean;
		x?: number;
		y?: number;
		glow?: boolean;
		strokeWidth?: number;
		ondragstart?: (e: KonvaDragTransformEvent) => void;
		ondragend?: (e: KonvaDragTransformEvent) => void;
	}

	let {
		image,
		rows,
		cols,
		row,
		col,
		seed,
		visible,
		draggable,
		listening,
		x = $bindable(),
		y = $bindable(),
		imageDimensions,
		glow,
		ondragstart,
		ondragend,
		strokeWidth = 1,
	}: Props = $props();

	// Function to draw a jigsaw piece shape
	function drawJigsawPiece(
		ctx: SceneContext | Context,
		x: number,
		y: number,
		width: number,
		height: number,
		row: number,
		col: number,
		rows: number,
		cols: number,
		seed: number = 0,
	) {
		// Predefined curve coordinates for smooth jigsaw piece edges (less curvy, bigger insets)
		const curvyCoords = [
			0, 0, 35, 8, 37, 3, 37, 3, 40, 0, 38, -3, 38, -3, 20, -20, 50, -20, 50, -20, 80, -20,
			62, -3, 62, -3, 60, 0, 63, 3, 63, 3, 65, 8, 100, 0,
		];

		// Calculate tile ratio based on piece dimensions
		const tileRatio = Math.min(width, height) / 100;

		// Determine tab directions for each edge
		// Edge pieces have no tabs (0), internal pieces have tabs (1 or -1)
		// Each edge between pieces has a unique ID, and adjacent pieces reference the same edge

		// Horizontal edges: identified by the row of the edge and column
		const topEdgeId = row * cols + col + seed;
		const bottomEdgeId = (row + 1) * cols + col + seed;

		// Vertical edges: identified by row and the column of the edge
		const leftEdgeId = row * cols + col + 100000 + seed;
		const rightEdgeId = row * cols + (col + 1) + 100000 + seed;

		// Generate tab directions - adjacent pieces get opposite values for shared edges
		const topTab = row === 0 ? 0 : getTabDirection(topEdgeId);
		const bottomTab = row === rows - 1 ? 0 : -getTabDirection(bottomEdgeId);
		const leftTab = col === 0 ? 0 : getTabDirection(leftEdgeId);
		const rightTab = col === cols - 1 ? 0 : -getTabDirection(rightEdgeId);

		ctx.beginPath();

		// Start at top-left corner
		const topLeftEdge = { x, y };
		ctx.moveTo(topLeftEdge.x, topLeftEdge.y);

		// Top edge - scale curve to fit width
		const widthRatio = width / 100;
		if (topTab === 0) {
			// Draw straight line for edge pieces
			ctx.lineTo(topLeftEdge.x + width, topLeftEdge.y);
		} else {
			// Draw curved edge with tabs
			for (let i = 0; i < curvyCoords.length / 6; i++) {
				const p1x = topLeftEdge.x + curvyCoords[i * 6 + 0] * widthRatio;
				const p1y = topLeftEdge.y + topTab * curvyCoords[i * 6 + 1] * tileRatio;
				const p2x = topLeftEdge.x + curvyCoords[i * 6 + 2] * widthRatio;
				const p2y = topLeftEdge.y + topTab * curvyCoords[i * 6 + 3] * tileRatio;
				const p3x = topLeftEdge.x + curvyCoords[i * 6 + 4] * widthRatio;
				const p3y = topLeftEdge.y + topTab * curvyCoords[i * 6 + 5] * tileRatio;

				ctx.bezierCurveTo(p1x, p1y, p2x, p2y, p3x, p3y);
			}
		}

		// Right edge - scale curve to fit height
		const topRightEdge = { x: topLeftEdge.x + width, y: topLeftEdge.y };
		const heightRatio = height / 100;
		if (rightTab === 0) {
			// Draw straight line for edge pieces
			ctx.lineTo(topRightEdge.x, topRightEdge.y + height);
		} else {
			// Draw curved edge with tabs
			for (let i = 0; i < curvyCoords.length / 6; i++) {
				const p1x = topRightEdge.x - rightTab * curvyCoords[i * 6 + 1] * tileRatio;
				const p1y = topRightEdge.y + curvyCoords[i * 6 + 0] * heightRatio;
				const p2x = topRightEdge.x - rightTab * curvyCoords[i * 6 + 3] * tileRatio;
				const p2y = topRightEdge.y + curvyCoords[i * 6 + 2] * heightRatio;
				const p3x = topRightEdge.x - rightTab * curvyCoords[i * 6 + 5] * tileRatio;
				const p3y = topRightEdge.y + curvyCoords[i * 6 + 4] * heightRatio;

				ctx.bezierCurveTo(p1x, p1y, p2x, p2y, p3x, p3y);
			}
		}

		// Bottom edge - scale curve to fit width
		const bottomRightEdge = { x: topRightEdge.x, y: topRightEdge.y + height };
		if (bottomTab === 0) {
			// Draw straight line for edge pieces
			ctx.lineTo(bottomRightEdge.x - width, bottomRightEdge.y);
		} else {
			// Draw curved edge with tabs
			for (let i = 0; i < curvyCoords.length / 6; i++) {
				const p1x = bottomRightEdge.x - curvyCoords[i * 6 + 0] * widthRatio;
				const p1y = bottomRightEdge.y - bottomTab * curvyCoords[i * 6 + 1] * tileRatio;
				const p2x = bottomRightEdge.x - curvyCoords[i * 6 + 2] * widthRatio;
				const p2y = bottomRightEdge.y - bottomTab * curvyCoords[i * 6 + 3] * tileRatio;
				const p3x = bottomRightEdge.x - curvyCoords[i * 6 + 4] * widthRatio;
				const p3y = bottomRightEdge.y - bottomTab * curvyCoords[i * 6 + 5] * tileRatio;

				ctx.bezierCurveTo(p1x, p1y, p2x, p2y, p3x, p3y);
			}
		}

		// Left edge - scale curve to fit height
		const bottomLeftEdge = { x: bottomRightEdge.x - width, y: bottomRightEdge.y };
		if (leftTab === 0) {
			// Draw straight line for edge pieces
			ctx.lineTo(bottomLeftEdge.x, bottomLeftEdge.y - height);
		} else {
			// Draw curved edge with tabs
			for (let i = 0; i < curvyCoords.length / 6; i++) {
				const p1x = bottomLeftEdge.x + leftTab * curvyCoords[i * 6 + 1] * tileRatio;
				const p1y = bottomLeftEdge.y - curvyCoords[i * 6 + 0] * heightRatio;
				const p2x = bottomLeftEdge.x + leftTab * curvyCoords[i * 6 + 3] * tileRatio;
				const p2y = bottomLeftEdge.y - curvyCoords[i * 6 + 2] * heightRatio;
				const p3x = bottomLeftEdge.x + leftTab * curvyCoords[i * 6 + 5] * tileRatio;
				const p3y = bottomLeftEdge.y - curvyCoords[i * 6 + 4] * heightRatio;

				ctx.bezierCurveTo(p1x, p1y, p2x, p2y, p3x, p3y);
			}
		}

		ctx.closePath();
	}

	// Helper function to determine tab direction for consistent adjacent pieces
	function getTabDirection(seed: number): number {
		// Use a simple pseudo-random function based on the seed
		// This ensures the same seed always produces the same result
		const random = Math.sin(seed * 12.9898) * 43758.5453;
		return random - Math.floor(random) > 0.5 ? 1 : -1;
	}

	let colors = $derived.by(() => {
		// Track theme change
		theme.value;

		const style = getComputedStyle(document.documentElement);

		return {
			stroke: style.getPropertyValue('--color-game-jigsaw-puzzle-stroke'),
			glow: style.getPropertyValue('--color-game-jigsaw-puzzle-glow'),
		};
	});
</script>

{#if imageDimensions}
	<!-- Outer group: container for both inner group and shape outline -->
	<Group
		bind:x
		bind:y
		width={imageDimensions.width / cols}
		height={imageDimensions.height / rows}
		id={`${row}-${col}`}
		{visible}
		{listening}
		{draggable}
		{ondragstart}
		{ondragend}
	>
		<!-- Inner group: contains only the clipped image -->
		<Group
			width={imageDimensions.width / cols}
			height={imageDimensions.height / rows}
			{listening}
			clipFunc={(ctx) => {
				const x = 0;
				const y = 0;
				const width = (imageDimensions?.width ?? 0) / cols;
				const height = (imageDimensions?.height ?? 0) / rows;

				// Draw jigsaw piece shape for clipping
				drawJigsawPiece(ctx, x, y, width, height, row, col, rows, cols, seed);
			}}
		>
			<Image
				{image}
				width={imageDimensions.width}
				height={imageDimensions.height}
				x={-(imageDimensions.width / cols) * col}
				y={-(imageDimensions.height / rows) * row}
				{listening}
				perfectDrawEnabled={false}
			/>
		</Group>

		<!-- Shape outline: drawn outside the clipped group -->
		<Shape
			stroke={glow ? colors.glow : colors.stroke}
			strokeWidth={glow ? 4 : strokeWidth}
			{listening}
			shadowForStrokeEnabled={false}
			perfectDrawEnabled={false}
			sceneFunc={(ctx, shape) => {
				const x = 0;
				const y = 0;
				const width = (imageDimensions?.width ?? 0) / cols;
				const height = (imageDimensions?.height ?? 0) / rows;

				// Draw the jigsaw piece shape outline
				drawJigsawPiece(ctx, x, y, width, height, row, col, rows, cols, seed);
				ctx.fillStrokeShape(shape);
			}}
			fill="transparent"
		/>
	</Group>
{/if}
