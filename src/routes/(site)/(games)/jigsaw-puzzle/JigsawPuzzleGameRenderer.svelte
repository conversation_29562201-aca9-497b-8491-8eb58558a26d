<script lang="ts">
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';
	import ImageIcon from '$lib/components/Icons/ImageIcon.svelte';
	import LightBulbIcon from '$lib/components/Icons/LightBulbIcon.svelte';
	import { Image, Layer, Stage, Group, Rect } from 'svelte-konva';
	import { type JigsawPuzzleGame } from './game/JigsawPuzzleGame.svelte';
	import JigsawFloatingBar from './JigsawFloatingBar.svelte';
	import Konva from 'konva';
	import { onMount, untrack } from 'svelte';
	import { stages } from 'konva/lib/Stage';
	import { Util } from 'konva/lib/Util';
	import { theme } from '$lib/stores/theme.svelte';
	import { siteSounds } from '$lib/stores/siteSounds.svelte';
	import JigsawPiece from './JigsawPiece.svelte';
	import { JigsawPuzzleKonvaTicker } from './game/JigsawPuzzleKonvaTicker';
	import { Fullscreen } from '$lib/util/Fullscreen.svelte';
	import { cn } from '$lib/util/cn';
	import ImageCenterIcon from '$lib/components/Icons/ImageCenterIcon.svelte';
	import FullscreenIcon from '$lib/components/Icons/FullscreenIcon.svelte';
	import FullscreenExitIcon from '$lib/components/Icons/FullscreenExitIcon.svelte';
	import { toast } from 'svelte-sonner';
	import KeyboardIcon from '$lib/components/Icons/KeyboardIcon.svelte';
	import JigsawPuzzleControlsModal from './JigsawPuzzleControlsModal.svelte';
	import { throttle } from 'lodash';

	interface Props {
		game: JigsawPuzzleGame;
		onBack: () => void;
	}

	let { game, onBack }: Props = $props();

	let debugHitBoxes = false;
	let stage = $state<{ node: Konva.Stage }>();
	let isControlsModalOpen = $state(false);

	let container = $state<HTMLDivElement>();
	let fullscreen = $derived.by(() => {
		if (container) {
			return new Fullscreen({ element: container });
		}
	});
	let canShowImage = $state(false);
	let rectColor = $state('white');
	let imageRef = $state<{ node: Konva.Node }>();
	let imageOpacityTween = $derived.by(() => {
		if (imageRef) {
			return new Konva.Tween({
				duration: 0.3,
				node: imageRef.node,
				opacity: 0.5,
				easing: Konva.Easings.EaseInOut,
			});
		}
	});

	function onResize() {
		game.container = {
			width: container?.clientWidth ?? 0,
			height: container?.clientHeight ?? 0,
		};
	}

	const throttledResize = throttle(onResize, 300, {
		leading: false,
		trailing: true,
	});

	function updateRectColor() {
		const style = getComputedStyle(document.documentElement);
		rectColor = style.getPropertyValue('--color-base-300');
	}

	function initGameTicker() {
		if (game && !game.ticker) {
			game.ticker = new JigsawPuzzleKonvaTicker(game.animationSystem);
			game.ticker.start();
		}
	}

	$effect(() => {
		// Track theme change
		theme.value;

		updateRectColor();
	});

	onMount(() => {
		if (import.meta.env.DEV) {
			(window as any).Konva.stages = stages;
			(window as any).Konva.Util = Util;
		}

		onResize();
		initGameTicker();
	});

	function toggleImage() {
		canShowImage = !canShowImage;

		if (canShowImage) {
			imageOpacityTween?.play();
			siteSounds.toggleOn.play();
		} else {
			imageOpacityTween?.reverse();
			siteSounds.toggleOff.play();
		}
	}

	function showHint() {
		const piece = game.throttledShowHint();

		if (piece) {
			const pieceInstance = stage?.node.findOne(`#${piece.row}-${piece.column}`);

			if (pieceInstance) {
				pieceInstance.moveToTop();
			}
		} else {
			// todo: play sound
			toast.warning('No more hints available', {
				id: 'jigsaw-no-hint',
				duration: 3_000,
			});
		}
	}

	function exit() {
		fullscreen?.exit();
		onBack();
	}

	function onkeydown(event: KeyboardEvent) {
		if (!game.enabled) {
			return;
		}

		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		if (event.code === 'KeyH') {
			showHint();
		} else if (event.code === 'KeyC') {
			game?.centralize();
		} else if (event.code === 'KeyI') {
			toggleImage();
		} else if (event.code === 'KeyF') {
			fullscreen?.toggle();
		} else if (event.code === 'KeyK') {
			isControlsModalOpen = !isControlsModalOpen;
		} else if (event.code === 'KeyN') {
			if (event.shiftKey) {
				exit();
			}
		}
	}

	$effect(() => {
		if (game.isGameWon) {
			untrack(() => {
				fullscreen?.exit();
			});
		}
	});
</script>

<svelte:window onresize={throttledResize} {onkeydown} />

<JigsawPuzzleControlsModal bind:isOpen={isControlsModalOpen} />

<!-- svelte-ignore a11y_no_static_element_interactions -->
<div
	class="size-full bg-base-100 overflow-clip"
	bind:this={container}
	oncontextmenu={(e) => e.preventDefault()}
>
	{#if container && game}
		<Stage
			width={game.container.width}
			height={game.container.height}
			scale={{
				x: game.camera.zoom,
				y: game.camera.zoom,
			}}
			x={game.camera.x}
			y={game.camera.y}
			bind:this={stage}
		>
			<!-- Background Image -->
			<Layer listening={false}>
				{#if game.imageElement && game.imageDimensions}
					<Group>
						<Rect
							width={game.imageDimensions.width}
							height={game.imageDimensions.height}
							x={game.imageDimensions.x}
							y={game.imageDimensions.y}
							fill={rectColor}
						/>

						<Image
							bind:this={imageRef as any}
							image={game.imageElement}
							width={game.imageDimensions.width}
							height={game.imageDimensions.height}
							x={game.imageDimensions.x}
							y={game.imageDimensions.y}
							perfectDrawEnabled={false}
							opacity={game.isGameWon ? 1 : canShowImage ? 0.8 : 0}
						/>
					</Group>
				{/if}
			</Layer>

			<!-- Game -->
			<Layer>
				{#if game.imageElement && game.imageDimensions}
					{#each game.pieceGroups as pieceGroup}
						<Group
							bind:x={pieceGroup.x}
							bind:y={pieceGroup.y}
							draggable
							listening
							ondragstart={(e) => {
								e.target.moveToTop();
							}}
							ondragend={() => {
								game.onGroupDrop(pieceGroup);
							}}
						>
							{#each pieceGroup.pieces as piece}
								<JigsawPiece
									bind:x={piece!.x}
									bind:y={piece!.y}
									row={piece!.row}
									col={piece!.column}
									rows={game.grid.rows}
									cols={game.grid.columns}
									image={game.imageElement}
									seed={game.seed}
									container={{
										width: game.container.width,
										height: game.container.height,
									}}
									objectFit="contain"
									imageDimensions={game.imageDimensions}
									draggable={false}
									listening
									visible
									glow={piece.glow}
									strokeWidth={game.strokeWidth}
								/>
							{/each}
						</Group>
					{/each}

					{#each game.grid.grid as rows}
						{#each rows as piece}
							{#if !piece!.isGrouped}
								<JigsawPiece
									bind:x={piece!.x}
									bind:y={piece!.y}
									row={piece!.row}
									col={piece!.column}
									rows={game.grid.rows}
									cols={game.grid.columns}
									image={game.imageElement}
									seed={game.seed}
									container={{
										width: game.container.width,
										height: game.container.height,
									}}
									objectFit="contain"
									imageDimensions={game.imageDimensions}
									draggable={!piece!.isInPlace}
									listening={!piece!.isInPlace}
									visible
									ondragstart={(e) => {
										e.target.moveToTop();
									}}
									ondragend={() => game.onDrop({ piece: piece! })}
									glow={piece!.glow}
									strokeWidth={game.strokeWidth}
								/>
							{/if}

							{#if debugHitBoxes}
								<Rect
									x={piece!.x - piece!.x + piece!.hitboxes.top.x}
									y={piece!.y - piece!.y + piece!.hitboxes.top.y}
									width={piece!.hitboxes.top.w}
									height={piece!.hitboxes.top.h}
									fill="red"
									listening={false}
								/>

								<Rect
									x={piece!.x - piece!.x + piece!.hitboxes.bottom.x}
									y={piece!.y - piece!.y + piece!.hitboxes.bottom.y}
									width={piece!.hitboxes.bottom.w}
									height={piece!.hitboxes.bottom.h}
									fill="blue"
									listening={false}
								/>

								<Rect
									x={piece!.x - piece!.x + piece!.hitboxes.left.x}
									y={piece!.y - piece!.y + piece!.hitboxes.left.y}
									width={piece!.hitboxes.left.w}
									height={piece!.hitboxes.left.h}
									fill="green"
									listening={false}
								/>

								<Rect
									x={piece!.x - piece!.x + piece!.hitboxes.right.x}
									y={piece!.y - piece!.y + piece!.hitboxes.right.y}
									width={piece!.hitboxes.right.w}
									height={piece!.hitboxes.right.h}
									fill="yellow"
									listening={false}
								/>

								<Rect
									x={piece!.x - piece!.x + piece!.hitboxes.center.x}
									y={piece!.y - piece!.y + piece!.hitboxes.center.y}
									width={piece!.hitboxes.center.w}
									height={piece!.hitboxes.center.h}
									fill="pink"
									opacity={0.5}
									listening={false}
								/>
							{/if}
						{/each}
					{/each}
				{/if}
			</Layer>
		</Stage>
	{/if}

	<JigsawFloatingBar>
		<div
			class="tooltip before:hidden after:hidden lg:before:block lg:after:block"
			data-tip="Exit puzzle (Shift + N)"
		>
			<button
				class="btn btn-ghost btn-circle"
				disabled={!game.enabled}
				onclick={exit}
				aria-label="Exit puzzle"
			>
				<ChevronLeftIcon class="size-6" />
			</button>
		</div>

		<div
			class="tooltip before:hidden after:hidden lg:before:block lg:after:block"
			data-tip="Show Hint (H)"
		>
			<button
				class="btn btn-ghost btn-circle"
				disabled={!game.enabled}
				onclick={() => {
					showHint();
				}}
				aria-label="Show hint"
			>
				<LightBulbIcon class="size-6" />
			</button>
		</div>

		<div
			class="tooltip before:hidden after:hidden lg:before:block lg:after:block"
			data-tip="Centralize (C)"
		>
			<button
				class="btn btn-ghost btn-circle"
				disabled={!game.enabled}
				onclick={() => {
					game.centralize();
				}}
				aria-label="Centralize"
			>
				<ImageCenterIcon class="size-6" />
			</button>
		</div>

		<div
			class="tooltip before:hidden after:hidden lg:before:block lg:after:block"
			data-tip={canShowImage ? 'Hide image (I)' : 'Show image (I)'}
		>
			<button
				class="btn btn-ghost btn-circle"
				class:btn-active={canShowImage}
				disabled={!game.enabled}
				onclick={toggleImage}
				aria-label={canShowImage ? 'Hide image' : 'Show image'}
			>
				<ImageIcon class="size-6" />
			</button>
		</div>

		<div
			class={cn('tooltip before:hidden after:hidden lg:before:block lg:after:block', {
				hidden: !fullscreen?.hasSupport(),
			})}
			data-tip={fullscreen?.isOnFullscreen ? 'Exit fullscreen (F)' : 'Fullscreen (F)'}
		>
			<button
				class={cn('btn btn-ghost btn-circle', {
					'btn-active': fullscreen?.isOnFullscreen,
				})}
				disabled={!game.enabled}
				onclick={() => {
					fullscreen?.toggle();
				}}
				aria-label={fullscreen?.isOnFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
			>
				{#if fullscreen?.isOnFullscreen}
					<FullscreenExitIcon class="size-6" />
				{:else}
					<FullscreenIcon class="size-6" />
				{/if}
			</button>
		</div>

		<div
			class="tooltip before:hidden after:hidden lg:before:block lg:after:block hidden lg:block"
			data-tip="Controls (K)"
		>
			<button
				class={cn('btn btn-ghost btn-circle', {
					'btn-active': isControlsModalOpen,
				})}
				disabled={!game.enabled}
				onclick={() => {
					isControlsModalOpen = !isControlsModalOpen;
				}}
				aria-label="Show controls"
			>
				<KeyboardIcon class="size-6" />
			</button>
		</div>
	</JigsawFloatingBar>
</div>
