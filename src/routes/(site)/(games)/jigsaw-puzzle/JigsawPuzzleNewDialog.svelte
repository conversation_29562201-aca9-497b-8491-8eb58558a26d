<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';
	import { cn } from '$lib/util/cn';
	import { onMount } from 'svelte';
	import { pieceSizes, type PieceSize } from './game/JigsawPuzzleGame.svelte';
	import { getJigsawPuzzleSize } from './util/getJigsawPuzzleSize';
	import type { jigsawPuzzleSoundResources } from './game/jigsawPuzzleSoundResources';
	import type { GameSound } from '$lib/util/GameSound.svelte';
	import { getImageDimensions } from './util/getImageDimensions';
	import type { Box2D } from '$lib/models/Box2D';
	import { isValidHttpUrl } from '$lib/util/isValidHttpUrl';
	import { fade } from 'svelte/transition';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';
	import LinkIcon from '$lib/components/Icons/LinkIcon.svelte';
	import PlayIcon from '$lib/components/Icons/PlayIcon.svelte';
	import type { JigsawImage } from './data/jigsawImages';
	import type { JigsawCategory } from './data/jigsawCategories';
	import type { Size } from '$lib/models/Size';
	import { copy } from 'svelte-copy';
	import CheckIcon from '$lib/components/Icons/CheckIcon.svelte';
	import CloseIcon from '$lib/components/Icons/CloseIcon.svelte';
	import { wait } from '$lib/functions/wait';
	import { pushState } from '$app/navigation';
	import Alert from '$lib/components/Alert.svelte';
	import { browser } from '$app/environment';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';

	interface Props {
		isOpen: boolean;
		sounds: Record<keyof typeof jigsawPuzzleSoundResources, GameSound>;
		onStart: (startProps: {
			image: JigsawImage;
			category?: JigsawCategory;
			originalImageSize: Size;
			seed: number;
			container: Size;
		}) => void;
		pieceSize: PieceSize;
		variant: 'community' | 'custom';
	}

	let {
		isOpen = $bindable(false),
		sounds,
		onStart,
		pieceSize = $bindable(pieceSizes[1]),
		variant = 'custom',
	}: Props = $props();

	let container = $state<HTMLDivElement>();
	let imageElement = $state<HTMLImageElement>();
	let imageUrl = $state('');
	let isImageUrlValid = $derived<boolean>(isValidHttpUrl(imageUrl));
	let imageState = $state('idle' as 'loading' | 'idle' | 'success' | 'error');
	let imageDimensions = $state<Box2D | null>(null);
	let gridSize = $derived(
		imageDimensions ? getJigsawPuzzleSize(pieceSize, imageDimensions) : null,
	);
	let copyState = $state<'none' | 'success' | 'error'>('none');
	let isImageLarge = $derived.by(() => {
		if (imageElement && imageState === 'success') {
			const threshhold = 2000;

			return (
				(imageElement?.naturalWidth ?? 0) > threshhold ||
				(imageElement?.naturalHeight ?? 0) > threshhold
			);
		}

		return false;
	});
	let isPrivate = $state(false);

	let JigsawPuzzlePreview = $state<typeof import('./JigsawPuzzlePreview.svelte').default>();

	let puzzleLinkPath = $derived(
		`/jigsaw-puzzle/custom?link=${encodeURIComponent(imageUrl)}&size=${pieceSize}`,
	);

	let puzzleLink = $derived.by(() => {
		if (!browser) {
			return '';
		}

		let host = window.location.host;

		if (!host.includes('http://') || !host.includes('https://')) {
			host = `http://${host}`;
		}

		return `${host}${puzzleLinkPath}`;
	});

	onMount(async () => {
		JigsawPuzzlePreview = (await import('./JigsawPuzzlePreview.svelte')).default;
	});

	$effect(function updateIsLoadingImage() {
		// Track it
		imageUrl;

		imageState = 'loading';
	});

	$effect(function resetCopyState() {
		if (copyState !== 'none') {
			wait(3000).then(() => (copyState = 'none'));
		}
	});
</script>

<Dialog modalBoxClass="sm:max-w-2xl" bind:isOpen>
	<div class="prose mt-4 mb-2">
		<h2>Create a Puzzle</h2>

		{#if }
		<p>
			Paste an image link to create your custom puzzle. Share the puzzle link with friends so
			they can try it too! We'll feature the best ones on the <strong>Community</strong> category!
		</p>
	</div>

	<fieldset class="fieldset w-full">
		<legend class="fieldset-legend">Image link</legend>

		<label class="input validator w-full">
			<LinkIcon class="opacity-50 size-4" />

			<input
				type="url"
				required
				placeholder="https://example.com/beautiful-landscape.jpg"
				title="Please enter a valid link"
				class="w-full"
				bind:value={imageUrl}
			/>
		</label>

		<p class="validator-hint">Please enter a valid link</p>
	</fieldset>

	<div class="aspect-video w-full mt-2 relative" bind:this={container}>
		{#if imageUrl && isImageUrlValid}
			<img
				class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 opacity-30 max-h-full h-full max-w-full margin-auto z-0 object-contain"
				src={imageUrl}
				alt=""
				style="width: {imageDimensions?.width}; height: {imageDimensions?.height}"
				bind:this={imageElement}
				onload={(e) => {
					imageDimensions = getImageDimensions({
						image: e.target as HTMLImageElement,
						container: {
							width: container?.clientWidth ?? 0,
							height: container?.clientHeight ?? 0,
						},
					});

					if (!imageDimensions) {
						imageState = 'error';
					} else {
						imageState = 'success';
					}
				}}
				onerror={() => {
					imageState = 'error';
				}}
			/>
		{/if}

		{#if JigsawPuzzlePreview && imageUrl && gridSize && imageState === 'success'}
			<div class="z-10 size-full" in:fade>
				<JigsawPuzzlePreview
					src={imageUrl}
					rows={gridSize.rows}
					cols={gridSize.columns}
					noOutTransition
					scale={1}
					strokeWidth={1}
					{sounds}
				/>

				{#if isImageLarge}
					<Alert
						class="absolute left-1/2 -translate-x-1/2 right-0 bottom-4 z-20 max-w-96 w-full"
						sentiment="warning"
						title="Large image detected"
						description="This image may cause performance issues, consider using a smaller one"
					/>
				{/if}
			</div>
		{/if}

		{#if imageState === 'loading'}
			<div class="bg-base-300 size-full absolute inset-0" transition:fade></div>
		{/if}

		{#if imageState === 'error'}
			<div
				transition:fade
				class={cn(
					'flex items-center justify-center flex-col gap-2 transition-all duration-500 w-full absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-base-300 min-h-48 aspect-video z-10',
				)}
			>
				<WarningSolidIcon class="size-12" />
				<span class="max-w-xs text-center"
					>Oops, we couldn't load the image. Please try again with a different URL.</span
				>
			</div>
		{/if}
	</div>

	<div class="flex items-center justify-center gap-3 w-full my-4">
		{#each pieceSizes as size}
			<button
				class={cn('btn btn-circle uppercase', {
					'btn-secondary': pieceSize === size,
					'btn-ghost': pieceSize !== size,
					'hidden md:block': pieceSizes.indexOf(size) >= pieceSizes.indexOf('xl'),
				})}
				onclick={() => {
					pieceSize = size;
				}}
				aria-label={`Select ${size} pieces`}
			>
				{size}
			</button>
		{/each}
	</div>

	{#if variant === 'custom'}
		<fieldset
			class="fieldset bg-base-200 border-base-300 rounded-box w-full border p-4 mx-auto mb-6"
		>
			<legend class="fieldset-legend">Options</legend>

			<Toggle bind:checked={isPrivate} aria-label="Make it private" class="p-0">
				Private puzzle

				{#snippet description()}
					Private puzzles are not sent to our servers
				{/snippet}
			</Toggle>
		</fieldset>
	{/if}

	<div class="flex sm:flex-row flex-col sm:items-center items-stretch justify-end gap-4">
		{#if variant === 'custom'}
			<button
				disabled={imageState !== 'success'}
				class={cn('btn min-w-44 flex gap-4', {
					'btn-success': copyState === 'success',
					'btn-error': copyState === 'error',
				})}
				use:copy={{
					text: puzzleLink,
					onCopy() {
						copyState = 'success';
					},
					onError() {
						copyState = 'error';
					},
				}}
			>
				{#if copyState === 'none'}
					<LinkIcon class="size-4" /> Copy puzzle link
				{:else if copyState === 'success'}
					<CheckIcon class="size-6" /> Copied!
				{:else}
					<CloseIcon class="size-6" /> Failed
				{/if}
			</button>
		{/if}

		<button
			class="btn btn-primary min-w-44"
			disabled={imageState !== 'success'}
			onclick={() => {
				if (!isPrivate || variant === 'community') {
					void fetch(`${import.meta.env.VITE_API_URL}/jigsaw-puzzle`, {
						method: 'POST',
						body: JSON.stringify({ imageUrl }),
						headers: { 'Content-Type': 'application/json' },
						credentials: 'include',
					});
				}

				isOpen = false;

				onStart({
					image: {
						id: encodeURIComponent(imageUrl),
						url: imageUrl,
					},
					originalImageSize: {
						width: imageElement?.naturalWidth ?? 0,
						height: imageElement?.naturalHeight ?? 0,
					},
					seed: 0,
					container: {
						width: container?.clientWidth ?? 0,
						height: container?.clientHeight ?? 0,
					},
				});

				pushState(puzzleLinkPath, {});
			}}
		>
			<PlayIcon variant="play" class="size-7" />
			{#if variant === 'custom'}
				Play puzzle
			{:else}
				Create puzzle and play
			{/if}
		</button>
	</div>
</Dialog>
