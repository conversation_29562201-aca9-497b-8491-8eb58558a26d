import type { JigsawImageResponse } from '../+page.server';

export function fetchPuzzle({
	page,
	pageSize,
	fetchFunc,
}: {
	page: number;
	pageSize: number;
	fetchFunc: typeof fetch;
}) {
	return fetchFunc(
		`${import.meta.env.VITE_API_URL}/jigsaw-puzzle?pageSize=${pageSize}&page=${page}`,
		{
			credentials: 'include',
			headers: {
				'Content-Type': 'application/json',
			},
		},
	).then((res) => res.json<JigsawImageResponse>());
}
