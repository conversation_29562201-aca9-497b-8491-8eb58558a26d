import type { JigsawImageResponse } from '../+page.server';

const cache: Record<string, JigsawImageResponse> = {};

export async function fetchPuzzle({
	page,
	pageSize,
	fetchFunc,
}: {
	page: number;
	pageSize: number;
	fetchFunc: typeof fetch;
}) {
	const key = `${page}-${pageSize}`;

	if (cache[key]) {
		return cache[key];
	}

	const res = await fetchFunc(
		`${import.meta.env.VITE_API_URL}/jigsaw-puzzle?pageSize=${pageSize}&page=${page}&key=${
			import.meta.env.VITE_JIGSAW_PUZZLE_GET_KEY
		}`,
		{
			credentials: 'include',
			headers: {
				'Content-Type': 'application/json',
			},
		},
	);

	if (res.status >= 400) {
		throw new Error('Error');
	}

	const response = await res.json<JigsawImageResponse>();

	cache[key] = response;

	return response;
}
