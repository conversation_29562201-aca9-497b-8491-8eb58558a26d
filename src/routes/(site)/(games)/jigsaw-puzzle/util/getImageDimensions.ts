import type { Box2D } from '$lib/models/Box2D';
import type { Size } from '$lib/models/Size';

interface Props {
	image: Size;
	container: Size;
	objectFit?: 'cover' | 'contain';
}

export function getImageDimensions({
	image,
	container,
	objectFit = 'contain',
}: Props): Box2D | null {
	if (!image || !container) return null;

	const imageWidth = image.width;
	const imageHeight = image.height;

	// Calculate the scaling ratios
	const scaleX = container.width / imageWidth;
	const scaleY = container.height / imageHeight;

	// Use the appropriate scale based on objectFit prop
	const scale =
		objectFit === 'cover'
			? Math.max(scaleX, scaleY) // cover: image covers entire container, may be cropped
			: Math.min(scaleX, scaleY); // contain: entire image fits within container

	// Calculate the new dimensions
	const newWidth = imageWidth * scale;
	const newHeight = imageHeight * scale;

	// Calculate position to center the image
	const x = (container.width - newWidth) / 2;
	const y = (container.height - newHeight) / 2;

	return {
		x,
		y,
		width: newWidth,
		height: newHeight,
	};
}
