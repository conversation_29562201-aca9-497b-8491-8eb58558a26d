import type { JigsawCategory } from '../data/jigsawCategories';
import type { Wallpaper } from '$lib/data/wallpapers';
import { generateHash } from '$lib/functions/generateHash';

const categoryPrefixes: Record<JigsawCategory, string> = {
	all: '',
	animals: 'an',
	'city-and-architecture': 'ca',
	ghibli: 'gb',
	interior: 'in',
	landscape: 'la',
	cars: 'cr',
	technology: 'tc',
};

export function getJigsawId({
	image,
	category,
}: {
	image: Wallpaper;
	category: JigsawCategory;
}): string {
	return `${categoryPrefixes[category]}${generateHash(image.url)}`;
}
