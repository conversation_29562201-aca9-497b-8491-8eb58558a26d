import { jigsawImages, type JigsawImages } from '../data/jigsawImages';
import { fetchPuzzle } from './fetchPuzzle';

export async function fetchAllPuzzles({ fetchFunc }: { fetchFunc: typeof fetch }) {
	const res = await fetchPuzzle({ page: 1, pageSize: 10, fetchFunc });
	const communityImages = {
		category: 'community',
		images: res.puzzles.map((puzzle) => {
			return {
				id: puzzle.id,
				url: puzzle.imageUrl,
			};
		}),
	} as JigsawImages;

	// const allCategory = jigsawImages.find((img) => img.category === 'all');
	// allCategory?.images.push(...communityImages.images);

	return {
		jigsawImages: [communityImages, ...jigsawImages],
	};
}
