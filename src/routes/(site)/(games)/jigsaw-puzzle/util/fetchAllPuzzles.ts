import { jigsawImages, type JigsawImages } from '../data/jigsawImages';
import { fetchPuzzle } from './fetchPuzzle';

export async function fetchAllPuzzles({ fetchFunc }: { fetchFunc: typeof fetch }) {
	const res = await fetchPuzzle({ page: 1, pageSize: 60, fetchFunc });

	if (res.puzzles.length === 0) {
		return { jigsawImages };
	}

	const communityImages = {
		category: 'community',
		images: res.puzzles.map((puzzle) => {
			return {
				id: puzzle.id,
				url: puzzle.imageUrl,
			};
		}),
	} as JigsawImages;

	// Create a new jigsawImages array without mutating the original
	const updatedJigsawImages = jigsawImages.map((categoryGroup) => {
		if (categoryGroup.category === 'all') {
			// Create a new category group with community images added
			return {
				...categoryGroup,
				images: [...categoryGroup.images, ...communityImages.images],
			};
		}
		// Return other categories unchanged
		return categoryGroup;
	});

	return {
		jigsawImages: [communityImages, ...updatedJigsawImages],
	};
}
