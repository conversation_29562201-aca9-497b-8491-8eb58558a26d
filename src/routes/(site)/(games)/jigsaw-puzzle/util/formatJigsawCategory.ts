import type { JigsawCategory } from '../data/jigsawCategories';

const map: Record<JigsawCategory, string> = {
	'city-and-architecture': 'City & Architecture',
	all: 'All',
	interior: 'Interior',
	landscape: 'Nature & Landscapes',
	ghibli: 'G<PERSON>bli',
	animals: 'Animals',
	technology: 'Technology',
	cars: 'Cars',
	community: 'Community',
};

export function formatJigsawCategory(category: JigsawCategory) {
	return map[category];
}
