import type { GridSize } from '$lib/models/GridSize';
import { getImageDimensions } from '../util/getImageDimensions';
import type { Size } from '$lib/models/Size';
import { Camera2D, type CameraEventType } from '../Camera2D.svelte';
import type { Timer } from '$lib/util/Timer.svelte';
import { Grid } from '$lib/util/Grid.svelte';
import {
	hitboxCenterRatio,
	JigsawPuzzlePiece,
	pieceMoveDuration,
} from './JigsawPuzzlePiece.svelte';
import type { Box2D } from '$lib/models/Box2D';
import {
	AnimationSystem,
	type CompleteAnimationEntity,
} from '../../solitaire/systems/AnimationSystem';
import type { JigsawPuzzleTickerAdapter } from './JigsawPuzzleTickerAdapter';
import { HitBox, type Rect } from '$lib/util/HitBox';
import { JigsawPuzzlePieceGroup } from './JigsawPuzzlePieceGroup.svelte';
import { shuffleMutate } from '$lib/functions/shuffle';
import { getRandomItemWithWeightsAt } from '$lib/functions/getRandomItemWithWeightsAt';
import throttle from 'lodash/throttle';
import { Easing, Tween } from '@tweenjs/tween.js';
import type { jigsawPuzzleSoundResources } from './jigsawPuzzleSoundResources';
import type { GameSound } from '$lib/util/GameSound.svelte';
import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import { getJigsawPuzzleSize } from '../util/getJigsawPuzzleSize';
import type { JigsawImage } from '../data/jigsawImages';

const hintMoveDuration = 800;
const gameWinAnimationDuration = 700;
const centerDuration = 500;

export const pieceSizes = ['xs', 's', 'm', 'l', 'xl'] as const;
export type PieceSize = (typeof pieceSizes)[number];

export const landscapeGridSizeByPieceAmount: Record<PieceSize, GridSize> = {
	xs: {
		rows: 4,
		columns: 6,
	},
	s: {
		rows: 5,
		columns: 8,
	},
	m: {
		rows: 8,
		columns: 12,
	},
	l: {
		rows: 9,
		columns: 15,
	},
	xl: {
		rows: 12,
		columns: 20,
	},
};

export const squareGridSizeByPieceAmount: Record<PieceSize, GridSize> = {
	xs: {
		rows: 5,
		columns: 5,
	},
	s: {
		rows: 7,
		columns: 7,
	},
	m: {
		rows: 10,
		columns: 10,
	},
	l: {
		rows: 12,
		columns: 12,
	},
	xl: {
		rows: 15,
		columns: 15,
	},
};

export interface JigsawPuzzleGameProps {
	pieceSize: PieceSize;
	image: JigsawImage;
	container: Size;
	seed: number;
	timer: Timer;
	target: HTMLElement;
	sounds: Record<keyof typeof jigsawPuzzleSoundResources, GameSound>;
	onGameWin: () => void;
}

export class JigsawPuzzleGame {
	image: JigsawImage;
	seed: number;
	camera: Camera2D;
	timer = $state() as Timer;
	grid: Grid<JigsawPuzzlePiece> = $state(new Grid({ rows: 0, columns: 0, reactive: false }));
	animationSystem = new AnimationSystem();
	ticker?: JigsawPuzzleTickerAdapter;
	pieceGroups: JigsawPuzzlePieceGroup[] = $state([]);
	defaultZoom = 0.5;
	isGameWon = $state(false);
	sounds: Record<keyof typeof jigsawPuzzleSoundResources, GameSound>;
	pieceSize: PieceSize;
	strokeWidth = $state(1);
	private _hintsPlayed = $state(0);
	private _soundPlaying?: GameSound;
	private _pieceSounds: GameSound[];
	onGameWin?: () => void;
	throttledShowHint: () => JigsawPuzzlePiece | null;

	private _container = $state() as Size;
	private _imageDimensions = $state<Box2D | null>(null);
	private _imageElement = $state<HTMLImageElement | null>(null);
	private _abortController = new AbortController();
	private _cleanupEffect: () => void;

	constructor({
		pieceSize,
		image,
		container,
		seed,
		timer,
		target,
		sounds,
		onGameWin,
	}: JigsawPuzzleGameProps) {
		this.image = image;
		this.seed = seed;
		this.sounds = sounds;
		this.pieceSize = pieceSize;
		this._pieceSounds = [
			sounds.jigsawPiece1,
			sounds.jigsawPiece2,
			sounds.jigsawPiece3,
			sounds.jigsawPiece4,
			sounds.jigsawPiece5,
		];
		this.onGameWin = onGameWin;

		this.throttledShowHint = throttle(this.showHint, hintMoveDuration, {
			leading: true,
			trailing: false,
		});

		this._container = container;
		this.timer = timer;
		this.camera = new Camera2D({
			target,
			enabled: false,
		});
		this.camera.onZoomChange = (zoom) => {
			if (this.isGameWon) {
				return;
			}

			this.strokeWidth = 1 / zoom;
		};
		this.strokeWidth = 1 / this.camera.zoom;

		// Set up camera interaction callback
		this.camera.canInteract = (positions, eventType) => {
			return this.canInteractAtPositions(positions, eventType);
		};

		if (image.url) {
			const img = document.createElement('img');

			img.onload = () => {
				this._imageDimensions = getImageDimensions({
					image: img,
					container: this.container,
				});
				this._imageElement = img;

				if (this._imageDimensions) {
					const size = getJigsawPuzzleSize(this.pieceSize, this._imageDimensions);

					this.centralize({ duration: 0, playSound: false });

					this.grid = new Grid(
						{
							rows: size.rows,
							columns: size.columns,
							reactive: true,
						},
						(row, column) => {
							return new JigsawPuzzlePiece({
								x:
									this._imageDimensions!.x +
									(this._imageDimensions!.width * column) / size.columns,
								y:
									this._imageDimensions!.y +
									(this._imageDimensions!.height * row) / size.rows,
								row,
								column,
								width: this._imageDimensions!.width / size.columns,
								height: this._imageDimensions!.height / size.rows,
							});
						},
					);

					this.shuffle();
				}
			};

			img.src = image.url;
		}

		this._cleanupEffect = $effect.root(() => {
			$effect(() => {
				this.camera.enabled = this.enabled;
			});
		});

		this.initAnimationSystem();
		this.addListeners();
	}

	private _piecesInPlace: number = $state(0);

	private _playDropSound() {
		this._soundPlaying = getRandomItemAt(this._pieceSounds);
		this._soundPlaying.play();
	}

	get amountOfPieces() {
		return this.grid.rows * this.grid.columns;
	}

	/** Smaller is better */
	get score(): number {
		return this.amountOfPieces + Math.floor(this.timer.time / 1000) + this._hintsPlayed * 10;
	}

	get piecesInPlace() {
		return this._piecesInPlace;
	}

	get enabled() {
		return this.timer.running;
	}

	get imageElement() {
		return this._imageElement;
	}

	get imageDimensions() {
		return this._imageDimensions;
	}

	get container() {
		return this._container;
	}

	set container(value: Size) {
		this._container = value;
	}

	private getCenterPosition({ zoom }: { zoom: number }) {
		// Calculate the center point of the image in world coordinates
		const imageCenterX = this._imageDimensions!.x + this._imageDimensions!.width / 2;
		const imageCenterY = this._imageDimensions!.y + this._imageDimensions!.height / 2;

		// Calculate the viewport center
		const viewportCenterX = this.container.width / 2;
		const viewportCenterY = this.container.height / 2;

		// Calculate where the camera should be positioned to center the image
		// Camera position = viewport center - (image center * zoom)
		return {
			x: viewportCenterX - imageCenterX * zoom,
			y: viewportCenterY - imageCenterY * zoom,
		};
	}

	centralize(props?: { duration: number; playSound?: boolean }) {
		const animation = this.getCenterTween({
			newZoom: this.defaultZoom,
			duration: props?.duration ?? centerDuration,
		});

		this.animationSystem.add({
			id: 'centralize',
			animation,
		});

		if (props?.playSound ?? true) {
			this.sounds.swoosh.play();
		}
	}

	/** Place pieces around image in a random order */
	private shuffle() {
		const shuffledPieces = shuffleMutate(this.grid.flatten());
		const pieceWidth = shuffledPieces[0]!.width;
		const pieceHeight = shuffledPieces[0]!.height;
		const maxDx = this._imageDimensions!.width / 4;
		const maxDy = this._imageDimensions!.height / 4;
		const imgLeft = this._imageDimensions!.x;
		const imgTop = this._imageDimensions!.y;
		const imgRight = this._imageDimensions!.x + this._imageDimensions!.width;
		const imgBottom = this._imageDimensions!.y + this._imageDimensions!.height;

		const regions: Rect[] = [
			// Top
			{
				x: imgLeft - maxDx - pieceWidth * 2,
				y: imgTop - maxDy - pieceHeight * 2,
				h: maxDy,
				w: (maxDx + pieceWidth) * 2 + this._imageDimensions!.width,
			},
			// Bottom
			{
				x: imgLeft - maxDx - pieceWidth * 2,
				y: imgBottom + pieceHeight,
				h: maxDy,
				w: (maxDx + pieceWidth) * 2 + this._imageDimensions!.width,
			},
			// Left
			{
				x: imgLeft - maxDx - pieceWidth * 2,
				y: imgTop - pieceHeight,
				h: pieceHeight * 2 + this._imageDimensions!.height,
				w: maxDx,
			},
			// Right
			{
				x: imgRight + pieceWidth,
				y: imgTop - pieceHeight,
				h: pieceHeight * 2 + this._imageDimensions!.height,
				w: maxDx,
			},
		];

		const weights = regions.map(({ w, h }) => w * h);

		shuffledPieces.forEach((piece) => {
			if (piece) {
				const region = getRandomItemWithWeightsAt(regions, weights);

				if (region) {
					piece.x = region.x + Math.random() * region.w;
					piece.y = region.y + Math.random() * region.h;
				}
			}
		});
	}

	onGroupDrop(group: JigsawPuzzlePieceGroup): boolean {
		if (group.isEmpty) {
			return false;
		}

		const { canonicalPieceLocationHitBox } = this.getCanonicalPieceLocationRects(
			group.pieces[0],
		);

		if (group.pieces[0]?.collidesWith(canonicalPieceLocationHitBox)) {
			// Handle onDrop on all pieces
			// Copy pieces because the group.pieces can be mutated
			const pieces = [...group.pieces];

			pieces.forEach((piece) => this.onDrop({ piece, playSound: false }));
			this._playDropSound();

			return true;
		}

		const firstMatch = group.pieces
			.map((piece) => {
				const piecesToCheck = this.grid
					.getItemValuesAround(piece, {
						diagonal: false,
					})
					.filter((piece) => !group.pieces.includes(piece));

				const matches = piecesToCheck.filter((adjacentPiece) => piece.match(adjacentPiece));

				if (matches.length > 0) {
					return {
						piece,
						match: matches[0],
					};
				}

				return null;
			})
			.filter(Boolean)[0];

		if (firstMatch) {
			const rowDiff = firstMatch.match.row - firstMatch.piece.row;
			const columnDiff = firstMatch.match.column - firstMatch.piece.column;

			// Move the whole group towards the first match
			const dx =
				firstMatch.piece.globalX -
				firstMatch.match.globalX +
				columnDiff * firstMatch.piece.width;
			const dy =
				firstMatch.piece.globalY -
				firstMatch.match.globalY +
				rowDiff * firstMatch.piece.height;

			const animation = group.moveTo({
				x: group.x - dx,
				y: group.y - dy,
				onComplete: () => {
					// Handle onDrop on all pieces
					// Copy pieces because the group.pieces can be mutated
					const pieces = [...group.pieces];
					let anchorPiece: JigsawPuzzlePiece | undefined;
					this._playDropSound();

					pieces.forEach((piece) => {
						anchorPiece = this.onDrop({
							piece,
							anchorPiece,
							playSound: false,
						}).anchorPiece;
					});
				},
			});

			if (animation) {
				this.animationSystem.add(animation);
			}

			return true;
		}

		return false;
	}

	onDrop({
		piece,
		anchorPiece: anchorPieceFromProps,
		playSound = true,
	}: {
		piece: JigsawPuzzlePiece;
		anchorPiece?: JigsawPuzzlePiece;
		playSound?: boolean;
	}): { anchorPiece?: JigsawPuzzlePiece } {
		const { canonicalPieceLocationHitBox, canonicalPieceLocationOnImage } =
			this.getCanonicalPieceLocationRects(piece);

		if (piece.collidesWith(canonicalPieceLocationHitBox)) {
			const pieceGroup = piece.group;

			piece.isInPlace = true;
			this._piecesInPlace += 1;
			this.checkGameWin();

			if (pieceGroup?.isEmpty) {
				this.pieceGroups = this.pieceGroups.filter((g) => g !== pieceGroup);
			}

			const animation = piece.moveTo({
				x: canonicalPieceLocationOnImage.x,
				y: canonicalPieceLocationOnImage.y,
				onComplete: () => {
					if (playSound) {
						this._playDropSound();
					}
					this.movePieceCollisionsOutOfTheWay(piece);
				},
			});

			if (animation) {
				this.animationSystem.add(animation);
			}

			return {};
		}

		const piecesToCheck = this.grid.getItemValuesAround(piece, {
			diagonal: false,
		});

		const matches = piecesToCheck.filter((adjacentPiece) => piece.match(adjacentPiece));

		if (matches.length > 0) {
			const matchingPieces = [piece, ...matches];
			const piecesToGroup = new Set(matchingPieces);

			// Remove pieces groups
			const groupsToDelete = matchingPieces
				.map((piece) => piece.group)
				.filter(Boolean) as JigsawPuzzlePieceGroup[];

			Array.from(groupsToDelete.values()).forEach((group) => {
				group.pieces.forEach((piece) => piecesToGroup.add(piece));
			});

			this.pieceGroups = this.pieceGroups.filter((group) => {
				if (groupsToDelete.includes(group)) {
					return false;
				}

				return true;
			});

			// Group pieces into one single group
			const group = new JigsawPuzzlePieceGroup();
			group.add(...piecesToGroup);
			this.pieceGroups.push(group);

			// Pick a match to be the anchored piece
			const [firstPiece] = matches;
			const anchorPiece = anchorPieceFromProps ?? firstPiece;

			// Collect all pieces that need to move (all pieces in the group except the anchor)
			const piecesToMove = Array.from(piecesToGroup).filter((p) => p !== anchorPiece);

			// Move all the other pieces to their correct location, relative to the anchor piece
			const animations = piecesToMove
				.map((pieceToMove, index) => {
					const rowDiff = pieceToMove.row - anchorPiece.row;
					const columnDiff = pieceToMove.column - anchorPiece.column;

					const animation = pieceToMove.moveTo({
						x: anchorPiece.x + columnDiff * pieceToMove.width,
						y: anchorPiece.y + rowDiff * pieceToMove.height,
						onComplete: () => {
							if (index === 0 && playSound) {
								this._playDropSound();
							}
						},
					});

					return animation;
				})
				.filter(Boolean) as CompleteAnimationEntity[];

			this.animationSystem.add(...animations);

			return { anchorPiece };
		}

		return {};
	}

	private getCanonicalPieceLocationRects(piece: JigsawPuzzlePiece) {
		const canonicalPieceLocationOnImage: Rect = {
			x:
				this._imageDimensions!.x +
				(this._imageDimensions!.width * piece.column) / this.grid.size.columns,
			y:
				this._imageDimensions!.y +
				(this._imageDimensions!.height * piece.row) / this.grid.size.rows,
			w: this._imageDimensions!.width / this.grid.size.columns,
			h: this._imageDimensions!.height / this.grid.size.rows,
		};
		const canonicalPieceLocationHitBox: Rect = {
			x:
				canonicalPieceLocationOnImage.x +
				((1 - hitboxCenterRatio) * canonicalPieceLocationOnImage.w) / 2,
			y:
				canonicalPieceLocationOnImage.y +
				((1 - hitboxCenterRatio) * canonicalPieceLocationOnImage.h) / 2,
			w: canonicalPieceLocationOnImage.w * hitboxCenterRatio,
			h: canonicalPieceLocationOnImage.h * hitboxCenterRatio,
		};

		return {
			canonicalPieceLocationHitBox,
			canonicalPieceLocationOnImage,
		};
	}

	movePieceCollisionsOutOfTheWay(piece: JigsawPuzzlePiece) {
		const pieces = this.grid.flatten();

		pieces.forEach((pieceToCheck) => {
			if (!pieceToCheck || pieceToCheck === piece || pieceToCheck.isInPlace) {
				return;
			}

			if (piece.group && pieceToCheck?.group === piece.group) {
				return;
			}

			if (
				HitBox.checkCollision(
					{
						x: piece.globalX,
						y: piece.globalY,
						h: piece.height,
						w: piece.width,
					},
					{
						x: pieceToCheck.globalX,
						y: pieceToCheck.globalY,
						h: pieceToCheck.height,
						w: pieceToCheck.width,
					},
				)
			) {
				if (pieceToCheck.group) {
					const biggestX = Math.max(...pieceToCheck.group.pieces.map((piece) => piece.x));
					const biggestY = Math.max(...pieceToCheck.group.pieces.map((piece) => piece.y));

					const animation = pieceToCheck.group.moveTo({
						x: -biggestX - piece.width,
						y: -biggestY - piece.height,
						duration: pieceMoveDuration,
					});

					if (animation) {
						this.animationSystem.add(animation);
					}
				} else {
					const animation = pieceToCheck.moveTo({
						x: -piece.width,
						y: -piece.height,
						duration: pieceMoveDuration,
					});

					if (animation) {
						this.animationSystem.add(animation);
					}
				}
			}
		});
	}

	private getCenterTween({ newZoom, duration }: { newZoom: number; duration: number }) {
		const center = this.getCenterPosition({ zoom: newZoom });
		return new Tween({
			zoom: this.camera.zoom,
			x: this.camera.x,
			y: this.camera.y,
		})
			.to({ x: center.x, y: center.y, zoom: newZoom })
			.duration(duration)
			.easing(Easing.Exponential.InOut)
			.onUpdate(({ x, y, zoom }) => {
				this.camera.zoom = zoom;
				this.camera.pos(x, y);
			});
	}

	checkGameWin() {
		if (
			this.grid.grid.every((row) => {
				return row.every((piece) => {
					return piece?.isInPlace;
				});
			})
		) {
			this.isGameWon = true;
			this.onGameWin?.();

			const strokeWidthAnimation = new Tween({
				strokeWidth: this.strokeWidth,
			})
				.to({
					strokeWidth: 0,
				})
				.easing(Easing.Quadratic.InOut)
				.duration(gameWinAnimationDuration * 0.8)
				.onUpdate(({ strokeWidth }) => {
					this.strokeWidth = strokeWidth;
				});

			this.animationSystem.add({
				id: 'game-win-stroke-width-animation',
				animation: strokeWidthAnimation,
			});

			const newZoom = this.defaultZoom + 0.3;
			const centerAnimation = this.getCenterTween({
				newZoom,
				duration: gameWinAnimationDuration,
			}).easing(Easing.Exponential.InOut);

			this.animationSystem.add({
				id: 'game-win-animation',
				animation: centerAnimation,
			});
		}
	}

	debugMovePiecesCloseToWin() {
		this.grid.grid.forEach((pieceRow, row) => {
			pieceRow.forEach((piece, col) => {
				if ((row === 0 && col === 0) || (row === 0 && col === 1)) {
					return;
				}

				if (piece) {
					const { canonicalPieceLocationOnImage } =
						this.getCanonicalPieceLocationRects(piece);

					const animation = piece.moveTo({
						x: canonicalPieceLocationOnImage.x,
						y: canonicalPieceLocationOnImage.y,
						onComplete: () => {
							piece.isInPlace = true;
							this._piecesInPlace += 1;
							this.checkGameWin();
						},
					});

					if (animation) {
						this.animationSystem.add(animation);
					}
				}
			});
		});
	}

	// TODO: Pause and resume ticker according to timer
	private initAnimationSystem() {
		this.ticker?.start();
	}

	private showHint(): JigsawPuzzlePiece | null {
		const shuffledPieces = shuffleMutate(
			this.grid.flatten().filter((piece) => !piece?.isGrouped && !piece?.isInPlace),
		);
		const piece = shuffledPieces.find((piece) => piece && !piece.isInPlace);

		if (piece) {
			this._hintsPlayed += 1;
			const { canonicalPieceLocationOnImage } = this.getCanonicalPieceLocationRects(piece);

			const animation = piece.moveTo({
				x: canonicalPieceLocationOnImage.x,
				y: canonicalPieceLocationOnImage.y,
				duration: hintMoveDuration,
				onComplete: () => {
					piece.isInPlace = true;
					this._piecesInPlace += 1;
					this.movePieceCollisionsOutOfTheWay(piece);
					this.checkGameWin();

					setTimeout(() => {
						piece.glow = false;
					}, 500);
				},
			});

			if (animation) {
				this.sounds.hint.play();
				piece.glow = true;
				this.animationSystem.add(animation);
			}
		}

		return piece ?? null;
	}

	private handleKeyDown(event: KeyboardEvent) {
		if (!this.enabled) {
			return;
		}

		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		if (import.meta.env.DEV) {
			if (event.code === 'Digit0' && event.shiftKey) {
				this.debugMovePiecesCloseToWin();
			}
		}
	}

	/**
	 * Check if camera interaction should be allowed at the given positions
	 * @param positions Array of points in canvas coordinates (relative to target element)
	 * @returns true if interaction should be allowed, false otherwise
	 */
	private canInteractAtPositions(
		positions: { x: number; y: number }[],
		eventType: CameraEventType,
	): boolean {
		if (eventType === 'wheel') {
			return true;
		}

		// Convert canvas coordinates to world coordinates
		const worldPositions = positions.map((pos) => ({
			x: (pos.x - this.camera.x) / this.camera.zoom,
			y: (pos.y - this.camera.y) / this.camera.zoom,
		}));

		// Check if any position collides with a jigsaw piece that is not in place
		const allPieces = this.grid.flatten().filter(Boolean) as JigsawPuzzlePiece[];

		for (const worldPos of worldPositions) {
			for (const piece of allPieces) {
				// Skip pieces that are already in place
				if (piece.isInPlace) {
					continue;
				}

				// Create rect for the piece using its global position and size
				const pieceRect = {
					x: piece.globalX,
					y: piece.globalY,
					w: piece.width,
					h: piece.height,
				};

				// Check if the world position is inside this piece
				if (HitBox.isPointInside(worldPos, pieceRect)) {
					// Collision found with a piece that is not in place, prevent camera interaction
					return false;
				}
			}
		}

		// No collisions found, allow camera interaction
		return true;
	}

	private addListeners() {
		// window.addEventListener('resize', () => this.handleResize, {
		// 	signal: this._abortController.signal,
		// });
		window.addEventListener('keydown', (e) => this.handleKeyDown(e), {
			signal: this._abortController.signal,
		});
	}

	dispose() {
		this._cleanupEffect();
		this.ticker?.stop();
		this._abortController.abort();
		this.camera.dispose();
		this._piecesInPlace = 0;
	}
}
