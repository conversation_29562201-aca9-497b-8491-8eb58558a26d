import Konva from 'konva';
import type { JigsawPuzzleTickerAdapter } from './JigsawPuzzleTickerAdapter';
import type { AnimationSystem } from '../../solitaire/systems/AnimationSystem';

export class JigsawPuzzleKonvaTicker implements JigsawPuzzleTickerAdapter {
	private _ticker: Konva.Animation;
	private _animationSystem: AnimationSystem;

	constructor(animationSystem: AnimationSystem) {
		this._animationSystem = animationSystem;

		this._ticker = new Konva.Animation(() => {
			this._animationSystem.update(performance.now());
		});
	}

	start() {
		this._ticker.start();
	}

	stop() {
		this._ticker.stop();
	}

	dispose() {
		this.stop();
	}
}
