import { Easing, Tween } from '@tweenjs/tween.js';
import { pieceMoveDuration, type JigsawPuzzlePiece } from './JigsawPuzzlePiece.svelte';
import type { CompleteAnimationEntity } from '../../solitaire/systems/AnimationSystem';

export class JigsawPuzzlePieceGroup {
	x = $state(0);
	y = $state(0);
	pieces = $state<JigsawPuzzlePiece[]>([]);

	constructor(props?: { x?: number; y?: number }) {
		this.x = props?.x ?? 0;
		this.y = props?.y ?? 0;
	}

	add = (...pieces: JigsawPuzzlePiece[]) => {
		// Adjust pieces position before adding them to the group
		pieces.forEach((piece) => {
			piece.x = piece.globalX;
			piece.y = piece.globalY;
		});

		// Add pieces to group
		this.pieces.push(...pieces);
		pieces.forEach((piece) => (piece.group = this));
	};

	remove = (...pieces: JigsawPuzzlePiece[]) => {
		// Adjust pieces position before adding them to the group
		pieces.forEach((piece) => {
			piece.x = piece.globalX;
			piece.y = piece.globalY;
		});

		this.pieces = this.pieces.filter((p) => !pieces.includes(p));
		pieces.forEach((piece) => (piece.group = null));
	};

	get isEmpty() {
		return this.pieces.length === 0;
	}

	moveTo({
		x,
		y,
		duration = pieceMoveDuration,
		onComplete,
	}: {
		x: number;
		y: number;
		duration?: number;
		onComplete?: () => void;
	}): CompleteAnimationEntity<Tween> | null {
		if (Math.abs(this.x - x) < 0.01 && Math.abs(this.y - y) < 0.01) {
			return null;
		}

		const animation = new Tween({
			x: this.x,
			y: this.y,
		})
			.to({ x, y })
			.duration(duration)
			.easing(Easing.Quadratic.InOut)
			.onUpdate(({ x, y }) => {
				this.x = x;
				this.y = y;
			})
			.onComplete(onComplete);

		return {
			id: `move-group-${this.x}-${this.y}`,
			animation,
		};
	}
}
