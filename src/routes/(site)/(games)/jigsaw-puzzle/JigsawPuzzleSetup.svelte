<script lang="ts">
	import { onMount, tick, untrack } from 'svelte';
	import { cn } from '$lib/util/cn';
	import { fade, fly } from 'svelte/transition';
	import { wait } from '$lib/functions/wait';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';
	import JigsawFloatingBar from './JigsawFloatingBar.svelte';
	import PlayIcon from '$lib/components/Icons/PlayIcon.svelte';
	import WallpaperAttributionLinks from '../../[[tab]]/Chill/WallpaperAttributionLinks.svelte';
	import type { Size } from '$lib/models/Size';
	import { MediaQuery } from '$lib/util/MediaQuery.svelte';
	import type { jigsawPuzzleSoundResources } from './game/jigsawPuzzleSoundResources';
	import type { GameSound } from '$lib/util/GameSound.svelte';
	import { type JigsawImage, type JigsawImages } from './data/jigsawImages';
	import { getJigsawPuzzleSize } from './util/getJigsawPuzzleSize';
	import { pieceSizes, type PieceSize } from './game/JigsawPuzzleGame.svelte';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';
	import { formatJigsawCategory } from './util/formatJigsawCategory';
	import { jigsawCategories, type JigsawCategory } from './data/jigsawCategories';
	import { pushState } from '$app/navigation';
	import JigsawPuzzleNewDialog from './JigsawPuzzleNewDialog.svelte';

	interface Props {
		jigsawImages: JigsawImages[];
		selectedImage?: JigsawImage;
		selectedCategory?: JigsawCategory;
		pieceSize: PieceSize;
		onStart: (startProps: {
			image: JigsawImage;
			category?: JigsawCategory;
			originalImageSize: Size;
			seed: number;
			container: Size;
		}) => void;
		sounds: Record<keyof typeof jigsawPuzzleSoundResources, GameSound>;
	}

	let {
		jigsawImages,
		pieceSize = $bindable(),
		onStart,
		sounds,
		selectedCategory: selectedCategoryFromProps = $bindable(),
		selectedImage: selectedImageFromProps = $bindable(),
	}: Props = $props();

	let container = $state<HTMLDivElement>();
	let seed = $state(0);
	let largeMediaQuery = new MediaQuery('min-width: 1024px');
	let smallMediaQuery = new MediaQuery('min-width: 640px');
	let wideMediaQuery = new MediaQuery('min-width: 1536px');
	let xsMediaQuery = new MediaQuery('min-width: 400px');
	let xsBellowMediaQuery = new MediaQuery('max-width: 400px');
	let selectedCategory = $state(selectedCategoryFromProps);
	let animatingCategory = $state(selectedCategoryFromProps);
	let animatingImage = $state<JigsawImage>();
	let isAnimatingCategorySelection = $state(false);
	let isAnimatingCategoryUnselection = $state(false);
	let isAnimatingInitialImageSelection = $state(
		untrack(() =>
			selectedImageFromProps
				? getImageIndexOnSelectedCategory(selectedImageFromProps) !== -1
				: false,
		),
	);
	let selectedImage = $state<JigsawImage>();
	let selectedImageElement = $state<HTMLImageElement>();
	let selectedImageRect = $state<DOMRect>();
	let alreadyLoadedCategories: Record<string, boolean> = $state({});
	let loadedImages: Record<string, boolean> = $state({});
	let failedImages: Record<string, boolean> = $state({});
	let savedScrollPosition = $state({ x: 0, y: 0 });
	let canInteract = $state(true);
	let canShowPreview = $state(false);
	let selectedImageOffset = $state({ x: 0, y: 0 });
	let selectedJigsawImages = $derived(
		jigsawImages.find((img) => img.category === selectedCategory),
	);
	let previewScale = $state(2.5);
	let imageIndexRenderRange = $state({ start: 0, end: Infinity });
	let isMounted = $state(false);
	let isCreatePuzzleDialogOpen = $state(false);

	let cols = $derived.by(() => {
		if (wideMediaQuery.current) {
			return 4;
		}
		if (largeMediaQuery.current) {
			return 3;
		}
		if (smallMediaQuery.current) {
			return 2;
		}
		if (xsMediaQuery.current) {
			return 1;
		}
		if (xsBellowMediaQuery.current) {
			return 1;
		}

		return 3;
	});
	let rows = $derived.by(() => {
		if (selectedJigsawImages) {
			return Math.ceil(selectedJigsawImages.images.length / cols);
		}

		return Math.ceil(jigsawImages.length / cols);
	});

	const zIndexes = {
		category: 99999,
		selectedImage: 999999,
		ui: 9999999,
	};

	let JigsawPuzzlePreview = $state<typeof import('./JigsawPuzzlePreview.svelte').default>();

	onMount(async () => {
		JigsawPuzzlePreview = (await import('./JigsawPuzzlePreview.svelte')).default;
		isMounted = true;
	});

	async function scrollToAndSelectImage(image: JigsawImage) {
		const imageIndex = image ? getImageIndexOnSelectedCategory(image) : -1;
		let timeoutId = -1;

		if (image && imageIndex !== -1 && JigsawPuzzlePreview && isMounted) {
			untrack(async () => {
				scrollToImage(image);

				async function selectImageFromProps() {
					const imageEl = document.getElementById(`${selectedCategory}-${image!.id}`);
					const hasSelectedImage =
						imageEl &&
						(await selectImage({
							image: image!,
							imageElement: imageEl as HTMLImageElement,
						}));

					if (!hasSelectedImage) {
						timeoutId = setTimeout(selectImageFromProps, 50) as unknown as number;
					} else {
						isAnimatingInitialImageSelection = false;
					}
				}

				selectImageFromProps();
			});
		}

		return () => {
			clearTimeout(timeoutId);
		};
	}

	$effect(function selectInitialImage() {
		const image = selectedImageFromProps;

		if (image && isAnimatingInitialImageSelection) {
			scrollToAndSelectImage(image);
		}
	});

	const rotationByIndex: Record<number, number> = {
		0: 0,
		1: 1.8,
		2: -1.8,
	};

	async function selectCategory({ category }: { category: JigsawCategory }): Promise<boolean> {
		if (!canInteract) {
			return false;
		}

		canInteract = false;
		isAnimatingCategorySelection = true;

		// Save current scroll position before scrolling to top
		if (container) {
			savedScrollPosition = {
				x: container.scrollLeft,
				y: container.scrollTop,
			};
		}

		container?.scrollTo({
			behavior: 'smooth',
			left: 0,
			top: 0,
		});

		animatingCategory = category;
		selectedCategory = category;
		selectedCategoryFromProps = category;
		alreadyLoadedCategories[category] = true;
		sounds.select.play();

		await wait(1000);

		canInteract = true;
		isAnimatingCategorySelection = false;

		return true;
	}

	async function unselectCategory(): Promise<boolean> {
		if (!canInteract) {
			return false;
		}

		canInteract = false;
		isAnimatingCategoryUnselection = true;

		// Restore saved scroll position instead of scrolling to top
		container?.scrollTo({
			behavior: 'smooth',
			left: savedScrollPosition.x,
			top: savedScrollPosition.y,
		});

		const dy = Math.abs(savedScrollPosition.y - (container?.scrollTop ?? 0));

		if (dy > 100) {
			await wait(300);
		}

		animatingCategory = selectedCategory;
		selectedCategory = undefined;
		selectedCategoryFromProps = undefined;

		await tick();

		sounds.swoosh.play();
		isAnimatingCategoryUnselection = false;

		canInteract = true;

		return true;
	}

	function getLink({ category, image }: { category: JigsawCategory; image?: JigsawImage }) {
		if (!image) {
			return `/jigsaw-puzzle/${category}`;
		}

		return `/jigsaw-puzzle/${category}/${image.id}`;
	}

	async function selectImage({
		image,
		imageElement,
	}: {
		image: JigsawImage;
		imageElement: HTMLImageElement;
	}): Promise<boolean> {
		if (!canInteract || !loadedImages[image.url] || !container) {
			return false;
		}

		canInteract = false;
		container.style.overflow = 'hidden';
		selectedImage = image;
		animatingImage = image;
		selectedImageElement = imageElement;
		sounds.select.play();

		// Distance between image and viewport center
		const rect = imageElement.getBoundingClientRect();
		const dx = rect.left + rect.width / 2 - container.offsetWidth / 2;
		const dy = rect.top + rect.height / 2 - container.offsetHeight / 2;

		selectedImageOffset = {
			x: -dx,
			y: -dy,
		};

		const bodyRect = document.body.getBoundingClientRect();

		previewScale = Math.min(
			2.5,
			(bodyRect.width - 32) / Math.min(rect.width, 640),
			(bodyRect.height - 224) / Math.min(rect.height, 640),
		);

		await wait(600);

		if (selectedImage.url === image.url) {
			selectedImageRect = imageElement.getBoundingClientRect();
			canShowPreview = true;
		}

		canInteract = true;
		selectedImageFromProps = image;

		return true;
	}

	async function unselectImage(): Promise<boolean> {
		if (!canInteract || !container) {
			return false;
		}

		animatingImage = selectedImage;
		canInteract = false;
		selectedImage = undefined;
		selectedImageElement = undefined;
		selectedImageRect = undefined;
		container.style.overflow = 'auto';
		canShowPreview = false;
		sounds.swoosh.play();

		await wait(600);
		canInteract = true;
		animatingImage = undefined;
		selectedImageFromProps = undefined;

		return true;
	}

	function selectSize(size: PieceSize) {
		if (!canInteract) {
			return;
		}

		pieceSize = size;
	}

	async function onResize() {
		updateImageIndexRangerRange();

		if (selectedImage) {
			if (await unselectImage()) {
				pushState(`/jigsaw-puzzle/${selectedCategory}`, {});
			}
		}
	}

	/** Virtualize images */

	let estimatedImageHeightElement = $state<HTMLDivElement>();
	const gap = 16;

	function updateImageIndexRangerRange() {
		if (!container || !estimatedImageHeightElement) {
			imageIndexRenderRange = { start: 0, end: Infinity };
			return;
		}

		const estimatedImageHeight = estimatedImageHeightElement?.offsetHeight ?? 0;
		const viewportHeight = container.offsetHeight ?? 0;
		const paddingTop = +getComputedStyle(container).paddingTop.replace('px', '');

		const extraRows = 2;

		const rowStart = Math.max(
			0,
			Math.floor((container.scrollTop - paddingTop) / (estimatedImageHeight + gap)) -
				extraRows,
		);
		const rowEnd = Math.max(
			0,
			Math.ceil(
				(container.scrollTop + viewportHeight - paddingTop) / (estimatedImageHeight + gap),
			) + extraRows,
		);

		imageIndexRenderRange = {
			start: rowStart * cols,
			end: rowEnd * cols,
		};
	}

	function getImageIndexOnSelectedCategory(image: Pick<JigsawImage, 'id'>) {
		if (!selectedCategory) {
			return -1;
		}

		const category = jigsawImages.find(({ category }) => category === selectedCategory);

		if (!category) {
			console.error(`Could not find category ${category}`);
			return -1;
		}

		return category.images.findIndex((img) => img.id === image.id);
	}

	function scrollToImage(image: JigsawImage) {
		if (!container) {
			return;
		}
		const initialImageIndex = getImageIndexOnSelectedCategory(image);

		if (initialImageIndex === -1) {
			console.error(`Cannot scroll to image ${image.url} if it's category is not selected`);
		}

		const row = Math.floor(initialImageIndex / cols);
		const estimatedImageHeight = estimatedImageHeightElement?.offsetHeight ?? 0;
		const paddingTop = +getComputedStyle(container).paddingTop.replace('px', '');

		container?.scrollTo({
			behavior: 'instant',
			left: 0,
			top:
				row * (estimatedImageHeight + gap) +
				paddingTop -
				container.offsetHeight / 2 +
				estimatedImageHeight / 2,
		});
	}

	let attribution = $derived.by(() => {
		if (isAnimatingInitialImageSelection) {
			return selectedImageFromProps?.attribution;
		}

		return selectedImage?.attribution;
	});

	async function onPopState() {
		const [_, __, categoryFromUrl, imageFromUrl] = location.pathname.split('/');

		if (categoryFromUrl) {
			if (!imageFromUrl && selectedImage) {
				await unselectImage();
			}

			if (
				selectedCategory !== categoryFromUrl &&
				jigsawCategories.includes(categoryFromUrl as any)
			) {
				await selectCategory({ category: categoryFromUrl as any });
			}

			if (imageFromUrl && selectedImage?.id !== imageFromUrl) {
				const index = getImageIndexOnSelectedCategory({ id: imageFromUrl });

				if (index !== -1) {
					const imageAndCategory = jigsawImages.find(
						({ category }) => categoryFromUrl === category,
					);

					if (imageAndCategory) {
						await scrollToAndSelectImage(imageAndCategory.images[index]);
					}
				}
			}
		} else {
			await unselectCategory();
		}
	}
</script>

<svelte:window onresize={onResize} onpopstate={onPopState} />

<JigsawPuzzleNewDialog
	bind:isOpen={isCreatePuzzleDialogOpen}
	{sounds}
	{onStart}
	bind:pieceSize
	variant={selectedCategory === 'community' ? 'community' : 'custom'}
/>

<div
	class="relative flex flex-col size-full max-h-screen overflow-auto [scrollbar-gutter:stable] pt-24 z-0"
	bind:this={container}
	onscroll={updateImageIndexRangerRange}
>
	<!-- Floating bar -->
	{#if isMounted}
		<div transition:fade={{ duration: 300 }} style="z-index: {zIndexes.ui}">
			{#if attribution}
				<div
					in:fly={{
						y: 20,
						duration: isAnimatingInitialImageSelection ? 0 : 300,
						delay: isAnimatingInitialImageSelection ? 0 : 300,
					}}
					out:fly={{ y: 20, duration: 300 }}
					class="fixed bottom-24 left-1/2 -translate-x-1/2 z-10 flex items-center justify-center w-full px-4"
				>
					<WallpaperAttributionLinks
						class="mb-2"
						attribution={{
							...attribution,
							work: {
								...attribution.work,
								name: 'Image',
							},
						}}
					/>
					<div class="sr-only">{attribution.work.name}</div>
				</div>
			{/if}

			{#if selectedImage || isAnimatingInitialImageSelection}
				<JigsawFloatingBar>
					<button
						class="btn btn-ghost btn-circle"
						onclick={async (e) => {
							if (e.metaKey || e.ctrlKey) {
								window.open(getLink({ category: selectedCategory! }), '_blank');
							} else {
								if (await unselectImage()) {
									pushState(`/jigsaw-puzzle/${selectedCategory}`, {});
								}
							}
						}}
						aria-label="Back to images"
					>
						<ChevronLeftIcon class="size-6" />
					</button>

					{#each pieceSizes as size}
						<button
							class={cn('btn btn-circle uppercase', {
								'btn-secondary': pieceSize === size,
								'btn-ghost': pieceSize !== size,
								'hidden md:block':
									pieceSizes.indexOf(size) >= pieceSizes.indexOf('xl'),
							})}
							onclick={() => {
								selectSize(size);
							}}
							aria-label={`Select ${size} pieces`}
						>
							{size}
						</button>
					{/each}

					<button
						class="btn btn-circle btn-primary"
						onclick={() => {
							if (
								selectedImage &&
								canInteract &&
								selectedImageElement &&
								selectedCategory
							) {
								onStart({
									image: selectedImage,
									category: selectedCategory,
									originalImageSize: {
										width: selectedImageElement.naturalWidth,
										height: selectedImageElement.naturalHeight,
									},
									seed,
									container: {
										width: container?.offsetWidth ?? 0,
										height: container?.offsetHeight ?? 0,
									},
								});
							}
						}}
						aria-label="Play jigsaw puzzle"
					>
						<PlayIcon variant="play" class="size-6" />
					</button>
				</JigsawFloatingBar>
			{:else if selectedCategory}
				<JigsawFloatingBar>
					<button
						class="btn btn-ghost btn-circle"
						onclick={async (e) => {
							if (e.metaKey || e.ctrlKey) {
								window.open('/jigsaw-puzzle', '_blank');
							} else {
								if (await unselectCategory()) {
									pushState('/jigsaw-puzzle', {});
								}
							}
						}}
						aria-label="Back to categories"
					>
						<ChevronLeftIcon class="size-6" />
					</button>

					{#if selectedCategory === 'community'}
						<div>
							Pick a <span class="max-sm:hidden">community</span> puzzle or
						</div>
						<button
							class="btn btn-primary rounded-full"
							onclick={() => (isCreatePuzzleDialogOpen = true)}
						>
							Create one
						</button>
					{:else}
						Pick an image
					{/if}
				</JigsawFloatingBar>
			{:else}
				<JigsawFloatingBar>
					Pick a category or <button
						class="btn btn-primary rounded-full"
						onclick={() => (isCreatePuzzleDialogOpen = true)}
					>
						Create a puzzle
					</button>
				</JigsawFloatingBar>
			{/if}
		</div>
	{/if}

	<!-- Preview -->
	{#if selectedImage && selectedImageRect && JigsawPuzzlePreview && canShowPreview}
		{@const { rows, columns: cols } = getJigsawPuzzleSize(pieceSize, selectedImageRect)}
		{@const { top, left, width, height } = selectedImageRect}
		<div
			class="fixed bg-base-100/80"
			style="top: {top}px; left: {left}px; width: {width}px; height: {height}px; z-index: {zIndexes.selectedImage +
				1};"
			in:fade={{ duration: 400 }}
		>
			<JigsawPuzzlePreview
				src={selectedImage.url}
				{rows}
				{cols}
				noOutTransition
				bind:seed
				scale={1}
				strokeWidth={1}
				{sounds}
			/>
		</div>
	{/if}

	<!-- Images -->
	<div class="grow px-4">
		{#if isMounted}
			<div class="relative z-0">
				<div
					class="aspect-video last-of-type:h-32 absolute -top-full -left-full pointer-events-none"
					style={`width: calc((100% - 16 * ${cols - 1}px) / ${cols});`}
					bind:this={estimatedImageHeightElement}
				></div>

				<!-- Placeholder to fix layout shift and bottom padding -->
				<div
					class="w-full"
					style={`padding-bottom: calc((${rows} * (9/16) * (100% - 16 * ${cols - 1}px) / ${cols}) + ${(rows - 1) * 16}px + 128px)`}
				></div>

				{#each jigsawImages as { images, category }, categoryIndex (category)}
					{#each images as image, imageIndex (`${category}-${image.id}`)}
						{@const isCategorySelected = selectedCategory === category}
						{@const index = isCategorySelected ? imageIndex : categoryIndex}
						{@const rotation = isCategorySelected
							? 0
							: (rotationByIndex[imageIndex] ?? 0)}
						{@const isImageSelected =
							isCategorySelected && selectedImage?.id === image.id}
						{@const canRender =
							selectedCategory ||
							isAnimatingCategoryUnselection ||
							isAnimatingCategoryUnselection
								? imageIndexRenderRange.start <= imageIndex &&
									imageIndex <= imageIndexRenderRange.end
								: true}

						{#if canRender}
							<button
								class={cn(
									'absolute top-0 left-0 transition-[transform,opacity] duration-500 cursor-pointer aspect-video',
									{
										'opacity-0 pointer-events-none':
											(selectedCategory && selectedCategory !== category) ||
											(selectedImage && selectedImage.id !== image.id),
										'cursor-default': selectedImage,
										'transition-transform':
											imageIndex !== 0 && isAnimatingCategorySelection,
										'delay-500':
											imageIndex !== 0 &&
											isAnimatingCategoryUnselection &&
											animatingCategory !== category,
									},
								)}
								disabled={!!selectedImage}
								style={`
									transform: translate(
										calc(${100 * (index % cols)}% + ${16 * (index % cols)}px + ${isImageSelected ? selectedImageOffset.x : 0}px),
										calc(${100 * Math.floor(index / cols)}% + ${16 * Math.floor(index / cols)}px + ${isImageSelected ? selectedImageOffset.y : 0}px)
										) rotate(${rotation}deg) scale(${isImageSelected ? previewScale : 1});
									width: calc((100% - 16 * ${cols - 1}px) / ${cols});
									z-index: ${isImageSelected || animatingImage?.id === image.id ? zIndexes.selectedImage : (category === selectedCategory || category === animatingCategory ? zIndexes.category : 0) + images.length - imageIndex};
								`}
								onclick={async (event: MouseEvent) => {
									if (event.metaKey || event.ctrlKey) {
										if (selectedCategory) {
											window.open(getLink({ category, image }), '_blank');
										} else {
											window.open(getLink({ category }), '_blank');
										}
									} else {
										if (selectedCategory) {
											if (
												await selectImage({
													image,
													imageElement: event.target as HTMLImageElement,
												})
											) {
												pushState(getLink({ category, image }), {});
											}
										} else {
											if (await selectCategory({ category })) {
												pushState(getLink({ category }), {});
											}
										}
									}
								}}
								aria-label={selectedCategory
									? `Select ${image.attribution?.work.name} image`
									: `Select ${formatJigsawCategory(category)} category`}
							>
								<div
									class={cn('size-full transition-[clip-path] duration-500', {
										'[clip-path:inset(0%_0%_0%_0%_round_var(--radius-box,1rem))] overflow-hidden':
											!isImageSelected,
										'[clip-path:inset(-100%_-100%_-100%_-100%_round_0)]':
											isImageSelected,
									})}
								>
									{#if isCategorySelected || alreadyLoadedCategories[category] || imageIndex < 3}
										<img
											id={`${category}-${image.id}`}
											class={cn(
												'w-full absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 [scrollbar-color:initial] min-h-0',
												{
													'bg-base-300 min-h-48 aspect-video opacity-0':
														!loadedImages[image.url],
													'min-h-full': !isImageSelected,
												},
											)}
											style="transition: min-height 100ms ease-in-out, opacity 500ms ease-in-out, background-color 500ms ease-in-out, translform 500ms ease-in-out;"
											alt=""
											src={image.url}
											loading="lazy"
											onload={() => {
												loadedImages[image.url] = true;
												failedImages[image.url] = false;
											}}
											onerror={() => {
												if (loadedImages[image.url]) {
													return;
												}
												failedImages[image.url] = true;
											}}
										/>
									{/if}

									{#if failedImages[image.url]}
										<div
											class={cn(
												'flex items-center justify-center flex-col gap-2 transition-all duration-500 w-full absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-base-300 min-h-48 aspect-video z-10',
											)}
										>
											<WarningSolidIcon class="size-12" />
											<span>Failed to load the image</span>
										</div>
									{/if}

									{#if !loadedImages[image.url]}
										<div
											out:fade
											class="bg-base-300 absolute inset-0 rounded-box z-10"
										></div>
									{/if}
								</div>

								{#if imageIndex === 0 && !selectedCategory}
									<div
										class="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/5 flex items-center rounded-full px-4 py-2 z-50 glass text-nowrap"
										in:fly={{
											y: -20,
											duration: 300,
											delay: 400 + 40 * categoryIndex,
										}}
										out:fly={{ y: -20, duration: 300 }}
									>
										<span class="text-lg font-medium">
											{formatJigsawCategory(category)}
										</span>
									</div>

									{#if category === 'community'}
										<div class="absolute top-0 right-0">
											<div class="badge badge-md badge-secondary">New</div>
										</div>
									{/if}
								{/if}
							</button>
						{/if}
					{/each}
				{/each}
			</div>
		{:else}
			<div class="size-full flex items-center justify-center">
				<div class="loading loading-xl"></div>
			</div>
		{/if}
	</div>
</div>
