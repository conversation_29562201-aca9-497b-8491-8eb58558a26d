import type { Point2D } from '$lib/models/Point2D';

export type CameraEventType = 'wheel' | 'mousedown' | 'touchstart' | 'touchmove';

interface Camera2DProps {
	target: HTMLElement;
	enabled?: boolean;
	minZoom?: number;
	maxZoom?: number;
}

export class Camera2D {
	target: HTMLElement;

	// Callback for when zoom changes - can be set by external code
	onZoomChange?: (newZoom: number, oldZoom: number) => void;

	// Callback to check if interaction should be allowed at given positions
	canInteract?: (positions: Point2D[], eventType: CameraEventType) => boolean;

	private _zoom = $state(1);
	private _minZoom: number;
	private _maxZoom: number;
	private _isPinching = false;
	private _lastPinchDistance = 0;
	private _pinchCenter = { x: 0, y: 0 };
	private _isMouseDragging = false;
	private _lastMousePos = { x: 0, y: 0 };
	private _isTouchDragging = false;
	private _lastTouchPos = { x: 0, y: 0 };
	private _enabled = $state(true);
	private _isRealCtrlPressed = false;
	private abortController = new AbortController();

	constructor({ target, enabled = true, minZoom = 0.25, maxZoom = 3 }: Camera2DProps) {
		this.target = target;
		this._enabled = enabled;
		this._minZoom = minZoom;
		this._maxZoom = maxZoom;

		// Ensure initial zoom is within bounds
		this._zoom = Math.max(minZoom, Math.min(maxZoom, this._zoom));

		this.initListeners();
	}

	private _pos: Point2D = $state({
		x: 0,
		y: 0,
	});

	get viewport() {
		const rect = this.target.getBoundingClientRect();

		return {
			width: rect.width,
			height: rect.height,
		};
	}

	get x() {
		return this._pos.x;
	}

	set x(value: number) {
		this._pos.x = value;
	}

	get y() {
		return this._pos.y;
	}

	set y(value: number) {
		this._pos.y = value;
	}

	get enabled() {
		return this._enabled;
	}

	set enabled(value: boolean) {
		this._enabled = value;
	}

	get zoom() {
		return this._zoom;
	}

	set zoom(value: number) {
		// Constrain zoom value within bounds
		const newZoom = Math.max(this._minZoom, Math.min(this._maxZoom, value));
		const oldZoom = this._zoom;
		this._zoom = newZoom;

		// Trigger zoom change callback if zoom actually changed
		if (oldZoom !== newZoom && this.onZoomChange) {
			this.onZoomChange(newZoom, oldZoom);
		}
	}

	get minZoom() {
		return this._minZoom;
	}

	get maxZoom() {
		return this._maxZoom;
	}

	pos(x: number, y: number) {
		this.x = x;
		this.y = y;
	}

	moveBy(x: number, y: number) {
		this.x += x;
		this.y += y;
	}

	zoomBy(zoom: number) {
		this.zoom = this.zoom + zoom;
	}

	private onWheel(event: WheelEvent) {
		if (!this.enabled) {
			return;
		}

		// Get pointer position relative to the target element
		const rect = this.target.getBoundingClientRect();
		const pointer = {
			x: event.clientX - rect.left,
			y: event.clientY - rect.top,
		};

		// Check if interaction is allowed at this position
		if (this.canInteract && !this.canInteract([pointer], 'wheel')) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

		// Handle zoom events
		if (Math.abs(event.deltaY) > 0) {
			// Real Ctrl+wheel zoom (for non-Mac devices)
			if (event.ctrlKey && this._isRealCtrlPressed) {
				this.handleWheelZoom(event, pointer, 0.001);
				return;
			}

			// Trackpad pinch-to-zoom gestures (ctrlKey without real Ctrl press)
			if (event.ctrlKey && !this._isRealCtrlPressed) {
				this.handleTrackpadPinch(event);
				return;
			}

			// Explicit zoom with modifier keys (for mouse wheel + modifier)
			if (event.metaKey || event.altKey) {
				this.handleWheelZoom(event, pointer, 0.001);
				return;
			}
		}

		// Handle horizontal scroll with shift key
		if (event.shiftKey) {
			this.moveBy(-event.deltaY, 0);
			return;
		}

		// Default behavior: two-finger scroll for camera panning
		this.moveBy(-event.deltaX, -event.deltaY);
	}

	private handleWheelZoom(event: WheelEvent, pointer: Point2D, sensitivity: number) {
		// Store old zoom value
		const oldZoom = this.zoom;

		// Calculate the point in world coordinates before zoom
		const mousePointTo = {
			x: (pointer.x - this.x) / oldZoom,
			y: (pointer.y - this.y) / oldZoom,
		};

		// Calculate zoom delta proportional to wheel delta
		const zoomDelta = -event.deltaY * sensitivity;

		// Calculate new zoom level
		const newZoom = oldZoom + zoomDelta;

		// Update zoom (setter will handle limits)
		this.zoom = newZoom;

		// Only update position if zoom actually changed
		if (this.zoom !== oldZoom) {
			// Calculate new position to keep the pointer position fixed
			const newPos = {
				x: pointer.x - mousePointTo.x * this.zoom,
				y: pointer.y - mousePointTo.y * this.zoom,
			};

			// Update position
			this.pos(newPos.x, newPos.y);
		}
	}

	private handleTrackpadPinch(event: WheelEvent) {
		// Get pointer position relative to the target element
		const rect = this.target.getBoundingClientRect();
		const pointer = {
			x: event.clientX - rect.left,
			y: event.clientY - rect.top,
		};

		// Trackpad pinch gestures use higher sensitivity
		this.handleWheelZoom(event, pointer, 0.01);
	}

	private onMouseDown(event: MouseEvent) {
		if (!this.enabled) {
			return;
		}

		// Get mouse position relative to the target element
		const rect = this.target.getBoundingClientRect();
		const mousePos = {
			x: event.clientX - rect.left,
			y: event.clientY - rect.top,
		};

		// Check if interaction is allowed at this position
		if (this.canInteract && !this.canInteract([mousePos], 'mousedown')) {
			return;
		}
		if (event.cancelable) {
			event.preventDefault();
		}

		this._isMouseDragging = true;
		this._lastMousePos = {
			x: event.clientX,
			y: event.clientY,
		};

		// Change cursor to indicate dragging mode
		this.target.style.cursor = 'grabbing';
	}

	private onMouseMove(event: MouseEvent) {
		if (!this.enabled || !this._isMouseDragging) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

		// Calculate mouse movement delta
		const deltaX = event.clientX - this._lastMousePos.x;
		const deltaY = event.clientY - this._lastMousePos.y;

		// Move camera by the delta
		this.moveBy(deltaX, deltaY);

		// Update last mouse position
		this._lastMousePos = {
			x: event.clientX,
			y: event.clientY,
		};
	}

	private onMouseUp() {
		if (!this.enabled) {
			return;
		}

		// Check for middle mouse button release
		if (this._isMouseDragging) {
			this._isMouseDragging = false;

			// Reset cursor
			this.target.style.cursor = '';
		}
	}

	private onTouchStart(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		if (event.touches.length === 1) {
			// Single touch - handle dragging
			const touch = event.touches[0];
			const rect = this.target.getBoundingClientRect();
			const touchPos = {
				x: touch.clientX - rect.left,
				y: touch.clientY - rect.top,
			};

			// Check if interaction is allowed at this position
			if (this.canInteract && !this.canInteract([touchPos], 'touchstart')) {
				return;
			}

			this._isTouchDragging = true;
			this._lastTouchPos = {
				x: touch.clientX,
				y: touch.clientY,
			};
		} else if (event.touches.length === 2) {
			// Two touches - handle pinch gesture
			// Stop single touch dragging if it was active
			this._isTouchDragging = false;

			const touch1 = event.touches[0];
			const touch2 = event.touches[1];

			// Calculate center point between the two touches
			const rect = this.target.getBoundingClientRect();
			const pinchCenter = {
				x: (touch1.clientX + touch2.clientX) / 2 - rect.left,
				y: (touch1.clientY + touch2.clientY) / 2 - rect.top,
			};

			// Get touch positions relative to the target element
			const touchPositions = [
				{ x: touch1.clientX - rect.left, y: touch1.clientY - rect.top },
				{ x: touch2.clientX - rect.left, y: touch2.clientY - rect.top },
			];

			// Check if interaction is allowed at these positions
			if (this.canInteract && !this.canInteract(touchPositions, 'touchstart')) {
				return;
			}

			this._isPinching = true;
			this._lastPinchDistance = Math.hypot(
				touch2.clientX - touch1.clientX,
				touch2.clientY - touch1.clientY,
			);
			this._pinchCenter = pinchCenter;
		}
	}

	private onTouchMove(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

		if (this._isPinching && event.touches.length === 2) {
			// Handle pinch gesture
			const touch1 = event.touches[0];
			const touch2 = event.touches[1];

			// Get touch positions relative to the target element for canInteract check
			const rect = this.target.getBoundingClientRect();
			const touchPositions = [
				{ x: touch1.clientX - rect.left, y: touch1.clientY - rect.top },
				{ x: touch2.clientX - rect.left, y: touch2.clientY - rect.top },
			];

			// Check if interaction is allowed at these positions
			if (this.canInteract && !this.canInteract(touchPositions, 'touchmove')) {
				return;
			}

			// Calculate current distance between touches
			const currentDistance = Math.hypot(
				touch2.clientX - touch1.clientX,
				touch2.clientY - touch1.clientY,
			);

			// Calculate zoom factor based on distance change
			const zoomFactor = currentDistance / this._lastPinchDistance;

			// Store old zoom value
			const oldZoom = this.zoom;

			// Calculate the point in world coordinates before zoom (using pinch center)
			const mousePointTo = {
				x: (this._pinchCenter.x - this.x) / oldZoom,
				y: (this._pinchCenter.y - this.y) / oldZoom,
			};

			// Apply zoom
			const newZoom = oldZoom * zoomFactor;
			this.zoom = newZoom;

			// Only update position if zoom actually changed
			if (this.zoom !== oldZoom) {
				// Calculate new position to keep the pinch center fixed
				const newPos = {
					x: this._pinchCenter.x - mousePointTo.x * this.zoom,
					y: this._pinchCenter.y - mousePointTo.y * this.zoom,
				};

				// Update position
				this.pos(newPos.x, newPos.y);
			}

			// Update last distance for next frame
			this._lastPinchDistance = currentDistance;
		} else if (this._isTouchDragging && event.touches.length === 1) {
			// Handle single touch dragging
			const touch = event.touches[0];
			const rect = this.target.getBoundingClientRect();
			const touchPos = {
				x: touch.clientX - rect.left,
				y: touch.clientY - rect.top,
			};

			// Check if interaction is allowed at this position
			if (this.canInteract && !this.canInteract([touchPos], 'touchmove')) {
				return;
			}

			// Calculate touch movement delta
			const deltaX = touch.clientX - this._lastTouchPos.x;
			const deltaY = touch.clientY - this._lastTouchPos.y;

			// Move camera by the delta
			this.moveBy(deltaX, deltaY);

			// Update last touch position
			this._lastTouchPos = {
				x: touch.clientX,
				y: touch.clientY,
			};
		}
	}

	private onTouchEnd(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		// Reset pinching state when touches end
		if (event.touches.length < 2) {
			this._isPinching = false;
			this._lastPinchDistance = 0;
		}

		// Reset touch dragging state when no touches remain
		if (event.touches.length === 0) {
			this._isTouchDragging = false;
		}
	}

	private onKeyDown(event: KeyboardEvent) {
		// Track real Control key presses
		if (event.code === 'ControlLeft' || event.code === 'ControlRight') {
			this._isRealCtrlPressed = true;
		}
	}

	private onKeyUp(event: KeyboardEvent) {
		// Track real Control key releases
		if (event.code === 'ControlLeft' || event.code === 'ControlRight') {
			this._isRealCtrlPressed = false;
		}
	}

	private initListeners() {
		this.target.addEventListener('wheel', (e) => this.onWheel(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchstart', (e) => this.onTouchStart(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchmove', (e) => this.onTouchMove(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchend', (e) => this.onTouchEnd(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('mousedown', (e) => this.onMouseDown(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('mousemove', (e) => this.onMouseMove(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('mouseup', (e) => this.onMouseUp(e), {
			signal: this.abortController.signal,
		});

		// Add keyboard event listeners to window to track Control key state
		// Use window to ensure we capture key events even when focus is elsewhere
		window.addEventListener('keydown', (e) => this.onKeyDown(e), {
			signal: this.abortController.signal,
		});
		window.addEventListener('keyup', (e) => this.onKeyUp(e), {
			signal: this.abortController.signal,
		});
	}

	dispose() {
		this.abortController.abort();
	}
}
