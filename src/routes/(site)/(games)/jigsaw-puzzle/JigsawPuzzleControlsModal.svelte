<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';
	import { isMacLike } from '$lib/functions/isMacLike';
	import { onMount } from 'svelte';

	interface Props {
		isOpen: boolean;
	}

	let { isOpen = $bindable() }: Props = $props();

	let isMac = $state(false);

	onMount(() => {
		isMac = isMacLike();
	});
</script>

<Dialog bind:isOpen>
	<article>
		<h3>Controls</h3>

		<div class="overflow-x-auto">
			<table class="table">
				<thead>
					<tr>
						<th>Action</th>
						<th>Keybinding</th>
					</tr>
				</thead>

				<tbody>
					<tr>
						<td>Drag Piece</td>
						<td>Click on piece and drag</td>
					</tr>
					<tr>
						<td>Move View</td>
						<td
							>Click on background and drag <br /> or Move with two fingers on trackpad</td
						>
					</tr>
					<tr>
						<td>Move View Up/Down</td>
						<td>Scroll mouse wheel</td>
					</tr>
					<tr>
						<td>Move view Left/Right</td>
						<td
							><kbd class="kbd dark:text-base-content">Shift</kbd> + Scroll mouse wheel</td
						>
					</tr>
					<tr>
						<td>Zoom In/Out</td>
						<td>
							<kbd class="kbd dark:text-base-content">{isMac ? '⌘' : 'Ctrl'}</kbd>
							+ Scroll mouse wheel or <br />
							<kbd class="kbd dark:text-base-content">{isMac ? '⌘' : 'Ctrl'}</kbd> +
							Move with two fingers on trackpad or<br /> Pinch with two fingers on trackpad
						</td>
					</tr>
					<tr>
						<td>Exit Puzzle</td>
						<td>
							<kbd class="kbd dark:text-base-content">Shift</kbd> +
							<kbd class="kbd dark:text-base-content">N</kbd>
						</td>
					</tr>
					<tr>
						<td>Show Hint</td>
						<td>
							<kbd class="kbd dark:text-base-content">H</kbd>
						</td>
					</tr>
					<tr>
						<td>Centralize</td>
						<td>
							<kbd class="kbd dark:text-base-content">C</kbd>
						</td>
					</tr>
					<tr>
						<td>Toggle Image</td>
						<td>
							<kbd class="kbd dark:text-base-content">I</kbd>
						</td>
					</tr>
					<tr>
						<td>Toggle Fullscreen</td>
						<td>
							<kbd class="kbd dark:text-base-content">F</kbd>
						</td>
					</tr>
					<tr>
						<td>Toggle Pause</td>
						<td>
							<kbd class="kbd dark:text-base-content">P</kbd>
						</td>
					</tr>
					<tr>
						<td>Toggle Controls</td>
						<td>
							<kbd class="kbd dark:text-base-content">K</kbd>
						</td>
					</tr>
				</tbody>
			</table>
		</div>
	</article>
</Dialog>
