<script lang="ts">
	import { MetaTags } from 'svelte-meta-tags';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import JigsawPuzzle from '../../JigsawPuzzle.svelte';
	import { page } from '$app/state';
	import { formatJigsawCategory } from '../../util/formatJigsawCategory';
	import { onMount, tick } from 'svelte';
	import { jigsawCategories } from '../../data/jigsawCategories';
	import { replaceState } from '$app/navigation';
	import type { PageProps } from './$types';

	let { data }: PageProps = $props();
	let jigsawImages = data.jigsawImages;
	let categoryParam = page.params.category;
	let idParam = page.params.imageId;
	let categoryAndImages = jigsawImages.find((img) => img.category === categoryParam);
	let category = $state(categoryAndImages?.category);
	let image = $state(categoryAndImages?.images.find((image) => image.id === idParam));

	onMount(async () => {
		await tick();

		if (!jigsawCategories.includes(categoryParam as any)) {
			replaceState('/jigsaw-puzzle', {});
		} else if (!image) {
			replaceState(`/jigsaw-puzzle/${categoryParam}`, {});
		}
	});

	let titlePrefix = $derived.by(() => {
		if (categoryParam) {
			if (!image) {
				return `${formatJigsawCategory(page.params.category as any)} Puzzles`;
			} else {
				const creators = image.attribution?.creator
					? (Array.isArray(image.attribution.creator)
							? image.attribution.creator
							: [image.attribution.creator]
						).filter(Boolean)
					: [];

				return (
					[
						image.attribution?.work.name,
						...creators.map((creator) => creator.name).join(', '),
					]
						.filter(Boolean)
						.join(' - ') || `Game ${image.id}`
				);
			}
		}
	});
</script>

<MetaTags
	title="{titlePrefix} | Play Jigsaw Puzzle Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play jigsaw puzzle online for free. Beautiful jigsaw puzzle game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/jigsaw-puzzle"
	openGraph={{
		url: 'https://www.lofiandgames.com/jigsaw-puzzle',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-jigsaw-puzzle.png',
				width: 1200,
				height: 630,
				alt: 'Jigsaw Puzzle Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Jigsaw Puzzle on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-jigsaw-puzzle.png',
		site: 'https://www.lofiandgames.com/jigsaw-puzzle',
	}}
/>

<PageTransition>
	<JigsawPuzzle {jigsawImages} bind:selectedCategory={category} bind:selectedImage={image} />
</PageTransition>
