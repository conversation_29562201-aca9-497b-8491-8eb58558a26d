import type { EntryGenerator } from './$types';
import { load as _load } from '../../+page.server';
import { fetchAllPuzzles } from '../../util/fetchAllPuzzles';

export const entries: EntryGenerator = async () => {
	const { jigsawImages } = await fetchAllPuzzles({ fetchFunc: fetch });

	return jigsawImages.flatMap(({ category, images }) => {
		return images.map((image) => {
			return {
				category,
				imageId: image.id,
			};
		});
	});
};

export const load = ({ fetch }) => {
	return fetchAllPuzzles({ fetchFunc: fetch });
};
