export const starBattleSizes = [6, 8, 10] as const;

export type StarBattleSize = (typeof starBattleSizes)[number];

export const starBattleStars = [1, 2] as const;

export type StarBattleStars = (typeof starBattleStars)[number];

export interface StarBattleDifficulty {
	stars: StarBattleStars;
	size: StarBattleSize;
}

export const starBattleDifficulties: StarBattleDifficulty[] = [
	{
		size: 6,
		stars: 1,
	},
	{
		size: 8,
		stars: 1,
	},
	{
		size: 10,
		stars: 1,
	},
	{
		size: 10,
		stars: 2,
	},
];
