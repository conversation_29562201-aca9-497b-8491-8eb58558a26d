<script lang="ts">
	import { StarBattleCellState } from './StarBattleGame.svelte';
	import { cn } from '$lib/util/cn';
	import type { StarBattleGame } from './StarBattleGame.svelte';
	import type { GridItem } from '$lib/models/GridItem';
	import StarSolidIcon from '$lib/components/Icons/StarSolidIcon.svelte';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';
	import { isSameGridItem } from '$lib/functions/isSameGridItem';
	import LightBulbIcon from '$lib/components/Icons/LightBulbIcon.svelte';
	import { shuffle } from '$lib/functions/shuffle';

	interface Props {
		game: StarBattleGame;
		class?: string;
		onpointermove?: (event: PointerEvent) => void;
		onCellClick?: (cell: { item: GridItem; value: StarBattleCellState }) => void;
		disabled?: boolean;
		shuffleColors?: boolean;
		borderVariant?: 'thick' | 'thin';
	}

	let {
		game,
		class: classFromProps = '',
		onpointermove = () => {},
		onCellClick,
		disabled,
		shuffleColors = true,
		borderVariant = 'thick',
	}: Props = $props();

	const colors = [
		'bg-game-star-battle-region-1',
		'bg-game-star-battle-region-2',
		'bg-game-star-battle-region-3',
		'bg-game-star-battle-region-4',
		'bg-game-star-battle-region-5',
		'bg-game-star-battle-region-6',
		'bg-game-star-battle-region-7',
		'bg-game-star-battle-region-8',
		'bg-game-star-battle-region-9',
		'bg-game-star-battle-region-10',
		'bg-game-star-battle-region-11',
		'bg-game-star-battle-region-12',
		'bg-game-star-battle-region-13',
		'bg-game-star-battle-region-14',
		'bg-game-star-battle-region-15',
		'bg-game-star-battle-region-16',
		'bg-game-star-battle-region-17',
		'bg-game-star-battle-region-18',
	];
	const regionColors = shuffleColors ? shuffle(colors) : colors;

	const thickBorderClasses = {
		top: 'border-t-4 rounded-t-none',
		bottom: 'border-b-4 rounded-b-none',
		left: 'border-l-4 rounded-l-none',
		right: 'border-r-4 rounded-r-none',
	};
	const thinBorderClasses = {
		top: 'border-t-3 rounded-t-none',
		bottom: 'border-b-3 rounded-b-none',
		left: 'border-l-3 rounded-l-none',
		right: 'border-r-3 rounded-r-none',
	};
	let borderClasses = $derived(
		borderVariant === 'thick' ? thickBorderClasses : thinBorderClasses,
	);
</script>

<div
	class={cn(
		'grid w-full aspect-square bg-base-content dark:bg-base-300 p-0.5 rounded-md overflow-clip',
		classFromProps,
	)}
	style="grid-template-columns: repeat({game.board.columns}, 1fr);"
	{onpointermove}
>
	{#each game.board.grid as row, rowIndex}
		{#each row as cell, colIndex (`${game.board.size.columns}, ${rowIndex}, ${colIndex}`)}
			{@const position = { row: rowIndex, column: colIndex }}
			{@const isInvalid = cell === StarBattleCellState.Star && game.invalidBoard.at(position)}
			{@const regionId = game.regions.at({ row: rowIndex, column: colIndex })!}
			{@const regionOnTop = game.regions.at({ row: rowIndex - 1, column: colIndex })}
			{@const regionOnBottom = game.regions.at({ row: rowIndex + 1, column: colIndex })}
			{@const regionOnLeft = game.regions.at({ row: rowIndex, column: colIndex - 1 })}
			{@const regionOnRight = game.regions.at({ row: rowIndex, column: colIndex + 1 })}
			{@const hasHint = game.hint && isSameGridItem(game.hint, position)}
			<div
				role="button"
				aria-disabled={disabled}
				aria-label={`Cell (${rowIndex + 1}, ${colIndex + 1}) in region ${regionId + 1} ${cell === StarBattleCellState.Star ? 'with a star' : cell === StarBattleCellState.Dot ? 'with a dot' : 'is empty'}`}
				class={cn(
					'flex items-center justify-center p-0 rounded-md cursor-pointer size-full transition-colors duration-150 relative border border-base-content dark:border-base-300 aspect-square',
					regionColors[regionId % regionColors.length],
					{
						[borderClasses.top]: regionOnTop === null || regionId !== regionOnTop,
						[borderClasses.bottom]:
							regionOnBottom === null || regionId !== regionOnBottom,
						[borderClasses.left]: regionOnLeft === null || regionId !== regionOnLeft,
						[borderClasses.right]: regionOnRight === null || regionId !== regionOnRight,
						'bg-warning dark:bg-warning text-warning-content dark:text-warning-content':
							hasHint,
						'rounded-tl-md': rowIndex === 0 && colIndex === 0,
						'rounded-tr-md': rowIndex === 0 && colIndex === game.board.columns - 1,
						'rounded-bl-md': rowIndex === game.board.rows - 1 && colIndex === 0,
						'rounded-br-md':
							rowIndex === game.board.rows - 1 && colIndex === game.board.columns - 1,
					},
				)}
				tabindex="0"
				data-row={rowIndex}
				data-column={colIndex}
				onkeydown={(e) => {
					if (e.key === 'Enter' || e.key === ' ') {
						e.stopPropagation();
						onCellClick?.({ item: { row: rowIndex, column: colIndex }, value: cell! });
					}
				}}
			>
				{#if cell === StarBattleCellState.Star}
					<StarSolidIcon
						class={cn(
							'w-4/5 aspect-square text-game-star-battle-star pointer-events-none stroke-game-star-battle-star-stroke',
							{
								'text-game-star-battle-error': isInvalid,
							},
						)}
						pathProps={{
							stroke: '2',
						}}
					/>
				{:else if cell === StarBattleCellState.Dot}
					<div
						class="w-1/4 aspect-square bg-base-content/50 dark:bg-base-300/50 rounded-full pointer-events-none"
					></div>
				{:else if game.hasAutoDot(position)}
					<div
						class="w-1/5 aspect-square bg-base-content/20 dark:bg-base-300/20 rounded-full pointer-events-none"
					></div>
				{/if}

				{#if hasHint}
					<LightBulbIcon
						class="hidden colorblind:block size-1/5 min-w-2 min-h-2 aspect-square absolute top-0.5 right-0.5 xs:top-1 xs:right-1 text-warning p-0.5 rounded-full bg-base-content dark:bg-base-300"
					/>
				{:else if isInvalid}
					<WarningSolidIcon
						class="hidden colorblind:block size-1/5 min-w-2 min-h-2 aspect-square absolute top-0.5 right-0.5 xs:top-1 xs:right-1 text-game-star-battle-error rounded-full bg-base-content dark:bg-base-300"
					/>
				{/if}
			</div>
		{/each}
	{/each}
</div>
