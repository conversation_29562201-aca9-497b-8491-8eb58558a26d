<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';
	import { untrack } from 'svelte';
	import { StarBattleCellState, StarBattleGame } from './StarBattleGame.svelte';
	import StarBattleRenderer from './StarBattleRenderer.svelte';
	import TouchTutorialFigure from '$lib/components/TouchTutorialFigure.svelte';

	let isOpen = $state(false);

	function getGame() {
		const game = new StarBattleGame({
			game: '2_8_1_4-a3ba2bdac2d2c2d',
			difficulty: {
				size: 4 as any,
				stars: 1,
			},
		});
		return game;
	}

	function getSmallInvalidGame() {
		const game = new StarBattleGame({
			game: '0_0-2a2a',
			difficulty: {
				size: 2 as any,
				stars: 1,
			},
		});
		game.regions.grid.forEach((row, rowIndex) =>
			row.forEach((_cell, colIndex) => {
				game.regions.grid[rowIndex][colIndex] = 3;
				game.invalidBoard.grid[rowIndex][colIndex] = true;
			}),
		);
		return game;
	}
</script>

<button onclick={() => (isOpen = true)} class="btn btn-sm">
	How <span class="hidden md:inline">to play</span>
</button>

<Dialog bind:isOpen>
	<article>
		<h2>How to Play Star Battle</h2>

		<p>
			Place the correct number of stars in every row, column, and region. The rules are
			simple:
		</p>

		<ul>
			<li>
				Each row, column, and colored region must contain the same number of stars. This
				number depends on the game settings (e.g. 8x8 - 1★ means a 8x8 grid with one star on
				each row, column, and region).
			</li>
			<li>Stars cannot be in adjacent to each other, not even diagonally.</li>
		</ul>

		<p>Click on a cell to place a star.</p>

		<TouchTutorialFigure
			touchContainerClass="p-1.5 gap-1"
			touchIconPosition={{
				row: 0,
				column: 2,
			}}
			rows={4}
			columns={4}
			fadeTouch
			figcaption="Placing a star"
		>
			{#snippet gameBeforeTouch()}
				<StarBattleRenderer
					borderVariant="thin"
					shuffleColors={false}
					game={untrack(() => {
						return getGame();
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<StarBattleRenderer
					borderVariant="thin"
					shuffleColors={false}
					game={untrack(() => {
						const game = getGame();
						game.board.grid[0][2] = StarBattleCellState.Star;
						return game;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>
			Clicking again will place a dot. Use dots to mark regions where stars cannot be placed.
		</p>

		<TouchTutorialFigure
			touchContainerClass="p-1.5 gap-1"
			touchIconPosition={{
				row: 0,
				column: 2,
			}}
			rows={4}
			columns={4}
			fadeTouch
			figcaption="Placing a dot"
		>
			{#snippet gameBeforeTouch()}
				<StarBattleRenderer
					borderVariant="thin"
					shuffleColors={false}
					game={untrack(() => {
						const game = getGame();
						game.board.grid[0][2] = StarBattleCellState.Star;
						return game;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<StarBattleRenderer
					borderVariant="thin"
					shuffleColors={false}
					game={untrack(() => {
						const game = getGame();
						game.board.grid[0][2] = StarBattleCellState.Dot;
						return game;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>You can't place stars next to each other, even diagonally.</p>

		<div class="grid grid-cols-3 gap-2 max-w-sm mx-auto">
			<StarBattleRenderer
				borderVariant="thin"
				shuffleColors={false}
				game={untrack(() => {
					const game = getSmallInvalidGame();
					game.board.grid[0][0] = StarBattleCellState.Star;
					game.board.grid[1][0] = StarBattleCellState.Star;
					return game;
				})}
			/>
			<StarBattleRenderer
				borderVariant="thin"
				shuffleColors={false}
				game={untrack(() => {
					const game = getSmallInvalidGame();
					game.board.grid[0][0] = StarBattleCellState.Star;
					game.board.grid[1][1] = StarBattleCellState.Star;
					return game;
				})}
			/>
			<StarBattleRenderer
				borderVariant="thin"
				shuffleColors={false}
				game={untrack(() => {
					const game = getSmallInvalidGame();
					game.board.grid[1][0] = StarBattleCellState.Star;
					game.board.grid[1][1] = StarBattleCellState.Star;
					return game;
				})}
			/>
		</div>

		<p>The goal is to place the correct number of stars in every row, column, and region.</p>

		<TouchTutorialFigure
			touchContainerClass="px-0 py-1.5 gap-1"
			touchIconPosition={{
				row: 3,
				column: 1,
			}}
			rows={4}
			columns={4}
			fadeTouch
			figcaption="Winning a star battle game"
		>
			{#snippet gameBeforeTouch()}
				<StarBattleRenderer
					borderVariant="thin"
					shuffleColors={false}
					game={untrack(() => {
						const game = getGame();
						game.board.grid[0][2] = StarBattleCellState.Star;
						game.board.grid[1][0] = StarBattleCellState.Star;
						game.board.grid[2][3] = StarBattleCellState.Star;
						return game;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<StarBattleRenderer
					borderVariant="thin"
					shuffleColors={false}
					game={untrack(() => {
						const game = getGame();
						game.board.grid[0][2] = StarBattleCellState.Star;
						game.board.grid[1][0] = StarBattleCellState.Star;
						game.board.grid[2][3] = StarBattleCellState.Star;
						game.board.grid[3][1] = StarBattleCellState.Star;
						return game;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>Have fun!</p>
	</article>
</Dialog>
