import { getGridItemsAround } from '$lib/functions/getGridItemsAround';
import { isSameGridItem } from '$lib/functions/isSameGridItem';
import { shuffleMutate } from '$lib/functions/shuffle';
import type { GridItem } from '$lib/models/GridItem';
import { AStarFinder } from 'astar-typescript';
import { minifyStarBattleGame } from './minifyStarBattleGame';
import { unminifyStarBattleGame } from './unminifyStarBattleGame';
import { appendFile } from 'node:fs/promises';

type StarsBoard = Array<Array<boolean | null>>;
type RegionsBoard = Array<Array<number | null>>;

function createBoard({ size, fill = null }: { size: number; fill?: any }) {
	return Array.from({ length: size }).map(() => Array.from({ length: size }).fill(fill));
}

function generateStarBattleGame({
	size,
	starsPerConstraint,
}: {
	size: number;
	starsPerConstraint: number;
}) {
	let starsBoard = createBoard({ size }) as StarsBoard;
	let regionsBoard = createBoard({ size }) as RegionsBoard;

	fillInWithStars({ starsBoard, starsPerConstraint });

	let foundUniqueSolution = false;
	let iterations = 0;

	while (!foundUniqueSolution) {
		try {
			regionsBoard = createBoard({ size }) as RegionsBoard;

			fillInWithRegions({ starsBoard, regionsBoard, starsPerConstraint });

			foundUniqueSolution = hasUniqueSolution({
				regionsBoard,
				fullBoard: true,
				starsPerConstraint,
			});
		} catch (e) {
			console.log('Failed to fill in with regions');
			iterations = Infinity;
		}

		iterations += 1;

		if (iterations > 5000) {
			console.log('Could not find a solution for given stars board, generating a new one');
			starsBoard = createBoard({ size }) as StarsBoard;
			fillInWithStars({ starsBoard, starsPerConstraint });
			iterations = 0;
		}
	}

	return { regionsBoard, starsBoard };
}

function fillInWithStars({
	starsBoard,
	starsPerConstraint,
	row = 0,
}: {
	starsBoard: StarsBoard;
	starsPerConstraint: number;
	row?: number;
}) {
	if (
		starsBoard.flatMap((_) => _).filter(Boolean).length ===
		starsBoard.length * starsPerConstraint
	) {
		return true;
	}

	if (row >= starsBoard.length) {
		return false;
	}

	const possibleIndexes = starsBoard[row]
		.map((_value, column) =>
			canPlaceStarAt({
				starsBoard,
				starsPerConstraint,
				position: {
					row,
					column,
				},
			}),
		)
		.map((canPlace, index) => {
			if (canPlace) {
				return index;
			}

			return null;
		})
		.filter((value) => value !== null);

	shuffleMutate(possibleIndexes);

	for (let index of possibleIndexes) {
		starsBoard[row][index] = true;

		const starsOnRow = starsBoard[row].filter((value) => value === true).length;

		if (
			fillInWithStars({
				starsBoard,
				row: starsOnRow === starsPerConstraint ? row + 1 : row,
				starsPerConstraint,
			})
		) {
			return true;
		}

		starsBoard[row][index] = null;
	}

	return false;
}

function hasStarAround({ position, starsBoard }: { position: GridItem; starsBoard: StarsBoard }) {
	const itemsAround = getGridItemsAround(
		position,
		{
			rows: starsBoard.length,
			columns: starsBoard[0].length,
		},
		{
			diagonal: true,
		},
	);

	return itemsAround.some((item) => starsBoard[item.row][item.column] === true);
}

function isRowAtMaxStarCapacity({
	position,
	starsBoard,
	starsPerConstraint,
}: {
	position: GridItem;
	starsBoard: StarsBoard;
	starsPerConstraint: number;
}) {
	return (
		starsBoard[position.row].reduce((count, value) => {
			if (value) {
				return count + 1;
			}
			return count;
		}, 0) === starsPerConstraint
	);
}

function isColumnAtMaxStarCapacity({
	position,
	starsBoard,
	starsPerConstraint,
}: {
	position: GridItem;
	starsBoard: StarsBoard;
	starsPerConstraint: number;
}) {
	return (
		starsBoard
			.map((row) => row[position.column])
			.reduce((count, value) => {
				if (value) {
					return count + 1;
				}
				return count;
			}, 0) === starsPerConstraint
	);
}

const isRegionAtMaxStarCapacity = ({
	starsBoard,
	regionsBoard,
	position,
	starsPerConstraint,
}: {
	starsBoard: StarsBoard;
	regionsBoard: RegionsBoard;
	position: GridItem;
	starsPerConstraint: number;
}) => {
	const regionAtPosition = regionsBoard[position.row][position.column];

	if (regionAtPosition === null) {
		return false;
	}

	const regionsMap = getRegionsMap({ regionsBoard });

	return (
		regionsMap.get(regionAtPosition)?.reduce((counter, position) => {
			if (starsBoard[position.row][position.column]) {
				return counter + 1;
			}
			return counter;
		}, 0) === starsPerConstraint
	);
};

function canPlaceStarAt({
	starsBoard,
	regionsBoard,
	position,
	starsPerConstraint,
}: {
	starsBoard: StarsBoard;
	regionsBoard?: RegionsBoard;
	position: GridItem;
	starsPerConstraint: number;
}) {
	return (
		starsBoard[position.row][position.column] === null &&
		!hasStarAround({ position, starsBoard }) &&
		!isRowAtMaxStarCapacity({ position, starsBoard, starsPerConstraint }) &&
		!isColumnAtMaxStarCapacity({ position, starsBoard, starsPerConstraint }) &&
		(regionsBoard
			? !isRegionAtMaxStarCapacity({ position, starsBoard, regionsBoard, starsPerConstraint })
			: true)
	);
}

function fillInWithRegions({
	starsBoard,
	regionsBoard,
	starsPerConstraint,
}: {
	starsBoard: StarsBoard;
	regionsBoard: RegionsBoard;
	starsPerConstraint: number;
}) {
	const starPositions = starsBoard.flatMap((row, rowIndex) => {
		return row
			.map((value, columnIndex) => {
				if (value) {
					return {
						row: rowIndex,
						column: columnIndex,
					} as GridItem;
				}
			})
			.filter(Boolean) as GridItem[];
	});

	// Swap a few positions for randomness
	for (let i = 0; i < starsBoard.length / 4; i++) {
		const randomIndex1 = Math.floor(Math.random() * starPositions.length);
		const randomIndex2 = Math.floor(Math.random() * starPositions.length);

		const temp = starPositions[randomIndex1];
		starPositions[randomIndex1] = starPositions[randomIndex2];
		starPositions[randomIndex2] = temp;
	}

	let generators: Generator<void>[] = [];

	// Create regions with the correct amount of stars
	if (starsPerConstraint > 1) {
		let foundSolution = false;
		let iterations = 0;

		while (!foundSolution) {
			const finderRegionsBoard = createBoard({
				size: regionsBoard.length,
				// fill: 0,
			}) as RegionsBoard;

			// Mark stars region so they're not a valid path
			starsBoard.forEach((row, rowIndex) => {
				row.forEach((value, columnIndex) => {
					if (value) {
						finderRegionsBoard[rowIndex][columnIndex] = -1;
					}
				});
			});
			let region = 1;
			let pass = 0;

			while (pass < 1) {
				for (let starPosition of starPositions) {
					for (let targetPosition of starPositions) {
						if (!isSameGridItem(starPosition, targetPosition)) {
							const starRegion =
								finderRegionsBoard[starPosition.row][starPosition.column];
							const targetRegion =
								finderRegionsBoard[targetPosition.row][targetPosition.column];

							if (starRegion !== -1 || targetRegion !== -1) {
								continue;
							}

							const starsOnRegion = getStarsOnRegion({
								region,
								regionsBoard: finderRegionsBoard,
								starsBoard,
							});

							if (starsOnRegion < starsPerConstraint) {
								finderRegionsBoard[starPosition.row][starPosition.column] = null;
								finderRegionsBoard[targetPosition.row][targetPosition.column] =
									null;

								const finder = new AStarFinder({
									grid: {
										matrix: finderRegionsBoard as any,
									},
									diagonalAllowed: false,
									includeStartNode: true,
									includeEndNode: true,
								});

								const path = finder
									.findPath(
										{
											x: starPosition.column,
											y: starPosition.row,
										},
										{
											x: targetPosition.column,
											y: targetPosition.row,
										},
									)
									.map(([column, row]) => ({ row, column }));

								if (path.length > 0) {
									path.forEach((item) => {
										finderRegionsBoard[item.row][item.column] = region;
									});

									region += 1;
								} else {
									finderRegionsBoard[starPosition.row][starPosition.column] = -1;
									finderRegionsBoard[targetPosition.row][targetPosition.column] =
										-1;
								}
							}
						}
					}
				}

				pass += 1;
			}

			const starsWithoutRegion =
				getStarsOnRegion({
					regionsBoard: finderRegionsBoard,
					region: null,
					starsBoard,
				}) +
				getStarsOnRegion({
					regionsBoard: finderRegionsBoard,
					region: -1,
					starsBoard,
				});

			if (starsWithoutRegion === 0) {
				const isUniqueSolution = hasUniqueSolution({
					regionsBoard: finderRegionsBoard,
					fullBoard: false,
					starsPerConstraint,
				});

				if (isUniqueSolution) {
					foundSolution = true;
					finderRegionsBoard.forEach((row, rowIndex) => {
						row.forEach((value, columnIndex) => {
							regionsBoard[rowIndex][columnIndex] = value;
						});
					});

					console.log('Successfully connected regions');
					printUnifiedBoard({
						starsBoard,
						regionsBoard,
					});
				} else {
					console.log('Failed, will retry from start');
					throw new Error('Failed to connect stars');
				}
			}

			iterations += 1;

			if (iterations > 3000) {
				throw new Error('Failed to connect stars');
			}
		}

		const regionsMap = getRegionsMap({ regionsBoard });

		generators = regionsMap
			.entries()
			.flatMap(([region, positions]) => {
				return positions.map((position) => {
					return floodFill({
						region,
						regionsBoard,
						starsPerConstraint,
						startPoint: position,
					});
				});
			})
			.toArray();
	} else {
		generators = starPositions.map((startPoint, index) => {
			return floodFill({
				region: index,
				regionsBoard,
				startPoint,
				starsPerConstraint,
			});
		});
	}

	// console.log('Flood filling...');

	let floodFillingIterations = 0;

	while (generators.length > 0) {
		for (let generator of generators) {
			if (generator.next().done) {
				generators.splice(generators.indexOf(generator), 1);
			}
		}

		floodFillingIterations += 1;

		if (floodFillingIterations > 2000) {
			throw new Error('Failed to flood fill');
		}
	}
}

function* floodFill({
	regionsBoard,
	startPoint,
	region,
	starsPerConstraint,
}: {
	regionsBoard: RegionsBoard;
	startPoint: GridItem;
	region: number;
	starsPerConstraint: number;
}): Generator<void> {
	let queue: GridItem[] = [startPoint];

	while (queue.length > 0) {
		const position = queue.pop()!;

		if (regionsBoard[position.row][position.column] === null || position === startPoint) {
			regionsBoard[position.row][position.column] = region;

			const isUnique = hasUniqueSolution({
				regionsBoard,
				fullBoard: false,
				starsPerConstraint,
			});

			if (!isUnique) {
				if (position !== startPoint) {
					regionsBoard[position.row][position.column] = null;
				}
				continue;
			}

			const itemsAround = getGridItemsAround(
				position,
				{
					rows: regionsBoard.length,
					columns: regionsBoard[0].length,
				},
				{
					diagonal: false,
				},
			);

			shuffleMutate(itemsAround);
			queue.push(...itemsAround);
		}

		yield;
	}
}

function getStarsOnRegion({
	regionsBoard,
	starsBoard,
	region,
}: {
	regionsBoard: RegionsBoard;
	starsBoard: StarsBoard;
	region: number | null;
}) {
	let starsCount = 0;

	regionsBoard.forEach((row, rowIndex) => {
		row.forEach((boardRegion, columnIndex) => {
			if (boardRegion === region && starsBoard[rowIndex][columnIndex]) {
				starsCount += 1;
			}
		});
	});

	return starsCount;
}

function getRegionsMap({ regionsBoard }: { regionsBoard: RegionsBoard }) {
	const regionsMap = new Map<number, GridItem[]>();

	regionsBoard.forEach((row, rowIndex) => {
		row.forEach((region, colIndex) => {
			if (region === null || region < 0) {
				return;
			}

			if (!regionsMap.has(region!)) {
				regionsMap.set(region!, []);
			}

			const regions = regionsMap.get(region!);

			regions?.push({ row: rowIndex, column: colIndex });
		});
	});

	return regionsMap;
}

function hasUniqueSolution({
	regionsBoard,
	fullBoard,
	starsPerConstraint,
}: {
	regionsBoard: RegionsBoard;
	fullBoard: boolean;
	starsPerConstraint: number;
}) {
	if (fullBoard) {
		const hasEmptyRegion = regionsBoard.some((row) =>
			row.some((value) => value === null || value < 0),
		);

		if (hasEmptyRegion) {
			return false;
		}
	}
	const regionsMap = getRegionsMap({ regionsBoard });
	const regionNumbers = regionsMap
		.keys()
		.toArray()
		.sort((a, b) => {
			return regionsMap.get(a)!.length - regionsMap.get(b)!.length;
		});

	const solutionBoard = Array.from({ length: regionsBoard.length }).map(() =>
		Array.from({ length: regionsBoard[0].length }).fill(null),
	) as StarsBoard;

	function solve({
		solutionBoard,
		regionIndex = 0,
		solutions = new Set(),
	}: {
		solutionBoard: StarsBoard;
		regionIndex?: number;
		solutions?: Set<string>;
	}) {
		if (regionNumbers[regionIndex] === undefined) {
			return solutions;
		}
		if (solutions.size > 1) {
			return solutions;
		}

		const region = regionNumbers[regionIndex];
		const availablePositions = regionsMap.get(region)!.filter((position) => {
			return canPlaceStarAt({
				starsBoard: solutionBoard,
				position,
				regionsBoard,
				starsPerConstraint,
			});
		});

		for (let availablePosition of availablePositions) {
			solutionBoard[availablePosition.row][availablePosition.column] = true;
			const starsOnRegion = getStarsOnRegion({
				regionsBoard,
				starsBoard: solutionBoard,
				region,
			});
			const hasRequiredStars = starsOnRegion === starsPerConstraint;

			if (
				regionIndex === regionNumbers.length - 1 &&
				availablePositions.length === 1 &&
				hasRequiredStars
			) {
				solutions.add(solutionBoard.map((row) => row.map((value) => `${value}`)).join(','));
			}

			solve({
				solutionBoard,
				regionIndex: hasRequiredStars ? regionIndex + 1 : regionIndex,
				solutions,
			});
			solutionBoard[availablePosition.row][availablePosition.column] = null;
		}

		return solutions;
	}

	const solutions = solve({ solutionBoard });

	return solutions.size === 1;
}

function printUnifiedBoard({
	starsBoard,
	regionsBoard,
}: {
	starsBoard: StarsBoard;
	regionsBoard: RegionsBoard;
}) {
	const unifiedBoard = starsBoard.map((row, rowIndex) => {
		return row.map((star, columnIndex) => {
			const region = regionsBoard[rowIndex][columnIndex];
			if (star) {
				return `${region ?? '-'}*`;
			}

			return `${region ?? '-'}`;
		});
	});

	console.table(unifiedBoard);
}

async function generateGameAndStoreToFile({
	size,
	starsPerConstraint,
	amountOfGames,
}: {
	size: number;
	starsPerConstraint: number;
	amountOfGames: number;
}) {
	const pid = Math.floor(Math.random() * 1e12);
	console.log('=== Star Game Generator ===');
	console.log(
		`Generating ${amountOfGames} games with size = ${size} and stars = ${starsPerConstraint}`,
	);

	for (let i = 0; i < amountOfGames; i += 1) {
		console.log('Generating game', i + 1);

		const game = generateStarBattleGame({
			size,
			starsPerConstraint,
		});

		console.log('=== Star Battle Game ===');

		printUnifiedBoard({
			regionsBoard: game.regionsBoard,
			starsBoard: game.starsBoard,
		});

		const minifiedGame = minifyStarBattleGame(game);
		const unminifiedGame = unminifyStarBattleGame(minifiedGame);

		const isStarsBoardTheSame = game.starsBoard.every((row, rowIndex) => {
			return row.every((value, columnIndex) => {
				return value === unminifiedGame.starsBoard[rowIndex][columnIndex];
			});
		});

		const isRegionsBoardTheSame = game.regionsBoard.every((row, rowIndex) => {
			return row.every((value, columnIndex) => {
				return value === unminifiedGame.regionsBoard[rowIndex][columnIndex];
			});
		});

		try {
			if (isStarsBoardTheSame && isRegionsBoardTheSame) {
				console.log('Successfuly minified and unminified game, will store it');

				await appendFile(
					`${size}-${starsPerConstraint}-${pid}.txt`,
					`${minifiedGame},${size},${starsPerConstraint}\n`,
				);
			} else {
				console.log('Oops, something went wrong');
				console.log('minified = ', minifiedGame);
				printUnifiedBoard(unminifiedGame);
			}
		} catch (e) {
			console.error(e);
		}
	}
}

await generateGameAndStoreToFile({
	size: 4,
	starsPerConstraint: 1,
	amountOfGames: 10000,
});
