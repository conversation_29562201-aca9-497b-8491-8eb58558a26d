import { getGridItemsAround } from '$lib/functions/getGridItemsAround';
import { isSameGridItem } from '$lib/functions/isSameGridItem';
import { shuffleMutate } from '$lib/functions/shuffle';
import type { GridItem } from '$lib/models/GridItem';
import { minifyStarBattleGame } from '../minifyStarBattleGame';
import { unminifyStarBattleGame } from '../unminifyStarBattleGame';
import { appendFile } from 'node:fs/promises';

/**
 * 1. Generate all regions
 * 2. Check if solution is unique
 * 3. Repeat
 */

type StarsBoard = Uint8Array[];
type RegionsBoard = Uint8Array[];

const NULL_REGION = 255;

function createBoard({ size, fill = 0 }: { size: number; fill?: number }): Uint8Array[] {
	const board = new Array(size);
	for (let i = 0; i < size; i++) {
		board[i] = new Uint8Array(size).fill(fill);
	}
	return board;
}

function generateStarBattleGame({
	size,
	starsPerConstraint,
}: {
	size: number;
	starsPerConstraint: number;
}) {
	let regionsBoard = createBoard({ size, fill: NULL_REGION }) as RegionsBoard;
	let uniqueSolution: StarsBoard | null = null;

	while (uniqueSolution === null) {
		try {
			regionsBoard = createBoard({ size, fill: NULL_REGION }) as RegionsBoard;

			fillInWithRegions({ regionsBoard });

			uniqueSolution = getSolution({
				regionsBoard,
				fullBoard: true,
				starsPerConstraint,
			});
		} catch (e) {
			console.log('Failed to fill in with regions');
		}
	}

	return { regionsBoard, starsBoard: uniqueSolution };
}

function hasStarAround({ position, starsBoard }: { position: GridItem; starsBoard: StarsBoard }) {
	const itemsAround = getGridItemsAround(
		position,
		{
			rows: starsBoard.length,
			columns: starsBoard[0].length,
		},
		{
			diagonal: true,
		},
	);

	return itemsAround.some((item) => starsBoard[item.row][item.column] === 1);
}

function isRowAtMaxStarCapacity({
	position,
	starsBoard,
	starsPerConstraint,
}: {
	position: GridItem;
	starsBoard: StarsBoard;
	starsPerConstraint: number;
}) {
	return (
		starsBoard[position.row].reduce((count, value) => {
			if (value === 1) {
				return count + 1;
			}
			return count;
		}, 0) === starsPerConstraint
	);
}

function isColumnAtMaxStarCapacity({
	position,
	starsBoard,
	starsPerConstraint,
}: {
	position: GridItem;
	starsBoard: StarsBoard;
	starsPerConstraint: number;
}) {
	return (
		starsBoard
			.map((row) => row[position.column])
			.reduce((count, value) => {
				if (value === 1) {
					return count + 1;
				}
				return count;
			}, 0) === starsPerConstraint
	);
}

const isRegionAtMaxStarCapacity = ({
	starsBoard,
	regionsBoard,
	position,
	starsPerConstraint,
}: {
	starsBoard: StarsBoard;
	regionsBoard: RegionsBoard;
	position: GridItem;
	starsPerConstraint: number;
}) => {
	const regionAtPosition = regionsBoard[position.row][position.column];

	if (regionAtPosition === NULL_REGION) {
		return false;
	}

	const regionsMap = getRegionsMap({ regionsBoard });

	return (
		regionsMap.get(regionAtPosition)?.reduce((counter, position) => {
			if (starsBoard[position.row][position.column] === 1) {
				return counter + 1;
			}
			return counter;
		}, 0) === starsPerConstraint
	);
};

function canPlaceStarAt({
	starsBoard,
	regionsBoard,
	position,
	starsPerConstraint,
}: {
	starsBoard: StarsBoard;
	regionsBoard?: RegionsBoard;
	position: GridItem;
	starsPerConstraint: number;
}) {
	return (
		starsBoard[position.row][position.column] === 0 &&
		!hasStarAround({ position, starsBoard }) &&
		!isRowAtMaxStarCapacity({ position, starsBoard, starsPerConstraint }) &&
		!isColumnAtMaxStarCapacity({ position, starsBoard, starsPerConstraint }) &&
		(regionsBoard
			? !isRegionAtMaxStarCapacity({ position, starsBoard, regionsBoard, starsPerConstraint })
			: true)
	);
}

function fillInWithRegions({ regionsBoard }: { regionsBoard: RegionsBoard }) {
	// Create regions
	const startPoints: GridItem[] = [];

	while (startPoints.length < regionsBoard.length) {
		const randomStartPoint: GridItem = {
			row: startPoints.length,
			column: Math.floor(Math.random() * regionsBoard[0].length),
		};

		if (startPoints.every((startPoint) => !isSameGridItem(startPoint, randomStartPoint))) {
			startPoints.push(randomStartPoint);
		}
	}

	const generators: Generator<void>[] = startPoints.map((startPoint, index) => {
		return floodFill({
			region: index,
			regionsBoard,
			startPoint,
		});
	});

	// console.log('Flood filling...');

	let floodFillingIterations = 0;

	while (generators.length > 0) {
		for (let generator of generators) {
			if (generator.next().done) {
				generators.splice(generators.indexOf(generator), 1);
			}
		}

		floodFillingIterations += 1;

		if (floodFillingIterations > 2000) {
			throw new Error('Failed to flood fill');
		}
	}

	// console.table(regionsBoard);
}

function* floodFill({
	regionsBoard,
	startPoint,
	region,
}: {
	regionsBoard: RegionsBoard;
	startPoint: GridItem;
	region: number;
}): Generator<void> {
	let queue: GridItem[] = [startPoint];

	while (queue.length > 0) {
		const position = queue.pop()!;

		if (
			regionsBoard[position.row][position.column] === NULL_REGION ||
			position === startPoint
		) {
			regionsBoard[position.row][position.column] = region;

			const itemsAround = getGridItemsAround(
				position,
				{
					rows: regionsBoard.length,
					columns: regionsBoard[0].length,
				},
				{
					diagonal: false,
				},
			);

			shuffleMutate(itemsAround);
			queue.push(...itemsAround);
		}

		yield;
	}
}

function getStarsOnRegion({
	regionsBoard,
	starsBoard,
	region,
}: {
	regionsBoard: RegionsBoard;
	starsBoard: StarsBoard;
	region: number | null;
}) {
	let starsCount = 0;

	regionsBoard.forEach((row, rowIndex) => {
		row.forEach((boardRegion, columnIndex) => {
			if (boardRegion === region && starsBoard[rowIndex][columnIndex] === 1) {
				starsCount += 1;
			}
		});
	});

	return starsCount;
}

function getRegionsMap({ regionsBoard }: { regionsBoard: RegionsBoard }) {
	const regionsMap = new Map<number, GridItem[]>();

	regionsBoard.forEach((row, rowIndex) => {
		row.forEach((region, colIndex) => {
			if (region === NULL_REGION || region < 0) {
				return;
			}

			if (!regionsMap.has(region!)) {
				regionsMap.set(region!, []);
			}

			const regions = regionsMap.get(region!);

			regions?.push({ row: rowIndex, column: colIndex });
		});
	});

	return regionsMap;
}

function getSolution({
	regionsBoard,
	fullBoard,
	starsPerConstraint,
}: {
	regionsBoard: RegionsBoard;
	fullBoard: boolean;
	starsPerConstraint: number;
}) {
	if (fullBoard) {
		const hasEmptyRegion = regionsBoard.some((row) =>
			row.some((value) => value === NULL_REGION || value < 0),
		);

		if (hasEmptyRegion) {
			return null;
		}
	}
	const regionsMap = getRegionsMap({ regionsBoard });
	const regionNumbers = regionsMap
		.keys()
		.toArray()
		.sort((a, b) => {
			return regionsMap.get(a)!.length - regionsMap.get(b)!.length;
		});

	const solutionBoard = createBoard({ size: regionsBoard.length }) as StarsBoard;

	const solutionBoards: StarsBoard[] = [];

	function solve({
		solutionBoard,
		regionIndex = 0,
		solutions = new Set(),
	}: {
		solutionBoard: StarsBoard;
		regionIndex?: number;
		solutions?: Set<string>;
	}) {
		if (regionNumbers[regionIndex] === undefined) {
			return solutions;
		}
		if (solutions.size > 1) {
			return solutions;
		}

		const region = regionNumbers[regionIndex];
		const availablePositions = regionsMap.get(region)!.filter((position) => {
			return canPlaceStarAt({
				starsBoard: solutionBoard,
				position,
				regionsBoard,
				starsPerConstraint,
			});
		});

		for (let availablePosition of availablePositions) {
			solutionBoard[availablePosition.row][availablePosition.column] = 1;
			const starsOnRegion = getStarsOnRegion({
				regionsBoard,
				starsBoard: solutionBoard,
				region,
			});
			const hasRequiredStars = starsOnRegion === starsPerConstraint;

			if (
				regionIndex === regionNumbers.length - 1 &&
				availablePositions.length === 1 &&
				hasRequiredStars
			) {
				const key = solutionBoard.map((row) => row.join('')).join(',');

				if (!solutions.has(key)) {
					solutions.add(key);
					solutionBoards.push(solutionBoard.map((row) => Uint8Array.from(row)));
				}
			}

			solve({
				solutionBoard,
				regionIndex: hasRequiredStars ? regionIndex + 1 : regionIndex,
				solutions,
			});
			solutionBoard[availablePosition.row][availablePosition.column] = 0;
		}

		return solutions;
	}

	const solutions = solve({ solutionBoard });

	if (solutions.size === 1) {
		return solutionBoards[0];
	}

	return null;
}

function printUnifiedBoard({
	starsBoard,
	regionsBoard,
}: {
	starsBoard: StarsBoard;
	regionsBoard: RegionsBoard;
}) {
	const unifiedBoard = starsBoard.map((row, rowIndex) => {
		return Array.from(row).map((star, columnIndex) => {
			const region = regionsBoard[rowIndex][columnIndex];
			if (star === 1) {
				return `${region === NULL_REGION ? '-' : region}*`;
			}

			return `${region === NULL_REGION ? '-' : region}`;
		});
	});

	console.table(unifiedBoard);
}

async function generateGameAndStoreToFile({
	size,
	starsPerConstraint,
	amountOfGames,
}: {
	size: number;
	starsPerConstraint: number;
	amountOfGames: number;
}) {
	const pid = Math.floor(Math.random() * 1e12);
	console.log('=== Star Game Generator ===');
	console.log(
		`Generating ${amountOfGames} games with size = ${size} and stars = ${starsPerConstraint}`,
	);

	for (let i = 0; i < amountOfGames; i += 1) {
		console.log('Generating game', i + 1);

		const game = generateStarBattleGame({
			size,
			starsPerConstraint,
		});

		console.log('=== Star Battle Game ===');

		printUnifiedBoard(game);

		const convertedGame = {
			starsBoard: Array.from(game.starsBoard).map((row) => {
				return Array.from(row).map((value) => (value === 1 ? true : null));
			}),
			regionsBoard: Array.from(game.regionsBoard).map((row) => {
				return Array.from(row);
			}),
		};

		const minifiedGame = minifyStarBattleGame(convertedGame);
		const unminifiedGame = unminifyStarBattleGame(minifiedGame);

		const isStarsBoardTheSame = convertedGame.starsBoard.every((row, rowIndex) => {
			return row.every((value, columnIndex) => {
				return value === unminifiedGame.starsBoard[rowIndex][columnIndex];
			});
		});

		const isRegionsBoardTheSame = convertedGame.regionsBoard.every((row, rowIndex) => {
			return row.every((value, columnIndex) => {
				return value === unminifiedGame.regionsBoard[rowIndex][columnIndex];
			});
		});

		try {
			if (isStarsBoardTheSame && isRegionsBoardTheSame) {
				console.log('Successfuly minified and unminified game, will store it');

				await appendFile(
					`${size}-${starsPerConstraint}-${pid}.txt`,
					`${minifiedGame},${size},${starsPerConstraint}\n`,
				);
			} else {
				console.log('Oops, something went wrong');
				console.log('minified = ', minifiedGame);
				console.log(unminifiedGame);
			}
		} catch (e) {
			console.error(e);
		}
	}
}

await generateGameAndStoreToFile({
	size: 14,
	starsPerConstraint: 3,
	amountOfGames: 5000,
});
