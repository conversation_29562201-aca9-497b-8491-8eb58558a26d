export function minifyStarBattleGame({
	starsBoard,
	regionsBoard,
}: {
	starsBoard: Array<Array<boolean | null | number>>;
	regionsBoard: Array<Array<number | null>>;
}): string {
	const num = getStarsAsString({ starsBoard });

	const regionsString = getRegionsAsString({ regionsBoard });

	return `${num}-${regionsString}`;
}

function getStarsAsString({ starsBoard }: { starsBoard: Array<Array<boolean | null | number>> }) {
	return starsBoard
		.map((row) =>
			row.map((value) => {
				return value ? 1 : (0 as number);
			}),
		)
		.map((row) => parseInt(row.join(''), 2))
		.join('_');
}

function labelRegions({ regionsBoard }: { regionsBoard: Array<Array<number | null>> }) {
	return regionsBoard.map((row) => {
		return row.map((region) => {
			if (region === null) {
				return '_';
			}

			return String.fromCharCode(97 + region);
		});
	});
}

function getRegionsAsString({ regionsBoard }: { regionsBoard: Array<Array<number | null>> }) {
	const labeledRegionsBoard = labelRegions({ regionsBoard });

	return labeledRegionsBoard
		.map((row) => {
			let counter = 0;
			let regionBeingCounted = row[0];
			let result = [] as string[];

			for (let region of row) {
				if (region === regionBeingCounted) {
					counter += 1;
				} else {
					result.push(getMinifiedRegionString({ counter, region: regionBeingCounted }));
					counter = 1;
					regionBeingCounted = region;
				}
			}

			result.push(getMinifiedRegionString({ counter, region: regionBeingCounted }));

			return result.join('');
		})
		.join('');
}

function getMinifiedRegionString({ counter, region }: { counter: number; region: string }) {
	if (counter > 1) {
		return `${counter}${region}`;
	}

	return region;
}
