export function unminifyStarBattleGame(text: string): {
	starsBoard: Array<Array<boolean | null>>;
	regionsBoard: Array<Array<number | null>>;
} {
	const [starsMinified, regionsMinified] = text.split('-');

	const { starsBoard, size } = unminifyStars(starsMinified);
	const regionsBoard = unminifyRegions(regionsMinified, size);

	return {
		starsBoard,
		regionsBoard,
	};
}

function unminifyStars(starsMinified: string): {
	size: number;
	starsBoard: Array<Array<boolean | null>>;
} {
	const size = starsMinified.split('_').length;
	const binaryStrings = starsMinified
		.split('_')
		.map((decimal) => parseInt(decimal, 10).toString(2));

	return {
		size,
		starsBoard: binaryStrings.map((binary) => {
			const paddedBinary = binary.padStart(size, '0');
			return paddedBinary.split('').map((char) => (char === '1' ? true : null));
		}),
	};
}

function unminifyRegions(regionsMinified: string, size: number): Array<Array<number | null>> {
	const regionsBoard: Array<Array<number | null>> = [];
	let currentRow: Array<number | null> = [];
	let i = 0;

	while (i < regionsMinified.length) {
		let count = 1;
		let char = '';

		if (/\d/.test(regionsMinified[i])) {
			let numStr = '';
			while (/\d/.test(regionsMinified[i])) {
				numStr += regionsMinified[i];
				i++;
			}
			count = parseInt(numStr, 10);
		}

		char = regionsMinified[i];
		i++;

		const region = char === '_' ? null : char.charCodeAt(0) - 97;

		for (let j = 0; j < count; j++) {
			currentRow.push(region);
			if (currentRow.length === size) {
				regionsBoard.push(currentRow);
				currentRow = [];
			}
		}
	}

	return regionsBoard;
}
