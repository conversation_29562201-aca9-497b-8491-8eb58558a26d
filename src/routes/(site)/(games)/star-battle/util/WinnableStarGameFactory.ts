import { supabase } from '$lib/api/supabase';
import { handleError } from '../../../../../hooks.client';
import type { StarBattleSize, StarBattleStars } from '../constants';

interface Game {
	game: string;
	size: StarBattleSize;
	stars: StarBattleStars;
}

type Key = `${StarBattleSize}-${StarBattleStars}`;

export class WinnableStarGameFactory {
	private _nextGames: Partial<Record<Key, string[]>> = {};
	private _abortController = new AbortController();

	private async fetchGames({ size, stars }: { size: StarBattleSize; stars: StarBattleStars }) {
		this._abortController.abort();
		this._abortController = new AbortController();

		const games = (
			await supabase
				.rpc('random_star_battle', {
					board_size_param: size,
					stars_param: stars,
				})
				.select('game')
				.throwOnError()
				.abortSignal(this._abortController.signal)
		).data as Game[];

		if (!games || games.length === 0) {
			throw new Error('Game not found');
		}

		const key = `${size}-${stars}` as const;

		if (!this._nextGames[key]) {
			this._nextGames[key] = [];
		}
		this._nextGames[key].push(...games.map((g) => g.game));
	}

	async build({
		size,
		stars,
	}: {
		size: StarBattleSize;
		stars: StarBattleStars;
	}): Promise<string> {
		try {
			const key = `${size}-${stars}` as const;

			if (this._nextGames[key]) {
				const game = this._nextGames[key].shift()!;

				if (this._nextGames[key].length === 0) {
					void this.fetchGames({ size, stars });
				}

				return game;
			}

			await this.fetchGames({ size, stars });

			return this.build({ size, stars });
		} catch (error: any) {
			handleError(error);

			throw error;
		}
	}
}
