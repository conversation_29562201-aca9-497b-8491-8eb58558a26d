<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full p-4', className)}
	fill="transparent"
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 498 498"
	{...props}
>
	<g clip-path="url(#star-battle-cover__a)">
		<rect
			x="23"
			y="23"
			width="453"
			height="453"
			rx="6"
			class="fill-game-star-battle-board-bg"
		/>
		<rect
			x="251"
			y="142"
			width="105"
			height="105"
			rx="6"
			class="fill-game-star-battle-region-2"
		/>
		<path d="M33 360h105v105H39a6 6 0 0 1-6-6v-99Z" class="fill-game-star-battle-region-3" />
		<path d="M360 33h99a6 6 0 0 1 6 6v99H360V33Z" class="fill-game-star-battle-region-2" />
		<rect
			x="251"
			y="251"
			width="105"
			height="105"
			rx="6"
			class="fill-game-star-battle-region-4"
		/>
		<path d="M142 33h105v99a6 6 0 0 1-6 6h-99V33Z" class="fill-game-star-battle-region-2" />
		<path d="M33 39a6 6 0 0 1 6-6h99v105H33V39Z" class="fill-game-star-battle-region-1" />
		<path d="M360 142h105v105h-99a6 6 0 0 1-6-6v-99Z" class="fill-game-star-battle-region-4" />
		<path
			d="M251 33h105v99a6 6 0 0 1-6 6h-93a6 6 0 0 1-6-6V33Z"
			class="fill-game-star-battle-region-2"
		/>
		<path
			d="M300.778 62.746c1.208-2.842 5.236-2.842 6.444 0l5.75 13.533a.5.5 0 0 0 .421.304l14.847 1.165c3.124.245 4.376 4.157 1.975 6.17l-11.249 9.434a.499.499 0 0 0-.164.501l3.447 14.15c.735 3.017-2.531 5.426-5.196 3.833l-12.796-7.651a.5.5 0 0 0-.514 0l-12.796 7.651c-2.665 1.593-5.931-.816-5.196-3.833l3.447-14.15a.499.499 0 0 0-.164-.5l-11.249-9.434c-2.401-2.014-1.149-5.926 1.975-6.171l14.847-1.165a.5.5 0 0 0 .421-.304l5.75-13.533Z"
			class="fill-game-star-battle-star stroke-game-star-battle-star-stroke"
			stroke-width="3"
		/>
		<path
			d="M33 142h99a6 6 0 0 1 6 6v93a6 6 0 0 1-6 6H33V142Z"
			class="fill-game-star-battle-region-1"
		/>
		<path
			d="M82.778 171.746c1.208-2.842 5.236-2.842 6.444 0l5.75 13.533a.5.5 0 0 0 .42.304l14.848 1.165c3.124.245 4.376 4.157 1.975 6.171l-11.249 9.434a.498.498 0 0 0-.164.501l3.447 14.149c.735 3.017-2.531 5.426-5.196 3.833l-12.796-7.651a.498.498 0 0 0-.514 0l-12.796 7.651c-2.665 1.593-5.931-.816-5.196-3.833l3.447-14.149a.5.5 0 0 0-.164-.501l-11.249-9.434c-2.401-2.014-1.15-5.926 1.975-6.171l14.847-1.165a.5.5 0 0 0 .421-.304l5.75-13.533Z"
			class="fill-game-star-battle-star stroke-game-star-battle-star-stroke"
			stroke-width="3"
		/>
		<rect
			x="142"
			y="251"
			width="105"
			height="105"
			rx="6"
			class="fill-game-star-battle-region-3"
		/>
		<rect
			x="142"
			y="142"
			width="105"
			height="105"
			rx="6"
			class="fill-game-star-battle-region-2"
		/>
		<path
			d="M33 251h99a6 6 0 0 1 6 6v93a6 6 0 0 1-6 6H33V251Z"
			class="fill-game-star-battle-region-1"
		/>
		<path
			d="M251 366a6 6 0 0 1 6-6h93a6 6 0 0 1 6 6v99H251v-99Zm109-109a6 6 0 0 1 6-6h99v105h-99a6 6 0 0 1-6-6v-93Z"
			class="fill-game-star-battle-region-4"
		/>
		<path
			d="M409.778 280.746c1.208-2.842 5.236-2.842 6.444 0l5.75 13.533a.5.5 0 0 0 .421.304l14.847 1.165c3.124.245 4.376 4.157 1.975 6.171l-11.249 9.434a.498.498 0 0 0-.164.501l3.447 14.149c.735 3.017-2.531 5.426-5.196 3.833l-12.796-7.651a.5.5 0 0 0-.514 0l-12.796 7.651c-2.665 1.593-5.931-.816-5.196-3.833l3.447-14.149a.498.498 0 0 0-.164-.501l-11.249-9.434c-2.401-2.014-1.149-5.926 1.975-6.171l14.847-1.165a.5.5 0 0 0 .421-.304l5.75-13.533Z"
			class="fill-game-star-battle-star stroke-game-star-battle-star-stroke"
			stroke-width="3"
		/>
		<path
			d="M142 366a6 6 0 0 1 6-6h93a6 6 0 0 1 6 6v99H142v-99Z"
			class="fill-game-star-battle-region-3"
		/>
		<path
			d="M191.778 389.746c1.208-2.842 5.236-2.842 6.444 0l5.75 13.533a.5.5 0 0 0 .421.304l14.847 1.165c3.124.245 4.376 4.157 1.975 6.171l-11.249 9.434a.498.498 0 0 0-.164.501l3.447 14.149c.735 3.017-2.531 5.426-5.196 3.833l-12.796-7.651a.5.5 0 0 0-.514 0l-12.796 7.651c-2.665 1.593-5.931-.816-5.196-3.833l3.447-14.149a.498.498 0 0 0-.164-.501l-11.249-9.434c-2.401-2.014-1.149-5.926 1.975-6.171l14.847-1.165a.5.5 0 0 0 .421-.304l5.75-13.533Z"
			class="fill-game-star-battle-star stroke-game-star-battle-star-stroke"
			stroke-width="3"
		/>
		<path
			d="M360 366a6 6 0 0 1 6-6h99v99a6 6 0 0 1-6 6h-99v-99Z"
			class="fill-game-star-battle-region-4"
		/>
		<path
			class="fill-game-star-battle-board-bg"
			d="M360 135h105v10H360zM33 353h105v10H33zm109-109h214v10H142zm112 10v214h-10V254z"
		/>
		<path class="fill-game-star-battle-board-bg" d="M145 33v323h-10V33zm218 109v105h-10V142z" />
	</g>
	<defs>
		<clipPath id="star-battle-cover__a">
			<path fill="#fff" d="M0 0h498v498H0z" />
		</clipPath>
	</defs>
</svg>
