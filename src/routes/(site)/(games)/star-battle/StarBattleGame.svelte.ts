import { Grid } from '$lib/util/Grid.svelte';
import { Undoable } from '$lib/util/Undoable/Undoable.svelte';
import type { Timer } from '$lib/util/Timer.svelte';
import type { GridItem } from '$lib/models/GridItem';
import type { StarBattleDifficulty } from './constants';
import { unminifyStarBattleGame } from './util/unminifyStarBattleGame';
import { WinnableStarGameFactory } from './util/WinnableStarGameFactory';
import throttle from 'lodash/throttle';

export enum StarBattleCellState {
	Empty,
	Star,
	Dot,
}

export type StarBattlePlacedItem = {
	previousItem: StarBattleCellState;
	item: StarBattleCellState;
};

enum StarGameErrorReason {
	TooManyStarsInRow,
	TooManyStarsInColumn,
	TooManyStarsInRegion,
	AdjacentStars,
}

interface StarGameError {
	reason: StarGameErrorReason;
	index?: number;
	cells?: GridItem[];
}

interface Hint {
	row: number;
	column: number;
}

interface StarBattleRuntimeSettings {
	autoFill: boolean;
	autoFillCellsAroundStars: boolean;
	autoFillRowsWithReachedStars: boolean;
	autoFillRegionsWithReachedStars: boolean;
}

interface StarBattleGameSettings {
	difficulty: StarBattleDifficulty;
	empty?: boolean;
	game?: string;
	timer?: Timer;
	onGameWin?: () => void;
	runtimeSettings?: StarBattleRuntimeSettings;
}

export class StarBattleGame {
	id = Symbol();
	difficulty: StarBattleDifficulty;
	invalidBoard = $state() as Grid<boolean>;
	board = $state() as Grid<StarBattleCellState>;
	regions = $state() as Grid<number | null>;
	solution: Grid<boolean | null>;
	winState = $state<{ won: boolean; errors: StarGameError[] }>({ won: false, errors: [] });
	history: Undoable<Grid<StarBattleCellState>>;
	empty: boolean;
	totalClicks = $state(0);
	hintsUsed = $state(0);
	hint = $state<Hint | undefined>();
	loadState: 'idle' | 'loading' | 'success' | 'error' = $state('idle');
	private _startOverPenalty = 0;
	private _runtimeSettings: StarBattleRuntimeSettings = $state({
		autoFill: false,
		autoFillCellsAroundStars: false,
		autoFillRowsWithReachedStars: false,
		autoFillRegionsWithReachedStars: false,
	});
	private winnableStarGameFactory = new WinnableStarGameFactory();
	private timer?: Timer;
	private onGameWin?: () => void;
	private initialClickState: StarBattleCellState | null = null;
	private hintTimeout: number | undefined;

	constructor({
		difficulty,
		empty = false,
		game,
		timer,
		onGameWin,
		runtimeSettings,
	}: StarBattleGameSettings) {
		this.difficulty = difficulty;
		this.empty = empty;
		this.board = new Grid(
			{ rows: difficulty.size, columns: difficulty.size },
			() => StarBattleCellState.Empty,
		);
		this.invalidBoard = new Grid(
			{ rows: difficulty.size, columns: difficulty.size },
			() => false,
		);
		this.regions = new Grid({ rows: difficulty.size, columns: difficulty.size }, () => -1);
		this.solution = new Grid({ rows: difficulty.size, columns: difficulty.size }, () => false);
		this.history = new Undoable(this.board.clone());
		this.timer = timer;
		this.onGameWin = onGameWin;
		if (runtimeSettings) {
			this.runtimeSettings = runtimeSettings;
		}

		if (!empty) {
			this.generateGame(game);
		}
	}

	async generateGame(game?: string) {
		if (!game) {
			try {
				this.loadState = 'loading';
				game = await this.winnableStarGameFactory.build({
					size: this.difficulty.size,
					stars: this.difficulty.stars,
				});
				this.loadState = 'success';
			} catch {
				this.loadState = 'error';
				return;
			}
		}

		const { regionsBoard, starsBoard } = unminifyStarBattleGame(game);
		const size = regionsBoard.length;

		this.regions = new Grid(
			{
				rows: size,
				columns: size,
				reactive: true,
			},
			(row, col) => {
				return regionsBoard[row][col];
			},
		);

		this.solution = new Grid(
			{
				rows: size,
				columns: size,
				reactive: true,
			},
			(row, col) => {
				return starsBoard[row][col];
			},
		);

		this.invalidBoard = new Grid({ rows: size, columns: size, reactive: true });

		this.board = new Grid(
			{ rows: size, columns: size, reactive: true },
			() => StarBattleCellState.Empty,
		);

		this.difficulty = {
			size,
			stars: this.solution.grid[0].filter((cell) => cell).length,
		} as StarBattleDifficulty;

		this.history.reset(this.board.clone());

		this.loadState = 'success';
	}

	placeItem({ row, column }: GridItem): StarBattlePlacedItem | null {
		if (this.isWon) {
			return null;
		}

		if (!this.timer?.running && !this.timer?.started) {
			this.timer?.start();
		}

		if (!this.timer?.running) {
			return null;
		}

		const currentState = this.board.at({ row, column })!;
		let nextState: StarBattleCellState;

		switch (currentState) {
			case StarBattleCellState.Empty:
				nextState = StarBattleCellState.Star;
				break;
			case StarBattleCellState.Star:
				nextState = StarBattleCellState.Dot;
				break;
			case StarBattleCellState.Dot:
				nextState = StarBattleCellState.Empty;
				break;
			default:
				nextState = StarBattleCellState.Empty;
		}

		this.board.set({ row, column }, nextState);
		this.totalClicks++;
		this.initialClickState = currentState;
		this.checkGameWin();

		if (this.isWon) {
			this.onGameWin?.();
		}

		return { previousItem: currentState, item: nextState };
	}

	continuePlacing({ row, column }: GridItem): boolean {
		if (this.isWon) {
			return false;
		}

		if (!this.timer?.running && !this.timer?.started) {
			this.timer?.start();
		}

		if (!this.timer?.running) {
			return false;
		}

		const currentState = this.board.at({ row, column });

		if (
			this.initialClickState === StarBattleCellState.Dot &&
			currentState !== StarBattleCellState.Star
		) {
			this.board.set({ row, column }, StarBattleCellState.Empty);
			return true;
		}

		if (currentState === StarBattleCellState.Empty) {
			if (
				this.initialClickState === StarBattleCellState.Empty ||
				this.initialClickState === StarBattleCellState.Star
			) {
				this.board.set({ row, column }, StarBattleCellState.Dot);
				return true;
			}
		}
		return false;
	}

	commitToHistory() {
		this.history.add(this.board.clone());
		this.initialClickState = null;
	}

	checkGameWin() {
		const { stars } = this.difficulty;
		const errors: StarGameError[] = [];
		this.invalidBoard.reset(() => false);

		// Check rows, columns, and regions for too many stars
		for (let i = 0; i < this.difficulty.size; i++) {
			const rowStars = this.board.grid[i].filter((s) => s === StarBattleCellState.Star);
			if (rowStars.length > stars) {
				errors.push({ reason: StarGameErrorReason.TooManyStarsInRow, index: i });
				this.board.getAllItems().forEach((item) => {
					if (item.row === i) {
						this.invalidBoard.set(item, true);
					}
				});
			}
		}
		for (let i = 0; i < this.difficulty.size; i++) {
			const colStars = this.board.grid
				.map((row) => row[i])
				.filter((s) => s === StarBattleCellState.Star);
			if (colStars.length > stars) {
				errors.push({ reason: StarGameErrorReason.TooManyStarsInColumn, index: i });
				this.board.getAllItems().forEach((item) => {
					if (item.column === i) {
						this.invalidBoard.set(item, true);
					}
				});
			}
		}
		for (let i = 0; i < this.difficulty.size; i++) {
			const regionCells = this.board
				.getAllItems()
				.filter((cell) => this.regions.at(cell) === i);
			const regionStars = regionCells.filter(
				(cell) => this.board.at(cell) === StarBattleCellState.Star,
			);
			if (regionStars.length > stars) {
				errors.push({ reason: StarGameErrorReason.TooManyStarsInRegion, index: i });
				regionStars.forEach((cell) => this.invalidBoard.set(cell, true));
			}
		}

		// Check for adjacent stars
		for (const cell of this.board.getAllItems()) {
			if (this.board.at(cell) === StarBattleCellState.Star) {
				const neighbors = this.board.getItemsAround(cell, { diagonal: true });
				for (const neighbor of neighbors) {
					if (this.board.at(neighbor) === StarBattleCellState.Star) {
						errors.push({
							reason: StarGameErrorReason.AdjacentStars,
							cells: [cell, neighbor],
						});
						this.invalidBoard.set(cell, true);
						this.invalidBoard.set(neighbor, true);
					}
				}
			}
		}

		const won =
			errors.length === 0 &&
			this.board.grid.flat().filter((cell) => cell === StarBattleCellState.Star).length ===
				this.difficulty.stars * this.difficulty.size;

		this.winState = { won, errors };
	}

	showHint() {
		if (this.isWon) {
			return;
		}

		if (!this.timer?.running && !this.timer?.started) {
			this.timer?.start();
		}

		if (!this.timer?.running) {
			return;
		}

		clearTimeout(this.hintTimeout);
		const solutionStars = this.solution.getAllItems().filter((item) => this.solution.at(item));
		const boardStars = this.board
			.getAllItems()
			.filter((item) => this.board.at(item) === StarBattleCellState.Star);
		const missingStars = solutionStars.filter(
			(solutionStar) =>
				!boardStars.some(
					(boardStar) =>
						boardStar.row === solutionStar.row &&
						boardStar.column === solutionStar.column,
				),
		);

		if (missingStars.length > 0) {
			const hint = missingStars[Math.floor(Math.random() * missingStars.length)];
			this.board.set(hint, StarBattleCellState.Star);
			this.commitToHistory();
			this.hintsUsed++;
			this.hint = hint;
			clearTimeout(this.hintTimeout);
			this.hintTimeout = setTimeout(() => {
				this.hint = undefined;
			}, 2000) as unknown as number;
			this.checkGameWin();
		}
	}

	throttledShowHint = throttle(this.showHint, 500, { leading: true, trailing: false });

	startOver() {
		this._startOverPenalty += this.getScore();
		this.totalClicks = 0;
		this.hintsUsed = 0;
		this.hint = undefined;
		clearTimeout(this.hintTimeout);
		this.history.reset();
		this.board = this.history.state.clone();
		this.winState = { won: false, errors: [] };
		this.checkGameWin();
		this.timer?.reset();
	}

	canUndo() {
		if (this.isWon || !this.timer?.running) {
			return false;
		}
		return this.history.canUndo();
	}

	canRedo() {
		if (this.isWon || !this.timer?.running) {
			return false;
		}
		return this.history.canRedo();
	}

	undo() {
		if (this.canUndo()) {
			this.history.undo();
			this.board = this.history.state.clone();
			this.hint = undefined;
		}
	}

	redo() {
		if (this.canRedo()) {
			this.history.redo();
			this.board = this.history.state.clone();
			this.hint = undefined;
		}
	}

	get isWon() {
		return this.winState.won;
	}

	get clicks() {
		return this.history.timeline.past.length;
	}

	get stars() {
		return this.board.grid.flat().filter((cell) => cell === StarBattleCellState.Star).length;
	}

	get dots() {
		return this.board.grid.flat().filter((cell) => cell === StarBattleCellState.Dot).length;
	}

	getScore() {
		const autoFillPenalty =
			2000 *
			(this.runtimeSettings.autoFill
				? [
						this.runtimeSettings.autoFillCellsAroundStars ? 1 : 0,
						this.runtimeSettings.autoFillRowsWithReachedStars ? 1 : 0,
						this.runtimeSettings.autoFillRegionsWithReachedStars ? 1 : 0,
					]
				: []
			).filter(Boolean).length;

		return Math.floor(
			Math.max(
				0,
				(this.timer?.elapsedTime ?? 0) / 10 +
					this.totalClicks * 10 -
					this.board.size.rows * 10 +
					this.hintsUsed * 3000 +
					autoFillPenalty +
					this._startOverPenalty,
			),
		);
	}

	get runtimeSettings() {
		return this._runtimeSettings;
	}

	set runtimeSettings(newRuntimeSettings: StarBattleRuntimeSettings) {
		this._runtimeSettings = newRuntimeSettings;
	}

	hasAutoDot(at: GridItem): boolean {
		if (!this._runtimeSettings.autoFill) {
			return false;
		}

		const item = this.board.at(at);

		if (item !== StarBattleCellState.Empty) {
			return false;
		}

		if (this.runtimeSettings.autoFillCellsAroundStars) {
			if (
				this.board
					.getItemsAround(at, { diagonal: true })
					.some((gridItem) => this.board.at(gridItem) === StarBattleCellState.Star)
			) {
				return true;
			}
		}

		if (this.runtimeSettings.autoFillRowsWithReachedStars) {
			if (
				this.starsByRow[at.row] === this.difficulty.stars ||
				this.starsByColumn[at.column] === this.difficulty.stars
			) {
				return true;
			}
		}

		if (this.runtimeSettings.autoFillRegionsWithReachedStars) {
			const region = this.regions.at(at);
			if (region !== null && this.starsByRegion[region] === this.difficulty.stars) {
				return true;
			}
		}

		return false;
	}

	get starsByRow() {
		return this.board.grid.map((row) => {
			return row.filter((cell) => cell === StarBattleCellState.Star).length;
		});
	}

	get starsByColumn() {
		return this.board.grid[0].map((_, colIndex) => {
			return this.board.grid.reduce((acc, row) => {
				if (row[colIndex] === StarBattleCellState.Star) {
					return acc + 1;
				}
				return acc;
			}, 0);
		});
	}

	get regionsPopulated() {
		const regionsPopulated: boolean[] = new Array(this.difficulty.size).fill(false);

		for (let i = 0; i < this.difficulty.size; i++) {
			for (let j = 0; j < this.difficulty.size; j++) {
				const region = this.regions.at({ row: i, column: j });
				if (region !== null) {
					regionsPopulated[region] = true;
				}
			}
		}
		return regionsPopulated;
	}

	get starsByRegion() {
		const starsByRegion: number[] = new Array(this.difficulty.size).fill(0);

		for (let i = 0; i < this.difficulty.size; i++) {
			for (let j = 0; j < this.difficulty.size; j++) {
				const region = this.regions.at({ row: i, column: j });
				if (
					region !== null &&
					this.board.at({ row: i, column: j }) === StarBattleCellState.Star
				) {
					starsByRegion[region]++;
				}
			}
		}
		return starsByRegion;
	}
}
