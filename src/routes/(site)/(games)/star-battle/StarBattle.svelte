<script lang="ts">
	import { createStarBattleContext } from './createStarBattleContext';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { onMount, onDestroy, untrack } from 'svelte';
	import StarBattleRenderer from './StarBattleRenderer.svelte';
	import {
		StarBattleCellState,
		StarBattleGame,
		type StarBattlePlacedItem,
	} from './StarBattleGame.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';
	import UndoIcon from '$lib/components/Icons/UndoIcon.svelte';
	import RedoIcon from '$lib/components/Icons/RedoIcon.svelte';
	import StarBattleHowToPlayButton from './StarBattleHowToPlayButton.svelte';
	import { starBattleDifficulties } from './constants';
	import StarBattleInfoModal from './StarBattleInfoModal.svelte';
	import { UndoableKeyboardListener } from '$lib/util/Undoable/UndoableKeyboardListener';
	import { isMacLike } from '$lib/functions/isMacLike';
	import { cn } from '$lib/util/cn';
	import { fade, fly } from 'svelte/transition';
	import { quadInOut } from 'svelte/easing';
	import LightBulbIcon from '$lib/components/Icons/LightBulbIcon.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import Collapse from '$lib/components/Collapse/Collapse.svelte';
	import CollapseContent from '$lib/components/Collapse/CollapseContent.svelte';
	import type { GridItem } from '$lib/models/GridItem';

	let isSizeDropdownOpen = $state(false);
	let isInfoModalOpen = $state(false);
	let isMac = $state(false);
	let context = createStarBattleContext();

	let game = $derived.by(() => {
		if (context.game) {
			return context.game as StarBattleGame;
		}

		return untrack(
			() =>
				new StarBattleGame({
					empty: true,
					difficulty: starBattleDifficulties[0],
				}),
		);
	});

	let undoableListener: UndoableKeyboardListener;
	let hasPlacedAnyItemSincePointerDown = false;
	let isTouching = false;

	onMount(() => {
		isMac = isMacLike();
		context.load();
		undoableListener = new UndoableKeyboardListener(
			() => game.undo(),
			() => game.redo(),
		);
		undoableListener.listen();
	});

	onDestroy(() => {
		context.dispose();
		if (undoableListener) {
			undoableListener.dispose();
		}
	});

	function playPlacedItemSound(placedItem: StarBattlePlacedItem | null) {
		if (!placedItem) {
			return;
		}

		if (placedItem.item === StarBattleCellState.Star) {
			context.sounds.placeStar.play();
		} else if (placedItem.previousItem === StarBattleCellState.Star) {
			context.sounds.removeStar.play();
		}
	}

	function onCellClick(cell: { item: GridItem }) {
		const placedItem = game.placeItem(cell.item);
		game.commitToHistory();

		playPlacedItemSound(placedItem);
	}

	function handlePointerDown(event: PointerEvent) {
		if (game.isWon) {
			return;
		}

		isTouching = true;

		const element = event.target as HTMLElement;
		const { row, column } = element.dataset;

		if (row !== undefined && column !== undefined) {
			const placedItem = game.placeItem({ row: +row, column: +column });
			hasPlacedAnyItemSincePointerDown = true;

			playPlacedItemSound(placedItem);
		}
	}

	function handlePointerMove(event: PointerEvent) {
		if (!isTouching || game.isWon) {
			return;
		}
		const element = event.target as HTMLElement;
		const { row, column } = element.dataset;

		if (row !== undefined && column !== undefined) {
			if (game.continuePlacing({ row: +row, column: +column })) {
				hasPlacedAnyItemSincePointerDown = true;
			}
		}
		event.preventDefault();
	}

	function handlePointerUp() {
		if (hasPlacedAnyItemSincePointerDown) {
			game.commitToHistory();
		}
		hasPlacedAnyItemSincePointerDown = false;
		isTouching = false;
	}

	function showHint() {
		game.throttledShowHint();
		context.sounds.hint.playIfNotPlaying();
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (event.key === 'h' && !event.repeat) {
			showHint();
		}
	}

	$effect(() => {
		if (game.isWon) {
			context.handleGameOver('won');
		}
	});

	$effect(function syncSettings() {
		const {
			autoFill,
			autoFillCellsAroundStars,
			autoFillRowsWithReachedStars,
			autoFillRegionsWithReachedStars,
		} = context.settingsManager.settings;

		untrack(() => {
			game.runtimeSettings = {
				autoFill,
				autoFillCellsAroundStars,
				autoFillRowsWithReachedStars,
				autoFillRegionsWithReachedStars,
			};
		});
	});
</script>

<svelte:window
	onpointerup={handlePointerUp}
	onpointerdown={handlePointerDown}
	onkeypress={handleKeyPress}
/>

<GameLayout>
	{#snippet Island()}
		<GameIsland {context} />
	{/snippet}

	<div class={cn('flex flex-col items-center w-full gap-4 justify-center pb-6 md:pb-0', {})}>
		<div class="flex items-center justify-between gap-2 w-full max-w-2xl">
			<div class="flex items-center gap-2">
				<StarBattleHowToPlayButton />

				<Dropdown bind:open={isSizeDropdownOpen}>
					<DropdownButton class="btn-sm text-nowrap">
						<span>
							{game.difficulty.size}<span class="hidden sm:inline"
								>x{game.difficulty.size}</span
							></span
						>
						- {game.difficulty.stars}★
					</DropdownButton>

					<DropdownContent menu>
						{#each starBattleDifficulties as difficulty}
							<DropdownItem>
								<button
									class={cn({
										'menu-active':
											game.difficulty.size === difficulty.size &&
											game.difficulty.stars === difficulty.stars,
										'hidden sm:block': difficulty.size >= 12,
									})}
									aria-label="{difficulty.size} by {difficulty.size} with {difficulty.stars} stars per row, column, and shape"
									onclick={() => {
										context.createGame({
											difficulty,
										});
										isSizeDropdownOpen = false;
									}}
								>
									{difficulty.size}x{difficulty.size} - {difficulty.stars}★
								</button>
							</DropdownItem>
						{/each}
					</DropdownContent>
				</Dropdown>

				<button
					class="btn btn-sm"
					onclick={() => {
						context.sounds.start.play();
						game.startOver();
					}}
				>
					Start over
				</button>

				<button
					aria-label="Open info dialog"
					class="btn btn-sm"
					onclick={() => (isInfoModalOpen = true)}
				>
					<InfoSolidIcon class="size-5" />
				</button>

				<MoreGamesButton class="btn-sm hidden xs:block" />

				{@render Settings('dropdown-end')}
			</div>

			<div class="flex items-center gap-2">
				<div class="tooltip tooltip-bottom hidden sm:inline-block" data-tip="Hint (H)">
					<button class="btn btn-sm" aria-label="Hint" onclick={showHint}>
						<LightBulbIcon class="size-6" />
					</button>
				</div>

				<div
					class="tooltip tooltip-bottom hidden sm:inline-block"
					data-tip="Undo ({isMac ? '⌘' : 'Ctrl'} + Z)"
				>
					<button
						class="btn btn-sm"
						disabled={!game.canUndo()}
						aria-label="Undo"
						onclick={() => game.undo()}
					>
						<UndoIcon />
					</button>
				</div>

				<div
					class="tooltip tooltip-left hidden sm:inline-block"
					data-tip="Redo ({isMac ? '⌘' : 'Ctrl'} + Shift + Z)"
				>
					<button
						class="btn btn-sm"
						disabled={!game.canRedo()}
						aria-label="Redo"
						onclick={() => game.redo()}
					>
						<RedoIcon />
					</button>
				</div>
			</div>
		</div>

		<div class="relative sm:grow-0 w-full flex items-center justify-center">
			<div class="relative size-full flex items-center justify-center">
				{#if game.loadState === 'success'}
					<div
						in:fade={{ duration: 300, easing: quadInOut }}
						out:fade={{ duration: 300, easing: quadInOut }}
						class="absolute inset-0 size-full flex items-center justify-center"
					>
						<StarBattleRenderer
							class="max-w-xl"
							disabled={game.empty}
							{game}
							onpointermove={handlePointerMove}
							{onCellClick}
						/>
					</div>
				{/if}
				<div class="size-full aspect-square max-w-xl"></div>
			</div>

			{#if context.game?.loadState === 'loading'}
				<div
					transition:fade
					class="loading loading-xl absolute left-1/2 top-1/2 -translate-1/2"
				></div>
			{/if}

			{#if context.game?.loadState === 'error'}
				<div
					in:fly={{ y: 20, delay: 300 }}
					out:fly={{ y: 20 }}
					class="fixed left-1/2 top-1/2 -translate-1/2 w-full"
				>
					<div
						role="alert"
						class="card bg-base-100 shadow-lg w-full mx-auto max-w-[400px]"
					>
						<div class="card-body">
							<h3 class="card-title">Oops!</h3>
							<p>We could not get a Star Battle game</p>

							<div class="card-actions justify-center xs:justify-end mt-2">
								<a href="/" class="btn btn-ghost w-full xs:w-auto">Go home</a>
								<button
									class="btn btn-primary w-full xs:w-auto"
									onclick={() => {
										game.generateGame();
									}}
								>
									Try again
								</button>
							</div>
						</div>
					</div>
				</div>
			{/if}
		</div>

		<div class="flex justify-between items-center gap-4 w-full grow">
			<div class="flex sm:hidden gap-4"></div>

			<div class="flex sm:hidden gap-4 self-end">
				<button
					class="btn btn-lg btn-circle"
					disabled={!game.canUndo()}
					onclick={() => game.undo()}
				>
					<UndoIcon class="size-8" />
				</button>

				<button
					class="btn btn-lg btn-circle"
					disabled={!game.canRedo()}
					onclick={() => game.redo()}
				>
					<RedoIcon class="size-8" />
				</button>
			</div>
		</div>
	</div>
</GameLayout>

<StarBattleInfoModal bind:isOpen={isInfoModalOpen} />

{#snippet Settings(className: string)}
	<Dropdown class={className}>
		<DropdownButton class="btn-sm" aria-label="Show settings">
			<SettingsIcon class="w-5 h-5" />
		</DropdownButton>

		<DropdownContent class="w-[328px]">
			<Collapse open={context.settingsManager.settings.autoFill}>
				<DropdownItem>
					<Toggle
						aria-label="toggle dots autofill"
						bind:checked={context.settingsManager.settings.autoFill}
					>
						Dots Autofill
					</Toggle>
				</DropdownItem>

				<CollapseContent asSettings>
					<DropdownItem>
						<Toggle
							aria-label="toggle cells around stars"
							bind:checked={context.settingsManager.settings.autoFillCellsAroundStars}
						>
							Cells around stars
						</Toggle>
					</DropdownItem>

					<DropdownItem>
						<Toggle
							aria-label="toggle rows/columns with reached stars"
							bind:checked={
								context.settingsManager.settings.autoFillRowsWithReachedStars
							}
						>
							Rows/columns with reached stars
						</Toggle>
					</DropdownItem>

					<DropdownItem>
						<Toggle
							aria-label="toggle regions with reached stars"
							bind:checked={
								context.settingsManager.settings.autoFillRegionsWithReachedStars
							}
						>
							Regions with reached stars
						</Toggle>
					</DropdownItem>
				</CollapseContent>
			</Collapse>
		</DropdownContent>
	</Dropdown>
{/snippet}
