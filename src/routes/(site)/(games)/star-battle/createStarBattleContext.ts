import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
import { starBattleDifficulties } from './constants';
import { pickRandom, Smush32 } from '@thi.ng/random';
import { MediaQuery } from '$lib/util/MediaQuery.svelte';
import { StarBattleGame } from './StarBattleGame.svelte';
import { Stats } from '$lib/util/Stats.svelte';
import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';
import { starBattleSoundResources } from './starBattleSoundResources';
import { supabase } from '$lib/api/supabase';

const smallMediaQuery = new MediaQuery('(min-width: 640px)');

export function createStarBattleContext() {
	return new GameContext({
		gameName: 'Star Battle',
		gameKey: 'star-battle',
		GameClass: StarBattleGame,
		sounds: {
			resources: starBattleSoundResources,
			lifecycle: {
				createGame: starBattleSoundResources.start,
				win: starBattleSoundResources.gameWin,
			},
		},
		settings: {
			defaultSettings: {
				difficulty: starBattleDifficulties[0],
				autoFill: false,
				autoFillCellsAroundStars: false,
				autoFillRowsWithReachedStars: false,
				autoFillRegionsWithReachedStars: false,
			},
		},
		variants: {
			map: {
				difficulty: {
					allValues: starBattleDifficulties,
					format(difficulty) {
						return `${difficulty.size}x${difficulty.size} - ${difficulty.stars}★`;
					},
				},
			},
			fromGame(game) {
				return {
					difficulty: game.difficulty,
				};
			},
			getStatsVariant(variants) {
				return variants.difficulty
					? `${variants.difficulty.size}x${variants.difficulty.size}-${variants.difficulty.stars}`
					: '6x6-1';
			},
			getLeaderboardVariant(variants) {
				return variants.difficulty
					? `${variants.difficulty.size}x${variants.difficulty.size}-${variants.difficulty.stars}`
					: '6x6-1';
			},
		},
		defaultGameProps(context) {
			const settings = context.settingsManager.settings;

			return {
				difficulty: settings.difficulty,
				timer: context.timer,
				onGameWin: () => context.handleGameOver('won'),
				runtimeSettings: {
					autoFill: settings.autoFill,
					autoFillCellsAroundStars: settings.autoFillCellsAroundStars,
					autoFillRowsWithReachedStars: settings.autoFillRowsWithReachedStars,
					autoFillRegionsWithReachedStars: settings.autoFillRegionsWithReachedStars,
				},
			};
		},
		stats({ props, context }) {
			const game = context.game!;

			return {
				stats: new Stats({
					...props,
					liveStats: {
						stars: {
							name: 'Stars',
							unit: 'plain',
							value() {
								return game.stars;
							},
						},
						dots: {
							name: 'Dots',
							unit: 'plain',
							value() {
								return game.dots;
							},
						},
						clicks: {
							name: 'Clicks',
							unit: 'plain',
							value: () => game.clicks,
							metrics: {
								total: {
									key: 'totalClicks',
									name: 'Total Clicks',
								},
								average: {
									key: 'averageClicks',
									name: 'Average Clicks',
								},
								min: {
									key: 'fewestClicks',
									name: 'Fewest Clicks',
									useAsBest: true,
								},
								max: {
									key: 'maxClicks',
									name: 'Max Clicks',
								},
							},
						},
						totalClicks: {
							name: 'Total Clicks',
							description: 'Includes undos and redos',
							unit: 'plain',
							value: () => game.totalClicks,
							metrics: {
								total: {
									key: 'totalTotalClicks',
									name: 'Total Total Clicks',
								},
								average: {
									key: 'averageTotalClicks',
									name: 'Average Total Clicks',
								},
								min: {
									key: 'fewestTotalClicks',
									name: 'Fewest Total Clicks',
									useAsBest: true,
								},
								max: {
									key: 'maxTotalClicks',
									name: 'Max Total Clicks',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'stars'],
				}),
				visibleStats: [
					'bestTime',
					'averageTime',
					'fewestClicks',
					'averageClicks',
					'wonGames',
					'totalGames',
				],
				canUpdateWithGameLost(game) {
					return !game.isWon && game.totalClicks > 3;
				},
			};
		},
		leaderboard({ props }) {
			return {
				leaderboard: new Leaderboard({
					...props,
					firstAvailableDate: new Date('2025/11/06'),
					order: 'lower-first',
					hasLevel: false,
					hasTime: true,
					hasMoves: true,
				}),
				sendScoreOn: ['won'],
				getScore(game) {
					return {
						score: game.getScore(),
						moves: game.clicks,
					};
				},
			};
		},
		dailyGame(context) {
			return {
				type: 'fetch',
				gameVariant: smallMediaQuery.current ? undefined : 'Mobile',
				firstAvailableGameDate: new Date('2025/11/06'),
				async fetchGame(date) {
					const games = (
						await supabase
							.rpc('daily_star_battle', {
								target_date: `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`,
							})
							.select('game')
							.throwOnError()
					).data as { game: string }[];

					if (!games || games.length === 0) {
						throw new Error('Game not found');
					}

					return games[0];
				},
				onPlay: ({ game }) => {
					context.createGame({
						game,
					});
				},
			};
		},
		specificGame(context) {
			return {
				toProps(encoded) {
					return {
						game: encoded,
						difficulty: context.settingsManager.settings.difficulty,
					};
				},
			};
		},
		onWillCreateGame({ newGameOptions, context }) {
			if (!newGameOptions.isDaily) {
				context.settingsManager.settings.difficulty = newGameOptions.difficulty;
			}
		},
	});
}
