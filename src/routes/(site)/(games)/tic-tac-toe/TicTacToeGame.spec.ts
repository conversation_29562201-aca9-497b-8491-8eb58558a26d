import { TicTacToeGame, type Board, type Winner } from './TicTacToeGame.svelte';

describe('TicTacToeGame', () => {
	describe('checkWinnerOnRows', () => {
		describe.each([
			[
				[
					['o', 'o', 'o'],
					[null, null, null],
					[null, null, null],
				] as Board,
				{
					player: 'o',
					colStart: 0,
					rowStart: 0,
					direction: 'row',
				} as Winner,
			],
			[
				[
					[null, null, null],
					['o', 'o', 'o'],
					[null, null, null],
				] as Board,
				{
					player: 'o',
					colStart: 0,
					rowStart: 1,
					direction: 'row',
				} as Winner,
			],
			[
				[
					[null, null, null],
					[null, null, null],
					['x', 'x', 'x'],
				] as Board,
				{
					player: 'x',
					colStart: 0,
					rowStart: 2,
					direction: 'row',
				} as Winner,
			],
		])('when there is a winning game', (board, winner) => {
			it('should return the correct winner', () => {
				const gameWinner = TicTacToeGame.checkWinnerOnRows(board);

				expect(gameWinner).toMatchObject(winner);
			});
		});
	});

	describe('checkWinnerOnCols', () => {
		describe.each([
			[
				[
					['o', null, null],
					['o', null, null],
					['o', null, null],
				] as Board,
				{
					player: 'o',
					colStart: 0,
					rowStart: 0,
					direction: 'col',
				} as Winner,
			],
			[
				[
					[null, 'o', null],
					[null, 'o', null],
					[null, 'o', null],
				] as Board,
				{
					player: 'o',
					colStart: 1,
					rowStart: 0,
					direction: 'col',
				} as Winner,
			],
			[
				[
					[null, null, 'x'],
					[null, null, 'x'],
					[null, null, 'x'],
				] as Board,
				{
					player: 'x',
					colStart: 2,
					rowStart: 0,
					direction: 'col',
				} as Winner,
			],
		])('when there is a winning game', (board, winner) => {
			it('should return the correct winner', () => {
				const gameWinner = TicTacToeGame.checkWinnerOnCols(board);

				expect(gameWinner).toMatchObject(winner);
			});
		});
	});

	describe('checkWinnerOnDiagonal', () => {
		describe.each([
			[
				[
					['o', null, null],
					[null, 'o', null],
					[null, null, 'o'],
				] as Board,
				{
					player: 'o',
					colStart: 0,
					rowStart: 0,
					direction: 'diagonal',
				} as Winner,
			],
			[
				[
					[null, null, 'x'],
					[null, 'x', null],
					['x', null, null],
				] as Board,
				{
					player: 'x',
					colStart: 2,
					rowStart: 0,
					direction: 'diagonal',
				} as Winner,
			],
		])('when there is a winning game', (board, winner) => {
			it('should return the correct winner', () => {
				const gameWinner = TicTacToeGame.checkWinnerOnDiagonal(board);

				expect(gameWinner).toMatchObject(winner);
			});
		});
	});
});
