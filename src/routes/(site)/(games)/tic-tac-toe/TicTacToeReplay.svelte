<script>
	import { blur } from 'svelte/transition';
	import { onMount } from 'svelte';
	import { getBrowser } from '$lib/functions/getBrowser';

	let safari = $state(false);

	onMount(() => {
		safari = getBrowser() === 'safari';
	});
</script>

<svg
	out:blur={{ duration: 200, amount: 20 }}
	class="pointer-events-none absolute top-1/2 left-1/2 z-30 size-full -translate-x-1/2 -translate-y-1/2 transform"
	class:safari
	viewBox="0 0 402 402"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<path
		d="M303.637 201.319C303.637 144.81 257.828 99 201.319 99C144.81 99 99 144.81 99 201.319C99 257.828 144.81 303.637 201.319 303.637C235.737 303.637 266.185 286.643 284.732 260.588M303.637 201.319L322.354 175.739M303.637 201.319L278.058 175.739"
		stroke="currentColor"
		class="text-base-100"
		stroke-width="21"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
	<path
		d="M303.637 201.319C303.637 144.81 257.828 99 201.319 99C144.81 99 99 144.81 99 201.319C99 257.828 144.81 303.637 201.319 303.637C235.737 303.637 266.185 286.643 284.732 260.588M303.637 201.319L322.354 175.739M303.637 201.319L278.058 175.739"
		stroke="currentColor"
		stroke-width="8"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
</svg>

<style>
	@keyframes dash {
		to {
			stroke-dashoffset: 0;
			transform: rotate(0deg);
		}
	}

	path {
		transform-origin: center;
		transform: rotate(-120deg);
		stroke-dasharray: 700;
		stroke-dashoffset: -700;
		animation: dash 800ms ease-in-out forwards;
		animation-delay: 500ms;
	}

	@keyframes reveal {
		to {
			opacity: 1;
			transform: rotate(0deg);
		}
	}

	.safari path {
		opacity: 0;
		stroke-dasharray: none;
		stroke-dashoffset: none;
		transform: rotate(-120deg);
		animation: reveal 500ms ease-in-out forwards;
		animation-delay: 600ms;
	}
</style>
