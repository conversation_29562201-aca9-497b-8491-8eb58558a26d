import { get2DGrid } from '$lib/functions/get2DGrid';
import { shuffle } from '$lib/functions/shuffle';
import { Timer } from '$lib/util/Timer.svelte';

export type Player = 'x' | 'o';

export type Board = (Player | null)[][];

export type Move = {
	row: number;
	col: number;
};

export type Difficulty = 'easy' | 'normal' | 'hard' | 'impossible';

export type TicTacToeOpponent = 'cpu' | 'player2';

export type Winner = {
	player: Player;
	colStart: number;
	rowStart: number;
	direction: 'row' | 'col' | 'diagonal';
};

const difficultyWinPercentage: Record<Difficulty, number> = {
	easy: 0.3,
	normal: 0.7,
	hard: 0.9,
	impossible: 1,
};

interface Props {
	difficulty: Difficulty;
	turn: Player;
	timer: Timer;
	opponent: TicTacToeOpponent;
}

export class TicTacToeGame {
	board = $state.raw(TicTacToeGame.getNewBoard());
	turn: Player = $state('x');
	winner: Winner | null = $state(null);
	tie = $state(false);
	gameOverAt: number | null = $state(null);
	difficulty: Difficulty = $state('normal');
	timer: Timer;
	opponent: TicTacToeOpponent;

	constructor({ difficulty = 'normal', turn = 'x', timer, opponent }: Props) {
		this.difficulty = difficulty;
		this.turn = turn;
		this.timer = timer;
		this.opponent = opponent;
	}

	canPlayAt(row: number, col: number, player: Player): boolean {
		return !this.isOver() && player === this.turn && this.board[row][col] === null;
	}

	playAt(row: number, col: number, player: Player): boolean {
		if ((this.timer.started && this.timer.paused) || this.timer.stopped) {
			return false;
		}

		if (this.canPlayAt(row, col, player)) {
			this.timer.start();
			this.board = TicTacToeGame.getNextBoard(this.board, row, col, player);
			this.turn = player === 'x' ? 'o' : 'x';
			this.winner = TicTacToeGame.checkWinner(this.board);
			this.tie = TicTacToeGame.checkTie(this.board);

			if (this.isOver()) {
				this.timer.stop();
				this.gameOverAt = performance.now();
			}

			return true;
		}

		return false;
	}

	/**
	 * Plays the CPU turn according to the difficulty
	 **/
	playCPUTurn(): Move | null {
		const shouldWin = Math.random() <= difficultyWinPercentage[this.difficulty];
		const nextMove = shouldWin
			? TicTacToeGame.getBestMove(this.board)
			: TicTacToeGame.getRandomMove(this.board);

		if (this.playAt(nextMove.row, nextMove.col, 'o')) {
			return nextMove;
		}

		return null;
	}

	isOver(): boolean {
		return this.winner !== null || this.tie;
	}

	/** @see https://www.geeksforgeeks.org/minimax-algorithm-in-game-theory-set-3-tic-tac-toe-ai-finding-optimal-move */
	static minimax(board: Board, depth: number, isMaximizing: boolean): number {
		const score = TicTacToeGame.evaluate(board);

		if (score > 0) {
			return score - depth;
		}

		if (score < 0) {
			return score + depth;
		}

		if (!TicTacToeGame.hasNextPossiblePlays(board)) {
			return 0;
		}

		let best = isMaximizing ? -Infinity : Infinity;
		const player: Player = isMaximizing ? 'x' : 'o';
		const nextMoves = TicTacToeGame.getNextPossibleMoves(board);

		nextMoves.forEach((move) => {
			board[move.row][move.col] = player;
			best = isMaximizing
				? Math.max(best, TicTacToeGame.minimax(board, depth + 1, false))
				: Math.min(best, TicTacToeGame.minimax(board, depth + 1, true));
			board[move.row][move.col] = null;
		});

		return best;
	}

	static getBestMove(board: Board): Move {
		let bestScore = Infinity;
		let bestMove: Move = {
			row: -1,
			col: -1,
		};
		const nextMoves = shuffle(TicTacToeGame.getNextPossibleMoves(board));

		nextMoves.forEach((move) => {
			board[move.row][move.col] = 'o';
			const moveScore = TicTacToeGame.minimax(board, 0, true);
			board[move.row][move.col] = null;

			if (moveScore < bestScore) {
				bestScore = moveScore;
				bestMove = move;
			}
		});

		return bestMove;
	}

	static getRandomMove(board: Board): Move {
		const nestMoves = TicTacToeGame.getNextPossibleMoves(board);

		return nestMoves[Math.floor(Math.random() * nestMoves.length)];
	}

	static evaluate(board: Board): number {
		const winner = TicTacToeGame.checkWinner(board);
		const maxScore = board.length * board[0].length + 1;

		if (winner) {
			if (winner.player === 'x') {
				return maxScore;
			}
			return -maxScore;
		}

		return 0;
	}

	static hasNextPossiblePlays(board: Board): boolean {
		return board.some((row) => row.some((col) => col === null));
	}

	static getNextPossibleMoves(board: Board): Move[] {
		return board.flatMap(
			(row, rowIndex) =>
				row
					.map((col, colIndex) => {
						if (col === null) {
							return { row: rowIndex, col: colIndex } as Move;
						}
					})
					.filter(Boolean) as Move[],
		);
	}

	static getNextBoard(board: Board, playRow: number, playCol: number, player: Player): Board {
		const newBoard = TicTacToeGame.cloneBoard(board);

		newBoard[playRow][playCol] = player;

		return newBoard;
	}

	static checkTie(board: Board): boolean {
		return !TicTacToeGame.hasNextPossiblePlays(board) && this.checkWinner(board) === null;
	}

	static checkWinner(board: Board): Winner | null {
		return (
			TicTacToeGame.checkWinnerOnRows(board) ||
			TicTacToeGame.checkWinnerOnCols(board) ||
			TicTacToeGame.checkWinnerOnDiagonal(board)
		);
	}

	static checkWinnerOnRows(board: Board): Winner | null {
		const winnerRowIndex = board.findIndex((row) =>
			row.every((player) => player === row[0] && player !== null),
		);

		if (winnerRowIndex > -1) {
			return {
				colStart: 0,
				rowStart: winnerRowIndex,
				direction: 'row',
				player: board[winnerRowIndex][0]!,
			};
		}

		return null;
	}

	static checkWinnerOnCols(board: Board): Winner | null {
		const rotatedBoard = board.map((row, rowIndex) =>
			row.map((_, colIndex) => board[colIndex][rowIndex]),
		);

		const winnerOnRows = TicTacToeGame.checkWinnerOnRows(rotatedBoard);

		if (winnerOnRows) {
			return {
				...winnerOnRows,
				direction: 'col',
				colStart: winnerOnRows.rowStart,
				rowStart: winnerOnRows.colStart,
			};
		}

		return null;
	}

	static checkWinnerOnDiagonal(board: Board): Winner | null {
		const firstDiagonal = Array(board.length)
			.fill(null)
			.map((_, i) => board[i][i]);

		const secondDiagonal = Array(board.length)
			.fill(null)
			.map((_, i) => board[i][board.length - 1 - i]);

		if (firstDiagonal.every((play) => play === firstDiagonal[0] && play !== null)) {
			return {
				colStart: 0,
				rowStart: 0,
				direction: 'diagonal',
				player: firstDiagonal[0]!,
			};
		}

		if (secondDiagonal.every((play) => play === secondDiagonal[0] && play !== null)) {
			return {
				colStart: board.length - 1,
				rowStart: 0,
				direction: 'diagonal',
				player: secondDiagonal[0]!,
			};
		}

		return null;
	}

	private static getNewBoard(size = 3): Board {
		return get2DGrid(size);
	}

	private static cloneBoard(board: Board): Board {
		return board.map((row) => row.map((item) => item));
	}
}
