<script>
	import { blur } from 'svelte/transition';
</script>

<svg
	class="size-full"
	out:blur={{ duration: 200, amount: 20 }}
	viewBox="0 0 134 134"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<circle
		cx="67"
		cy="67"
		r="27"
		transform="rotate(-90 67 67)"
		class="stroke-game-tic-tac-toe-o"
		stroke-width="20"
		stroke-linejoin="round"
		stroke-linecap="round"
	/>
</svg>

<style>
	@keyframes dash {
		to {
			stroke-dashoffset: 0;
		}
	}

	circle {
		stroke-dasharray: 223;
		stroke-dashoffset: 223;
		animation: dash 400ms ease-in forwards;
	}
</style>
