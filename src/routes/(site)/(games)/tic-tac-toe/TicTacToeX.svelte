<script>
	import { blur } from 'svelte/transition';
</script>

<svg
	class="size-full"
	out:blur={{ duration: 200, amount: 20 }}
	viewBox="0 0 134 134"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<line
		x1="43.4584"
		y1="42.7513"
		x2="92.2487"
		y2="91.5416"
		class="stroke-game-tic-tac-toe-x"
		stroke-width="26"
		stroke-linecap="round"
	/>
	<line
		x1="91.5415"
		y1="42.7513"
		x2="42.7511"
		y2="91.5416"
		class="stroke-game-tic-tac-toe-x"
		stroke-width="26"
		stroke-linecap="round"
	/>
</svg>

<style>
	@keyframes dash {
		to {
			stroke-dashoffset: 0;
		}
	}

	line {
		stroke-dasharray: 120;
		stroke-dashoffset: 120;
		animation: dash 200ms ease-in forwards;
	}

	line:nth-child(1) {
		animation-delay: 0ms;
	}
	line:nth-child(2) {
		animation-delay: 200ms;
	}
</style>
