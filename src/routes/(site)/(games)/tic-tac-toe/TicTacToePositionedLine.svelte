<script lang="ts">
	import type { Winner } from './TicTacToeGame.svelte';
	import TicTacToeLine from './TicTacToeLine.svelte';

	interface Props {
		winner: Winner | null;
	}

	let { winner }: Props = $props();
</script>

{#if winner?.direction === 'row'}
	<div
		class="pointer-events-none absolute top-0 left-0 z-20 h-1/3 w-full"
		style:transform={`translateY(${winner.rowStart * 100}%)`}
	>
		<TicTacToeLine />
	</div>
{/if}

{#if winner?.direction === 'col'}
	<div
		class="pointer-events-none absolute top-0 left-0 z-20 h-1/3 w-full origin-top-left"
		style:transform={`rotateZ(90deg) translateY(-${100 + winner.colStart * 100}%)`}
	>
		<TicTacToeLine />
	</div>
{/if}

{#if winner?.direction === 'diagonal'}
	<div
		class="pointer-events-none absolute top-0 left-0 z-20 h-1/3 w-full origin-center"
		style:transform={winner.colStart === 0
			? 'translateY(100%) rotate(45deg) scaleX(1.4)'
			: 'translateY(100%) rotate(135deg) scaleX(1.4)'}
	>
		<TicTacToeLine />
	</div>
{/if}
