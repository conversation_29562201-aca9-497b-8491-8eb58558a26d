<script>
	import { blur } from 'svelte/transition';
</script>

<svg
	out:blur={{ duration: 200, amount: 20 }}
	class="size-full"
	viewBox="0 0 403 134"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<line
		x1="6"
		y1="67"
		x2="398"
		y2="67"
		stroke="currentColor"
		class="text-base-100"
		stroke-width="10"
		stroke-linecap="round"
	/>
	<line
		x1="6"
		y1="67"
		x2="398"
		y2="67"
		stroke="currentColor"
		stroke-width="4"
		stroke-linecap="round"
	/>
</svg>

<style>
	@keyframes dash {
		to {
			stroke-dashoffset: 0;
		}
	}

	line {
		stroke-dasharray: 398;
		stroke-dashoffset: 398;
		animation: dash 200ms ease-in forwards;
		animation-delay: 400ms;
	}
</style>
