<script lang="ts">
	import NumberFlow from '@number-flow/svelte';
	import type { Player } from './TicTacToeGame.svelte';

	interface Props {
		playerScore: number;
		cpuScore: number;
		player2Score: number;
		opponent: 'cpu' | 'player2';
		turn: Player;
	}

	let { playerScore, cpuScore, player2Score, opponent, turn }: Props = $props();
</script>

<div class="align-center grid auto-cols-max grid-flow-col justify-center gap-5 text-center">
	<div class="indicator">
		{#if turn === 'x'}
			<span class="badge badge-xs indicator-item bg-game-tic-tac-toe-x"></span>
		{/if}

		<div class="flex flex-col items-center justify-center" class:px-4={opponent === 'cpu'}>
			<NumberFlow
				class="text-5xl"
				value={playerScore}
				format={{
					minimumIntegerDigits: 2,
				}}
			/>

			{#if opponent === 'cpu'}
				YOU (X)
			{/if}

			{#if opponent === 'player2'}
				PLAYER 1 (X)
			{/if}
		</div>
	</div>

	<div class="flex flex-col items-center justify-center">
		<NumberFlow
			class="text-5xl invisible"
			value={0}
			format={{
				minimumIntegerDigits: 2,
			}}
		/>
		-
	</div>

	<div class="indicator">
		{#if turn === 'o'}
			<span class="badge badge-xs indicator-item bg-game-tic-tac-toe-o"></span>
		{/if}

		<div class="flex flex-col items-center justify-center" class:px-4={opponent === 'cpu'}>
			{#if opponent === 'cpu'}
				<NumberFlow
					class="text-5xl"
					value={cpuScore}
					format={{
						minimumIntegerDigits: 2,
					}}
				/>
				CPU (O)
			{/if}

			{#if opponent === 'player2'}
				<NumberFlow
					class="text-5xl"
					value={player2Score}
					format={{
						minimumIntegerDigits: 2,
					}}
				/>
				PLAYER 2 (O)
			{/if}
		</div>
	</div>
</div>
