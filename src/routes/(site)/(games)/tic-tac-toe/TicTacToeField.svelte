<svg
	class="absolute inset-0 z-0"
	viewBox="0 0 402 402"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<line
		x1="134"
		y1="2"
		x2="134"
		y2="400"
		stroke="currentColor"
		stroke-width="4"
		stroke-linecap="round"
	/>
	<line
		x1="268"
		y1="2"
		x2="268"
		y2="400"
		stroke="currentColor"
		stroke-width="4"
		stroke-linecap="round"
	/>
	<line
		x1="2"
		y1="132"
		x2="400"
		y2="132"
		stroke="currentColor"
		stroke-width="4"
		stroke-linecap="round"
	/>
	<line
		x1="2"
		y1="266"
		x2="400"
		y2="266"
		stroke="currentColor"
		stroke-width="4"
		stroke-linecap="round"
	/>
</svg>

<style>
	@keyframes dash {
		to {
			stroke-dashoffset: 0;
		}
	}

	line {
		stroke-dasharray: 398;
		stroke-dashoffset: 398;
		animation: dash 200ms ease-in forwards;
	}
	line:nth-child(1) {
		animation-delay: 0ms;
	}
	line:nth-child(2) {
		animation-delay: 200ms;
	}
	line:nth-child(3) {
		animation-delay: 400ms;
	}
	line:nth-child(4) {
		animation-delay: 600ms;
	}
</style>
