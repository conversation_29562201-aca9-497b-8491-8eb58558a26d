import { MovableStackElementRenderer } from './MovableStackElementRenderer';

export type MovableOptions = {
	canMove?: () => boolean;
	onDrop?: (event: PointerEvent, siblingsAmount: number) => void;
	canMoveWithNextSiblings?: () => boolean;
};

export class MovableStackElement {
	initialTouchAction: string;
	renderer: MovableStackElementRenderer;
	siblingsRenderers: Array<MovableStackElementRenderer> = [];
	shouldReplicateMovementsOnSiblings = false;
	startX = 0;
	startY = 0;
	currentX = 0;
	currentY = 0;
	element: HTMLElement;
	options: MovableOptions;
	lastAnimationFrameRequest: number | null = null;
	posDiffX = 0;
	posDiffY = 0;

	constructor(element: HTMLElement, options: MovableOptions) {
		this.element = element;
		this.options = options;
		this.initialTouchAction = element.style.touchAction;
		this.renderer = new MovableStackElementRenderer(element);
		this.element.style.touchAction = 'none';
		this.element.addEventListener('pointerdown', this.handlePointerDown);

		this.initSiblings();
	}

	resetPosition = () => {
		this.renderer.goToInitialPosition();

		if (this.shouldReplicateMovementsOnSiblings) {
			this.siblingsRenderers.forEach((sibling) => sibling.goToInitialPosition());
		}
	};

	handlePointerUp = (event: PointerEvent) => {
		document.removeEventListener('pointerup', this.handlePointerUp);
		document.removeEventListener('pointermove', this.handlePointerMove);

		// Cancel move animation frame
		if (this.lastAnimationFrameRequest) {
			cancelAnimationFrame(this.lastAnimationFrameRequest);
			this.lastAnimationFrameRequest = null;
		}

		requestAnimationFrame(() => {
			if (!this.options.onDrop?.(event, this.siblingsRenderers.length)) {
				this.resetPosition();
			}
		});
	};

	handlePointerMove = (e: MouseEvent) => {
		e.preventDefault();

		// If an animation frame was already requested after last repaint, cancel it in favour of the newest event
		if (this.lastAnimationFrameRequest) {
			cancelAnimationFrame(this.lastAnimationFrameRequest);
		}

		// Save the requested frame so we can check next time if one was already requested
		this.lastAnimationFrameRequest = requestAnimationFrame(() => {
			// Do the distance calculation inside the animation frame request also, so the browser doesn't have to do it more often than necessary

			this.currentX = e.clientX;
			this.currentY = e.clientY;

			this.posDiffX = this.currentX - this.startX;
			this.posDiffY = this.currentY - this.startY;

			this.translateRenderers();

			// Since this frame didn't get cancelled, the this.lastUpdateCall should be reset so new frames can be called.
			this.lastAnimationFrameRequest = null;
		});
	};

	translateRenderers = () => {
		this.renderer.translateBy(this.posDiffX, this.posDiffY);

		if (this.shouldReplicateMovementsOnSiblings) {
			this.siblingsRenderers.forEach((sibling) =>
				sibling.translateBy(this.posDiffX, this.posDiffY),
			);
		}
	};

	handlePointerDown = (e: MouseEvent) => {
		if (this.options.canMove?.()) {
			e.preventDefault();

			this.startX = e.clientX;
			this.startY = e.clientY;

			this.renderer.startMovement();

			if (this.options.canMoveWithNextSiblings?.()) {
				this.shouldReplicateMovementsOnSiblings = true;
				this.siblingsRenderers.forEach((sibling) => sibling.startMovement());
			}

			document.addEventListener('pointerup', this.handlePointerUp);
			document.addEventListener('pointermove', this.handlePointerMove);
		}
	};

	initSiblings() {
		this.siblingsRenderers = [];
		let currentElement = this.element.nextElementSibling as HTMLElement | null;

		while (currentElement) {
			this.siblingsRenderers.push(new MovableStackElementRenderer(currentElement));

			currentElement = currentElement.nextElementSibling as HTMLElement | null;
		}
	}

	refresh() {
		this.renderer.refresh();
		this.initialTouchAction = this.element.style.touchAction;
		// TODO: Handle other state variables
	}

	dispose() {
		this.resetPosition();
		this.renderer.dispose();
		this.element.style.touchAction = this.initialTouchAction;
		this.element.removeEventListener('pointerdown', this.handlePointerDown);
		document.removeEventListener('pointerup', this.handlePointerUp);
		document.removeEventListener('pointermove', this.handlePointerMove);
		this.siblingsRenderers = [];
	}
}
