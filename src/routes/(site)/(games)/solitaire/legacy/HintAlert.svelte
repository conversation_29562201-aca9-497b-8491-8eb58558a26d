<script>
	import { fly } from 'svelte/transition';
</script>

<div
	transition:fly={{ y: 120, duration: 500 }}
	role="alert"
	class="alert alert-warning sticky bottom-4 mx-auto mb-4 flex w-64 flex-nowrap items-center shadow-lg"
>
	<svg
		xmlns="http://www.w3.org/2000/svg"
		class="size-6 shrink-0 stroke-current"
		fill="none"
		viewBox="0 0 24 24"
	>
		<path
			stroke-linecap="round"
			stroke-linejoin="round"
			stroke-width="2"
			d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
		/>
	</svg>
	<span>Could not find any hint</span>
</div>
