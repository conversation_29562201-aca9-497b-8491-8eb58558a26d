<script lang="ts">
	import { browser } from '$app/environment';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import { SolitaireGameLegacy } from './SolitaireGameLegacy';
	import { onMount } from 'svelte';

	interface Props {
		onChange: () => void;
		disabled?: boolean;
	}

	let { onChange, disabled = false }: Props = $props();
	let isOpen = $state(false);
	let shouldFinishAutomatically = $state(false);
	let loadedSettings = $state(false);

	$effect(() => {
		if (browser && loadedSettings) {
			SolitaireGameLegacy.shouldFinishAutomatically = shouldFinishAutomatically;
			onChange();
		}
	});

	onMount(() => {
		SolitaireGameLegacy.loadSettings();
		shouldFinishAutomatically = SolitaireGameLegacy.shouldFinishAutomatically;
		loadedSettings = true;
	});
</script>

<Dropdown bind:open={isOpen}>
	<DropdownButton
		class="btn btn-neutral btn-xs flex flex-nowrap md:btn-sm"
		aria-label="Show settings"
	>
		<SettingsIcon class="size-5" />
	</DropdownButton>

	<DropdownContent class="w-60">
		<DropdownItem>
			<Toggle
				aria-label="toggle automatic finish"
				{disabled}
				bind:checked={shouldFinishAutomatically}
			>
				Automatic Finish
			</Toggle>
		</DropdownItem>
	</DropdownContent>
</Dropdown>
