<script lang="ts">
	import { MovableElementsStack } from '../MovableElementsStack/MovableElementsStack';
	import type { Card } from '$lib/models/card-game';
	import { onDestroy, onMount } from 'svelte';
	import GameCard from './GameCard.svelte';
	import CardSlot from './CardSlot.svelte';
	import { twMerge } from 'tailwind-merge';

	interface Props {
		class?: string;
		aligned?: boolean;
		horizontal?: boolean;
		name: string;
		cards: Card[];
		cardsToRender?: number;
		cardsClass?: string;
		onCardClick?: (card: Card, cardIndex: number, stack: string) => void;
		canMove?: (card: Card, cardIndex: number, stack: string) => boolean;
		canMoveWithNextSiblings?: (card: Card, cardIndex: number, stack: string) => boolean;
		onclick?: (event: MouseEvent) => void;
		onDrop?: (
			cardIndex: number,
			cardsAmount: number,
			fromStackName: string,
			toStackName: string,
		) => boolean;
	}

	let {
		class: classFromProps = '',
		cardsClass,
		aligned = false,
		horizontal = false,
		name,
		cards,
		cardsToRender = -1,
		onclick,
		onCardClick = () => {},
		canMove = () => false,
		canMoveWithNextSiblings = () => true,
		onDrop = () => false,
	}: Props = $props();

	let element = $state<HTMLDivElement>();
	let stack: MovableElementsStack<string>;
	let observer: MutationObserver;

	let effectiveCards = $derived(
		cards.slice(
			-(cardsToRender < 0 ? (aligned ? 2 : cards.length) : Math.max(2, cardsToRender)),
		),
	);

	let stackedHiddenCards = $derived(
		horizontal
			? cards.slice(-(effectiveCards.length - 1) * 2, -(effectiveCards.length - 1))
			: [],
	);

	const getCardsIndex = (effectiveIndex: number) => {
		return effectiveIndex + cards.length - effectiveCards.length;
	};

	async function createNewStack() {
		stack?.dispose();

		if (element) {
			stack = new MovableElementsStack<string>(name, element, {
				getChildNodes: (element) => {
					const container = element.querySelector('[data-elements-container]');

					return container?.children;
				},
				canMove: (index) => {
					if (aligned) {
						return canMove(cards[cards.length - 1], cards.length - 1, name);
					}

					return canMove(effectiveCards[index], getCardsIndex(index), name);
				},
				canMoveWithNextSiblings: (index) => {
					if (aligned || horizontal) {
						return false;
					}
					return canMoveWithNextSiblings?.(
						effectiveCards[index],
						getCardsIndex(index),
						name,
					);
				},
				onDrop: (index, siblingsAmount, toStackName) => {
					if (aligned) {
						return onDrop(cards.length - 1, 1, name, toStackName);
					}
					return onDrop(getCardsIndex(index), siblingsAmount + 1, name, toStackName);
				},
			});
		}
	}

	onMount(() => {
		createNewStack();

		observer = new MutationObserver(() => {
			createNewStack();
		});

		observer.observe(element!, {
			childList: true,
			subtree: true,
		});
	});

	onDestroy(() => {
		stack?.dispose();
		observer.disconnect();
	});
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<div class="h-full {classFromProps ?? ''}" {onclick} bind:this={element}>
	<CardSlot class="absolute" />

	<div class="relative">
		<div class="card-stack h-full" class:horizontal class:aligned data-elements-container>
			{#each effectiveCards as card, stackPosition (`${card.suit}-${card.value}`)}
				<GameCard
					class={cardsClass}
					onclick={() => {
						onCardClick?.(
							card,
							cards.length - effectiveCards.length + stackPosition,
							name,
						);
					}}
					{stackPosition}
					{card}
				/>
			{/each}
		</div>
		{#each stackedHiddenCards as card, stackPosition (`${card.suit}-${card.value}`)}
			<GameCard
				class={twMerge('absolute inset-0 -z-10 opacity-0', cardsClass)}
				{stackPosition}
				{card}
			/>
		{/each}
	</div>
</div>

<style>
	div {
		position: relative;
	}

	.card-stack > :global(*) {
		position: absolute;
		left: 0;
		top: calc(var(--stack-position) * 44%);
	}

	.card-stack.horizontal > :global(*) {
		top: 0;
		left: calc(var(--stack-position) * 46%);
	}

	@media screen and (min-width: 768px) {
		.card-stack > :global(*) {
			top: calc(var(--stack-position) * 22%);
		}

		.card-stack.horizontal > :global(*) {
			top: 0;
			left: calc(var(--stack-position) * 23%);
		}
	}

	.card-stack.aligned > :global(*) {
		top: 0;
	}

	.card-stack.aligned > :global(*):not(:global(:first-of-type)):not(:global(:last-of-type)) {
		box-shadow: none;
		/* Remove drop-shadow filter */
		filter: none;
	}

	.card-stack > :global(*):first-of-type {
		position: relative;
	}
</style>
