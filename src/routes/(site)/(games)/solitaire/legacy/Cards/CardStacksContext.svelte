<script lang="ts">
	import { setContext, type Snippet } from 'svelte';
	import { cardStacksKey } from './cardStacksKey';
	import { heroTranslate } from '$lib/functions/heroTranslate';

	interface Props {
		children?: Snippet;
	}

	let { children }: Props = $props();

	const duration = 300;

	const [send, receive] = heroTranslate({
		duration,
	});

	setContext(cardStacksKey, {
		send,
		receive,
	});
</script>

{@render children?.()}
