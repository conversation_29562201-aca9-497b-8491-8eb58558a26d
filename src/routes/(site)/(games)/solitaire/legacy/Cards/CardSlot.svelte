<script lang="ts">
	interface Props {
		alt?: string;
		class?: string;
	}

	let { alt = '', class: classFromProps = '' }: Props = $props();
</script>

<picture>
	<source
		media="(max-width: 767px)"
		srcset="https://static.lofiandgames.com/images/cards/mobile/card-slot.svg"
	/>
	<source
		media="(min-width: 768px)"
		srcset="https://static.lofiandgames.com/images/cards/desktop/card-slot.svg"
	/>
	<img
		draggable="false"
		class="aspect-39/53 h-auto w-full select-none {classFromProps} -z-10"
		src="https://static.lofiandgames.com/images/cards/mobile/card-slot.svg"
		{alt}
	/>
</picture>
