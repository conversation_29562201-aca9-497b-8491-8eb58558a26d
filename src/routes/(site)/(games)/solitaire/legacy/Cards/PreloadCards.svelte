<script>
	import { getStandardDeck } from '$lib/functions/getStandardDeck';

	const cards = getStandardDeck();
</script>

{#each cards as card}
	<picture>
		<source
			media="(max-width: 767px)"
			srcset="https://static.lofiandgames.com/images/cards/mobile/{card.value}-{card.suit}.svg"
		/>
		<source
			media="(min-width: 768px)"
			srcset="https://static.lofiandgames.com/images/cards/desktop/{card.value}-{card.suit}.svg"
		/>
		<img
			width="0"
			height="0"
			src="https://static.lofiandgames.com/images/cards/mobile/{card.value}-{card.suit}.svg"
			alt="preload card"
		/>
	</picture>
{/each}

<picture>
	<source
		media="(max-width: 767px)"
		srcset="https://static.lofiandgames.com/images/cards/mobile/card-back.svg"
	/>
	<source
		media="(min-width: 768px)"
		srcset="https://static.lofiandgames.com/images/cards/desktop/card-back.svg"
	/>
	<img
		width="0"
		height="0"
		src="https://static.lofiandgames.com/images/cards/mobile/card-back.svg"
		alt="preload card"
	/>
</picture>
<picture>
	<source
		media="(max-width: 767px)"
		srcset="https://static.lofiandgames.com/images/cards/mobile/card-slot.svg"
	/>
	<source
		media="(min-width: 768px)"
		srcset="https://static.lofiandgames.com/images/cards/desktop/card-slot.svg"
	/>
	<img
		width="0"
		height="0"
		src="https://static.lofiandgames.com/images/cards/mobile/card-slot.svg"
		alt="preload card"
	/>
</picture>

<style>
	img {
		position: absolute;
		top: 99999px;
		left: 99999px;
	}
</style>
