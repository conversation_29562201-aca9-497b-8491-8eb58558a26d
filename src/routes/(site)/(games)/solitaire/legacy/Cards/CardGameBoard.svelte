<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		controls?: Snippet;
		children?: Snippet;
	}

	let { controls, children }: Props = $props();
</script>

<section
	class="flex min-h-screen-no-navbar w-full touch-manipulation select-none flex-col bg-game-solitaire-field px-4 md:px-8"
>
	<div class="limit-width-by-screen-height mx-auto w-full max-w-4xl py-4 xl:max-w-5xl">
		{@render controls?.()}
	</div>

	<div
		class="limit-width-by-screen-height z-0 mx-auto flex size-full max-w-4xl grow flex-col xl:max-w-5xl"
	>
		{@render children?.()}
	</div>
</section>

<style>
	@media screen and (max-height: 768px) {
		.limit-width-by-screen-height {
			max-width: 768px;
		}
	}
</style>
