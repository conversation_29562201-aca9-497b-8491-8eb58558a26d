<script lang="ts">
	import CardStack from './Cards/CardStack.svelte';
	import CardStacksContext from './Cards/CardStacksContext.svelte';
	import type { Card, CardFace } from '$lib/models/card-game';
	import { SolitaireGameLegacy, type CardHint, type SolitaireStack } from './SolitaireGameLegacy';
	import { onMount, onDestroy } from 'svelte';
	import { wait } from '$lib/functions/wait';
	import UndoIcon from '$lib/components/Icons/UndoIcon.svelte';
	import RedoIcon from '$lib/components/Icons/RedoIcon.svelte';
	import GameCard from './Cards/GameCard.svelte';
	import { fade, fly } from 'svelte/transition';
	import { Undoable } from '$lib/util/Undoable/Undoable.svelte';
	import { UndoableKeyboardListener } from '$lib/util/Undoable/UndoableKeyboardListener';
	import { browser } from '$app/environment';
	import PreloadCards from './Cards/PreloadCards.svelte';
	import PlayNewGameModal from '$lib/components/PlayNewGameModal.svelte';
	import ReplayCurrentGameModal from '$lib/components/ReplayCurrentGameModal.svelte';
	import HintAlert from './HintAlert.svelte';
	import CardGameBoard from './Cards/CardGameBoard.svelte';
	import { solitaireSoundResources } from './solitaireSoundResources';
	import SolitaireSettingsButton from './SolitaireSettingsButton.svelte';
	import RefreshIcon from '$lib/components/Icons/RefreshIcon.svelte';
	import { isMacLike } from '$lib/functions/isMacLike';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';

	const playCardWaitTimeAfterDrawInMs = 500;
	const hintWaitTimeInMs = 1000;
	let timerStarted = $state(false);
	let _shouldPreventUserInteractionId: symbol | null = null;
	let _shouldPreventDrawCardsId: symbol | null = null;

	function shouldPreventUserInteraction() {
		return (
			_shouldPreventDrawCardsId !== null ||
			_shouldPreventUserInteractionId !== null ||
			(timerStarted ? timer.paused : false)
		);
	}

	let isNewGameDropdownOpen = $state(false);
	let userSelectedCardsToDraw: 1 | 3 = $state(1);
	let gameHistory = $state(new Undoable(new SolitaireGameLegacy()));
	let game = $state(new SolitaireGameLegacy());
	let gamePlayId: number | null = $state(null);
	let undoListener: UndoableKeyboardListener = new UndoableKeyboardListener(undo, redo);
	let isFinishingGame = $state(false);
	let canUndo = $state(false);
	let canRedo = $state(false);
	let moves = $state(0);
	let totalMoves = $state(0);
	let hintsPlayed = $state(0);
	let shouldShowWinModal = $state(false);
	let confettiPartyIntervalId = -1;
	let lastDrawCardsTime = -1;
	let lastHintTime = -1;
	let gameHint: SolitaireGameLegacy | null = $state(null);
	let shouldShowHintAlert = $state(false);
	let cardHint: CardHint | null = $state(null);
	let isMac = $state(false);
	let versionDropdownOpen = $state(false);
	let context = new GameContext({
		gameName: 'Solitaire (Legacy)',
		gameKey: 'solitaire-legacy',
		settings: {
			defaultSettings: {
				drawCards: 1 as 1 | 3,
			},
		},
		sounds: {
			resources: solitaireSoundResources,
		},
		variants: {
			map: {
				drawCards: {
					allValues: [1, 3],
					format: (drawCards: 1 | 3) => `Draw ${drawCards}`,
				},
			},
			fromGame() {
				return {
					drawCards: (game.amountOfCardsToDraw ?? 1) as 1 | 3,
				};
			},
			getStatsVariant(variants) {
				return `draw-${variants.drawCards}`;
			},
			getLeaderboardVariant(variants) {
				return `draw-${variants.drawCards}`;
			},
		},
		stats({ props }) {
			return {
				stats: new Stats({
					...props,
					liveStats: {
						moves: {
							name: 'Moves',
							unit: 'plain',
							value: () => moves,
							metrics: {
								total: {
									key: 'totalMoves',
									name: 'Total Moves',
								},
								average: {
									key: 'averageMoves',
									name: 'Average Moves',
								},
								min: {
									key: 'fewestMoves',
									name: 'Fewest Moves',
									useAsBest: true,
								},
								max: {
									key: 'maxMoves',
									name: 'Max Moves',
								},
							},
						},
						totalMoves: {
							name: 'Total Moves',
							description: 'Includes undos, redos, and automatic finish moves',
							unit: 'plain',
							value: () => totalMoves,
							metrics: {
								total: {
									key: 'totalTotalMoves',
									name: 'Total Total Moves',
								},
								average: {
									key: 'averageTotalMoves',
									name: 'Average Total Moves',
								},
								min: {
									key: 'fewestTotalMoves',
									name: 'Fewest Total Moves',
									useAsBest: true,
								},
								max: {
									key: 'maxTotalMoves',
									name: 'Max Total Moves',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'moves'],
				}),
				canUpdateWithGameLost() {
					return moves > 10 && !game.isWon();
				},
				visibleStats: [
					'bestTime',
					'averageTime',
					'fewestMoves',
					'averageMoves',
					'fewestTotalMoves',
					'averageTotalMoves',
					'wonGames',
					'totalGames',
				],
			};
		},
		leaderboard: ({ props, context }) => {
			return {
				leaderboard: new Leaderboard({
					...props,
					firstAvailableDate: new Date('2025/08/29'),
					hasMoves: true,
					order: 'lower-first',
				}),
				sendScoreOn: ['won'],
				getScore: () => {
					const score =
						totalMoves + Math.floor(context.timer.time / 1000) + hintsPlayed * 10;

					return {
						score,
						moves,
					};
				},
			};
		},
		onWillCreateGame() {
			hintsPlayed = 0;
		},
	});

	let sounds = $derived(context.sounds);
	let timer = $derived(context.timer);

	// State replica
	let deck = $state<Card[]>([]);
	let nextCards = $state<Card[]>([]);

	$effect(() => {
		if (shouldShowWinModal) {
			const throwConfetti = () => {
				context?.addConfetti();
			};
			throwConfetti();
			confettiPartyIntervalId = setInterval(throwConfetti, 3000) as unknown as number;
		} else {
			clearInterval(confettiPartyIntervalId);
			confettiPartyIntervalId = -1;
		}
	});

	function canHandleCardEvents() {
		if (shouldPreventUserInteraction()) {
			return false;
		}

		return performance.now() - lastDrawCardsTime > playCardWaitTimeAfterDrawInMs;
	}

	async function finishGame() {
		hideHint();
		timer.stop();
		isFinishingGame = true;
		moves = gameHistory.timeline.past.length;

		sounds.gameWin?.play();

		await context.addConfetti();

		while (isFinishingGame && !game.isWon() && game.canFinishAutomatically()) {
			gameHistory.add(game.playNextCardAutomatically());
			renderGame();
			totalMoves += 1;

			if (!sounds.deckShuffle?.playing()) {
				sounds.deckShuffle?.play();
			}

			await wait(80);
		}

		context.handleGameOver('won', { handleConfetti: false });

		if (isFinishingGame) {
			sounds.applause?.play();

			isFinishingGame = false;
			shouldShowWinModal = true;
		}
	}

	async function undo() {
		if (!canHandleCardEvents()) {
			return;
		}

		const currentId = Symbol();
		_shouldPreventUserInteractionId = currentId;

		await hideHint();

		if (game?.started && canUndo) {
			gameHistory.undo();
			renderGame();
			sounds.swoosh?.play();
			totalMoves += 1;

			await wait(300);
		}

		if (_shouldPreventUserInteractionId === currentId) {
			_shouldPreventUserInteractionId = null;
		}
	}

	async function redo() {
		if (!canHandleCardEvents()) {
			return;
		}

		const currentId = Symbol();
		_shouldPreventUserInteractionId = currentId;

		await hideHint();

		if (game?.started && canRedo) {
			gameHistory.redo();
			renderGame();
			sounds.swoosh?.play();
			totalMoves += 1;

			await wait(300);
		}

		if (_shouldPreventUserInteractionId === currentId) {
			_shouldPreventUserInteractionId = null;
		}
	}

	const renderGame = () => {
		game = gameHint ?? gameHistory.state;
		deck = game.deck;
		nextCards = game.nextCards;
		canUndo = !game.isWon() && !isFinishingGame && gameHistory.canUndo();
		canRedo = !game.isWon() && !isFinishingGame && gameHistory.canRedo();

		if (!game.isWon() && !isFinishingGame) {
			moves = gameHistory.timeline.past.length;
		}
	};

	const onDrop = (cardIndex: number, _cardsAmount: number, from: string, to: string): boolean => {
		if (!canHandleCardEvents()) {
			return false;
		}

		if (game.isWon()) {
			return true;
		}

		hideHint();

		if (game.canMove(cardIndex, from as SolitaireStack, to as SolitaireStack)) {
			const newGame = game.move(cardIndex, from as SolitaireStack, to as SolitaireStack);
			gameHistory.add(newGame);
			totalMoves += 1;

			initTimerIfNeeded();
			renderGame();

			sounds.cardPlay?.play();

			if (newGame.isWon() || newGame.canFinishAutomatically()) {
				finishGame();
			}

			return true;
		}

		return false;
	};

	const canMove = (_: Card, index: number, stack: string): boolean => {
		if (!canHandleCardEvents()) {
			return false;
		}

		return game.canMoveFrom(index, stack as SolitaireStack);
	};

	const initTimerIfNeeded = () => {
		if (!timer.running) {
			initTimer();
		}
	};

	const drawCards = async (): Promise<void> => {
		if (_shouldPreventDrawCardsId !== null) {
			return;
		}

		const currentId = Symbol();
		_shouldPreventDrawCardsId = currentId;

		await hideHint();

		if (game.canDrawCards()) {
			gameHistory.add(game.drawCards());
			totalMoves += 1;
			initTimerIfNeeded();
			renderGame();
			sounds.cardFlip?.play();
		} else if (game.canPutNextCardsBackOnDeck()) {
			gameHistory.add(game.putNextCardsBackOnDeck());
			totalMoves += 1;
			renderGame();
			sounds.deckBack?.play();
		}

		if (currentId === _shouldPreventDrawCardsId) {
			_shouldPreventDrawCardsId = null;
		}
	};

	const revealLastDeckCardAndDraw = async (draw = drawCards, recordTime = true) => {
		if (!canHandleCardEvents()) {
			return;
		}

		const currentPreventInteractionId = Symbol();
		const currentShouldPreventDrawCardsId = Symbol();
		_shouldPreventUserInteractionId = currentPreventInteractionId;
		_shouldPreventDrawCardsId = currentShouldPreventDrawCardsId;

		if (recordTime) {
			lastDrawCardsTime = performance.now();
		}

		if (deck.length > 0) {
			const newDeck = [...deck];

			const cardsToDraw = newDeck.slice(-game.amountOfCardsToDraw);

			cardsToDraw.forEach((card, index) => {
				// Reverse order of cards
				newDeck[newDeck.length - index - 1] = card;
			});

			if (cardsToDraw.length > 1) {
				deck = newDeck;

				await wait(200);
			}

			cardsToDraw.forEach((card, index) => {
				// Reverse order of cards
				newDeck[newDeck.length - index - 1] = {
					...card,
					face: 'up' as CardFace,
				};
			});

			deck = newDeck;

			await wait(300);

			// Draw cards
			_shouldPreventDrawCardsId = null;
			draw();
		} else if (deck.length === 0 && nextCards.length > 0) {
			// Hide cards
			nextCards = nextCards.map(
				(card) =>
					({
						...card,
						face: 'down',
					}) as Card,
			);

			await wait(300);

			// Reverse cards
			nextCards = nextCards.reverse();

			await wait(200);

			// Draw cards
			_shouldPreventDrawCardsId = null;
			draw();
		} else {
			_shouldPreventDrawCardsId = null;
			draw();
		}

		if (_shouldPreventUserInteractionId === currentPreventInteractionId) {
			_shouldPreventUserInteractionId = null;
		}

		if (_shouldPreventDrawCardsId === currentShouldPreventDrawCardsId) {
			_shouldPreventDrawCardsId = null;
		}
	};

	const onCardClick = async (_card: Card, cardIndex: number, stack: string) => {
		if (!canHandleCardEvents()) {
			return;
		}

		if (game.isWon()) {
			return;
		}

		const currentPreventUserInteractionId = Symbol();
		const currentShouldPreventDrawCardsId = Symbol();
		_shouldPreventUserInteractionId = currentPreventUserInteractionId;
		_shouldPreventDrawCardsId = currentShouldPreventDrawCardsId;

		await hideHint();

		const newGame = game.moveCardsOnStackToABetterPosition(cardIndex, stack as SolitaireStack);

		if (newGame !== game) {
			gameHistory.add(newGame);
			totalMoves += 1;
			initTimerIfNeeded();
			renderGame();

			sounds.cardPlay?.play();

			if (newGame.isWon() || newGame.canFinishAutomatically()) {
				finishGame();
			}

			await wait(200);
		}

		if (_shouldPreventUserInteractionId === currentPreventUserInteractionId) {
			_shouldPreventUserInteractionId = null;
		}

		if (_shouldPreventDrawCardsId === currentShouldPreventDrawCardsId) {
			_shouldPreventDrawCardsId = null;
		}
	};

	const initTimer = () => {
		timerStarted = true;
		timer.start();
	};

	const reset = () => {
		totalMoves = 0;
		gamePlayId = null;
		isNewGameDropdownOpen = false;
		shouldShowWinModal = false;
		isFinishingGame = false;
		timerStarted = false;
		timer.reset();
		renderGame();
	};

	const createNewGame = async (
		animate: boolean,
		amountOfCardsToDraw: 1 | 3 = game?.amountOfCardsToDraw,
		debug = false,
	) => {
		context.settingsManager.settings.drawCards = amountOfCardsToDraw;
		// Must call handleWillCreateGame instead of createGame, otherwise the canUpdateWithGameLost will not work correctly
		context.handleWillCreateGame();
		game = new SolitaireGameLegacy(game.amountOfCardsToDraw);
		gameHistory = new Undoable(game);

		reset();

		sounds.applause?.stop();

		const newGame = new SolitaireGameLegacy(amountOfCardsToDraw);

		if (debug) {
			newGame.debugDistributeCardsNextToWin();
		} else {
			newGame.distributeCards();
		}

		SolitaireGameLegacy.drawAmount = amountOfCardsToDraw;
		// if (Math.random() > 0.5) {
		// newGame.debugDistributeCardsNextToWin();
		// } else {
		// 	newGame.distributeCards();
		// }

		sounds.deckShuffle?.stop();

		if (animate) {
			sounds.deckShuffle?.play();

			await wait(800);
		}

		gamePlayId = Math.random() * 1e9;
		gameHistory = new Undoable(newGame);
		game = newGame;

		context.handleGameCreated();
	};

	const replayCurrentGame = async () => {
		gameHistory.reset();
		sounds.deckShuffle?.stop();
		reset();

		sounds.deckShuffle?.play();

		await wait(800);

		gamePlayId = Math.random() * 1e9;
		renderGame();
	};

	const showHint = async () => {
		if (shouldPreventUserInteraction()) {
			return;
		}

		if (cardHint || gameHint || isFinishingGame || !gameHistory.state?.started) {
			return;
		}

		if (performance.now() - lastHintTime < hintWaitTimeInMs) {
			return;
		}

		lastHintTime = performance.now();

		cardHint = gameHistory.state.getCardHint();

		if (cardHint) {
			hintsPlayed += 1;
			if (cardHint.to) {
				gameHint = gameHistory.state.move(cardHint.cardIndex, cardHint.from, cardHint.to);
			} else {
				if (cardHint.from === 'deck') {
					await revealLastDeckCardAndDraw(async () => {
						gameHint = gameHistory.state.drawCards();
					}, false);

					// Prevent Svelte from breaking
					if (cardHint?.from === 'deck' && game.amountOfCardsToDraw === 3) {
						_shouldPreventUserInteractionId = Symbol();
					}
				} else if (cardHint.from === 'next-cards') {
					await revealLastDeckCardAndDraw(async () => {
						gameHint = gameHistory.state.putNextCardsBackOnDeck();
					}, false);
				}
			}
			await wait(800);
			_shouldPreventUserInteractionId = null;

			if (cardHint) {
				cardHint = null;
			}

			if (gameHint) {
				await hideHint();
			}
		} else {
			shouldShowHintAlert = true;
			setTimeout(() => {
				shouldShowHintAlert = false;
			}, 2000);
		}
	};

	const hideHint = async () => {
		if (gameHint) {
			gameHint = null;
			cardHint = null;
			game = gameHistory.state;
			await wait(300);
		}
	};

	const cardSpacer = {
		face: 'down',
		suit: 'club',
		value: 'card-spacer' as unknown,
	} as Card;

	async function handleDeckClick() {
		await hideHint();
		await revealLastDeckCardAndDraw();
	}

	function handleKeyDown(event: KeyboardEvent) {
		if (shouldPreventUserInteraction()) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (key === 'h') {
			showHint();
		}

		if (key === 'd') {
			handleDeckClick();
		}

		if (key === 'n' && event.shiftKey) {
			createNewGame(true);
		}

		if (event.code === 'Digit0' && event.shiftKey && import.meta.env.DEV) {
			createNewGame(true, 1, true);
		}

		if (key === 'c' && event.shiftKey) {
			replayCurrentGame();
		}

		if (event.code === 'Digit1' && event.shiftKey) {
			userSelectedCardsToDraw = 1;
			createNewGame(true, 1);
		}

		if (event.code === 'Digit3' && event.shiftKey) {
			userSelectedCardsToDraw = 3;
			createNewGame(true, 3);
		}
	}

	onMount(async () => {
		context.load();
		undoListener.listen();
		isMac = isMacLike();

		try {
			SolitaireGameLegacy.loadSettings();
		} catch (_) {
			// Ignore
		}

		createNewGame(false, SolitaireGameLegacy.drawAmount);
	});

	onDestroy(() => {
		if (browser) {
			context.dispose();
			undoListener?.dispose();
			clearInterval(confettiPartyIntervalId);
			confettiPartyIntervalId = -1;
		}
	});
</script>

<svelte:window onkeydown={handleKeyDown} />

<PreloadCards />

<PlayNewGameModal id="new-game-modal" onPlayNewGame={() => createNewGame(true)} />
<PlayNewGameModal
	id="new-game-draw-cards-modal"
	onPlayNewGame={() => createNewGame(true, userSelectedCardsToDraw)}
/>
<ReplayCurrentGameModal id="replay-game-modal" onReplay={replayCurrentGame} />

<GameLayout
	adsProps={{ desktopVariant: 'default' }}
	mobileOrientation="all"
	noPadding
	class="bg-game-solitaire-field"
>
	{#snippet Island()}
		<GameIsland {context} gameOverIslandDelay={1500} onNewGame={() => createNewGame(true)} />
	{/snippet}

	<CardGameBoard>
		{#snippet controls()}
			<div class="flex items-center justify-between gap-2">
				<div class="flex items-center gap-2">
					<SolitaireSettingsButton
						disabled={!game?.started || game?.isWon()}
						onChange={renderGame}
					/>

					<Dropdown bind:open={versionDropdownOpen}>
						<DropdownButton class="btn-neutral btn-xs md:btn-sm">V1</DropdownButton>

						<DropdownContent menu class="w-44 lg:w-44">
							<a href="/solitaire">
								<DropdownItem>
									<button onclick={() => (versionDropdownOpen = false)}
										>Version 2 (New)</button
									>
								</DropdownItem>
							</a>

							<DropdownItem>
								<button
									class="menu-active"
									onclick={() => (versionDropdownOpen = false)}
									>Version 1 (Legacy)</button
								>
							</DropdownItem>
						</DropdownContent>
					</Dropdown>

					<Dropdown bind:open={isNewGameDropdownOpen}>
						<DropdownButton
							class="btn-neutral btn btn-xs flex flex-nowrap gap-0 md:btn-sm"
						>
							New<span class="hidden md:inline">&nbsp;game</span>
						</DropdownButton>

						<DropdownContent menu class="w-52 lg:w-64">
							<DropdownItem>
								<label
									class="flex flex-row justify-between items-center"
									for="replay-game-modal"
								>
									Replay Current Game <span class="hidden lg:block"
										>Shift + C</span
									>
								</label>
							</DropdownItem>
							<DropdownItem>
								<label
									class="flex flex-row justify-between items-center"
									for="new-game-modal"
								>
									Play a New Game <span class="hidden lg:block">Shift + N</span>
								</label>
							</DropdownItem>

							<div class="divider my-1"></div>

							<a href="/solitaire">
								<DropdownItem>
									<button class="w-full justify-between">Play New Version</button>
								</DropdownItem>
							</a>
						</DropdownContent>
					</Dropdown>

					<div class="join">
						<div
							class="tooltip before:hidden after:hidden lg:before:block lg:after:block"
							data-tip="Draw 1 mode (Shift + 1)"
						>
							<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
							<!-- svelte-ignore a11y_click_events_have_key_events -->
							<label
								class="btn btn-xs join-item md:btn-sm"
								class:btn-active={game.amountOfCardsToDraw === 1}
								class:btn-primary={game.amountOfCardsToDraw === 1}
								class:btn-neutral={game.amountOfCardsToDraw !== 1}
								for="new-game-draw-cards-modal"
								onclick={() => (userSelectedCardsToDraw = 1)}
							>
								1
							</label>
						</div>
						<div
							class="tooltip before:hidden after:hidden lg:before:block lg:after:block"
							data-tip="Draw 3 mode (Shift + 3)"
						>
							<!-- svelte-ignore a11y_click_events_have_key_events -->
							<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
							<label
								class="btn btn-xs join-item md:btn-sm"
								class:btn-active={game.amountOfCardsToDraw === 3}
								class:btn-primary={game.amountOfCardsToDraw === 3}
								class:btn-neutral={game.amountOfCardsToDraw !== 3}
								for="new-game-draw-cards-modal"
								onclick={() => (userSelectedCardsToDraw = 3)}
							>
								3
							</label>
						</div>
					</div>
				</div>

				<div class="flex gap-2">
					<button
						disabled={isFinishingGame || !game?.started || game?.isWon()}
						onclick={showHint}
						class="btn-neutral btn btn-xs flex flex-nowrap md:btn-sm"
					>
						<span><u class="no-underline lg:underline">H</u>int</span>
					</button>
					<div
						class="tooltip before:hidden after:hidden lg:before:block lg:after:block"
						data-tip="Undo ({isMac ? '⌘' : 'Ctrl'} + Z)"
					>
						<button
							disabled={!canUndo || !game?.started}
							aria-label="undo"
							onclick={undo}
							class="btn-neutral btn btn-xs flex md:btn-sm"
						>
							<UndoIcon />
						</button>
					</div>
					<div
						class="tooltip before:hidden after:hidden lg:before:block lg:after:block tooltip-left xl:tooltip-top"
						data-tip="Redo ({isMac ? '⌘' : 'Ctrl'} + Shift + Z)"
					>
						<button
							disabled={!canRedo || !game?.started}
							aria-label="redo"
							onclick={redo}
							class="btn-neutral btn btn-xs flex md:btn-sm"
						>
							<RedoIcon />
						</button>
					</div>
				</div>
			</div>
		{/snippet}

		{#if gamePlayId !== null}
			{#key gamePlayId}
				<CardStacksContext>
					<div
						class="grid w-full grow grid-cols-7 gap-2 gap-y-4 xl:gap-4"
						transition:fly|global={{ duration: 300, y: 50 }}
					>
						<!-- Deck -->
						<div
							class="relative rounded-md outline outline-yellow-300 transition-all"
							class:outline-0={cardHint?.from !== 'deck' && cardHint?.to !== 'deck'}
							class:outline-4={cardHint?.from === 'deck' || cardHint?.to === 'deck'}
						>
							<div
								class="absolute left-1/2 top-1/2 size-full -translate-x-1/2 -translate-y-1/2"
							>
								<div
									class="flex size-full flex-col items-center justify-center gap-2 p-2"
								>
									<RefreshIcon class="size-8 text-white sm:h-12 sm:w-12" />
									<span
										class="hidden text-center text-xs text-white md:block md:text-base"
									>
										Restore
									</span>
								</div>

								{#if deck.length !== 0}
									<div
										class="absolute inset-0"
										in:fade={{ duration: 100, delay: 500 }}
									>
										<GameCard card={cardSpacer} />
									</div>
								{/if}
							</div>

							<CardStack
								class="cursor-pointer"
								aligned
								name="deck"
								cards={deck}
								cardsToRender={game?.amountOfCardsToDraw}
								onclick={handleDeckClick}
								cardsClass="z-30"
							/>
						</div>

						<!-- Next cards -->
						<div class="relative">
							<div
								class="absolute left-1/2 top-1/2 hidden size-full -translate-x-1/2 -translate-y-1/2 md:block"
							>
								<div class="flex-center size-full p-4">
									<span
										class="hidden text-center text-xs text-white md:block md:text-base"
									>
										Press <strong>D</strong> to <u>d</u>raw cards
									</span>
								</div>
							</div>

							<CardStack
								aligned={game?.amountOfCardsToDraw === 1}
								horizontal={game?.amountOfCardsToDraw === 3}
								cardsToRender={game?.amountOfCardsToDraw}
								name="next-cards"
								{canMove}
								{onDrop}
								{onCardClick}
								cards={nextCards}
							/>
						</div>

						<!-- Spacer -->
						<GameCard class="invisible opacity-0" card={cardSpacer} />

						<!-- Slots -->
						<CardStack
							aligned
							name="slot1"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.slot1}
							cardsToRender={99}
						/>
						<CardStack
							aligned
							name="slot2"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.slot2}
							cardsToRender={99}
						/>
						<CardStack
							aligned
							name="slot3"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.slot3}
							cardsToRender={99}
						/>
						<CardStack
							aligned
							name="slot4"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.slot4}
							cardsToRender={99}
						/>

						<!-- Card Stacks -->
						<CardStack
							name="stack1"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.stack1}
						/>
						<CardStack
							name="stack2"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.stack2}
						/>
						<CardStack
							name="stack3"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.stack3}
						/>
						<CardStack
							name="stack4"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.stack4}
						/>
						<CardStack
							name="stack5"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.stack5}
						/>
						<CardStack
							name="stack6"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.stack6}
						/>
						<CardStack
							name="stack7"
							{canMove}
							{onDrop}
							{onCardClick}
							cards={game.stack7}
						/>
					</div>
				</CardStacksContext>
			{/key}
		{/if}

		{#if shouldShowHintAlert}
			<HintAlert />
		{/if}
	</CardGameBoard>
</GameLayout>

<style>
	.grid {
		grid-template-rows: min-content 1fr;
	}
</style>
