import { getStandardDeck } from '$lib/functions/getStandardDeck';
import { shuffle } from '$lib/functions/shuffle';
import { suitToColor, type Card, type CardValue } from '$lib/models/card-game';

export type SolitaireSpeed = 'normal' | 'fast';

export type SolitaireSettings = {
	drawAmount: 1 | 3;
	alignDeckOnRight: boolean;
	winnableGamesOnly: boolean;
	enableAnimations: boolean;
	animationsSpeed: SolitaireSpeed;
	enableAutoPlayHint: boolean;
	autoPlayHintFromDeck: boolean;
	autoPlayHintFromWasteToDeck: boolean;
	autoPlayHintFromWasteToFoundation: boolean;
	autoPlayHintFromWasteToTableau: boolean;
	autoPlayHintFromTableauToFoundation: boolean;
	autoPlayHintFromTableauToTableau: boolean;
	shouldFinishAutomatically: boolean;
	alignWasteCards: boolean;
	bouncyCards: boolean;
};

export type SolitaireStack =
	| 'deck'
	| 'next-cards'
	| 'slot1'
	| 'slot2'
	| 'slot3'
	| 'slot4'
	| 'stack1'
	| 'stack2'
	| 'stack3'
	| 'stack4'
	| 'stack5'
	| 'stack6'
	| 'stack7';

export type CardHint = {
	card: Card;
	cardIndex: number;
	from: SolitaireStack;
	to?: SolitaireStack;
};

export interface GameStateChange {
	game: SolitaireGame;
	movingCards: Card[];
	changedStacks: SolitaireStack[];
}

export class SolitaireGame {
	id: number = Math.floor(Math.random() * 1e9);
	deck: Card[] = [];
	nextCards: Card[] = [];
	slot1: Card[] = [];
	slot2: Card[] = [];
	slot3: Card[] = [];
	slot4: Card[] = [];
	stack1: Card[] = [];
	stack2: Card[] = [];
	stack3: Card[] = [];
	stack4: Card[] = [];
	stack5: Card[] = [];
	stack6: Card[] = [];
	stack7: Card[] = [];
	amountOfCardsToDraw: number;
	started = false;
	isDebug = false;
	shouldFinishAutomatically = true;

	constructor(amountOfCardsToDraw = 1, shouldFinishAutomatically = true) {
		this.amountOfCardsToDraw = amountOfCardsToDraw;
		this.shouldFinishAutomatically = shouldFinishAutomatically;
	}

	get stacks() {
		return [
			this.stack1,
			this.stack2,
			this.stack3,
			this.stack4,
			this.stack5,
			this.stack6,
			this.stack7,
		];
	}

	get slots() {
		return [this.slot1, this.slot2, this.slot3, this.slot4];
	}

	/**
	 * Checks if all cards are valid and throws an error if not
	 */
	validate() {
		const cardsSet = new Set<string>();

		const allCards = [
			...this.deck,
			...this.nextCards,
			...this.slots.flatMap((_) => _),
			...this.stacks.flatMap((_) => _),
		];

		if (allCards.length !== 52) {
			throw new Error(`Amount of cards is ${allCards.length}, expected 52`);
		}

		allCards.forEach((card) => {
			const key = `${card.suit}-${card.value}`;

			if (cardsSet.has(key)) {
				throw new Error(`Card ${card.suit}-${card.value} is repeated`);
			}

			cardsSet.add(key);
		});
	}

	debugDistributeCardsNextToWin() {
		this.isDebug = true;
		this.stack1 = SolitaireGame.stackCardValueSequence.flatMap((value, index) => {
			return {
				suit: index % 2 === 0 ? 'heart' : 'club',
				value,
				face: 'up',
			};
		});
		this.stack2 = SolitaireGame.stackCardValueSequence.flatMap((value, index) => {
			return {
				suit: index % 2 === 0 ? 'club' : 'heart',
				value,
				face: 'up',
			};
		});
		this.stack3 = SolitaireGame.stackCardValueSequence.flatMap((value, index) => {
			return {
				suit: index % 2 === 0 ? 'diamond' : 'spade',
				value,
				face: 'up',
			};
		});
		this.stack4 = SolitaireGame.stackCardValueSequence.flatMap((value, index) => {
			return {
				suit: index % 2 === 0 ? 'spade' : 'diamond',
				value,
				face: 'up',
			};
		});
		this.deck = [{ ...this.stack4.pop()!, face: 'down' }];
		this.nextCards = [this.stack4.pop()!];
		this.stack5 = [{ ...this.stack4.pop()!, face: 'down' }, this.stack4.pop()!];

		this.started = true;
	}

	// Mutates current instance state
	distributeCards() {
		this.deck = shuffle(getStandardDeck());

		for (let index = 0; index < this.stacks.length; index += 1) {
			const stack = this.stacks[index];

			while (stack.length !== SolitaireGame.amountOfCardsOnEachStackOnStart[index]) {
				stack.push(this.deck.pop()!);

				if (stack.length === SolitaireGame.amountOfCardsOnEachStackOnStart[index]) {
					stack[stack.length - 1] = {
						...stack[stack.length - 1],
						face: 'up',
					};
				}
			}
		}

		this.started = true;
	}

	canDrawCards(): boolean {
		return this.deck.length > 0;
	}

	drawCards(): GameStateChange | null {
		if (this.canDrawCards()) {
			const newGame = this.clone();
			const cardsToAdd = this.deck
				.slice(-this.amountOfCardsToDraw)
				.reverse()
				.map(
					(card) =>
						({
							...card,
							face: 'up',
						}) as Card,
				);

			newGame.nextCards = [...this.nextCards, ...cardsToAdd].map(
				(card) =>
					({
						...card,
						face: 'up',
					}) as Card,
			);
			newGame.deck = [...this.deck].slice(0, -this.amountOfCardsToDraw);

			return {
				game: newGame,
				changedStacks: ['deck', 'next-cards'],
				movingCards: cardsToAdd,
			};
		}

		return null;
	}

	canPutNextCardsBackOnDeck(): boolean {
		return this.deck.length === 0 && this.nextCards.length > 0;
	}

	putNextCardsBackOnDeck(): GameStateChange | null {
		if (this.canPutNextCardsBackOnDeck()) {
			const newGame = this.clone();
			newGame.deck = [...newGame.nextCards].reverse().map((card) => ({
				...card,
				face: 'down',
			}));
			newGame.nextCards = [];

			return {
				game: newGame,
				changedStacks: ['next-cards', 'deck'],
				movingCards: newGame.deck,
			};
		}

		return null;
	}

	canMoveFrom(cardIndex: number, from: SolitaireStack): boolean {
		if (from === 'deck') {
			return false;
		}

		if (from === 'next-cards') {
			return cardIndex === this.nextCards.length - 1;
		}

		const fromStack = this.getStackByName(from);
		const card = fromStack[cardIndex];

		if (!card) {
			return false;
		}

		return card.face === 'up';
	}

	canMove(cardIndex: number, from: SolitaireStack, to: SolitaireStack): boolean {
		if (to === 'deck' || to === 'next-cards') {
			return false;
		}

		if (SolitaireGame.isSlot(to)) {
			return this.canMoveToSlot(cardIndex, from, to);
		}

		if (SolitaireGame.isStack(to)) {
			return this.canMoveToStack(cardIndex, from, to);
		}

		return this.canMoveFrom(cardIndex, from);
	}

	move(cardIndex: number, from: SolitaireStack, to: SolitaireStack): GameStateChange | null {
		if (SolitaireGame.isSlot(to)) {
			return this.moveToSlot(cardIndex, from, to);
		}

		if (SolitaireGame.isStack(to)) {
			return this.moveToStack(cardIndex, from, to);
		}

		return null;
	}

	moveToStack(
		cardIndex: number,
		from: SolitaireStack,
		to: SolitaireStack,
	): GameStateChange | null {
		if (!this.canMoveToStack(cardIndex, from, to)) {
			return null;
		}

		const newGame = this.clone();
		const fromStack = newGame.getStackByName(from);
		const toStack = newGame.getStackByName(to);
		const cardsToMove = fromStack.slice(cardIndex);

		toStack.push(...cardsToMove);
		fromStack.splice(cardIndex, cardsToMove.length);

		SolitaireGame.revealLastCardOnStack(fromStack);

		return {
			game: newGame,
			changedStacks: [from, to],
			movingCards: cardsToMove,
		};
	}

	canMoveToStack(cardIndex: number, from: SolitaireStack, to: SolitaireStack): boolean {
		if (!this.canMoveFrom(cardIndex, from)) {
			return false;
		}

		if (!SolitaireGame.isStack(to)) {
			return false;
		}

		const fromStack = this.getStackByName(from);
		const toStack = this.getStackByName(to);
		const cardsToMove = fromStack.slice(cardIndex);
		const firstCardToMove = cardsToMove[0];
		const lastCardOnTargetStack = toStack[toStack.length - 1];

		// Check if stack is empty
		if (!lastCardOnTargetStack) {
			return cardsToMove[0].value === SolitaireGame.stackCardValueSequence[0];
		}

		// Check color of suits
		if (suitToColor[firstCardToMove.suit] === suitToColor[lastCardOnTargetStack.suit]) {
			return false;
		}

		// Check if card is the next on the sequence
		return SolitaireGame.isNextOnSequence(
			firstCardToMove,
			lastCardOnTargetStack,
			SolitaireGame.stackCardValueSequence,
		);
	}

	moveToSlot(
		cardIndex: number,
		from: SolitaireStack,
		to: SolitaireStack,
	): GameStateChange | null {
		if (!this.canMoveToSlot(cardIndex, from, to)) {
			return null;
		}

		const newGame = this.clone();
		const fromStack = newGame.getStackByName(from);
		const toStack = newGame.getStackByName(to);
		const cardToMove = fromStack.pop()!;
		toStack.push(cardToMove);

		SolitaireGame.revealLastCardOnStack(fromStack);

		return {
			game: newGame,
			changedStacks: [from, to],
			movingCards: [cardToMove],
		};
	}

	canMoveToSlot(cardIndex: number, from: SolitaireStack, to: SolitaireStack) {
		if (!this.canMoveFrom(cardIndex, from)) {
			return false;
		}

		if (!SolitaireGame.isSlot(to)) {
			return false;
		}

		if (SolitaireGame.isSlot(from)) {
			return false;
		}

		const fromStack = this.getStackByName(from);
		const slot = this.getStackByName(to);
		const card = fromStack[cardIndex];

		// Check if card is the last one on the stack
		if (cardIndex !== fromStack.length - 1) {
			return false;
		}

		// Check if there is some slot already using the card suit
		if (slot.length === 0) {
			const suitIsAlreadyOnSomeSlot = [this.slot1, this.slot2, this.slot3, this.slot4].some(
				(slot) => slot.some((slotCard) => slotCard.suit === card.suit),
			);

			if (suitIsAlreadyOnSomeSlot) {
				return false;
			}
		}

		// Check if the card has the same suit as the slot
		if (slot.length > 0 && card.suit !== slot[0].suit) {
			return false;
		}

		// Check if card is the next on the sequence
		return SolitaireGame.isNextOnSequence(
			card,
			slot[slot.length - 1],
			SolitaireGame.slotCardValueSequence,
		);
	}

	moveCardsOnStackToABetterPosition(
		cardIndex: number,
		from: SolitaireStack,
	): GameStateChange | null {
		const fromStack = this.getStackByName(from);

		// Check if index is valid
		if (cardIndex > fromStack.length - 1 || cardIndex < 0) {
			return null;
		}

		// If card is the last one on a stack, check if it can go to a slot
		if (cardIndex === fromStack.length - 1) {
			const bestSlot = SolitaireGame.slotNames
				.filter((slot) => slot !== from)
				.find((slot) => this.canMoveToSlot(cardIndex, from, slot));

			if (bestSlot) {
				return this.moveToSlot(cardIndex, from, bestSlot);
			}
		}

		// Check if card can go to a stack
		const bestStack = SolitaireGame.stackNames
			.filter((stack) => stack !== from)
			.find((stack) => this.canMoveToStack(cardIndex, from, stack));

		if (bestStack) {
			return this.moveToStack(cardIndex, from, bestStack);
		}

		return null;
	}

	canFinishAutomatically() {
		return (
			this.shouldFinishAutomatically &&
			this.stacks.every((stack) => stack.every((card) => card.face === 'up')) &&
			(this.amountOfCardsToDraw === 3
				? this.deck.length === 0 && this.nextCards.length === 0
				: true)
		);
	}

	playNextCardAutomatically(): GameStateChange | null {
		for (const stackName of ['next-cards', ...SolitaireGame.stackNames] as SolitaireStack[]) {
			const stack = this.getStackByName(stackName);

			if (stack.length > 0) {
				for (const slotName of SolitaireGame.slotNames) {
					if (this.canMoveToSlot(stack.length - 1, stackName, slotName)) {
						return this.moveToSlot(stack.length - 1, stackName, slotName);
					}
				}
			}
		}

		if (this.canDrawCards()) {
			return this.drawCards();
		}

		if (this.canPutNextCardsBackOnDeck()) {
			return this.putNextCardsBackOnDeck();
		}

		return null;
	}

	getCardHint(): CardHint | null {
		const fromStacks: SolitaireStack[] = [...SolitaireGame.stackNames, 'next-cards'];

		for (const fromStackName of fromStacks) {
			const fromStack = this.getStackByName(fromStackName);
			const isFromStackAStack = SolitaireGame.isStack(fromStackName);
			const toStacks = [...SolitaireGame.slotNames, ...SolitaireGame.stackNames].filter(
				(stack) => stack !== fromStackName,
			);

			for (let cardIndex = 0; cardIndex < fromStack.length; cardIndex += 1) {
				const card = fromStack[cardIndex];
				const previousCard = fromStack[cardIndex - 1];

				for (const toStackName of toStacks) {
					const toStack = this.getStackByName(toStackName);
					const isToStackEmpty = toStack.length === 0;
					const isToStackAStack = SolitaireGame.isStack(toStackName);

					// Handle K going from the top of one stack to another empty one
					if (!previousCard && isFromStackAStack && isToStackAStack && isToStackEmpty) {
						continue;
					}

					// Handle useless movement from a card on a stack to another one
					if (isFromStackAStack && isToStackAStack && previousCard?.face === 'up') {
						continue;
					}

					if (this.canMove(cardIndex, fromStackName, toStackName)) {
						return {
							card,
							cardIndex,
							from: fromStackName,
							to: toStackName,
						};
					}
				}
			}
		}

		if (this.deck.length > 0) {
			return {
				card: this.deck[this.deck.length - 1],
				cardIndex: this.deck.length - 1,
				from: 'deck',
			};
		} else if (this.nextCards.length > 0) {
			return {
				card: this.nextCards[this.nextCards.length - 1],
				cardIndex: this.nextCards.length - 1,
				from: 'next-cards',
				to: 'deck',
			};
		}

		return null;
	}

	moveFromHint(hint: CardHint): GameStateChange | null {
		if (hint.from === 'next-cards' && hint.to === 'deck') {
			return this.putNextCardsBackOnDeck();
		}

		if (hint.from === 'deck') {
			return this.drawCards();
		}

		if (hint.to) {
			return this.move(hint.cardIndex, hint.from, hint.to);
		}

		return null;
	}

	isWon() {
		return (
			this.deck.length === 0 &&
			this.nextCards.length === 0 &&
			this.stacks.every((stack) => stack.length === 0)
		);
	}

	clone() {
		return SolitaireGame.from(this);
	}

	getStackByName(stack: SolitaireStack) {
		switch (stack) {
			case 'deck':
				return this.deck;
			case 'next-cards':
				return this.nextCards;
			case 'slot1':
				return this.slot1;
			case 'slot2':
				return this.slot2;
			case 'slot3':
				return this.slot3;
			case 'slot4':
				return this.slot4;
			case 'stack1':
				return this.stack1;
			case 'stack2':
				return this.stack2;
			case 'stack3':
				return this.stack3;
			case 'stack4':
				return this.stack4;
			case 'stack5':
				return this.stack5;
			case 'stack6':
				return this.stack6;
			case 'stack7':
				return this.stack7;
		}
	}

	/**
	 * Reveal last card on the stack by mutating the original stack
	 **/
	static revealLastCardOnStack(stack: Card[]) {
		const lastCardOnFromStack = stack[stack.length - 1];

		if (lastCardOnFromStack) {
			stack[stack.length - 1] = {
				...lastCardOnFromStack,
				face: 'up',
			};
		}
	}

	static isNextOnSequence(card: Card, base: Card, sequence: CardValue[]): boolean {
		// If there is no base, assume it should be the first card of the sequence
		if (!base) {
			return card.value === sequence[0];
		}

		const baseCardIndexOnSequence = sequence.findIndex((value) => value === base.value);
		const cardIndexOnSequence = sequence.findIndex((value) => value === card.value);

		return cardIndexOnSequence === baseCardIndexOnSequence + 1;
	}

	static slotNames: SolitaireStack[] = ['slot1', 'slot2', 'slot3', 'slot4'];

	static stackNames: SolitaireStack[] = [
		'stack1',
		'stack2',
		'stack3',
		'stack4',
		'stack5',
		'stack6',
		'stack7',
	];

	static isStack(stack: SolitaireStack) {
		return SolitaireGame.stackNames.includes(stack);
	}

	static isSlot(stack: SolitaireStack) {
		return SolitaireGame.slotNames.includes(stack);
	}

	static stackCardValueSequence: CardValue[] = ['K', 'Q', 'J', 10, 9, 8, 7, 6, 5, 4, 3, 2, 'A'];

	static slotCardValueSequence: CardValue[] = ['A', 2, 3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K'];

	static amountOfCardsOnEachStackOnStart = [1, 2, 3, 4, 5, 6, 7, 8];

	static from(other: SolitaireGame) {
		const newGame = new SolitaireGame(
			other.amountOfCardsToDraw,
			other.shouldFinishAutomatically,
		);

		newGame.id = other.id;
		newGame.deck = [...other.deck];
		newGame.nextCards = [...other.nextCards];
		newGame.slot1 = [...other.slot1];
		newGame.slot2 = [...other.slot2];
		newGame.slot3 = [...other.slot3];
		newGame.slot4 = [...other.slot4];
		newGame.stack1 = [...other.stack1];
		newGame.stack2 = [...other.stack2];
		newGame.stack3 = [...other.stack3];
		newGame.stack4 = [...other.stack4];
		newGame.stack5 = [...other.stack5];
		newGame.stack6 = [...other.stack6];
		newGame.stack7 = [...other.stack7];
		newGame.amountOfCardsToDraw = other.amountOfCardsToDraw;
		newGame.started = other.started;

		return newGame;
	}
}
