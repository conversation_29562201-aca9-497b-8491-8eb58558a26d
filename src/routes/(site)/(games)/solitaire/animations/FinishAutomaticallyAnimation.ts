import type { Application } from 'pixi.js';
import type { GameStateChange, SolitaireGame } from '../model/SolitaireGame';
import type { SolitaireGameLayout } from '../entities/SolitaireGameLayout';
import { SteppedAnimation } from '../utils/SteppedAnimation.svelte';

interface Params {
	app: Application;
	layout: SolitaireGameLayout;
	tickDuration: number;
	onMoveCard: (stateChange: GameStateChange) => void;
	onComplete: (finalGame: SolitaireGame) => void;
}

export class FinishAutomaticallyAnimation {
	private animation: SteppedAnimation;
	private layout: SolitaireGameLayout;
	private game: SolitaireGame | null = null;
	private onMoveCard: (stateChange: GameStateChange) => void;

	constructor({ app, layout, tickDuration, onMoveCard, onComplete }: Params) {
		this.layout = layout;
		this.onMoveCard = onMoveCard;
		this.animation = new SteppedAnimation({
			strategy: 'timeout',
			app,
			frameDuration: tickDuration,
			onNext: () => this.moveNextCard(),
			onComplete: () => {
				onComplete(this.game!);
			},
		});
	}

	isPlaying() {
		return this.animation.isPlaying();
	}

	start(game: SolitaireGame) {
		if (this.isPlaying()) {
			return;
		}

		this.game = game;
		this.animation.start();
	}

	set tickDuration(newDuration: number) {
		this.animation.frameDuration = newDuration;
	}

	private moveNextCard(): boolean {
		const stateChange = this.game?.playNextCardAutomatically();

		if (stateChange) {
			this.game = stateChange.game;
			this.layout.sync(stateChange);
			this.onMoveCard(stateChange);
			return true;
		}

		return false;
	}

	dispose() {
		this.animation.dispose();
	}
}
