import type { Application } from 'pixi.js';
import { SteppedAnimation } from '../utils/SteppedAnimation.svelte';
import { SolitaireGame, type SolitaireStack } from '../model/SolitaireGame';
import type { SolitaireGameLayout } from '../entities/SolitaireGameLayout';
import type { Card } from '$lib/models/card-game';
import { getCardId } from '../utils/getCardId';

interface Params {
	app: Application;
	tickDuration: number;
	layout: SolitaireGameLayout;
	onComplete: (finalGame: SolitaireGame) => void;
}

export class DistributeCardsAnimation {
	private animation: SteppedAnimation;
	private layout: SolitaireGameLayout;
	private game: SolitaireGame | null = null;
	private finalGame: SolitaireGame | null = null;
	private nextCardGenerator: Generator<void> | null = null;

	constructor({ app, layout, onComplete, tickDuration }: Params) {
		this.layout = layout;
		this.animation = new SteppedAnimation({
			app,
			strategy: 'timeout',
			frameDuration: tickDuration,
			onNext: () => this.playNextCard(),
			onComplete: () => {
				onComplete(this.finalGame!);
			},
		});
	}

	set tickDuration(newDuration: number) {
		this.animation.frameDuration = newDuration;
	}

	isPlaying() {
		return this.animation.isPlaying();
	}

	isFinished() {
		return this.animation.isFinished();
	}

	start(finalGame: SolitaireGame) {
		this.finalGame = finalGame;
		this.game = new SolitaireGame(finalGame.amountOfCardsToDraw);
		this.putCardsOnDeck();
		this.nextCardGenerator = this.getNextCardGenerator();
		this.animation.start();
	}

	private playNextCard(): boolean {
		const { done } = this.nextCardGenerator!.next();

		return !done;
	}

	private putCardsOnDeck() {
		const withFaceDown = (card: Card): Card => ({
			...card,
			face: 'down',
		});

		const {
			deck,
			slot1,
			slot2,
			slot3,
			slot4,
			stack1,
			stack2,
			stack3,
			stack4,
			stack5,
			stack6,
			stack7,
		} = this.finalGame!;
		this.game!.deck = [
			...deck,
			...[...stack7].reverse(),
			...[...stack6].reverse(),
			...[...stack5].reverse(),
			...[...stack4].reverse(),
			...[...stack3].reverse(),
			...[...stack2].reverse(),
			...[...stack1].reverse(),
			...[...slot4].reverse(),
			...[...slot3].reverse(),
			...[...slot2].reverse(),
			...[...slot1].reverse(),
		].map(withFaceDown);

		this.layout.sync({
			game: this.game!,
			animated: false,
		});
	}

	private *getNextCardGenerator() {
		const {
			nextCards,
			slot1,
			slot2,
			slot3,
			slot4,
			stack1,
			stack2,
			stack3,
			stack4,
			stack5,
			stack6,
			stack7,
		} = this.finalGame!;

		const movements = [
			{
				from: nextCards,
				to: this.game!.nextCards,
				name: 'next-cards',
			},
			{
				from: slot1,
				to: this.game!.slot1,
				name: 'slot1',
			},
			{
				from: slot2,
				to: this.game!.slot2,
				name: 'slot2',
			},
			{
				from: slot3,
				to: this.game!.slot3,
				name: 'slot3',
			},
			{
				from: slot4,
				to: this.game!.slot4,
				name: 'slot4',
			},
			{
				from: stack1,
				to: this.game!.stack1,
				name: 'stack1',
			},
			{
				from: stack2,
				to: this.game!.stack2,
				name: 'stack2',
			},
			{
				from: stack3,
				to: this.game!.stack3,
				name: 'stack3',
			},
			{
				from: stack4,
				to: this.game!.stack4,
				name: 'stack4',
			},
			{
				from: stack5,
				to: this.game!.stack5,
				name: 'stack5',
			},
			{
				from: stack6,
				to: this.game!.stack6,
				name: 'stack6',
			},
			{
				from: stack7,
				to: this.game!.stack7,
				name: 'stack7',
			},
		].filter((movement) => {
			return movement.from.length > 0;
		}) as Array<{
			from: Card[];
			to: Card[];
			name: SolitaireStack;
		}>;

		for (let i = 0; i < movements.length; i += 1) {
			const { from, to, name: stackName } = movements[i];

			for (let j = 0; j < from.length; j += 1) {
				const card = from[j];
				this.game!.deck = this.game!.deck.filter(
					(deckCard) => getCardId(deckCard) !== getCardId(card),
				);
				to.push(card);

				this.layout.sync({
					game: this.game!,
					movingCards: [card],
					changedStacks: ['deck', stackName],
					animated: true,
					movingCardsZIndexOffset: 100 * i + j,
				});

				yield;
			}
		}
	}

	dispose() {
		this.animation.dispose();
	}
}
