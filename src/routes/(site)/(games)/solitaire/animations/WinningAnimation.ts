import {
	<PERSON><PERSON><PERSON>,
	Container,
	type Filter,
	RenderTexture,
	Sprite,
	Texture,
	type Application,
} from 'pixi.js';
import type { SolitaireGame } from '../model/SolitaireGame';
import { GameCard } from '../entities/GameCard';
import type { Card, CardSuit, CardValue } from '$lib/models/card-game';
import { getCardId } from '../utils/getCardId';
import { SteppedAnimation } from '../utils/SteppedAnimation.svelte';

type CardId = `${CardSuit}-${CardValue}`;

interface Params {
	app: Application;
	cards: Record<CardId, GameCard<Card>>;
	onComplete: () => void;
}

export class WinningAnimation {
	app: Application;
	private animation: SteppedAnimation;
	private clonedCards: Partial<Record<CardId, GameCard<Card>>> = {};
	private originalCards: Record<CardId, GameCard<Card>>;
	private container = new Container();
	private texture: Texture;
	private sprite: Sprite;
	private game: SolitaireGame | null = null;
	private maxSpeed = {
		x: 0,
		y: 0,
	};
	private minSpeed = {
		x: 0,
		y: 0,
	};
	private speed = {
		x: 0,
		y: 0,
	};
	private yDragRate = 0.8;
	private yAccelarationRate = 0.12;
	private frameDuration = 15;
	private filters?: Filter | Filter[];
	private getNextCard: Generator<GameCard<Card>> | null = null;
	private cardsCounter = 0;
	private currentCard: GameCard<Card> | null = null;

	constructor({ app, cards, onComplete }: Params) {
		this.app = app;
		this.originalCards = cards;
		this.texture = RenderTexture.create({
			width: this.app.screen.width,
			height: this.app.screen.height,
			resolution: devicePixelRatio,
		});
		this.sprite = new Sprite(this.texture);
		this.sprite.interactive = true;
		this.sprite.zIndex = 2000;
		this.app.stage.addChild(this.sprite);
		this.animation = new SteppedAnimation({
			strategy: 'ticker',
			app,
			frameDuration: this.frameDuration,
			onNext: () => this.onNext(),
			onComplete,
		});
	}

	isPlaying() {
		return this.animation.isPlaying();
	}

	start(game: SolitaireGame) {
		if (this.isPlaying()) {
			return;
		}
		this.game = game;
		this.getNextCard = this.getNextCardGenerator();
		this.cardsCounter = 0;
		const speedBase = this.originalCards['club-10'].view.width;
		this.maxSpeed = {
			x: speedBase * 0.15,
			y: speedBase * 0.15,
		};
		this.minSpeed = {
			x: speedBase * 0.06,
			y: speedBase * 0.06,
		};

		this.animation.start();
	}

	stop() {
		this.animation.stop();
	}

	private onNext(): boolean {
		if (
			!this.currentCard ||
			(this.currentCard &&
				!this.app.screen
					.getBounds()
					.intersects(this.currentCard.view.getBounds().rectangle))
		) {
			this.currentCard = this.getNextCard?.next()?.value;
			this.cardsCounter += 1;
			this.speed.x =
				Math.max(this.minSpeed.x, this.maxSpeed.x * Math.random()) *
				(Math.random() < 0.5 ? 1 : -1);
			this.speed.y =
				Math.max(this.minSpeed.y, this.maxSpeed.y * Math.random()) *
				(Math.random() < 0.5 ? 1 : -1);

			if (this.currentCard) {
				this.currentCard.view.zIndex = this.cardsCounter + 1;
			}
		}

		if (this.currentCard) {
			const card = this.currentCard.view;

			this.app.renderer.render({
				container: this.container,
				target: this.texture,
				clear: CLEAR.NONE,
			});

			const newX = card.x + this.speed.x;
			let newY = card.y + this.speed.y;

			if (newY + card.height / 2 > this.app.screen.height) {
				this.speed.y *= -this.yDragRate;
				newY = this.app.screen.height - card.height / 2;
			}

			card.position.set(newX, newY);

			this.speed.y += Math.abs(this.maxSpeed.y * this.yAccelarationRate);
		}

		return !!this.currentCard;
	}

	private *getNextCardGenerator() {
		const length = this.game?.slot1.length ?? 0;

		for (let i = 0; i < length; i += 1) {
			yield this.getCardOrClone(getCardId(this.game!.slot1[length - 1 - i]));
			yield this.getCardOrClone(getCardId(this.game!.slot2[length - 1 - i]));
			yield this.getCardOrClone(getCardId(this.game!.slot3[length - 1 - i]));
			yield this.getCardOrClone(getCardId(this.game!.slot4[length - 1 - i]));
		}
	}

	private getCardOrClone(id: CardId): GameCard<Card> {
		if (!this.clonedCards[id]) {
			const originalCard = this.originalCards[id];
			const clonedCard = originalCard.clone();
			this.clonedCards[id] = clonedCard;

			const localPosition = originalCard.view.toLocal({ x: 0, y: 0 }, this.container);
			clonedCard.view.position.set(-localPosition.x, -localPosition.y);
			clonedCard.view.zIndex = originalCard.view.zIndex;

			if (this.filters) {
				clonedCard.view.filters = this.filters;
			}

			this.container.addChild(clonedCard.view);

			return clonedCard;
		}

		return this.clonedCards[id];
	}

	dispose() {
		this.animation.dispose();
		this.app.stage.removeChild(this.sprite);
	}
}
