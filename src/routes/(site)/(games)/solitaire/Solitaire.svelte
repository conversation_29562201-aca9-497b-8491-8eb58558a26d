<script lang="ts">
	import { fade, fly } from 'svelte/transition';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import { onDestroy, onMount } from 'svelte';
	import { browser } from '$app/environment';
	import SolitaireSettingsButton from './SolitaireSettingsButton.svelte';
	import UndoIcon from '$lib/components/Icons/UndoIcon.svelte';
	import RedoIcon from '$lib/components/Icons/RedoIcon.svelte';
	import { isMacLike } from '$lib/functions/isMacLike';
	import SolitaireInfoModal from './SolitaireInfoModal.svelte';
	import FeedbackModal from '$lib/components/FeedbackModal.svelte';
	import HintAlert from './HintAlert.svelte';
	import LightBulbIcon from '$lib/components/Icons/LightBulbIcon.svelte';
	import type { SolitaireApp } from './game/SolitaireApp.svelte';
	import { page } from '$app/state';
	import SolitaireHowToPlayButton from './SolitaireHowToPlayButton.svelte';
	import FeedbackIcon from '$lib/components/Icons/FeedbackIcon.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import NewGameButton from '$lib/components/NewGameButton.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';
	import { ads } from '$lib/stores/ads.svelte';
	import { cn } from '$lib/util/cn';
	import {
		buildLayoutConfig,
		type AllScreensSolitaireGameLayoutConfig,
	} from './utils/buildLayoutConfig';
	import type { CardGameAssetsManagerConfig } from './assets/CardGameAssetsManager';

	export interface SolitaireProps {
		targetOrigin?: string;
		layoutConfig?: DeepPartial<AllScreensSolitaireGameLayoutConfig>;
		assets?: DeepPartial<CardGameAssetsManagerConfig>;
		backgroundColor?: string;
		noNavbar?: boolean;
		ui: 'all' | 'actions' | 'none';
		disableLeaderboard?: boolean;
	}

	let props: SolitaireProps = $props();

	let canvasContainer = $state<HTMLDivElement>();
	let game = $state<SolitaireApp | undefined>();
	let drawAmountDropdownOpen = $state(false);
	let versionDropdownOpen = $state(false);
	let infoModalOpen = $state(false);
	let feedbackOpen = $state(false);
	let isMac = $state(false);
	let gameIsland: GameIsland<any, any, any> | null = $state(null);
	let context = $derived(game?.gameManager?.context);

	function closeDropdownsAndModals() {
		versionDropdownOpen = false;
		drawAmountDropdownOpen = false;
		infoModalOpen = false;
		feedbackOpen = false;
	}

	onMount(async () => {
		try {
			const { SolitaireApp } = await import('./game/SolitaireApp.svelte');
			const navbarHeight = 64;
			const uiHeight = 64;
			const uiHeightMobile = 56;

			let desktopPaddingTop = 16;
			let mobilePaddingTop = 16;

			if (!props.noNavbar) {
				desktopPaddingTop += navbarHeight;
				mobilePaddingTop += navbarHeight;
			}

			if (props.ui !== 'none') {
				desktopPaddingTop += uiHeight;
			}

			if (props.ui === 'all') {
				mobilePaddingTop += uiHeightMobile;
			}

			game = new SolitaireApp({
				disableLeaderboard: props.disableLeaderboard,
				targetOrigin: props.targetOrigin,
				canvasContainer: canvasContainer!,
				showFPS: page.url.searchParams.get('fps') !== null,
				layoutConfig:
					props.layoutConfig ??
					buildLayoutConfig({
						desktop: {
							paddingTop: desktopPaddingTop,
						},
						tablet: {
							paddingTop: desktopPaddingTop,
						},
						mobile: {
							paddingTop: mobilePaddingTop,
						},
					}),
				assets: props.assets,
			});

			isMac = isMacLike();
		} catch (error: any) {
			console.error(error);
		}
	});

	onDestroy(() => {
		if (browser) {
			try {
				game?.dispose();
			} catch (error) {
				// Ignore
				console.error(error);
			}
		}
	});

	function showDailyGameIsland() {
		closeDropdownsAndModals();
		gameIsland?.changeVariant('daily-game');
	}

	function onkeydown(event: KeyboardEvent) {
		if (event.shiftKey && event.key.toLocaleLowerCase() === 'd') {
			showDailyGameIsland();
		}
	}
</script>

<svelte:window {onkeydown} />

<GameLayout
	mobileOrientation="all"
	noPadding
	navbarStyle={props.noNavbar ? 'gone' : 'on-top-opaque'}
	class="bg-game-solitaire-field"
	adsProps={{
		variant: 'on-top',
	}}
	style={props.backgroundColor ? `background-color: ${props.backgroundColor}` : undefined}
>
	{#snippet Island()}
		{#if context}
			<GameIsland
				bind:this={gameIsland}
				{context}
				gameOverIslandDelay={1500}
				onNewGame={(dailyGameDate) => {
					if (dailyGameDate) {
						context?.dailyGame?.fetchAndPlay(dailyGameDate);
					} else {
						game?.gameManager?.throttledStartNewGame({
							draw: game.gameManager?.drawAmount,
						});
					}
				}}
			/>
		{/if}
	{/snippet}

	{#if game?.gameManager?.couldNotFindAnyHint}
		<HintAlert />
	{/if}

	{#if !game?.gameManager?.context.isGameReady && !game?.error}
		<span
			transition:fade={{ duration: 150 }}
			class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 loading loading-spinner loading-lg text-base-300"
		></span>
	{/if}

	{#if props.ui !== 'none'}
		<div
			class={cn(
				'absolute left-0 right-0 flex gap-2 max-w-4xl xl:max-w-5xl py-4 mx-auto w-full lg:px-0 md:px-8 px-4 limit-width-by-screen-height justify-between items-center',
				{
					'lg:px-4': ads.canShowGameAds,
				},
			)}
			style="top: {props.noNavbar
				? ads.effectiveSize?.height
				: ads.effectiveSizeWithNavbar?.height}px"
		>
			{#if props.ui === 'all'}
				<div class="flex flex-row gap-2 items-center justify-center">
					<SolitaireHowToPlayButton />

					<Dropdown bind:open={versionDropdownOpen}>
						<DropdownButton class="btn-xs md:btn-sm">V2</DropdownButton>

						<DropdownContent menu class="w-44 lg:w-44">
							<DropdownItem>
								<button class="menu-active" onclick={closeDropdownsAndModals}
									>Version 2 (New)</button
								>
							</DropdownItem>

							<a href="/solitaire/legacy">
								<DropdownItem>
									<button onclick={closeDropdownsAndModals}
										>Version 1 (Legacy)</button
									>
								</DropdownItem>
							</a>
						</DropdownContent>
					</Dropdown>

					<NewGameButton
						buttonClass="btn-xs md:btn-sm"
						onPlayNewGame={() => {
							game?.gameManager?.throttledStartNewGame({
								draw: game?.gameManager?.drawAmount,
							});
						}}
						onPlayDailyGame={() => {
							showDailyGameIsland();
						}}
						onReplayCurrentGame={() => {
							game?.gameManager?.throttledRestartCurrentGame();
							closeDropdownsAndModals();
						}}
					>
						<a href="/solitaire/legacy">
							<DropdownItem>
								<button class="w-full">Play Legacy Version</button>
							</DropdownItem>
						</a>
					</NewGameButton>

					<Dropdown bind:open={drawAmountDropdownOpen}>
						<DropdownButton class="btn-xs md:btn-sm gap-1">
							<span class="hidden sm:inline">Draw</span>
							{game?.gameManager?.drawAmount ?? 1}
						</DropdownButton>

						<DropdownContent menu class="w-24 lg:w-44">
							<DropdownItem>
								<button
									class="w-full justify-between gap-4"
									class:menu-active={game?.gameManager?.drawAmount === 1}
									onclick={() => {
										game?.gameManager?.throttledStartNewGame({
											draw: 1,
										});
										closeDropdownsAndModals();
									}}
								>
									Draw 1 <span class="hidden lg:block">Shift + 1</span>
								</button>
							</DropdownItem>
							<DropdownItem>
								<button
									class="w-full justify-between gap-4"
									class:menu-active={game?.gameManager?.drawAmount === 3}
									onclick={() => {
										game?.gameManager?.throttledStartNewGame({
											draw: 3,
										});
										closeDropdownsAndModals();
									}}
								>
									Draw 3 <span class="hidden lg:block">Shift + 3</span>
								</button>
							</DropdownItem>
						</DropdownContent>
					</Dropdown>

					<SolitaireSettingsButton {game} />

					<button
						class="btn btn-xs md:btn-sm"
						onclick={() => {
							feedbackOpen = true;
						}}
						aria-label="send feedback"
					>
						<FeedbackIcon class="size-5" />
					</button>

					<button
						class="btn btn-xs md:btn-sm"
						onclick={() => (infoModalOpen = true)}
						aria-label="Open info dialog"
					>
						<InfoSolidIcon class="size-5" />
					</button>

					<MoreGamesButton class="btn-xs md:btn-sm" />
				</div>
			{:else}
				<!-- Spacer -->
				<div></div>
			{/if}

			{#if props.ui === 'all' || props.ui === 'actions'}
				<div class="flex-row gap-2 hidden md:flex">
					<div
						class="tooltip before:hidden after:hidden lg:before:block lg:after:block tooltip-bottom"
						data-tip="Hint (H)"
					>
						<button
							class="btn btn-xs md:btn-sm"
							disabled={!game?.gameManager?.canHandleUserEvents}
							onclick={() => game?.gameManager?.throttledShowHint()}
							aria-label="show hint"
						>
							<LightBulbIcon class="size-6" />
						</button>
					</div>
					<div
						class="tooltip before:hidden after:hidden lg:before:block lg:after:block tooltip-bottom"
						data-tip="Undo ({isMac ? '⌘' : 'Ctrl'} + Z)"
					>
						<button
							class="btn btn-xs md:btn-sm"
							disabled={!game?.gameManager?.canUndo()}
							onclick={() => game?.gameManager?.throttledOnUndo()}
							aria-label="undo"
						>
							<UndoIcon class="size-5" />
						</button>
					</div>
					<div
						class="tooltip before:hidden after:hidden lg:before:block lg:after:block tooltip-left xl:tooltip-bottom"
						data-tip="Redo ({isMac ? '⌘' : 'Ctrl'} + Shift + Z)"
					>
						<button
							class="btn btn-xs md:btn-sm"
							disabled={!game?.gameManager?.canRedo()}
							onclick={() => game?.gameManager?.throttledOnRedo()}
							aria-label="redo"
						>
							<RedoIcon class="size-5" />
						</button>
					</div>
				</div>
			{/if}
		</div>
	{/if}

	<div class="w-full h-screen overflow-hidden">
		{#if props.ui === 'all'}
			{#if game?.error === 'webgl-not-supported'}
				<div
					in:fly={{ y: 20, delay: 300 }}
					out:fly={{ y: 20 }}
					class="fixed p-4 top-1/2 -translate-y-1/2 w-full"
				>
					<div
						role="alert"
						class="card bg-base-100 shadow-lg w-full mx-auto max-w-[400px]"
					>
						<div class="card-body">
							<h3 class="card-title">Oops!</h3>
							<p>
								It looks like your browser is not compatible with this Solitaire
								version (WebGL is not supported). Would you like to play the legacy
								version?
							</p>

							<div class="card-actions justify-center xs:justify-end mt-2">
								<a href="/" class="btn btn-ghost w-full xs:w-auto">No, go home</a>
								<a
									href="/solitaire/legacy?no-alert"
									class="btn btn-primary w-full xs:w-auto"
								>
									Yes, play now
								</a>
							</div>
						</div>
					</div>
				</div>
			{/if}

			{#if game?.error === 'device-error'}
				<div
					in:fly={{ y: 20, delay: 300 }}
					out:fly={{ y: 20 }}
					class="fixed p-4 top-1/2 -translate-y-1/2 w-full"
				>
					<div
						role="alert"
						class="card bg-base-100 shadow-lg w-full mx-auto max-w-[400px]"
					>
						<div class="card-body">
							<h3 class="card-title">Oops!</h3>
							<p>
								It looks like your device could not run the game correctly. Would
								you like to play the legacy version?
							</p>

							<div class="card-actions justify-center xs:justify-end mt-2">
								<a href="/" class="btn btn-ghost w-full xs:w-auto">No, go home</a>
								<a
									href="/solitaire/legacy?no-alert"
									class="btn btn-primary w-full xs:w-auto"
								>
									Yes, play now
								</a>
							</div>
						</div>
					</div>
				</div>
			{/if}

			{#if game?.gameManager?.winnableGameFetchState === 'error'}
				<div
					in:fly={{ y: 20, delay: 300 }}
					out:fly={{ y: 20 }}
					class="fixed p-4 top-1/2 -translate-y-1/2 w-full"
				>
					<div
						role="alert"
						class="card bg-base-100 shadow-lg w-full mx-auto max-w-[400px]"
					>
						<div class="card-body">
							<h3 class="card-title">Oops!</h3>
							<p>
								We could not get a winnable game. Would you like to play a random
								one?
							</p>

							<div class="card-actions justify-center xs:justify-end mt-2">
								<button
									class="btn btn-ghost w-full xs:w-auto"
									onclick={() => {
										game?.gameManager?.throttledStartNewGame({
											draw: game?.gameManager?.drawAmount,
											mode: 'winnable',
										});
									}}
								>
									No, try again
								</button>
								<button
									class="btn btn-primary w-full xs:w-auto"
									onclick={() => {
										game?.gameManager?.throttledStartNewGame({
											draw: game?.gameManager?.drawAmount,
											mode: 'random',
										});
									}}
								>
									Yes, play a random game
								</button>
							</div>
						</div>
					</div>
				</div>
			{/if}
		{/if}

		<div
			bind:this={canvasContainer}
			class="size-full bg-game-solitaire-field [&_button]:outline-4 [&_button]:outline-transparent [&_button]:rounded-md [&_button]:focus:outline-warning [&_button]:outline-offset-2"
			style={props.backgroundColor ? `background-color: ${props.backgroundColor}` : undefined}
		>
			<!-- Canvas will be inserted here -->
		</div>
	</div>

	{#if props.ui === 'actions' || props.ui === 'all'}
		<div
			class="flex-row md:hidden flex absolute bottom-0 p-4 items-center justify-between w-full"
		>
			<button
				class="btn btn-circle btn-lg"
				disabled={!game?.gameManager?.canHandleUserEvents}
				onclick={() => game?.gameManager?.throttledShowHint()}
				aria-label="hint"
			>
				<LightBulbIcon class="size-8" />
			</button>

			<div class="flex flex-row gap-4">
				<button
					class="btn btn-circle btn-lg"
					disabled={!game?.gameManager?.canUndo()}
					onclick={() => game?.gameManager?.throttledOnUndo()}
					aria-label="undo"
				>
					<UndoIcon class="size-8" />
				</button>
				<button
					class="btn btn-circle btn-lg"
					disabled={!game?.gameManager?.canRedo()}
					onclick={() => game?.gameManager?.throttledOnRedo()}
					aria-label="redo"
				>
					<RedoIcon class="size-8" />
				</button>
			</div>
		</div>
	{/if}
</GameLayout>

<SolitaireInfoModal bind:isOpen={infoModalOpen} />

<FeedbackModal
	context="Solitaire"
	bind:isOpen={feedbackOpen}
	extra={`== Last Played Games ==\n\n${game?.gameManager?.lastPlayedGames.join('\n') ?? ''}`}
/>

<style>
	@media screen and (max-height: 768px) {
		.limit-width-by-screen-height {
			max-width: 768px;
		}
	}
</style>
