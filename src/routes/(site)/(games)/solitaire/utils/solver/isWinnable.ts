import {
	SolitaireGame,
	type GameStateChange,
	type SolitaireStack,
} from '../../model/SolitaireGame';

const allFromStacks = ['next-cards', ...SolitaireGame.stackNames] as SolitaireStack[];
const allToStacks = [...SolitaireGame.slotNames, ...SolitaireGame.stackNames] as SolitaireStack[];

/**
 * Uses brute-force to check if a solitaire game is winnable.
 * This should be used just to eventually check if a solitaire
 * game is winnable, since we cannot 100% trust the winnable games
 *
 * DO NOT USE IT ON THE USER SIDE
 **/
export function isWinnable(
	game: SolitaireGame,
	counter = 0,
	cache = {} as Record<string, boolean>,
) {
	game.shouldFinishAutomatically = true;
	const cacheKey = JSON.stringify(game);

	if (cache[cacheKey]) {
		return false;
	}

	cache[cacheKey] = true;

	if (game.isWon() || game.canFinishAutomatically()) {
		return true;
	}

	if (counter > 200) {
		return false;
	}

	const allMoves = allFromStacks.flatMap((from) => {
		const stack = game.getStackByName(from);

		const allMovesFromStack = allToStacks
			.filter((to) => to !== from)
			.flatMap((to) => {
				return stack.map((_, i) => game.move(i, from, to));
			})
			.filter(Boolean) as GameStateChange[];

		return allMovesFromStack;
	});

	if (game.canDrawCards()) {
		allMoves.push(game.drawCards()!);
	}

	if (game.canPutNextCardsBackOnDeck()) {
		allMoves.push(game.putNextCardsBackOnDeck()!);
	}

	for (let i = 0; i < allMoves.length; i += 1) {
		const move = allMoves[i];

		if (isWinnable(move.game, counter + 1, cache)) {
			return true;
		}
	}

	return false;
}
