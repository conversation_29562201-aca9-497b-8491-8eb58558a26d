import { Easing, Group, Tween } from '@tweenjs/tween.js';
import type { Container } from 'pixi.js';

interface ShakeParams {
	view: Container;
	dx?: number;
	dy?: number;
	duration?: number;
}

export function shake({ view, dx = 3, dy = 0, duration = 200 }: ShakeParams) {
	const x = view.x;
	const y = view.y;
	const group = new Group();

	const update = ({ x, y }: { x: number; y: number }) => {
		view.updateTransform({
			x,
			y,
		});
	};
	const easing = Easing.Quadratic.InOut;

	new Tween(
		{
			x,
			y,
		},
		group,
	)
		.to({ x: x + dx, y: y + dy })
		.onUpdate(update)
		.easing(easing)
		.duration(duration / 4)
		.chain(
			new Tween(
				{
					x: x + dx,
					y: y + dy,
				},
				group,
			)
				.to({
					x: x - dx,
					y: y - dy,
				})
				.onUpdate(update)
				.easing(easing)
				.duration(duration / 2)
				.chain(
					new Tween(
						{
							x: x - dx,
							y: y - dy,
						},
						group,
					)
						.to({ x, y })
						.onUpdate(update)
						.easing(easing)
						.duration(duration / 4)
						.onComplete(() => {
							update({ x, y });
						}),
				),
		);

	return group;
}
