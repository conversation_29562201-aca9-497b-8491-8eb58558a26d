import { supabase } from '$lib/api/supabase';
import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import { handleError } from '../../../../../hooks.client';
import { SolitaireGame } from '../model/SolitaireGame';
import { unminifySolitaireGame } from './minifier/unminifySolitaireGame';

interface Game {
	game: string;
	draw: 1 | 3;
}

export class WinnableSolitaireGameFactory {
	private _fallback = new WinnableSolitaireGameFromTextFilesFactory();
	private _nextGames: {
		1: Game[];
		3: Game[];
	} = {
		1: [],
		3: [],
	};
	private _abortController = new AbortController();

	private async fetchGames(draw: 1 | 3) {
		this._abortController.abort();
		this._abortController = new AbortController();

		const games = (
			await supabase
				.rpc('random_solitaire', {
					draw_param: draw,
				})
				.select('game,draw')
				.throwOnError()
				.abortSignal(this._abortController.signal)
		).data as { game: string; draw: 1 | 3 }[];

		if (!games || games.length === 0) {
			throw new Error('Game not found');
		}

		this._nextGames[draw].push(...games);
	}

	async build(draw: 1 | 3): Promise<SolitaireGame> {
		try {
			if (this._nextGames[draw].length > 0) {
				const game = this._nextGames[draw].shift()!;

				if (this._nextGames[draw].length === 0) {
					void this.fetchGames(draw);
				}

				return unminifySolitaireGame(game.game, game.draw);
			}

			await this.fetchGames(draw);

			return this.build(draw);
		} catch (error: any) {
			handleError(error);

			// Fallback to legacy version
			return this._fallback.build(draw);
		}
	}
}

export class WinnableSolitaireGameFromTextFilesFactory {
	private draw1MinifiedGames: string[] = [];
	private draw3MinifiedGames: string[] = [];

	async build(draw: 1 | 3): Promise<SolitaireGame> {
		if (draw === 3 && this.draw3MinifiedGames.length > 0) {
			return unminifySolitaireGame(getRandomItemAt(this.draw3MinifiedGames), 3);
		}

		if (draw === 1 && this.draw1MinifiedGames.length > 0) {
			return unminifySolitaireGame(getRandomItemAt(this.draw1MinifiedGames), 1);
		}

		let allGamesText: string | undefined;

		if (draw === 3) {
			allGamesText = await fetch(
				'https://static.lofiandgames.com/generated-games/solitaire/draw-3.txt',
			).then((res) => res.text());
		} else {
			const part = getRandomItemAt([0, 1, 2, 3, 4, 5]);

			switch (part) {
				case 0: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-0.txt',
					).then((res) => res.text());
					break;
				}
				case 1: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-1.txt',
					).then((res) => res.text());
					break;
				}
				case 2: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-2-v2.txt',
					).then((res) => res.text());
					break;
				}
				case 3: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-3.txt',
					).then((res) => res.text());
					break;
				}
				case 4: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-4.txt',
					).then((res) => res.text());
					break;
				}
				case 5: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-5.txt',
					).then((res) => res.text());
					break;
				}
			}
		}

		if (!allGamesText) {
			throw new Error('Failed to get winnable solitaire game');
		}

		const allGames = allGamesText.split('\n').map((line) => line.replace('\r', ''));

		if (draw === 3) {
			this.draw3MinifiedGames = allGames;
		} else {
			this.draw1MinifiedGames = allGames;
		}

		return unminifySolitaireGame(getRandomItemAt(allGames), draw);
	}
}
