import { Ads } from '$lib/stores/ads.svelte';

export const solitaireBreakpoints = {
	tablet: 768,
	desktop: 1280,
} as const;

export interface SolitaireGameLayoutConfig {
	columnGap: number;
	rowGap: number;
	paddingX: number;
	paddingTop: number;
	paddingBottom: number;
	maxWidth: number;
	nextCardsOffsetXPercentage: number;
	stackCardsUpOffsetYPercentage: number;
	stackCardsDownOffsetYPercentage: number;
	minStackCardsDownOffsetYPercentage: number;
	adsHeight: number;
	adsWidth: number;
}

const desktopLayoutConfig: SolitaireGameLayoutConfig = {
	columnGap: 16,
	rowGap: 16,
	paddingX: 32,
	paddingTop: 16,
	paddingBottom: 0,
	maxWidth: 1024,
	nextCardsOffsetXPercentage: 0.25,
	stackCardsUpOffsetYPercentage: 0.22,
	stackCardsDownOffsetYPercentage: 0.18,
	minStackCardsDownOffsetYPercentage: 0.03,
	adsHeight: 0,
	adsWidth: Ads.desktopGameAdSize.width + 32,
};

const tabletLayoutConfig: SolitaireGameLayoutConfig = {
	...desktopLayoutConfig,
	columnGap: 8,
	rowGap: 16,
	maxWidth: 896,
	adsHeight: Ads.tabletGameAdSize.height + 32,
	adsWidth: 0,
};

const mobileLayoutConfig: SolitaireGameLayoutConfig = {
	columnGap: 8,
	rowGap: 16,
	paddingX: 16,
	paddingTop: 16,
	paddingBottom: 60,
	maxWidth: 896,
	nextCardsOffsetXPercentage: 0.45,
	stackCardsUpOffsetYPercentage: 0.45,
	stackCardsDownOffsetYPercentage: 0.45,
	minStackCardsDownOffsetYPercentage: 0.03,
	adsHeight: Ads.mobileGameAdSize.height + 32,
	adsWidth: 0,
};

export interface AllScreensSolitaireGameLayoutConfig {
	desktop: SolitaireGameLayoutConfig;
	tablet: SolitaireGameLayoutConfig;
	mobile: SolitaireGameLayoutConfig;
	breakpoints: typeof solitaireBreakpoints;
}

export function buildLayoutConfig(overrides?: DeepPartial<AllScreensSolitaireGameLayoutConfig>) {
	return {
		desktop: {
			...desktopLayoutConfig,
			...overrides?.desktop,
		},
		tablet: {
			...tabletLayoutConfig,
			...overrides?.tablet,
		},
		mobile: {
			...mobileLayoutConfig,
			...overrides?.mobile,
		},
		breakpoints: {
			...solitaireBreakpoints,
			...overrides?.breakpoints,
		},
	};
}
