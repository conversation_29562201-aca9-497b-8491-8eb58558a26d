import { type Card, type CardFace, type CardSuit, type CardValue } from '$lib/models/card-game';
import { SolitaireGame } from '../../model/SolitaireGame';

export const unminifySolitaireGame = (text: string, draw: 1 | 3): SolitaireGame => {
	if (text.length !== 52) {
		throw new Error(`Invalid game. Expected 52 characters, got ${text.length}`);
	}

	const game = new SolitaireGame(draw);

	for (let i = 0; i < 24; i += 1) {
		game.deck[i] = getCardFromChar(text[i]);
	}

	game.stack1[0] = getCardFromChar(text[24], 'up');
	game.stack2 = [getCardFromChar(text[25]), getCardFrom<PERSON>har(text[26], 'up')];
	game.stack3 = [
		getCardFromChar(text[27]),
		getCardFromChar(text[28]),
		getCardFromChar(text[29], 'up'),
	];
	game.stack4 = [
		getCard<PERSON>rom<PERSON>har(text[30]),
		getCard<PERSON>rom<PERSON>har(text[31]),
		getCard<PERSON>rom<PERSON>har(text[32]),
		getCard<PERSON>rom<PERSON>har(text[33], 'up'),
	];
	game.stack5 = [
		getCardFromChar(text[34]),
		getCardFromChar(text[35]),
		getCardFromChar(text[36]),
		getCardFromChar(text[37]),
		getCardFromChar(text[38], 'up'),
	];
	game.stack6 = [
		getCardFromChar(text[39]),
		getCardFromChar(text[40]),
		getCardFromChar(text[41]),
		getCardFromChar(text[42]),
		getCardFromChar(text[43]),
		getCardFromChar(text[44], 'up'),
	];
	game.stack7 = [
		getCardFromChar(text[45]),
		getCardFromChar(text[46]),
		getCardFromChar(text[47]),
		getCardFromChar(text[48]),
		getCardFromChar(text[49]),
		getCardFromChar(text[50]),
		getCardFromChar(text[51], 'up'),
	];

	game.validate();

	game.started = true;

	return game;
};

function getCardFromChar(char: string, face: CardFace = 'down'): Card {
	const card = textToCardMap.get(char);

	if (!card) {
		throw new Error(`Failed to get card from char ${char}`);
	}

	return {
		...card,
		face,
	};
}

/** Same as minifySolitaireGame.js. Do not change any values below here */
const suits: CardSuit[] = ['club', 'diamond', 'heart', 'spade'];
const values: CardValue[] = [2, 3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K', 'A'];

const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ' as const;

export const cardToTextMap = new Map<`${CardSuit}-${CardValue}`, (typeof characters)[number]>();

suits.forEach((suit, i) => {
	values.forEach((value, j) => {
		const charIndex = j + i * values.length;
		const char = characters[charIndex];

		cardToTextMap.set(`${suit}-${value}`, char);
	});
});

export const textToCardMap: Map<string, { suit: CardSuit; value: CardValue }> = new Map();

Array.from(cardToTextMap.entries()).forEach(([card, text]) => {
	const [suit, value] = card.split('-');
	textToCardMap.set(text, {
		suit: suit as CardSuit,
		value: (['J', 'Q', 'K', 'A'].includes(value) ? value : +value) as CardValue,
	});
});
