import { MessageBroker } from '$lib/util/MessageBroker/MessageBroker';
import { ToParentWindowMessageBrokerAdapter } from '$lib/util/MessageBroker/ToParentWindowMessageBrokerAdapter';

export function createSolitaireMessageBroker({ targetOrigin }: { targetOrigin: string }) {
	return new MessageBroker({
		targetOrigin,
		send: ['ready', 'game-start', 'game-over', 'win-animation-over', 'game-error'],
		receive: ['setup-game', 'enable-accessibility', 'start-game', 'show-hint', 'undo', 'redo'],
		adapter: new ToParentWindowMessageBrokerAdapter(),
	});
}

export type SolitaireMessageBroker = ReturnType<typeof createSolitaireMessageBroker>;
