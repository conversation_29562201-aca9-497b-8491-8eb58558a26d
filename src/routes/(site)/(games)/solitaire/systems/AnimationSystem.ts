import { Tween, Group } from '@tweenjs/tween.js';

export interface AnimationEntity<T, Meta = any> {
	id: string;
	animation: T;
	completeOnClear?: boolean;
	resolveConflict?: (
		playingAnimation: CompleteAnimationEntity<Meta>,
		newAnimation: CompleteAnimationEntity<Meta>,
	) => CompleteAnimationEntity<Meta>;
	meta?: Meta;
}

export type CompleteAnimationEntity<Meta = any> = AnimationEntity<Tween | Group, Meta>;

function defaultConflictResolver<Meta>(
	_playingAnimation: CompleteAnimationEntity<Meta>,
	newAnimation: CompleteAnimationEntity<Meta>,
) {
	return newAnimation;
}

export class AnimationSystem {
	private static cleanUpInterval = 10_000000;
	private scheduledCleanUpTime = -1;
	private animations: CompleteAnimationEntity[] = [];

	add(...animations: CompleteAnimationEntity[]) {
		if (animations.length === 0) {
			return;
		}

		// Remove animations with same id
		const { animationsToRemove, animationsToAdd } = animations
			.map((newAnimation) => {
				const currentAnimation = this.animations.find(
					(animation) => animation.id === newAnimation.id && isPlaying(animation),
				);

				if (currentAnimation) {
					const animationToKeep = (
						currentAnimation.resolveConflict ?? defaultConflictResolver
					)(currentAnimation, newAnimation);

					if (animationToKeep === currentAnimation) {
						return {
							remove: null,
							add: null,
						};
					}

					return {
						remove: currentAnimation,
						add: newAnimation,
					};
				}

				return {
					remove: null,
					add: newAnimation,
				};
			})
			.reduce(
				(acc, current) => {
					if (current.remove) {
						acc.animationsToRemove.push(current.remove);
					}
					if (current.add) {
						acc.animationsToAdd.push(current.add);
					}

					return acc;
				},
				{
					animationsToRemove: [] as CompleteAnimationEntity[],
					animationsToAdd: [] as CompleteAnimationEntity[],
				},
			);

		this.remove(...animationsToRemove);

		animationsToAdd.forEach((animation) => {
			start(animation);
		});

		this.animations.push(...animationsToAdd);
	}

	isPlaying(id: string) {
		return this.animations.some((animation) => animation.id === id && isPlaying(animation));
	}

	removeAndCompleteById(...ids: string[]) {
		const animationsToRemove = this.animations.filter((animation) =>
			ids.includes(animation.id),
		);

		animationsToRemove.forEach((animation) => (animation.completeOnClear = true));

		this.remove(...animationsToRemove);
	}

	removeById(...ids: string[]) {
		const animationsToRemove = this.animations.filter((animation) =>
			ids.includes(animation.id),
		);

		this.remove(...animationsToRemove);
	}

	remove(...animations: CompleteAnimationEntity[]) {
		animations.forEach(({ animation, completeOnClear }) => {
			if (completeOnClear) {
				if (animation instanceof Tween && animation.isPlaying()) {
					animation.end();
				} else if (animation instanceof Group && !animation.allStopped()) {
					animation.getAll().forEach((animation) => animation.end());
				}
			}
		});

		this.animations = this.animations.filter((animation) => !animations.includes(animation));
	}

	update(time: number) {
		this.animations.forEach(({ animation }) => {
			animation.update(time);
		});
		this.cleanUpIfNeeded();
	}

	reset() {
		this.remove(...this.animations);
	}

	private cleanUpIfNeeded() {
		if (performance.now() >= this.scheduledCleanUpTime) {
			this.cleanUp();
		}
	}

	private cleanUp() {
		if (this.animations.length !== 0) {
			const finishedAnimations = this.animations.filter(isStopped);

			this.remove(...finishedAnimations);
		}
		this.scheduledCleanUpTime = performance.now() + AnimationSystem.cleanUpInterval;
	}
}

function isPlaying({ animation }: CompleteAnimationEntity): boolean {
	if (animation instanceof Tween) {
		return animation.isPlaying();
	} else {
		return animation.getAll().some((tween) => tween.isPlaying());
	}
}

function isStopped({ animation }: CompleteAnimationEntity): boolean {
	if (animation instanceof Tween) {
		return !animation.isPlaying();
	} else {
		return animation.allStopped();
	}
}

function start(
	{ animation }: CompleteAnimationEntity,
	start?: number,
	overrideStartingValues?: boolean,
) {
	if (animation instanceof Tween) {
		animation.start(start, overrideStartingValues);
	} else {
		animation.getAll()[0]?.start(start, overrideStartingValues);
	}
}
