import 'pixi.js/accessibility';
import { Application, isWebGLSupported, isWebGPUSupported, Text } from 'pixi.js';
import { AnimationSystem } from '../systems/AnimationSystem';
import {
	CardGameAssetsManager,
	type CardGameAssetsManagerConfig,
} from '../assets/CardGameAssetsManager';
import { GameManager } from '../entities/GameManager.svelte';
import { getElementSize } from '$lib/functions/getElementSize';
import throttle from 'lodash/throttle';
import { ads } from '$lib/stores/ads.svelte';
import { untrack } from 'svelte';
import {
	buildLayoutConfig,
	type AllScreensSolitaireGameLayoutConfig,
} from '../utils/buildLayoutConfig';
import {
	createSolitaireMessageBroker,
	type SolitaireMessageBroker,
} from '../systems/createSolitaireMessageBroker';

interface SolitaireAppOptions {
	disableLeaderboard?: boolean;
	targetOrigin?: string;
	canvasContainer: HTMLDivElement;
	showFPS: boolean;
	layoutConfig?: DeepPartial<AllScreensSolitaireGameLayoutConfig>;
	assets?: DeepPartial<CardGameAssetsManagerConfig>;
}

export class SolitaireApp {
	private canvasContainer: HTMLDivElement;
	private app?: Application;
	private animationSystem = new AnimationSystem();
	private assetsManager: CardGameAssetsManager;
	gameManager: GameManager | null = $state(null);
	private throttledResize: () => void;
	private fps = new Text();
	private _isLoading = $state(true);
	private _cleanUpAdsListener?: () => void;
	private _layoutConfig: AllScreensSolitaireGameLayoutConfig;
	private _messageBroker?: SolitaireMessageBroker;
	private _error?: 'webgl-not-supported' | 'device-error' = $state();
	private _disableLeaderboard = false;

	constructor({
		disableLeaderboard,
		canvasContainer,
		showFPS,
		layoutConfig,
		assets,
		targetOrigin,
	}: SolitaireAppOptions) {
		if (targetOrigin) {
			this._messageBroker = createSolitaireMessageBroker({ targetOrigin });
		}

		this._disableLeaderboard = !!disableLeaderboard;
		this.assetsManager = new CardGameAssetsManager(assets);
		this.canvasContainer = canvasContainer;
		this._layoutConfig = buildLayoutConfig(layoutConfig);
		this.throttledResize = throttle(this.resize, 300, { leading: false, trailing: true });
		this.init();
		this.fps.visible = showFPS;
	}

	get error() {
		return this._error;
	}

	set error(error) {
		this._error = error;

		if (error) {
			this._messageBroker?.send('game-error', error);
		}
	}

	private async init() {
		this.initListeners();
		this._isLoading = true;

		try {
			if (isWebGLSupported(true) || (await isWebGPUSupported())) {
				this.error = undefined;
			} else {
				this.error = 'webgl-not-supported';
			}
		} catch (error: any) {
			this.error = 'webgl-not-supported';
			console.error(error);
		}

		if (this.error) {
			this._isLoading = false;
			return;
		}

		try {
			this.app = new Application();

			const isOnChromebook = /CrOS/.test(navigator.userAgent);

			await this.app.init({
				resizeTo: this.canvasContainer,
				width: this.canvasContainer.clientWidth,
				backgroundColor: getComputedStyle(this.canvasContainer).backgroundColor,
				resolution: window.devicePixelRatio,
				autoDensity: true,
				antialias: false,
				roundPixels: false,
				preference: isOnChromebook ? 'webgl' : 'webgpu',
				failIfMajorPerformanceCaveat: false,
			});

			this.app.renderer.accessibility.init({
				accessibilityOptions: {
					activateOnTab: false,
					deactivateOnMouseMove: true,
					debug: false,
					enabledByDefault: false,
				},
			});

			this.canvasContainer.appendChild(this.app.canvas);

			this.app.stage.sortableChildren = true;
		} catch (error: any) {
			this.error = 'device-error';
			this._isLoading = false;
			console.error(error);
			return;
		}

		if (import.meta.env.DEV) {
			(globalThis as any).__PIXI_APP__ = this.app;
		}

		try {
			await this.assetsManager.loadAndUse(
				this.app.screen.width >= this._layoutConfig.breakpoints.tablet
					? 'desktop'
					: 'mobile',
			);
		} catch (error: any) {
			this.error = 'device-error';
			this._isLoading = false;
			console.error(error);
			return;
		}

		this._isLoading = false;

		this.gameManager = new GameManager({
			disableLeaderboard: this._disableLeaderboard,
			app: this.app,
			assetsManager: this.assetsManager,
			animationSystem: this.animationSystem,
			layoutConfig: this._layoutConfig,
			messageBroker: this._messageBroker,
		});

		this.app.stage.addChild(this.gameManager.view);

		let minFps = Infinity;
		this.app.stage.addChild(this.fps);
		let lastFPSUpdate = 0;
		let lastMinFPSReset = 0;

		this.app.ticker.add(() => {
			const now = performance.now();

			if (this.fps.visible) {
				if (now - lastFPSUpdate > 50) {
					const newFps = Math.floor(this.app?.ticker.FPS ?? 0);
					if (now - lastMinFPSReset > 2000) {
						minFps = newFps;
						lastMinFPSReset = now;
					} else if (minFps > newFps) {
						minFps = newFps;
					}
					this.fps.text = `${newFps} - ${minFps}`;
					lastFPSUpdate = now;
				}
			}

			this.animationSystem.update(now);
		});

		this.resize();
	}

	get isLoading() {
		return this._isLoading;
	}

	resize = async () => {
		if (!this.app?.renderer) {
			return;
		}
		const canvas = this.app.canvas;
		const parent = canvas.parentElement!;
		const width = Math.min(document.body.clientWidth, parent.clientWidth);
		const height = Math.min(document.body.clientHeight, parent.clientHeight);

		this.app.canvas.style.width = `${width}px`;
		this.app.canvas.style.height = `${height}px`;

		// Update renderer  and navigation screens dimensions
		this.app?.renderer.resize(width, height);

		const currentSize = this.assetsManager.size;

		await this.assetsManager.loadAndUse(
			this.app?.screen.width >= this._layoutConfig.breakpoints.tablet ? 'desktop' : 'mobile',
		);

		// Check again because the app can be destroyed at this moment
		if (!this.app?.renderer || !this.app?.canvas) {
			return;
		}

		this.gameManager?.resize({
			reloadTextures: currentSize !== this.assetsManager.size,
		});

		this.fps.x = 100;
		this.fps.y = height - 50;
	};

	onKeyDown = (event: KeyboardEvent) => {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (key === 'f' && event.shiftKey) {
			this.fps.visible = !this.fps.visible;
		}
	};

	private onWebGLContextLost = (event: Event) => {
		event.preventDefault();
		this.dispose();
		this.error = 'device-error';
	};

	private onWebGLContextRestored = () => {
		this.init();
	};

	private initListeners() {
		this._messageBroker?.start();
		this.app?.canvas.addEventListener('webglcontextlost', this.onWebGLContextLost);
		this.app?.canvas.addEventListener('webglcontextrestored', this.onWebGLContextRestored);

		window.addEventListener('resize', this.throttledResize);
		window.addEventListener('orientationchange', this.throttledResize);
		window.addEventListener('keydown', this.onKeyDown);

		this._cleanUpAdsListener = $effect.root(() => {
			$effect(() => {
				// Track ads
				ads.effectiveSize;

				untrack(() => {
					this.resize();
				});
			});
		});
	}

	async dispose() {
		this._cleanUpAdsListener?.();
		this.app?.canvas.removeEventListener('webglcontextlost', this.onWebGLContextLost);
		this.app?.canvas.removeEventListener('webglcontextrestored', this.onWebGLContextRestored);
		window.removeEventListener('resize', this.throttledResize);
		window.removeEventListener('orientationchange', this.throttledResize);
		window.removeEventListener('keydown', this.onKeyDown);
		this.gameManager?.dispose();
		this.gameManager = null;
		this.animationSystem.reset();
		try {
			this.app?.destroy(true);
		} catch (error) {
			// Ignore
			console.error(error);
		}
		await this.assetsManager.unload();
	}
}
