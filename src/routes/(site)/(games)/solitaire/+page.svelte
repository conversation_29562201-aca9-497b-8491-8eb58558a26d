<script lang="ts">
	import { MetaTags } from 'svelte-meta-tags';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import Solitaire from './Solitaire.svelte';
</script>

<MetaTags
	title="Play Solitaire Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play solitaire online for free. Beautiful solitaire game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/solitaire"
	openGraph={{
		url: 'https://www.lofiandgames.com/solitaire',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-solitaire.png',
				width: 1200,
				height: 630,
				alt: 'Solitaire Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Solitaire on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-solitaire.png',
		site: 'https://www.lofiandgames.com/solitaire',
	}}
/>

<PageTransition>
	<Solitaire ui="all" />
</PageTransition>
