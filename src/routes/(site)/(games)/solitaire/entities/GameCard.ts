import { Container, Sprite, Texture } from 'pixi.js';
import { Easing, Group, Tween } from '@tweenjs/tween.js';
import type { AnimationEntity, CompleteAnimationEntity } from '../systems/AnimationSystem';
import { hero } from '../utils/hero';
import type { CardFace } from '$lib/models/card-game';

type GameCardFace = 'up' | 'down';

interface GameCardParams<T> {
	id: string;
	upTexture: Texture;
	downTexture: Texture;
	face: GameCardFace;
	meta?: T;
	/** Card name for accessibility */
	name: string;
}

export class GameCard<Meta> {
	flipDuration = 250;
	view = new Container();

	id: string;
	upSprite: Sprite;
	downSprite: Sprite;
	meta?: Meta;
	name: string;
	private _face: GameCardFace = 'down';

	constructor({ id, downTexture, upTexture, face, meta, name }: GameCardParams<Meta>) {
		this.id = id;
		this.upSprite = Sprite.from(upTexture);
		this.downSprite = Sprite.from(downTexture);
		this.upSprite.anchor.set(0.5);
		this.downSprite.anchor.set(0.5);
		this.face = face;
		this.meta = meta;
		this.name = name;

		this.view.addChild(this.upSprite, this.downSprite);
		this.view.eventMode = 'static';
		this.view.cursor = 'pointer';

		this.view.interactive = true;
		this.view.accessible = true;
		this.view.accessibleType = 'button';
		this.view.accessibleTitle = this.name;
		this.view.tabIndex = 0;
	}

	get face() {
		return this._face;
	}

	set face(newFace: GameCardFace) {
		this._face = newFace;
		this.downSprite.renderable = newFace === 'down';
		this.upSprite.renderable = newFace === 'up';
	}

	flip(toFace: GameCardFace, duration = this.flipDuration): AnimationEntity<Group> | undefined {
		if (toFace === this.face && this.view.scale.x === 1) {
			return;
		}

		if (duration === 0) {
			this.face = toFace;
			this.view.scale.x = 1;
			return;
		}

		const group = new Group();

		if (this.face !== toFace) {
			const rotate180 = new Tween({
				scaleX: this.view.scale.x,
			})
				.to(
					{
						scaleX: 0,
					},
					duration / 2,
				)
				.easing(Easing.Quadratic.In)
				.onUpdate(({ scaleX }) => {
					this.view.scale.x = scaleX;
				})
				.onComplete(() => {
					this.face = toFace;
				});

			const rotateBack = new Tween({
				scaleX: 0,
			})
				.to(
					{
						scaleX: 1,
					},
					duration / 2,
				)
				.easing(Easing.Quadratic.Out)
				.onUpdate(({ scaleX }) => {
					this.view.scale.x = scaleX;
				});

			rotate180.chain(rotateBack);

			group.add(rotate180, rotateBack);
		} else {
			const rotateUntilFinished = new Tween({
				scaleX: this.view.scale.x,
			})
				.to(
					{
						scaleX: 1,
					},
					duration / 2,
				)
				.easing(Easing.Quadratic.Out)
				.onUpdate(({ scaleX }) => {
					this.view.scale.x = scaleX;
				});

			group.add(rotateUntilFinished);
		}

		return {
			id: `flip-${this.id}`,
			animation: group,
			meta: {
				fromFace: this.face,
				toFace,
				...this.meta,
			},
			resolveConflict: resolveCardFlipConflict,
		};
	}

	setSize(size: { width: number; height: number }) {
		this.downSprite.setSize(size);
		this.upSprite.setSize(size);
	}

	move(
		position: { x: number; y: number; zIndex: number },
		duration: number,
		easingFunction = Easing.Quadratic.InOut,
	): AnimationEntity<Tween, typeof position & { meta: Meta }> | undefined {
		// Same position, do not move
		if (
			Math.abs(this.view.x - position.x) <= 0.001 &&
			Math.abs(this.view.y - position.y) <= 0.001
		) {
			this.view.zIndex = position.zIndex;
			this.view.x = position.x;
			this.view.y = position.y;
			return undefined;
		}

		// Move instantly
		if (duration === 0) {
			this.view.zIndex = position.zIndex;
			this.view.position.set(position.x, position.y);
			return;
		}

		const heroAnimation = hero({
			target: this.view,
			duration,
			easingFunction,
			updateToFinalPosition: (target) => {
				target.zIndex = position.zIndex;
				target.position.set(position.x, position.y);
			},
		});

		heroAnimation.onComplete(() => {
			this.view.zIndex = position.zIndex;
		});

		const animation: AnimationEntity<Tween> = {
			id: `move-${this.id}`,
			animation: heroAnimation,
			meta: {
				...position,
				meta: this.meta,
			},
			resolveConflict: resolveCardMoveConflict,
		};

		return animation;
	}

	clone(interactive = false) {
		const card = new GameCard({
			id: this.id,
			downTexture: this.downSprite.texture,
			upTexture: this.upSprite.texture,
			face: this.face,
			meta: this.meta,
			name: this.name,
		});

		if (!interactive) {
			card.view.eventMode = 'none';
			card.view.cursor = 'default';
		}

		card.view.scale.set(this.view.scale.x, this.view.scale.y);
		card.view.setSize(this.view.getSize());
		card.view.position.set(this.view.position.x, this.view.position.y);
		card.view.zIndex = this.view.zIndex;

		return card;
	}
}

function resolveCardMoveConflict(
	playingAnimation: CompleteAnimationEntity<{ x: number; y: number }>,
	newAnimation: CompleteAnimationEntity<{ x: number; y: number }>,
): CompleteAnimationEntity<{ x: number; y: number }> {
	if (
		Math.abs(playingAnimation.meta!.x - newAnimation.meta!.x) < 0.01 &&
		Math.abs(playingAnimation.meta!.y - newAnimation.meta!.y) < 0.01
	) {
		return playingAnimation;
	}

	return newAnimation;
}

function resolveCardFlipConflict(
	playingAnimation: CompleteAnimationEntity<{ fromFace: CardFace; toFace: CardFace }>,
	newAnimation: CompleteAnimationEntity<{ fromFace: CardFace; toFace: CardFace }>,
): CompleteAnimationEntity<{ fromFace: CardFace; toFace: CardFace }> {
	if (playingAnimation.meta?.toFace === newAnimation.meta?.toFace) {
		return playingAnimation;
	}

	return newAnimation;
}
