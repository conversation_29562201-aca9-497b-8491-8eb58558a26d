import { Application, Container, type FederatedPointerEvent, Sprite } from 'pixi.js';
import type { CardGameAssetsManager } from '../assets/CardGameAssetsManager';
import type { AnimationEntity, AnimationSystem } from '../systems/AnimationSystem';
import type { GameCard } from './GameCard';
import type { Card, CardSuit, CardValue } from '$lib/models/card-game';
import { getCardId } from '../utils/getCardId';
import { Easing, type Group, type Tween } from '@tweenjs/tween.js';
import { SolitaireGame, type SolitaireStack } from '../model/SolitaireGame';
import { ads } from '$lib/stores/ads.svelte';
import {
	buildLayoutConfig,
	type AllScreensSolitaireGameLayoutConfig,
	type SolitaireGameLayoutConfig,
} from '../utils/buildLayoutConfig';

const movingCardsZIndex = 1000;
const draggingCardsZIndex = 1500;
const initialTabIndex = 1;

/**
 * Offsets to differentiate a tap from a drag event.
 * Below the threshold, it's considered a tap, above it is a drag.
 **/
const tapOrDragOffsets = {
	duration: 300,
	distance: 5,
};

interface Config extends SolitaireGameLayoutConfig {
	boardWidth: number;
	boardHeight: number;
	itemSize: { width: number; height: number };
	moveDuration: number;
	flipDuration: number;
}

interface SolitaireGameLayoutParams {
	app: Application;
	assetsManager: CardGameAssetsManager;
	animationSystem: AnimationSystem;
	cards: Record<`${CardSuit}-${CardValue}`, GameCard<Card>>;
	callbacks: SolitaireGameLayoutCallbacks;
	layoutConfig?: AllScreensSolitaireGameLayoutConfig;
}

interface SolitaireGameLayoutCallbacks {
	onDeckTap: () => void;
	onCardTap: (card: Card, cardIndex: number, stack: SolitaireStack) => void;
	/** @returns true if the drop changed the game state */
	onCardsDrop: (cardIndex: number, from: SolitaireStack, to: SolitaireStack) => boolean;
	/** @returns true if the drag is allowed to start */
	onDragStart: () => boolean;
}

type CardId = `${CardSuit}-${CardValue}`;

const regionsZIndex = 2000;

export class SolitaireGameLayout {
	app: Application;
	view = new Container();
	animationSystem: AnimationSystem;
	cards: Record<CardId, GameCard<Card>>;
	assetsManager: CardGameAssetsManager;
	layoutConfig: AllScreensSolitaireGameLayoutConfig;

	private _settings = {
		alignDeckOnRight: false,
		alignNextCards: false,
		bouncyCards: false,
	};
	private lastSyncedGame: SolitaireGame | null = null;
	private deckRegion = new Sprite();
	private topRow: Array<Sprite>;
	private bottomRow: Array<Sprite>;
	private viewOffset = {
		x: 0,
		y: 0,
	};

	sprites: {
		deck: Sprite;
		nextCards: Sprite;
		spacer: Sprite;
		slot1: Sprite;
		slot2: Sprite;
		slot3: Sprite;
		slot4: Sprite;
		stack1: Sprite;
		stack2: Sprite;
		stack3: Sprite;
		stack4: Sprite;
		stack5: Sprite;
		stack6: Sprite;
		stack7: Sprite;
	};

	callbacks: SolitaireGameLayoutCallbacks;
	private movingCardsZIndexOffset = 0;
	movingCardIds: CardId[] = [];
	pointerDownInfo: {
		/**
		 * Prevent multi touch dragging bugs by identifying
		 * the pointer id before handling the event
		 **/
		pointerId: number;
		card: Card;
		index: number;
		stackName: SolitaireStack;
		startedAt: number;
		globalX: number;
		globalY: number;
		dx: number;
		dy: number;
		draggingCards: GameCard<Card>[];
		draggingCardsDistances: { dx: number; dy: number }[];
		startedDragging: boolean;
	} | null = null;

	config!: Config;

	constructor({
		app,
		assetsManager,
		animationSystem,
		cards,
		callbacks,
		layoutConfig = buildLayoutConfig(),
	}: SolitaireGameLayoutParams) {
		this.callbacks = callbacks;
		this.cards = cards;
		this.app = app;
		this.animationSystem = animationSystem;
		this.assetsManager = assetsManager;
		this.layoutConfig = layoutConfig;
		const slotTexture = assetsManager.slotTexture();
		this.updateConfig();

		this.sprites = {
			deck: Sprite.from(assetsManager.deckTexture()),
			spacer: Sprite.from(slotTexture),
			nextCards: Sprite.from(slotTexture),
			slot1: Sprite.from(assetsManager.foundationTexture()),
			slot2: Sprite.from(assetsManager.foundationTexture()),
			slot3: Sprite.from(assetsManager.foundationTexture()),
			slot4: Sprite.from(assetsManager.foundationTexture()),
			stack1: Sprite.from(slotTexture),
			stack2: Sprite.from(slotTexture),
			stack3: Sprite.from(slotTexture),
			stack4: Sprite.from(slotTexture),
			stack5: Sprite.from(slotTexture),
			stack6: Sprite.from(slotTexture),
			stack7: Sprite.from(slotTexture),
		};

		this.sprites.spacer.visible = false;

		this.view.addChild(this.deckRegion);
		this.view.addChild(...Object.values(this.sprites));
		this.view.addChild(...Object.values(this.cards).map((card) => card.view));

		this.deckRegion.anchor.set(0.5);
		Object.values(this.sprites).forEach((sprite) => {
			sprite.anchor.set(0.5);
		});

		this.topRow = [
			this.sprites.deck,
			this.sprites.nextCards,
			this.sprites.spacer,
			this.sprites.slot1,
			this.sprites.slot2,
			this.sprites.slot3,
			this.sprites.slot4,
		];

		this.bottomRow = [
			this.sprites.stack1,
			this.sprites.stack2,
			this.sprites.stack3,
			this.sprites.stack4,
			this.sprites.stack5,
			this.sprites.stack6,
			this.sprites.stack7,
		];

		this.view.eventMode = 'static';
		this.view.hitArea = this.app.screen;
		this.view.sortableChildren = true;
		this.centralize();
		this.resizeAll();
		this.placeSlots();
		this.addDeckListener();
		this.addGlobalDragListeners();
		this.addAccessibilityListeners();
		this.moveToDeck(
			Object.values(this.cards).map((gameCard) => gameCard.meta!),
			0,
		);
	}

	get settings() {
		return this._settings;
	}

	set settings(settings: typeof this._settings) {
		this._settings = settings;
		this.topRow = settings.alignDeckOnRight
			? settings.alignNextCards
				? [
						this.sprites.slot1,
						this.sprites.slot2,
						this.sprites.slot3,
						this.sprites.slot4,
						this.sprites.spacer,
						this.sprites.nextCards,
						this.sprites.deck,
					]
				: [
						this.sprites.slot1,
						this.sprites.slot2,
						this.sprites.slot3,
						this.sprites.slot4,
						this.sprites.nextCards,
						this.sprites.spacer,
						this.sprites.deck,
					]
			: [
					this.sprites.deck,
					this.sprites.nextCards,
					this.sprites.spacer,
					this.sprites.slot1,
					this.sprites.slot2,
					this.sprites.slot3,
					this.sprites.slot4,
				];

		this.handleResize({ reloadTextures: false, game: this.lastSyncedGame });
	}

	handleResize({
		reloadTextures,
		game,
	}: {
		reloadTextures: boolean;
		game?: SolitaireGame | null;
	}) {
		if (reloadTextures) {
			this.reloadTextures();
		}
		this.updateConfig();
		this.centralize();
		this.resizeAll();
		this.placeSlots();

		if (game) {
			this.sync({ game, animated: false });
		}
	}

	private reloadTextures() {
		[this.sprites.slot1, this.sprites.slot2, this.sprites.slot3, this.sprites.slot4].forEach(
			(sprite) => {
				sprite.texture = this.assetsManager.foundationTexture();
			},
		);
		this.bottomRow.forEach((sprite) => {
			sprite.texture = this.assetsManager.slotTexture();
		});
		this.sprites.nextCards.texture = this.assetsManager.slotTexture();
		this.sprites.deck.texture = this.assetsManager.deckTexture();
	}

	private centralize() {
		const config = this.config;
		let offsetX = Math.max(0, (this.app.screen.width - config.boardWidth) / 2);

		const adsMargin = 30;
		const belowAdsOffsetX = ads.canShowGameAds
			? Math.max(
					0,
					-(
						this.app.screen.width -
						(offsetX + config.boardWidth) -
						config.adsWidth -
						adsMargin
					),
				)
			: 0;

		const willBeBellowAds = belowAdsOffsetX > 0;

		if (willBeBellowAds) {
			offsetX -= config.adsWidth / 2;
		} else {
			offsetX -= belowAdsOffsetX;
		}

		const offsetY = ads.effectiveSize.height;

		this.viewOffset = {
			x: offsetX,
			y: config.paddingTop + offsetY,
		};
	}

	private resizeAll() {
		const size = this.config.itemSize;
		const setSize = (sprite: Sprite) => {
			sprite.setSize(size);
		};

		this.deckRegion.setSize(size);
		this.topRow.forEach(setSize);
		this.bottomRow.forEach(setSize);
		Object.values(this.cards).forEach((card) => card.setSize(size));
	}

	private placeSlots() {
		const { width, height } = this.config.itemSize;
		const config = this.config;
		const offsetX = width / 2 + this.viewOffset.x;
		const offsetY = height / 2 + this.viewOffset.y;

		this.topRow.forEach((entity, i) => {
			entity.position.set(offsetX + (width + config.columnGap) * i, offsetY);
		});
		this.bottomRow.forEach((entity, i) => {
			entity.position.set(
				offsetX + (width + config.columnGap) * i,
				height + config.rowGap + offsetY,
			);
		});
		this.deckRegion.position.set(this.sprites.deck.x, this.sprites.deck.y);
	}

	private syncAccessibility(game: SolitaireGame) {
		[
			game.deck,
			game.nextCards,
			game.slot1,
			game.slot2,
			game.slot3,
			game.slot4,
			game.stack1,
			game.stack2,
			game.stack3,
			game.stack4,
			game.stack5,
			game.stack6,
			game.stack7,
		]
			.flatMap((_) => _)
			.map((card) => this.getGameCard(card))
			.forEach((gameCard) => {
				gameCard.view.accessible = false;
			});

		if (!this.app.renderer?.accessibility?.isActive) {
			return;
		}

		if (game.nextCards.length > 0) {
			const lastCard = this.getGameCard(game.nextCards[game.nextCards.length - 1]);

			lastCard.view.accessible = true;

			if (lastCard.view._accessibleDiv) {
				lastCard.view._accessibleDiv.tabIndex = initialTabIndex + 1;
			}
		}

		let tabIndex = initialTabIndex + 2;

		[game.slot1, game.slot2, game.slot3, game.slot4]
			.map((slot) => slot[slot.length - 1])
			.filter(Boolean)
			.map((card) => this.getGameCard(card))
			.forEach((lastCard) => {
				lastCard.view.accessible = true;

				if (lastCard.view._accessibleDiv) {
					lastCard.view._accessibleDiv.tabIndex = tabIndex++;
				}
			});

		[game.stack1, game.stack2, game.stack3, game.stack4, game.stack5, game.stack6, game.stack7]
			.flatMap((_) => _)
			.forEach((card) => {
				const gameCard = this.getGameCard(card);

				/**
				 * Use card.face because it's the correct current state,
				 * since gameCard.face can be changed after the animation is finished
				 */
				if (card.face === 'up') {
					gameCard.view.accessible = true;

					if (gameCard.view._accessibleDiv) {
						gameCard.view._accessibleDiv.tabIndex = tabIndex++;
					}
				}
			});
	}

	sync({
		game,
		movingCards,
		changedStacks,
		animated = true,
		keepCardListeners = false,
		movingCardsZIndexOffset = 0,
	}: {
		game: SolitaireGame;
		animated?: boolean;
		movingCards?: Card[];
		changedStacks?: SolitaireStack[];
		keepCardListeners?: boolean;
		movingCardsZIndexOffset?: number;
	}) {
		this.movingCardsZIndexOffset = movingCardsZIndexOffset;
		this.lastSyncedGame = game;
		this.movingCardIds = movingCards?.map(getCardId) ?? [];
		if (!keepCardListeners) {
			this.removeCardListeners();
			this.addCardListeners(game);
		}
		this.cancelDrag();
		const moveDuration = animated ? this.config.moveDuration : 0;
		const flipDuration = animated ? this.config.flipDuration : 0;

		const animationCandidates = [
			{
				stack: 'deck',
				move: () => this.moveToDeck(game.deck, moveDuration),
			},
			{
				stack: 'next-cards',
				move: () => this.moveToNextCards(game.nextCards, moveDuration),
			},
			{
				stack: 'slot1',
				move: () =>
					this.moveToSlot({
						slot: this.sprites.slot1,
						cards: game.slot1,
						duration: moveDuration,
					}),
			},
			{
				stack: 'slot2',
				move: () =>
					this.moveToSlot({
						slot: this.sprites.slot2,
						cards: game.slot2,
						duration: moveDuration,
					}),
			},
			{
				stack: 'slot3',
				move: () =>
					this.moveToSlot({
						slot: this.sprites.slot3,
						cards: game.slot3,
						duration: moveDuration,
					}),
			},
			{
				stack: 'slot4',
				move: () =>
					this.moveToSlot({
						slot: this.sprites.slot4,
						cards: game.slot4,
						duration: moveDuration,
					}),
			},
			{
				stack: 'stack1',
				move: () =>
					this.moveToStack({
						stack: this.sprites.stack1,
						cards: game.stack1,
						duration: moveDuration,
					}),
			},
			{
				stack: 'stack2',
				move: () =>
					this.moveToStack({
						stack: this.sprites.stack2,
						cards: game.stack2,
						duration: moveDuration,
					}),
			},
			{
				stack: 'stack3',
				move: () =>
					this.moveToStack({
						stack: this.sprites.stack3,
						cards: game.stack3,
						duration: moveDuration,
					}),
			},
			{
				stack: 'stack4',
				move: () =>
					this.moveToStack({
						stack: this.sprites.stack4,
						cards: game.stack4,
						duration: moveDuration,
					}),
			},
			{
				stack: 'stack5',
				move: () =>
					this.moveToStack({
						stack: this.sprites.stack5,
						cards: game.stack5,
						duration: moveDuration,
					}),
			},
			{
				stack: 'stack6',
				move: () =>
					this.moveToStack({
						stack: this.sprites.stack6,
						cards: game.stack6,
						duration: moveDuration,
					}),
			},
			{
				stack: 'stack7',
				move: () =>
					this.moveToStack({
						stack: this.sprites.stack7,
						cards: game.stack7,
						duration: moveDuration,
					}),
			},
		] as Array<{ stack: SolitaireStack; move: () => AnimationEntity<Tween>[] }>;

		const moveAnimations = (
			changedStacks
				? animationCandidates.filter((animation) => changedStacks.includes(animation.stack))
				: animationCandidates
		)
			.map(({ move }) => move())
			.flatMap((_) => _);

		// Sync card faces
		const flipAnimations = [
			game.deck,
			game.nextCards,
			game.slot1,
			game.slot2,
			game.slot3,
			game.slot3,
			game.stack1,
			game.stack2,
			game.stack3,
			game.stack4,
			game.stack5,
			game.stack6,
			game.stack7,
		]
			.flatMap((_) => _)
			.map((card) => this.syncCardFace(card, flipDuration))
			.filter(Boolean) as AnimationEntity<Group>[];

		this.animationSystem.add(...moveAnimations);
		this.animationSystem.add(...flipAnimations);

		this.movingCardIds = [];

		this.syncAccessibility(game);
	}

	private syncCardFace(card: Card, duration: number): AnimationEntity<Group> | undefined {
		const gameCard = this.getGameCard(card);

		return gameCard.flip(card.face, duration);
	}

	private moveToDeck(cards: Card[], duration: number): AnimationEntity<Tween>[] {
		return cards
			.map((card, index) => {
				const gameCard = this.getGameCard(card);
				const deck = this.sprites.deck;
				const isMoving = this.movingCardIds.includes(gameCard.id as CardId);

				const animation = gameCard.move(
					{
						x: deck.x,
						y: deck.y,
						zIndex:
							(isMoving ? movingCardsZIndex + this.movingCardsZIndexOffset : 0) +
							gameCard.view.zIndex, // Keep zIndex and update it just later
					},
					duration,
					this.settings.bouncyCards ? Easing.Back.Out : undefined,
				);

				// Update zIndex to the correct one
				this.scheduleZIndexReset({
					animation,
					gameCard,
					zIndex: index + 1,
				});

				return animation;
			})
			.filter(Boolean) as AnimationEntity<Tween>[];
	}

	private moveToStack({
		stack,
		cards,
		duration,
	}: {
		stack: Sprite;
		cards: Card[];
		duration: number;
	}): AnimationEntity<Tween>[] {
		const firstCardUpIndex = cards.findIndex((card) => card.face === 'up');

		return cards
			.map((card, index) => {
				const gameCard = this.getGameCard(card);
				const isMoving = this.movingCardIds.includes(gameCard.id as CardId);
				const zIndex = index + 1;
				const offset = this.getStackCardOffsetY(index, cards.length, firstCardUpIndex);

				const animation = gameCard.move(
					{
						x: stack.x,
						y: stack.y + offset,
						zIndex:
							(isMoving ? movingCardsZIndex + this.movingCardsZIndexOffset : 0) +
							zIndex,
					},
					duration,
					this.settings.bouncyCards ? Easing.Back.Out : undefined,
				);

				if (isMoving) {
					this.scheduleZIndexReset({
						animation,
						gameCard,
						zIndex,
					});
				}

				return animation;
			})
			.filter(Boolean) as AnimationEntity<Tween>[];
	}

	private moveToSlot({
		slot,
		cards,
		duration,
	}: {
		slot: Sprite;
		cards: Card[];
		duration: number;
	}): AnimationEntity<Tween>[] {
		return cards
			.map((card, index) => {
				const gameCard = this.getGameCard(card);
				const isMoving = this.movingCardIds.includes(gameCard.id as CardId);
				const zIndex = index + 1;

				const animation = gameCard.move(
					{
						x: slot.x,
						y: slot.y,
						zIndex:
							(isMoving ? movingCardsZIndex + this.movingCardsZIndexOffset : 0) +
							zIndex,
					},
					duration,
					this.settings.bouncyCards ? Easing.Back.Out : undefined,
				);

				if (isMoving) {
					this.scheduleZIndexReset({
						animation,
						gameCard,
						zIndex,
					});
				}

				return animation;
			})
			.filter(Boolean) as AnimationEntity<Tween>[];
	}

	private moveToNextCards(cards: Card[], duration: number): AnimationEntity<Tween>[] {
		return cards
			.map((card, index) => {
				const gameCard = this.getGameCard(card);
				const isMoving = this.movingCardIds.includes(gameCard.id as CardId);
				const nextCards = this.sprites.nextCards;
				const x = nextCards.x + this.getNextCardsOffsetX(index, cards.length);
				const zIndex = index + 1;

				const animation = gameCard.move(
					{
						x,
						y: nextCards.y,
						zIndex:
							(isMoving ? movingCardsZIndex + this.movingCardsZIndexOffset : 0) +
							zIndex,
					},
					duration,
					this.settings.bouncyCards ? Easing.Back.Out : undefined,
				);

				if (isMoving) {
					this.scheduleZIndexReset({
						animation,
						gameCard,
						zIndex,
					});
				}

				return animation;
			})
			.filter(Boolean) as AnimationEntity<Tween>[];
	}

	private getGameCard(card: Card) {
		return this.cards[getCardId(card)];
	}

	private getNextCardsOffsetX(index: number, length: number): number {
		if (index === 0 || this.settings.alignNextCards) {
			return 0;
		}

		const offsetX = this.config.itemSize.width * this.config.nextCardsOffsetXPercentage;
		const lastIndex = length - 1;
		const indexBeforeLast = lastIndex - 1;

		if (length === 2 && index === 1) {
			return offsetX;
		}

		if (index === lastIndex) {
			return offsetX * 2;
		}
		if (index === indexBeforeLast) {
			return offsetX;
		}

		return 0;
	}

	private getStackCardOffsetY(index: number, length: number, firstCardUpIndex: number): number {
		const {
			paddingBottom,
			stackCardsUpOffsetYPercentage,
			stackCardsDownOffsetYPercentage,
			minStackCardsDownOffsetYPercentage,
			boardHeight,
			itemSize,
		} = this.config;
		if (firstCardUpIndex < 0) {
			firstCardUpIndex = length;
		}
		const cardsDown = firstCardUpIndex;
		const cardsUp = Math.max(0, length - cardsDown);
		const stack1 = this.sprites.stack1;
		const availableSpace = Math.max(
			0,
			Math.floor(boardHeight - stack1.y - itemSize.height / 2 - paddingBottom),
		);
		const maxOffset = availableSpace / Math.max(length, 1);
		const desiredCardUpOffset = itemSize.height * stackCardsUpOffsetYPercentage;
		const desiredCardDownOffset = itemSize.height * stackCardsDownOffsetYPercentage;
		const overflowAmount = Math.max(
			0,
			desiredCardDownOffset * cardsDown + desiredCardUpOffset * cardsUp - availableSpace,
		);
		const cardDownOffset = Math.max(
			itemSize.height * minStackCardsDownOffsetYPercentage,
			Math.min(desiredCardDownOffset - overflowAmount / Math.max(1, cardsDown), maxOffset),
		);
		const maxUpOffset = (availableSpace - cardDownOffset * cardsDown) / (cardsUp || 1);
		const cardUpOffset = Math.min(desiredCardUpOffset, maxUpOffset);

		if (index <= firstCardUpIndex) {
			return index * cardDownOffset;
		}

		return cardDownOffset * cardsDown + (index - firstCardUpIndex) * cardUpOffset;
	}

	private scheduleZIndexReset({
		gameCard,
		animation,
		zIndex,
	}: {
		gameCard: GameCard<Card>;
		animation?: AnimationEntity<Tween>;
		zIndex: number;
	}) {
		if (animation) {
			animation.animation.onComplete(() => {
				gameCard.view.zIndex = zIndex;
			});
		} else {
			gameCard.view.zIndex = zIndex;
		}
	}

	private addDeckListener() {
		const deck = this.deckRegion;
		deck.eventMode = 'static';
		deck.cursor = 'pointer';
		deck.zIndex = regionsZIndex;
		deck.addEventListener('pointertap', () => {
			this.callbacks.onDeckTap();
		});
		deck.accessible = true;
		deck.accessibleTitle = 'Draw card';
		deck.tabIndex = initialTabIndex;
	}

	private onDragMove = (event: FederatedPointerEvent) => {
		if (event.pointerId !== this.pointerDownInfo?.pointerId) {
			return;
		}

		if (this.isDrag(event, this.pointerDownInfo)) {
			if (this.pointerDownInfo) {
				const { draggingCards, draggingCardsDistances, dx, dy, startedDragging } =
					this.pointerDownInfo;

				if (!startedDragging) {
					/** onDragStart */

					if (!this.callbacks.onDragStart()) {
						return;
					}

					this.animationSystem.removeAndCompleteById(
						...draggingCards.map((card) => `move-${card.id}`),
					);
				}

				draggingCards.forEach((card, index) => {
					card.view.parent?.toLocal(
						{
							x: event.global.x - dx - draggingCardsDistances[index].dx,
							y: event.global.y - dy - draggingCardsDistances[index].dy,
						},
						undefined,
						card.view.position,
					);

					card.view.zIndex = draggingCardsZIndex + index;
				});

				this.pointerDownInfo.startedDragging = true;
			}
		}
	};

	private isDrag(
		event: FederatedPointerEvent,
		pointerDownInfo?: typeof this.pointerDownInfo | null,
	) {
		if (!pointerDownInfo) {
			return false;
		}

		return (
			Math.abs(event.globalX - pointerDownInfo.globalX) > tapOrDragOffsets.distance ||
			Math.abs(event.globalY - pointerDownInfo.globalY) > tapOrDragOffsets.distance ||
			performance.now() - pointerDownInfo.startedAt > tapOrDragOffsets.duration
		);
	}

	private getNearestStack(
		gameCard: GameCard<Card>,
	): Omit<SolitaireStack, 'deck' | 'next-cards'> | null {
		const cardStacks = [
			{
				stack: this.sprites.slot1,
				stackName: 'slot1',
			},
			{
				stack: this.sprites.slot2,
				stackName: 'slot2',
			},
			{
				stack: this.sprites.slot3,
				stackName: 'slot3',
			},
			{
				stack: this.sprites.slot4,
				stackName: 'slot4',
			},
			{
				stack: this.sprites.stack1,
				stackName: 'stack1',
				infiniteBottom: true,
			},
			{
				stack: this.sprites.stack2,
				stackName: 'stack2',
				infiniteBottom: true,
			},
			{
				stack: this.sprites.stack3,
				stackName: 'stack3',
				infiniteBottom: true,
			},
			{
				stack: this.sprites.stack4,
				stackName: 'stack4',
				infiniteBottom: true,
			},
			{
				stack: this.sprites.stack5,
				stackName: 'stack5',
				infiniteBottom: true,
			},
			{
				stack: this.sprites.stack6,
				stackName: 'stack6',
				infiniteBottom: true,
			},
			{
				stack: this.sprites.stack7,
				stackName: 'stack7',
				infiniteBottom: true,
			},
		] as Array<{ stack: Sprite; stackName: SolitaireStack; infiniteBottom?: boolean }>;
		const { x: cardX, y: cardY, width: cardWidth, height: cardHeight } = gameCard.view;

		const stackName = cardStacks
			.map(({ stack, stackName, infiniteBottom }) => {
				const { x, y, width } = stack;
				const height = infiniteBottom ? Infinity : stack.height;

				const intersects =
					cardX < x + width &&
					cardX + cardWidth > x &&
					cardY < y + height &&
					cardY + cardHeight > y;

				if (intersects) {
					const x1 = Math.max(cardX, x);
					const x2 = Math.min(cardX + cardWidth, x + width);
					const y1 = Math.max(cardY, y);
					const y2 = Math.min(cardY + cardHeight, y + height);

					const area = (x2 - x1) * (y2 - y1);

					return {
						stackName,
						area,
					};
				}

				return {
					stackName,
					area: 0,
				};
			})
			.sort((a, b) => b.area - a.area)
			.find((intersection) => intersection.area > 0)?.stackName;

		return stackName ?? null;
	}

	private onDragEnd = (event: FederatedPointerEvent) => {
		if (event.pointerId !== this.pointerDownInfo?.pointerId) {
			return;
		}

		const pointerDownInfo = this.pointerDownInfo;

		if (pointerDownInfo) {
			const { card, index, stackName: from, draggingCards } = pointerDownInfo;

			if (this.isDrag(event, pointerDownInfo)) {
				const to = this.getNearestStack(draggingCards[0]) as SolitaireStack;

				if (to !== null && to !== 'deck' && to !== 'next-cards') {
					if (!this.callbacks.onCardsDrop(index, from, to as SolitaireStack)) {
						return this.cancelDrag();
					}
				}

				return this.cancelDrag();
			} else {
				this.cancelDrag();
				// Handle card tap
				this.callbacks.onCardTap(card, index, from);
				return;
			}
		}

		this.cancelDrag();
	};

	private cancelDrag() {
		if (!this.pointerDownInfo) {
			return;
		}

		this.view.off('pointermove', this.onDragMove);

		if (this.lastSyncedGame && this.pointerDownInfo) {
			const movingCards = this.pointerDownInfo.draggingCards.map(
				(card) => card.meta,
			) as Card[];

			this.pointerDownInfo = null;

			this.sync({
				game: this.lastSyncedGame,
				movingCards,
			});
		}
	}

	private onKeyDown = (e: KeyboardEvent) => {
		if (e.key === 'Tab') {
			if (!this.app.renderer?.accessibility?.isActive) {
				this.app.renderer?.accessibility?.setAccessibilityEnabled(true);
			}

			if (this.lastSyncedGame) {
				this.sync({
					game: this.lastSyncedGame,
				});
			}
		}
	};

	private onMouseMove = () => {
		if (this.app.renderer?.accessibility?.isActive) {
			this.app.renderer?.accessibility?.setAccessibilityEnabled(false);

			if (this.lastSyncedGame) {
				this.sync({
					game: this.lastSyncedGame,
				});
			}
		}
	};

	private addAccessibilityListeners() {
		window.addEventListener('keydown', this.onKeyDown);
		window.addEventListener('mousemove', this.onMouseMove);
	}

	private removeAccessibilityListeners() {
		window.removeEventListener('keydown', this.onKeyDown);
		window.removeEventListener('mousemove', this.onMouseMove);
	}

	private addGlobalDragListeners() {
		this.view.on('pointermove', this.onDragMove);
		this.view.on('pointerup', this.onDragEnd);
		this.view.on('pointerupoutside', this.onDragEnd);
	}

	private removeGlobalDragListeners() {
		this.view.off('pointermove', this.onDragMove);
		this.view.off('pointerup', this.onDragEnd);
		this.view.off('pointerupoutside', this.onDragEnd);
	}

	private addCardListeners(game: SolitaireGame) {
		const cardStacks = [
			{
				stack: game.deck,
				stackName: 'deck',
			},
			{
				stack: game.nextCards,
				stackName: 'next-cards',
			},
			{
				stack: game.slot1,
				stackName: 'slot1',
			},
			{
				stack: game.slot2,
				stackName: 'slot2',
			},
			{
				stack: game.slot3,
				stackName: 'slot3',
			},
			{
				stack: game.slot4,
				stackName: 'slot4',
			},
			{
				stack: game.stack1,
				stackName: 'stack1',
			},
			{
				stack: game.stack2,
				stackName: 'stack2',
			},
			{
				stack: game.stack3,
				stackName: 'stack3',
			},
			{
				stack: game.stack4,
				stackName: 'stack4',
			},
			{
				stack: game.stack5,
				stackName: 'stack5',
			},
			{
				stack: game.stack6,
				stackName: 'stack6',
			},
			{
				stack: game.stack7,
				stackName: 'stack7',
			},
		] as Array<{ stack: Card[]; stackName: SolitaireStack }>;

		cardStacks.forEach(({ stack, stackName }) => {
			const firstCardUpIndex = stack.findIndex((card) => card.face === 'up');

			stack.forEach((card, index) => {
				const gameCard = this.getGameCard(card);
				const onTap = () => {
					this.callbacks.onCardTap(card, index, stackName);
				};
				const canHandleDrag =
					card.face === 'up' &&
					(stackName === 'next-cards' ? index === stack.length - 1 : true) &&
					!this.app.renderer?.accessibility?.isActive;

				if (canHandleDrag) {
					/** onDragStart */
					gameCard.view.on('pointerdown', (event: FederatedPointerEvent) => {
						/**
						 * Handle one drag gesture at a time, mainly on multi touch devices */
						if (this.pointerDownInfo) {
							return;
						}

						const distance = event.getLocalPosition(
							gameCard.view,
							undefined,
							event.global,
						);
						const draggingCards = stack
							.slice(index)
							.map((card) => this.getGameCard(card));
						const firstCardOffsetY = this.getStackCardOffsetY(
							index,
							stack.length,
							firstCardUpIndex,
						);

						draggingCards.forEach((card, i) => {
							card.view.zIndex = draggingCardsZIndex + i;
						});

						this.pointerDownInfo = {
							startedDragging: false,
							pointerId: event.pointerId,
							card,
							index,
							stackName,
							startedAt: performance.now(),
							globalX: event.global.x,
							globalY: event.global.y,
							dx: distance.x,
							dy: distance.y,
							draggingCards,
							draggingCardsDistances: draggingCards.map(
								(_card, draggingCardIndex) => {
									if (
										(
											[
												'deck',
												'slot1',
												'slot2',
												'slot3',
												'slot4',
											] as SolitaireStack[]
										).includes(stackName)
									) {
										return {
											dx: 0,
											dy: 0,
										};
									}

									return {
										dx: 0,
										dy:
											firstCardOffsetY -
											this.getStackCardOffsetY(
												index + draggingCardIndex,
												stack.length,
												firstCardUpIndex,
											),
									};
								},
							),
						};

						this.view.on('pointermove', this.onDragMove);
					});
				} else {
					gameCard.view.on('pointertap', onTap);
				}
			});
		});
	}

	private removeCardListeners() {
		Object.values(this.cards).forEach((card) => card.view.removeAllListeners());
	}

	private removeListeners() {
		this.removeCardListeners();
		this.sprites.deck.removeAllListeners();
		this.removeGlobalDragListeners();
		this.removeAccessibilityListeners();
	}

	private updateConfig() {
		const screenWidth = this.app.screen.width;
		const layoutConfig =
			screenWidth >= this.layoutConfig.breakpoints.desktop
				? this.layoutConfig.desktop
				: screenWidth >= this.layoutConfig.breakpoints.tablet
					? this.layoutConfig.tablet
					: this.layoutConfig.mobile;
		const { paddingX, adsWidth } = layoutConfig;
		let rowGap = layoutConfig.rowGap;
		let columnGap = layoutConfig.columnGap;

		let boardWidth =
			Math.min(
				screenWidth - (ads.canShowGameAds ? adsWidth : 0),
				layoutConfig.maxWidth + 2 * paddingX,
			) -
			2 * paddingX;
		const boardHeight = this.app.screen.height;

		const navbarHeight = 64;

		if (boardHeight <= this.layoutConfig.breakpoints.tablet - navbarHeight) {
			boardWidth = Math.min(boardWidth, this.layoutConfig.breakpoints.tablet);
			rowGap = this.layoutConfig.tablet.columnGap;
			columnGap = this.layoutConfig.tablet.columnGap;
		}

		const itemWidth = (boardWidth - 6 * columnGap) / 7;
		const itemHeight = (itemWidth * 53) / 39;

		this.config = {
			...layoutConfig,
			rowGap,
			columnGap,
			boardWidth,
			boardHeight,
			itemSize: {
				width: itemWidth,
				height: itemHeight,
			},
			moveDuration: this.config?.moveDuration ?? 300,
			flipDuration: this.config?.flipDuration ?? 250,
		};
	}

	dispose() {
		this.removeListeners();
	}
}
