import type { GridSize } from '$lib/models/GridSize';
import { Grid } from '$lib/util/Grid.svelte';
import { Undoable } from '$lib/util/Undoable/Undoable.svelte';
import {
	HitoriCellState,
	HitoriGameNotWonReason,
	hitoriGameNotWonReasonPriority,
	hitoriHintDuration,
	type HitoriGameWinState,
	type HitoriNotWonError,
} from './constants';
import { Smush32 } from '@thi.ng/random';
import type { Timer } from '$lib/util/Timer.svelte';
import type { GridItem } from '$lib/models/GridItem';
import { wait } from '$lib/functions/wait';

function createLatinSquare(size: number, seed?: number): number[][] {
	const random = new Smush32(seed);
	const grid: number[][] = Array(size)
		.fill(0)
		.map(() => Array(size).fill(0));

	// Fill first row
	for (let i = 0; i < size; i++) {
		grid[0][i] = i + 1;
	}
	// Shuffle first row
	for (let i = size - 1; i > 0; i--) {
		const j = random.int() % (i + 1);
		[grid[0][i], grid[0][j]] = [grid[0][j], grid[0][i]];
	}

	// Fill remaining rows by shifting
	for (let r = 1; r < size; r++) {
		for (let c = 0; c < size; c++) {
			grid[r][c] = grid[r - 1][(c + 1) % size];
		}
	}

	// Shuffle rows
	for (let i = size - 1; i > 0; i--) {
		const j = random.int() % (i + 1);
		[grid[i], grid[j]] = [grid[j], grid[i]];
	}

	// Shuffle columns
	for (let i = size - 1; i > 0; i--) {
		const j = random.int() % (i + 1);
		for (let r = 0; r < size; r++) {
			[grid[r][i], grid[r][j]] = [grid[r][j], grid[r][i]];
		}
	}

	return grid;
}

interface HitoriGameSettings {
	size: GridSize;
	empty?: boolean;
	seed?: number;
	timer?: Timer;
	onGameWin?: () => void;
}

export class HitoriGame {
	id = Symbol();
	invalidBoard = $state() as Grid<boolean>;
	board = $state() as Grid<HitoriCellState>;
	numbers = $state() as Grid<number>;
	solution = $state() as Grid<boolean>;
	winState = $state<HitoriGameWinState>({
		won: false,
		errors: [],
	});
	hint?: HitoriNotWonError = $state();
	history = $state() as Undoable<Grid<HitoriCellState>>;
	empty: boolean;
	totalClicks = $state(0);
	cellsToEliminate = $state(0);
	private startOverPenalty = 0;
	private hintsUsed = $state(0);
	private timer?: Timer;
	private onGameWin?: () => void;

	static checkGameWin(
		board: Grid<HitoriCellState>,
		numbers: Grid<number>,
	): { won: boolean; errors: HitoriNotWonError[]; invalidBoard: Grid<boolean> } {
		const invalidBoard = new Grid<boolean>(board.size, () => false);
		let won = true;
		const errors: HitoriNotWonError[] = [];

		// Rule 1: No duplicates in white cells in rows/columns
		for (let i = 0; i < board.size.rows; i++) {
			const rowNumbers = new Map<number, { row: number; column: number }>();
			for (let j = 0; j < board.size.columns; j++) {
				if (board.at({ row: i, column: j }) !== HitoriCellState.Eliminated) {
					const num = numbers.at({ row: i, column: j })!;
					if (rowNumbers.has(num)) {
						won = false;
						const otherCell = rowNumbers.get(num)!;
						errors.push({
							reason: HitoriGameNotWonReason.DuplicateInRow,
							cells: [{ row: i, column: j }, otherCell],
						});
					}
					rowNumbers.set(num, { row: i, column: j });
				}
			}
		}
		for (let j = 0; j < board.size.columns; j++) {
			const colNumbers = new Map<number, { row: number; column: number }>();
			for (let i = 0; i < board.size.rows; i++) {
				if (board.at({ row: i, column: j }) !== HitoriCellState.Eliminated) {
					const num = numbers.at({ row: i, column: j })!;
					if (colNumbers.has(num)) {
						won = false;
						const otherCell = colNumbers.get(num)!;
						errors.push({
							reason: HitoriGameNotWonReason.DuplicateInColumn,
							cells: [{ row: i, column: j }, otherCell],
						});
					}
					colNumbers.set(num, { row: i, column: j });
				}
			}
		}

		// Rule 2: No adjacent black cells
		for (let r = 0; r < board.size.rows; r++) {
			for (let c = 0; c < board.size.columns; c++) {
				if (board.at({ row: r, column: c }) === HitoriCellState.Eliminated) {
					const neighbors = board.getItemsAround(
						{ row: r, column: c },
						{ diagonal: false },
					);
					for (const neighbor of neighbors) {
						if (board.at(neighbor) === HitoriCellState.Eliminated) {
							won = false;
							invalidBoard.set({ row: r, column: c }, true);
							invalidBoard.set(neighbor, true);
							errors.push({
								reason: HitoriGameNotWonReason.AdjacentEliminatedCells,
								cells: [{ row: r, column: c }, neighbor],
							});
						}
					}
				}
			}
		}

		// Rule 3: All white cells are connected
		const whiteCells: { row: number; column: number }[] = [];
		for (let r = 0; r < board.size.rows; r++) {
			for (let c = 0; c < board.size.columns; c++) {
				if (board.at({ row: r, column: c }) !== HitoriCellState.Eliminated) {
					whiteCells.push({ row: r, column: c });
				}
			}
		}

		if (whiteCells.length > 0) {
			const visited = new Set<string>();
			const queue = [whiteCells[0]];
			visited.add(`${whiteCells[0].row},${whiteCells[0].column}`);
			let count = 0;
			while (queue.length > 0) {
				const cell = queue.shift()!;
				count++;
				const neighbors = board.getItemsAround(cell, { diagonal: false });
				for (const neighbor of neighbors) {
					const key = `${neighbor.row},${neighbor.column}`;
					if (!visited.has(key) && board.at(neighbor) !== HitoriCellState.Eliminated) {
						visited.add(key);
						queue.push(neighbor);
					}
				}
			}

			if (count !== whiteCells.length) {
				won = false;
				const unvisitedCells = whiteCells.filter(
					(cell) => !visited.has(`${cell.row},${cell.column}`),
				);
				errors.push({
					reason: HitoriGameNotWonReason.DisconnectedWhiteCells,
					cells: unvisitedCells,
				});
				for (const cell of unvisitedCells) {
					invalidBoard.set(cell, true);
				}
			}
		}

		// Rule 4: Eliminated cells must be necessary
		for (let r = 0; r < board.size.rows; r++) {
			for (let c = 0; c < board.size.columns; c++) {
				if (board.at({ row: r, column: c }) === HitoriCellState.Eliminated) {
					const num = numbers.at({ row: r, column: c })!;
					let hasNonEliminatedDuplicateInRow = false;
					let hasNonEliminatedDuplicateInCol = false;

					// Check for a non-eliminated duplicate in the same row
					for (let c2 = 0; c2 < board.size.columns; c2++) {
						if (
							c !== c2 &&
							numbers.at({ row: r, column: c2 }) === num &&
							board.at({ row: r, column: c2 }) !== HitoriCellState.Eliminated
						) {
							hasNonEliminatedDuplicateInRow = true;
							break;
						}
					}

					// Check for a non-eliminated duplicate in the same column
					for (let r2 = 0; r2 < board.size.rows; r2++) {
						if (
							r !== r2 &&
							numbers.at({ row: r2, column: c }) === num &&
							board.at({ row: r2, column: c }) !== HitoriCellState.Eliminated
						) {
							hasNonEliminatedDuplicateInCol = true;
							break;
						}
					}

					if (!hasNonEliminatedDuplicateInRow && !hasNonEliminatedDuplicateInCol) {
						won = false;
						errors.push({
							reason: HitoriGameNotWonReason.UnnecessaryEliminatedCell,
							cells: [{ row: r, column: c }],
						});
					}
				}
			}
		}

		errors.sort((a, b) => {
			if (a.reason === b.reason) {
				return a.cells[0].row - b.cells[0].row;
			}

			return (
				hitoriGameNotWonReasonPriority.indexOf(a.reason) -
				hitoriGameNotWonReasonPriority.indexOf(b.reason)
			);
		});

		return {
			won,
			errors,
			invalidBoard,
		};
	}

	constructor({ size, empty = false, seed, timer, onGameWin }: HitoriGameSettings) {
		this.empty = empty;
		this.board = new Grid(size, () => HitoriCellState.Initial);
		this.invalidBoard = new Grid(size, () => false);
		this.numbers = new Grid(size, () => 0);
		this.solution = new Grid(size, () => false);
		this.history = new Undoable(this.board.clone());
		this.timer = timer;
		this.onGameWin = onGameWin;

		if (!empty) {
			this.generateGame(seed);

			if (import.meta.env.DEV) {
				console.log('=== Solution ===');
				this.solution.print();
			}
		}
	}

	get isWon() {
		return this.winState.won;
	}

	clicks = $derived(this.history.timeline.past.length);

	private generateGame(seed: number = 0): void {
		const random = new Smush32(seed);
		const size = this.board.size.rows;
		const latinSquare = createLatinSquare(size, random.int());

		this.generateSolution(random.int());

		// Create puzzle from latin square and solution
		for (let r = 0; r < size; r++) {
			for (let c = 0; c < size; c++) {
				this.numbers.set({ row: r, column: c }, latinSquare[r][c]);
			}
		}

		for (let r = 0; r < size; r++) {
			for (let c = 0; c < size; c++) {
				if (this.solution.at({ row: r, column: c })) {
					// This is a black cell, find a white cell in the same row or column to create a duplicate
					const isRow = random.int() > 0.5;
					if (isRow) {
						for (let c2 = 0; c2 < size; c2++) {
							if (!this.solution.at({ row: r, column: c2 })) {
								this.numbers.set(
									{ row: r, column: c },
									this.numbers.at({ row: r, column: c2 }),
								);
								break;
							}
						}
					} else {
						for (let r2 = 0; r2 < size; r2++) {
							if (!this.solution.at({ row: r2, column: c })) {
								this.numbers.set(
									{ row: r, column: c },
									this.numbers.at({ row: r2, column: c }),
								);
								break;
							}
						}
					}
				}
			}
		}

		const board = new Grid(
			{
				...this.solution.size,
				reactive: false,
			},
			(row, column) => {
				return this.solution.at({ row, column })
					? HitoriCellState.Eliminated
					: HitoriCellState.NotEliminated;
			},
		);
		const { errors } = HitoriGame.checkGameWin(board, this.numbers);

		if (errors.length > 0) {
			this.generateGame(seed + 1);
		}
	}

	handleClick({ row, column }: GridItem) {
		if (this.isWon) {
			return;
		}

		if (!this.timer?.running) {
			this.timer?.start();
		}

		const currentState = this.board.at({ row, column });
		let nextState: HitoriCellState;

		switch (currentState) {
			case HitoriCellState.Initial:
				nextState = HitoriCellState.Eliminated;
				break;
			case HitoriCellState.Eliminated:
				nextState = HitoriCellState.NotEliminated;
				break;
			case HitoriCellState.NotEliminated:
				nextState = HitoriCellState.Initial;
				break;

			default:
				nextState = HitoriCellState.Initial;
		}

		this.board.set({ row, column }, nextState);
		this.history.add(this.board.clone());
		this.totalClicks += 1;
		this.checkGameWin();
		this.dismissHint();

		if (this.isWon) {
			this.onGameWin?.();
		}
	}

	checkGameWin() {
		const { won, errors, invalidBoard } = HitoriGame.checkGameWin(this.board, this.numbers);
		this.winState = {
			won,
			errors,
		};
		this.invalidBoard = invalidBoard;
	}

	private generateSolution(seed?: number): void {
		// Reset solution
		this.solution.reset(() => false);

		const random = new Smush32(seed);
		const size = this.board.size.rows;

		const allCells: GridItem[] = [];
		for (let r = 0; r < size; r++) {
			for (let c = 0; c < size; c++) {
				allCells.push({ row: r, column: c });
			}
		}
		// Fisher-Yates shuffle
		for (let i = allCells.length - 1; i > 0; i--) {
			const j = random.int() % (i + 1);
			[allCells[i], allCells[j]] = [allCells[j], allCells[i]];
		}

		for (const cell of allCells) {
			// Check for adjacent black cells
			const neighbors = this.solution.getItemsAround(cell, { diagonal: false });
			let hasAdjacentBlackCell = false;
			for (const neighbor of neighbors) {
				if (this.solution.at(neighbor)) {
					hasAdjacentBlackCell = true;
					break;
				}
			}
			if (hasAdjacentBlackCell) {
				continue;
			}

			// Check for white cell connectivity
			this.solution.set(cell, true); // Tentatively mark as black
			if (!this.areWhiteCellsConnected()) {
				this.solution.set(cell, false); // Revert if it breaks connectivity
				continue;
			}
		}

		this.cellsToEliminate = this.solution
			.flatten()
			.reduce((acc, val) => acc + (val ? 1 : 0), 0);
	}

	private areWhiteCellsConnected(): boolean {
		const whiteCells: { row: number; column: number }[] = [];
		for (let r = 0; r < this.board.size.rows; r++) {
			for (let c = 0; c < this.board.size.columns; c++) {
				if (!this.solution.at({ row: r, column: c })) {
					whiteCells.push({ row: r, column: c });
				}
			}
		}

		if (whiteCells.length === 0) return true;

		const visited = new Set<string>();
		const queue = [whiteCells[0]];
		visited.add(`${whiteCells[0].row},${whiteCells[0].column}`);
		let count = 0;
		while (queue.length > 0) {
			const cell = queue.shift()!;
			count++;
			const neighbors = this.solution.getItemsAround(cell, { diagonal: false });
			for (const neighbor of neighbors) {
				const key = `${neighbor.row},${neighbor.column}`;
				if (!visited.has(key) && !this.solution.at(neighbor)) {
					visited.add(key);
					queue.push(neighbor);
				}
			}
		}

		return count === whiteCells.length;
	}

	startOver() {
		this.startOverPenalty = this.getScore();
		this.dismissHint();
		this.hintsUsed = 0;
		this.totalClicks = 0;
		this.history.reset();
		this.board = this.history.state.clone();
		this.winState = {
			won: false,
			errors: [],
		};
		this.checkGameWin();
		this.timer?.reset();
	}

	canUndo() {
		if (this.isWon || !this.timer?.running) {
			return false;
		}
		return this.history.canUndo();
	}

	canRedo() {
		if (this.isWon || !this.timer?.running) {
			return false;
		}
		return this.history.canRedo();
	}

	undo() {
		if (this.canUndo()) {
			this.history.undo();
			this.board = this.history.state.clone();
			this.dismissHint();
		}
	}

	redo() {
		if (this.canRedo()) {
			this.history.redo();
			this.board = this.history.state.clone();
			this.dismissHint();
		}
	}

	canShowHint() {
		return this.timer?.running && this.winState.errors.length > 0;
	}

	async showHint(options?: { duration: number }) {
		if (!this.canShowHint()) {
			return;
		}

		if (this.hint) {
			return;
		}

		const hint = this.winState.errors[0];
		this.hint = hint;
		this.hintsUsed += 1;

		await wait(options?.duration ?? hitoriHintDuration);

		if (this.hint === hint) {
			this.dismissHint();
		}
	}

	dismissHint() {
		this.hint = undefined;
	}

	get remainingCells() {
		return Math.max(0, this.cellsToEliminate - this.eliminatedCells);
	}

	get eliminatedCells() {
		let count = 0;

		for (const row of this.board.grid) {
			for (const cell of row) {
				if (cell === HitoriCellState.Eliminated) {
					count++;
				}
			}
		}

		return count;
	}

	getScore() {
		return Math.floor(
			(this.timer?.elapsedTime ?? 0) / 1000 +
				this.totalClicks +
				this.hintsUsed * 15 +
				this.startOverPenalty,
		);
	}
}
