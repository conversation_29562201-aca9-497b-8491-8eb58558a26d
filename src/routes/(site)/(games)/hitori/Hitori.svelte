<script lang="ts">
	import { createHitoriContext } from './createHitoriContext';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { onMount, onDestroy, untrack } from 'svelte';
	import HitoriRenderer from './HitoriRenderer.svelte';
	import { HitoriGame } from './HitoriGame.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';
	import UndoIcon from '$lib/components/Icons/UndoIcon.svelte';
	import RedoIcon from '$lib/components/Icons/RedoIcon.svelte';
	import HitoriHowToPlayButton from './HitoriHowToPlayButton.svelte';
	import { hitoriSizes, hitoriGameNotWornReasonMessage, hitoriHintDuration } from './constants';
	import HitoriInfoModal from './HitoriInfoModal.svelte';
	import {
		elementTagsToIgnoreEvents,
		UndoableKeyboardListener,
		type ShortcutsTags,
	} from '$lib/util/Undoable/UndoableKeyboardListener';
	import { isMacLike } from '$lib/functions/isMacLike';
	import { cn } from '$lib/util/cn';
	import LightBulbIcon from '$lib/components/Icons/LightBulbIcon.svelte';
	import { toast } from 'svelte-sonner';

	let isSizeDropdownOpen = $state(false);
	let isInfoModalOpen = $state(false);
	let isMac = $state(false);
	let context = createHitoriContext();

	let game = $derived.by(() => {
		if (context.game) {
			return context.game as HitoriGame;
		}

		return untrack(
			() =>
				new HitoriGame({
					empty: true,
					size: hitoriSizes[0],
				}),
		);
	});

	let undoableListener: UndoableKeyboardListener;

	onMount(() => {
		isMac = isMacLike();
		context.load();
		undoableListener = new UndoableKeyboardListener(
			() => game.undo(),
			() => game.redo(),
		);
		undoableListener.listen();
	});

	onDestroy(() => {
		context.dispose();
		if (undoableListener) {
			undoableListener.dispose();
		}
	});

	function onkeypress(e: KeyboardEvent) {
		const tagName = (e.target as HTMLElement)?.tagName as unknown;

		if (elementTagsToIgnoreEvents.includes(tagName as ShortcutsTags)) {
			return;
		}

		if (e.key === 'h') {
			game.showHint();
		}
	}

	$effect(function showHint() {
		if (game.hint) {
			toast.warning(hitoriGameNotWornReasonMessage[game.hint.reason], {
				id: 'hitori-hint',
				duration: hitoriHintDuration,
				dismissable: true,
			});
		} else {
			toast.dismiss('hitori-hint');
		}
	});
</script>

<svelte:window {onkeypress} />

<GameLayout>
	{#snippet Island()}
		<GameIsland {context} />
	{/snippet}

	<div class={cn('flex flex-col items-center w-full gap-4 justify-center pb-6 md:pb-0', {})}>
		<div class="flex items-center justify-between gap-2 w-full max-w-2xl">
			<div class="flex items-center gap-2">
				<HitoriHowToPlayButton />

				<Dropdown bind:open={isSizeDropdownOpen}>
					<DropdownButton class="btn-sm">
						{game.board.size.rows}x{game.board.size.columns}
					</DropdownButton>

					<DropdownContent menu>
						{#each hitoriSizes as size}
							<DropdownItem>
								<button
									class={cn({
										'menu-active': game.board.size.rows === size.rows,
										'hidden sm:block': size.rows >= 12,
									})}
									onclick={() => {
										context.createGame({
											size,
										});
										isSizeDropdownOpen = false;
									}}
								>
									{size.rows}x{size.columns}
								</button>
							</DropdownItem>
						{/each}
					</DropdownContent>
				</Dropdown>

				<button
					class="btn btn-sm"
					onclick={() => {
						context.sounds.start.play();
						game.startOver();
					}}
				>
					Start over
				</button>

				<button
					aria-label="Open info dialog"
					class="btn btn-sm"
					onclick={() => (isInfoModalOpen = true)}
				>
					<InfoSolidIcon class="size-5" />
				</button>

				<MoreGamesButton class="btn-sm hidden xs:block" />
			</div>

			<div class="flex items-center gap-2">
				<div class="tooltip tooltip-bottom hidden sm:inline-block" data-tip="Hint (H)">
					<button
						class="btn btn-sm"
						disabled={!game.canShowHint()}
						aria-label="Show hint"
						onclick={() => game.showHint()}
					>
						<LightBulbIcon class="size-6" />
					</button>
				</div>

				<div
					class="tooltip tooltip-bottom hidden sm:inline-block"
					data-tip="Undo ({isMac ? '⌘' : 'Ctrl'} + Z)"
				>
					<button
						class="btn btn-sm"
						disabled={!game.canUndo()}
						aria-label="Undo"
						onclick={() => game.undo()}
					>
						<UndoIcon />
					</button>
				</div>

				<div
					class="tooltip tooltip-left hidden sm:inline-block"
					data-tip="Redo ({isMac ? '⌘' : 'Ctrl'} + Shift + Z)"
				>
					<button
						class="btn btn-sm"
						disabled={!game.canRedo()}
						aria-label="Redo"
						onclick={() => game.redo()}
					>
						<RedoIcon />
					</button>
				</div>
			</div>
		</div>

		<div class="sm:grow-0 w-full flex items-center justify-center">
			<HitoriRenderer
				class="max-w-xl"
				disabled={game.empty}
				{game}
				onPlayOnSound={() => context.sounds.switch2.play()}
				onPlayOffSound={() => context.sounds.switch1.play()}
			/>
		</div>

		<div class="flex justify-between items-center gap-4 w-full grow">
			<div class="flex sm:hidden gap-4">
				<button
					class="btn btn-lg btn-circle"
					disabled={!game.canShowHint()}
					aria-label="Show hint"
					onclick={() => game.showHint()}
				>
					<LightBulbIcon class="size-6" />
				</button>
			</div>

			<div class="flex sm:hidden gap-4 self-end">
				<button
					class="btn btn-lg btn-circle"
					disabled={!game.canUndo()}
					onclick={() => game.undo()}
				>
					<UndoIcon class="size-8" />
				</button>

				<button
					class="btn btn-lg btn-circle"
					disabled={!game.canRedo()}
					onclick={() => game.redo()}
				>
					<RedoIcon class="size-8" />
				</button>
			</div>
		</div>
	</div>
</GameLayout>

<HitoriInfoModal bind:isOpen={isInfoModalOpen} />
