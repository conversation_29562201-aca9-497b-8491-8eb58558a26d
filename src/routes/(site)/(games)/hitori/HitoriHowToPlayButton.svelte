<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';
	import TouchTutorialFigure from '$lib/components/TouchTutorialFigure.svelte';
	import { Timer } from '$lib/util/Timer.svelte';
	import { untrack } from 'svelte';
	import { HitoriGame } from './HitoriGame.svelte';
	import HitoriRenderer from './HitoriRenderer.svelte';
	import { HitoriCellState } from './constants';

	let isOpen = $state(false);

	function getGame() {
		const game = new HitoriGame({
			timer: new Timer(),
			seed: 1,
			size: {
				rows: 3,
				columns: 3,
			},
		});

		game.numbers.set({ row: 0, column: 0 }, 1);
		game.numbers.set({ row: 0, column: 1 }, 1);
		game.numbers.set({ row: 0, column: 2 }, 2);

		game.numbers.set({ row: 1, column: 0 }, 2);
		game.numbers.set({ row: 1, column: 1 }, 1);
		game.numbers.set({ row: 1, column: 2 }, 3);

		game.numbers.set({ row: 2, column: 0 }, 2);
		game.numbers.set({ row: 2, column: 1 }, 2);
		game.numbers.set({ row: 2, column: 2 }, 3);

		return game;
	}
</script>

<button onclick={() => (isOpen = true)} class="btn btn-sm">
	How <span class="hidden md:inline">to play</span>
</button>

<Dialog bind:isOpen>
	<article>
		<h2>How to Play Hitori</h2>

		<p>
			Hitori is a logic puzzle where you eliminate cells to remove duplicate numbers. The
			rules are simple:
		</p>

		<ul>
			<li>No duplicate numbers in rows and columns</li>
			<li>Eliminated cells cannot be next to each other (horizontally or vertically)</li>
			<li>Every remaining cell must be connected (horizontally or vertically)</li>
		</ul>

		<p>Click on a cell to mark it as eliminated.</p>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 0,
				column: 1,
			}}
			rows={3}
			columns={3}
			fadeTouch
			figcaption="Eliminating a cell"
		>
			{#snippet gameBeforeTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						return game;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 1 }, HitoriCellState.Eliminated);

						return game;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>You can't have two eliminated cells next to each other (horizontally or vertically).</p>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 0,
				column: 1,
			}}
			rows={3}
			columns={3}
			fadeTouch
			figcaption="Eliminated cells horizontally next to each other"
		>
			{#snippet gameBeforeTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 0 }, HitoriCellState.Eliminated);
						game.checkGameWin();

						return game;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 0 }, HitoriCellState.Eliminated);
						game.board.set({ row: 0, column: 1 }, HitoriCellState.Eliminated);
						game.checkGameWin();

						return game;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 1,
				column: 1,
			}}
			rows={3}
			columns={3}
			fadeTouch
			figcaption="Eliminated cells vertically next to each other"
		>
			{#snippet gameBeforeTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 1 }, HitoriCellState.Eliminated);
						game.checkGameWin();

						return game;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 1 }, HitoriCellState.Eliminated);
						game.board.set({ row: 1, column: 1 }, HitoriCellState.Eliminated);
						game.checkGameWin();

						return game;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>Remaining cells should be connected (horizontally or vertically).</p>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 1,
				column: 2,
			}}
			rows={3}
			columns={3}
			fadeTouch
			figcaption="Remaining cell disconnected from other cells"
		>
			{#snippet gameBeforeTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 1 }, HitoriCellState.Eliminated);
						game.checkGameWin();

						return game;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 1 }, HitoriCellState.Eliminated);
						game.board.set({ row: 1, column: 2 }, HitoriCellState.Eliminated);
						game.checkGameWin();

						return game;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>
			The goal is to eliminate cells so that there are no duplicate numbers in any row or
			column.
		</p>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 2,
				column: 0,
			}}
			rows={3}
			columns={3}
			fadeTouch
			figcaption="Winning a Hitori game"
		>
			{#snippet gameBeforeTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 1 }, HitoriCellState.Eliminated);
						game.board.set({ row: 2, column: 2 }, HitoriCellState.Eliminated);

						game.checkGameWin();

						return game;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 1 }, HitoriCellState.Eliminated);
						game.board.set({ row: 2, column: 2 }, HitoriCellState.Eliminated);
						game.board.set({ row: 2, column: 0 }, HitoriCellState.Eliminated);
						game.checkGameWin();

						return game;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<h3>Tip</h3>

		<p>
			Click again to mark a cell as definitely not eliminated. This is not required to solve
			the puzzle, but can be helpful.
		</p>

		<TouchTutorialFigure
			touchContainerClass="p-1 gap-1"
			touchIconPosition={{
				row: 0,
				column: 0,
			}}
			rows={3}
			columns={3}
			fadeTouch
			figcaption="Marking a cell as definitely not eliminated"
		>
			{#snippet gameBeforeTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 0 }, HitoriCellState.Eliminated);

						return game;
					})}
				/>
			{/snippet}
			{#snippet gameAfterTouch()}
				<HitoriRenderer
					disabled
					game={untrack(() => {
						const game = getGame();

						game.board.set({ row: 0, column: 0 }, HitoriCellState.NotEliminated);

						return game;
					})}
				/>
			{/snippet}
		</TouchTutorialFigure>

		<p>Have fun!</p>
	</article>
</Dialog>
