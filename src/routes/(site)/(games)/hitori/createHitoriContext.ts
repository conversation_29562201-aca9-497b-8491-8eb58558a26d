import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
import { hitoriSizes } from './constants';
import { pickRandom, Smush32 } from '@thi.ng/random';
import { MediaQuery } from '$lib/util/MediaQuery.svelte';
import { HitoriGame } from './HitoriGame.svelte';
import { Stats } from '$lib/util/Stats.svelte';
import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';
import { hitoriSoundResources } from './hitoriSoundResources';

const smallMediaQuery = new MediaQuery('(min-width: 640px)');

export function createHitoriContext() {
	return new GameContext({
		gameName: 'Hitori',
		gameKey: 'hitori',
		GameClass: HitoriGame,
		sounds: {
			resources: hitoriSoundResources,
			lifecycle: {
				createGame: hitoriSoundResources.start,
				win: hitoriSoundResources.gameWin,
			},
		},
		settings: {
			defaultSettings: {
				size: hitoriSizes[0],
			},
		},
		variants: {
			map: {
				size: {
					allValues: hitoriSizes,
					format(size) {
						return `${size.rows}x${size.columns}`;
					},
				},
			},
			fromGame(game) {
				return {
					size: {
						rows: game.board.size.rows,
						columns: game.board.size.columns,
					},
				};
			},
			getStatsVariant(variants) {
				return `${variants.size?.rows ?? 6}x${variants.size?.columns ?? 6}`;
			},
			getLeaderboardVariant(variants) {
				return `${variants.size?.rows ?? 6}x${variants.size?.columns ?? 6}`;
			},
		},
		defaultGameProps(context) {
			const settings = context.settingsManager.settings;

			return {
				size: settings.size,
				timer: context.timer,
				seed: performance.now(),
				onGameWin: () => context.handleGameOver('won'),
			};
		},
		stats({ props, context }) {
			const game = context.game!;

			return {
				stats: new Stats({
					...props,
					liveStats: {
						remainingCells: {
							name: 'Remaining Cells',
							unit: 'plain',
							value() {
								return game.remainingCells;
							},
						},
						eliminatedCells: {
							name: 'Eliminated Cells',
							unit: 'plain',
							value() {
								return game.eliminatedCells;
							},
						},
						clicks: {
							name: 'Clicks',
							unit: 'plain',
							value: () => game.clicks,
							metrics: {
								total: {
									key: 'totalClicks',
									name: 'Total Clicks',
								},
								average: {
									key: 'averageClicks',
									name: 'Average Clicks',
								},
								min: {
									key: 'fewestClicks',
									name: 'Fewest Clicks',
									useAsBest: true,
								},
								max: {
									key: 'maxClicks',
									name: 'Max Clicks',
								},
							},
						},
						totalClicks: {
							name: 'Total Clicks',
							description: 'Includes undos and redos',
							unit: 'plain',
							value: () => game.totalClicks,
							metrics: {
								total: {
									key: 'totalTotalClicks',
									name: 'Total Total Clicks',
								},
								average: {
									key: 'averageTotalClicks',
									name: 'Average Total Clicks',
								},
								min: {
									key: 'fewestTotalClicks',
									name: 'Fewest Total Clicks',
									useAsBest: true,
								},
								max: {
									key: 'maxTotalClicks',
									name: 'Max Total Clicks',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'remainingCells'],
				}),
				visibleStats: [
					'bestTime',
					'averageTime',
					'fewestClicks',
					'averageClicks',
					'fewestTotalClicks',
					'averageTotalClicks',
					'wonGames',
					'totalGames',
				],
				canUpdateWithGameLost(game) {
					return !game.isWon && game.totalClicks > 3;
				},
			};
		},
		leaderboard({ props }) {
			return {
				leaderboard: new Leaderboard({
					...props,
					firstAvailableDate: new Date('2025/10/06'),
					order: 'lower-first',
					hasLevel: false,
					hasTime: true,
					hasMoves: true,
				}),
				sendScoreOn: ['won'],
				getScore(game) {
					return {
						score: game.getScore(),
						moves: game.clicks,
					};
				},
			};
		},
		dailyGame() {
			return {
				type: 'seed',
				gameVariant: smallMediaQuery.current ? undefined : 'Mobile',
				firstAvailableGameDate: new Date('2025/10/06'),
				toProps(seed: number) {
					const random = new Smush32(seed);
					const sizes = smallMediaQuery.current ? [6, 8, 10] : [6, 8, 10, 12, 15];
					const size = pickRandom(sizes, random);

					return {
						size: {
							rows: size,
							columns: size,
						},
						seed,
					};
				},
			};
		},
		onWillCreateGame({ newGameOptions, context }) {
			if (!newGameOptions.isDaily) {
				context.settingsManager.settings.size = newGameOptions.size;
			}
		},
	});
}
