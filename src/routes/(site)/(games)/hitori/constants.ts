import type { GridItem } from '$lib/models/GridItem';
import type { GridSize } from '$lib/models/GridSize';

export const hitoriSizes: GridSize[] = [
	{
		rows: 6,
		columns: 6,
	},
	{
		rows: 8,
		columns: 8,
	},
	{
		rows: 10,
		columns: 10,
	},
	{
		rows: 12,
		columns: 12,
	},
	{
		rows: 15,
		columns: 15,
	},
];

export enum HitoriGameNotWonReason {
	DuplicateInRow,
	DuplicateInColumn,
	AdjacentEliminatedCells,
	DisconnectedWhiteCells,
	UnnecessaryEliminatedCell,
}

export const hitoriGameNotWonReasonPriority = [
	HitoriGameNotWonReason.AdjacentEliminatedCells,
	HitoriGameNotWonReason.DisconnectedWhiteCells,
	HitoriGameNotWonReason.DuplicateInRow,
	HitoriGameNotWonReason.DuplicateInColumn,
	HitoriGameNotWonReason.UnnecessaryEliminatedCell,
];

export const hitoriGameNotWornReasonMessage: Record<HitoriGameNotWonReason, string> = {
	[HitoriGameNotWonReason.DuplicateInRow]: 'Same numbers in a row',
	[HitoriGameNotWonReason.DuplicateInColumn]: 'Same numbers in a column',
	[HitoriGameNotWonReason.AdjacentEliminatedCells]: 'Eliminated cells are touching each other',
	[HitoriGameNotWonReason.DisconnectedWhiteCells]: 'All remaining cells must be connected',
	[HitoriGameNotWonReason.UnnecessaryEliminatedCell]:
		"A cell was eliminated when it didn't need to be",
};

export const hitoriHintDuration = 3000;

export enum HitoriCellState {
	Initial,
	Eliminated,
	NotEliminated,
}

export interface HitoriNotWonError {
	reason: HitoriGameNotWonReason;
	cells: GridItem[];
}

export interface HitoriGameWinState {
	won: boolean;
	errors: Array<HitoriNotWonError>;
}
