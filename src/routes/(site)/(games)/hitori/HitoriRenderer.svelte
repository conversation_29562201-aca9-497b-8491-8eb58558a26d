<script lang="ts">
	import { HitoriCellState } from './constants';
	import { cn } from '$lib/util/cn';
	import { switchSoundAttachment } from '$lib/attachments/switchSoundAttachment.svelte';
	import type { HitoriGame } from './HitoriGame.svelte';
	import { isSameGridItem } from '$lib/functions/isSameGridItem';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';
	import LightBulbIcon from '$lib/components/Icons/LightBulbIcon.svelte';
	import { fly } from 'svelte/transition';

	interface Props {
		game: HitoriGame;
		class?: string;
		onPlayOnSound?: () => void;
		onPlayOffSound?: () => void;
		disabled?: boolean;
	}

	let {
		game,
		class: classFromProps = '',
		onPlayOnSound = () => {},
		onPlayOffSound = () => {},
		disabled,
	}: Props = $props();
</script>

<div
	class={cn('grid gap-1 w-full aspect-square bg-game-hitori-board-bg p-1', classFromProps)}
	style="grid-template-columns: repeat({game.board.columns}, 1fr);"
>
	{#each game.board.grid as row, rowIndex}
		{#each row as cell, colIndex (`${game.board.size.columns}, ${rowIndex}, ${colIndex}`)}
			{@const value = game.numbers.at({ row: rowIndex, column: colIndex })}
			{@const hasError = game.invalidBoard.at({
				row: rowIndex,
				column: colIndex,
			})}
			{@const hasHint = game.hint?.cells?.some((c) =>
				isSameGridItem(c, {
					row: rowIndex,
					column: colIndex,
				}),
			)}
			<div
				role="button"
				aria-label={`${value} at (${rowIndex + 1}, ${colIndex + 1})${cell === HitoriCellState.Eliminated ? ', eliminated' : cell === HitoriCellState.Initial ? '' : ', definitely not eliminated'} ${hasHint ? ', hinted' : ''} ${hasError ? ', invalid' : ''}`}
				class={cn(
					'@container btn p-0 rounded-none btn-ghost cursor-pointer size-full transition-colors duration-150 relative z-auto!',
					{
						'bg-game-hitori-initial-bg text-game-hitori-initial-text':
							cell === HitoriCellState.Initial,
						'bg-game-hitori-eliminated-bg': cell === HitoriCellState.Eliminated,
						'bg-game-hitori-not-eliminated-bg text-game-hitori-not-eliminated-text':
							cell === HitoriCellState.NotEliminated,
						'text-transparent text-shadow-none':
							(cell === HitoriCellState.Eliminated && !hasError) || game.empty,
					},
					{
						'bg-error dark:bg-error text-error-content dark:text-error-content':
							hasError && cell === HitoriCellState.Eliminated,
						'bg-error/20 dark:bg-error/20 text-error dark:text-error':
							hasError && cell !== HitoriCellState.Eliminated,
					},
					{
						'border-warning-content bg-warning dark:bg-warning text-warning-content dark:text-warning-content':
							hasHint,
					},
				)}
				tabindex="0"
				onclick={() => {
					if (disabled) {
						return;
					}

					game.handleClick({ row: rowIndex, column: colIndex });
				}}
				onkeydown={(e) => {
					if (e.key === 'Enter') {
						game.handleClick({ row: rowIndex, column: colIndex });
					}
				}}
				{@attach switchSoundAttachment({
					action: cell === HitoriCellState.Eliminated ? 'on' : 'off',
					disabled,
					onPlayOnSound,
					onPlayOffSound,
				})}
			>
				<span class="text-[60cqw]">
					{value}
				</span>

				{#if hasHint}
					<div
						class="absolute top-0 right-0 -translate-y-1/3 translate-x-1/3 size-1/3 aspect-square z-10"
						in:fly={{ y: 20 }}
						out:fly={{ y: -20 }}
					>
						<LightBulbIcon
							class="p-[10%] text-warning rounded-full bg-base-100 dark:bg-base-300 hidden colorblind:block"
						/>
					</div>
				{:else if hasError}
					<div
						class="absolute top-0 right-0 -translate-y-1/3 translate-x-1/3 size-1/3 aspect-square z-10"
						in:fly={{ y: 20 }}
						out:fly={{ y: -20 }}
					>
						<WarningSolidIcon
							class="text-error rounded-full bg-base-100 dark:bg-base-300 hidden colorblind:block"
						/>
					</div>
				{/if}
			</div>
		{/each}
	{/each}
</div>
