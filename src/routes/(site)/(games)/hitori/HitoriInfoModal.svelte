<script lang="ts">
	import InfoModal from '$lib/components/InfoModal.svelte';
	import { hitoriSoundResources as sounds } from './hitoriSoundResources';

	interface Props {
		isOpen?: boolean;
	}

	let { isOpen = $bindable(false) }: Props = $props();
</script>

<InfoModal {sounds} bind:isOpen>
	<article class="prose">
		<h1><PERSON>ori</h1>

		<p>
			In the vast universe of logic puzzles, a few stars shine brighter than the rest. Sudoku,
			with its elegant simplicity, has become a global phenomenon. Crosswords have tested our
			vocabulary for generations. But nestled among these giants is a lesser-known, yet
			equally captivating puzzle from Japan: <strong><PERSON>ori</strong>. If you're a fan of
			brain-teasing challenges that blend simple rules with deep, satisfying logic, then
			you've arrived at the right place. This guide will illuminate the world of Hitori, a
			game that's as rewarding as it is challenging.
		</p>

		<h2>What is Hitori? A First Glance</h2>
		<p>
			Hitori, which translates to "Leave me alone" or "one person" in Japanese (一人にしてくれ
			- hitori ni shite kure), is a logic puzzle played on a grid of squares, each containing
			a number. At first glance, it might resemble a Sudoku grid, but the objective is
			entirely different. Instead of filling in numbers, your goal is to <em>eliminate</em> them
			by shading or coloring the squares black. The goal is to create a grid that adheres to three
			simple, yet powerful, rules. It's a game of deduction, elimination, and spatial reasoning
			that offers a unique and stimulating mental workout. Unlike many puzzles that require you
			to build something, Hitori is about carving away the non-essential to reveal the elegant
			solution hidden within.
		</p>

		<h2>The Origins of Hitori: A Nikoli Classic</h2>
		<p>
			Like many of the world's most beloved logic puzzles, Hitori hails from Japan,
			specifically from the puzzle masters at Nikoli Co. Ltd. Nikoli is the same company that
			popularized Sudoku, and they have a long history of creating and curating high-quality
			logic puzzles. Hitori first appeared in the magazine <em>Puzzle Communication Nikoli</em
			> in March 1990. Its name, "Leave me alone," is a clever nod to the gameplay, where you are
			essentially isolating duplicate numbers, leaving only single instances of each number in
			every row and column.
		</p>
		<p>
			Nikoli's philosophy emphasizes puzzles that are solvable through pure logic, without the
			need for guessing. This design principle is at the heart of Hitori's appeal. Every
			well-formed Hitori puzzle has a single, unique solution that can be reached through a
			series of logical deductions. This guarantee of a fair challenge is what makes Hitori,
			and other Nikoli puzzles, so satisfying to solve.
		</p>

		<h2>How to Play Hitori: The Three Golden Rules</h2>
		<p>
			The beauty of Hitori lies in its simple rule set. Understanding these three rules is all
			you need to begin your journey as a Hitori solver. The entire game revolves around the
			interplay between them.
		</p>

		<h3>Rule 1: No Duplicate Numbers in Rows or Columns</h3>
		<p>
			This is the central rule of Hitori. The final grid, after you've shaded the black cells,
			must not contain any duplicate numbers in any row or column. The numbers that remain
			visible in the white cells must be unique within their respective rows and columns. This
			is the rule that drives your initial deductions, as you hunt for duplicate or triplicate
			numbers as candidates for elimination.
		</p>

		<h3>Rule 2: Black Cells Cannot Touch</h3>
		<p>
			The cells you choose to shade black cannot be adjacent to each other, either
			horizontally or vertically. They can, however, touch diagonally. This rule introduces a
			spatial element to the puzzle. Eliminating one cell has immediate consequences for its
			neighbors, forcing them to remain white. This creates a chain reaction of deductions
			that ripple across the grid.
		</p>

		<h3>Rule 3: All White Cells Must Be Connected</h3>
		<p>
			This is arguably the most interesting and complex rule of Hitori. All the white
			(un-shaded) cells in the grid must form a single, continuous area. This means you should
			be able to travel from any white cell to any other white cell by moving only
			horizontally or vertically through other white cells. You cannot have isolated "islands"
			of white cells. This rule forces you to think about the overall structure of the grid
			and prevents you from shading cells in a way that would sever the connection between the
			remaining white cells.
		</p>

		<h2>Your First Hitori Puzzle: A Step-by-Step Walkthrough</h2>
		<p>
			Let's put these rules into practice. Imagine a simple 4x4 Hitori grid. The best way to
			start is to look for obvious duplicates.
		</p>
		<ol>
			<li>
				<strong>Scan for Duplicates:</strong> Look for pairs or triplets of the same number in
				any row or column. These are your starting points. For example, if a row contains two
				'4's, you know that at least one of them must be shaded black.
			</li>
			<li>
				<strong>Mark Definite White Cells:</strong> If a number is unique in its row and column
				from the start, you know it cannot be shaded. It's often helpful to circle these numbers
				to mark them as "definitely white."
			</li>
			<li>
				<strong>Apply Rule 2:</strong> Once you shade a cell black, you immediately know that
				its four orthogonal neighbors (up, down, left, right) must be white. Circle them! This
				is a powerful step that often reveals new information.
			</li>
			<li>
				<strong>Apply Rule 1 with New Information:</strong> If you've just circled a '4', look
				at its row and column again. If there's another '4' in that same row or column, it must
				now be shaded black.
			</li>
			<li>
				<strong>Check for Connectivity (Rule 3):</strong> As you shade more cells, keep an eye
				on the white cells. Ask yourself: "If I shade this cell, will it cut off a white cell
				or a group of white cells from the rest?" If the answer is yes, then that cell cannot
				be black; it must be white. Circle it and continue your deductions.
			</li>
		</ol>
		<p>
			Continue this process of applying the rules iteratively. Each cell you mark, whether
			black or white, gives you more information to work with. The puzzle is solved when all
			three rules are satisfied across the entire grid.
		</p>

		<h2>Core Hitori Solving Techniques</h2>
		<p>
			As you progress, you'll start to recognize patterns and develop a more intuitive feel
			for the logic. Here are some of the fundamental techniques that will become second
			nature.
		</p>

		<h3>1. Obvious Duplicates and Triplicates</h3>
		<ul>
			<li>
				<strong>Pairs:</strong> If you have two identical numbers in a row, like `...3...3...`,
				you know one of them must be black. You may not know which one yet, but this is a crucial
				piece of information.
			</li>
			<li>
				<strong>Adjacent Pairs:</strong> If you have `...3 3...`, you know one is black and one
				is white. Furthermore, if you determine a third cell between them must be black, then
				both '3's must be white.
			</li>
			<li>
				<strong>Triplets:</strong> A pattern like `...3...3...3...` in a single row or column
				is very powerful. If all three were white, it would violate Rule 1. Therefore, at least
				two must be black. However, if two of them are adjacent, this can lead to further deductions.
				A pattern like `3 3 3` is a goldmine. The two outer '3's cannot both be black (Rule 2),
				so the middle '3' must be black to resolve the duplicates.
			</li>
		</ul>

		<h3>2. The Power of a Black Cell</h3>
		<p>
			This is the most direct and satisfying deduction. Once you are certain a cell must be
			black, you can immediately circle all its orthogonal neighbors as white. This is a
			guaranteed move that always expands your knowledge of the grid.
		</p>

		<h3>3. The Certainty of a White Cell</h3>
		<p>
			Conversely, when you mark a cell as white (by circling it), you can then look for its
			duplicates. Any other instance of that same number in the same row or column must now be
			black. This is often how you find your first definite black cells.
		</p>

		<h3>4. The Connectivity Rule in Action</h3>
		<p>
			This rule is often the key to solving more difficult puzzles. Always be on the lookout
			for "checkmate" positions. For example, if you have a group of white cells that are only
			connected to the rest of the grid by a single cell, that single cell cannot be shaded
			black. It forms a critical "bridge" that must be preserved. Another common pattern is a
			2x2 block of four white cells. If you shade one, you must consider if it will lead to a
			violation. For instance, shading two diagonally opposite cells in a 2x2 block is often
			problematic for connectivity.
		</p>

		<h2>Advanced Strategies for the Hitori Master</h2>
		<p>
			Ready to tackle the toughest puzzles? These advanced strategies require more complex,
			multi-step thinking.
		</p>
		<ul>
			<li>
				<strong>Chaining Deductions:</strong> This involves following a chain of logic across
				the grid. "If this cell is black, then this one must be white, which makes that one black,
				which makes..." and so on. Sometimes, this chain will lead to a contradiction, proving
				your initial assumption was wrong.
			</li>
			<li>
				<strong>Reductio ad Absurdum (Proof by Contradiction):</strong> This is the "what if?"
				strategy. When you're stuck, tentatively shade a candidate cell. Follow the logical deductions
				that result from this move. If you arrive at a contradiction (e.g., two black cells become
				adjacent, or the white cells get disconnected), you know your initial tentative move
				was incorrect. Therefore, the cell must be the opposite (i.e., white). This is a powerful
				but risky technique if you're not careful.
			</li>
			<li>
				<strong>Recognizing Advanced Patterns:</strong> Experienced players learn to spot recurring
				patterns that lead to specific deductions. For example, a checkerboard pattern of black
				and white cells is impossible because it violates the connectivity rule. Recognizing
				the start of such a pattern can help you avoid it.
			</li>
		</ul>

		<h2>The Cognitive Benefits of Playing Hitori</h2>
		<p>
			Hitori is more than just a fun pastime; it's a workout for your brain. Regularly
			engaging with logic puzzles like Hitori can have significant cognitive benefits.
		</p>
		<ul>
			<li>
				<strong>Enhanced Logical Reasoning:</strong> Hitori is pure logic. It trains your brain
				to think systematically and make deductions based on a set of fixed rules.
			</li>
			<li>
				<strong>Improved Concentration and Focus:</strong> Solving a Hitori puzzle requires sustained
				attention. You need to keep the rules and the state of the grid in your mind, which can
				sharpen your ability to concentrate on complex tasks.
			</li>
			<li>
				<strong>Boosted Spatial Reasoning:</strong> The connectivity and adjacency rules mean
				you're constantly thinking about the spatial relationships between cells. This is a great
				way to exercise the parts of your brain responsible for spatial awareness.
			</li>
			<li>
				<strong>Stress Relief:</strong> The immersive nature of Hitori can be a form of meditation.
				Focusing on the logical steps can provide a welcome escape from daily stresses and promote
				a sense of calm and order.
			</li>
		</ul>

		<h2>Hitori vs. The World: A Puzzle Comparison</h2>
		<p>How does Hitori stack up against other famous logic puzzles?</p>
		<ul>
			<li>
				<strong>Hitori vs. Sudoku:</strong> While both are Japanese number puzzles on a grid,
				the gameplay is fundamentally different. Sudoku is a "placement" puzzle where you fill
				in a grid based on what's missing. Hitori is an "elimination" puzzle where you remove
				what's superfluous. The connectivity rule in Hitori adds a layer of spatial complexity
				not found in Sudoku.
			</li>
			<li>
				<strong>Hitori vs. Minesweeper:</strong> Both involve marking squares on a grid. However,
				Minesweeper's logic is numerical and localized (the number in a square tells you about
				its immediate neighbors). Hitori's logic is based on the entire row and column, as well
				as the global connectivity of the grid.
			</li>
		</ul>

		<h2>Play Hitori Online at Lofi and Games</h2>
		<p>
			The digital age has made Hitori more accessible than ever. Playing online offers many
			advantages over pen and paper. Here at <strong>lofiandgames.com</strong>, you can enjoy
			a premium Hitori experience for free. Our version includes helpful features like:
		</p>
		<ul>
			<li>
				<strong>Error Checking:</strong> The game can automatically highlight mistakes, like
				adjacent black cells or disconnected white cells.
			</li>
			<li>
				<strong>Undo/Redo:</strong> Don't be afraid to experiment! You can easily take back moves
				and try different approaches.
			</li>
			<li>
				<strong>Hints:</strong> If you get truly stuck, our hint system can give you a gentle
				nudge in the right direction without spoiling the puzzle.
			</li>
			<li>
				<strong>A Beautiful, Clean Interface:</strong> We believe a great puzzle deserves a great
				presentation. Our clean, minimalist design, combined with relaxing lofi music, creates
				the perfect environment for deep thought.
			</li>
		</ul>

		<h2>Conclusion: Your New Favorite Puzzle Awaits</h2>
		<p>
			Hitori is a gem of a puzzle. It's easy to learn but offers a depth of strategy that can
			keep you engaged for years. It's a perfect blend of numerical logic and spatial
			reasoning. It's a game that respects your intelligence, offering a fair and solvable
			challenge every time. If you're looking for a new puzzle to test your wits and provide a
			deep sense of satisfaction, look no further.
		</p>
		<p>
			Dive into the world of Hitori today. Experience the quiet thrill of deduction, the
			"aha!" moment when a complex pattern resolves, and the satisfaction of a perfectly
			solved grid.
			<strong>Play Hitori for free now at lofiandgames.com</strong> and discover your new favorite
			brain game.
		</p>
	</article>
</InfoModal>
