<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full p-4', className)}
	fill="transparent"
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 498 498"
	{...props}
>
	<g clip-path="url(#hitori-cover__a)">
		<path class="fill-game-hitori-board-bg" d="M23 23h453v453H23z" />
		<path class="fill-game-hitori-initial-bg" d="M31 31h143v143H31z" />
		<path class="fill-game-hitori-eliminated-bg" d="M178 31h143v143H178zM31 325h143v143H31z" />
		<path
			d="M102.594 127V90.562h-.188l-11.5 7.813V90l11.656-8.094h9.376V127h-9.344Z"
			class="fill-game-hitori-initial-text"
		/>
		<path class="fill-game-hitori-initial-bg" d="M325 31h143v143H325z" />
		<path
			d="M381.375 127v-6.469l15.937-14.906c5.813-5.437 7.032-7.656 7.032-10.781v-.063c-.032-3.687-2.844-6.312-6.844-6.312-4.625 0-7.75 3.031-7.875 7.094l-.031.28h-8.75v-.25c0-8.5 7.062-14.468 16.594-14.468 9.531 0 16.093 5.438 16.093 13.125v.063c0 5.468-2.593 9.031-10.375 16.187l-9.437 8.625v.375h20.437v7.5h-32.781Z"
			class="fill-game-hitori-initial-text"
		/>
		<path class="fill-game-hitori-initial-bg" d="M31 178h143v143H31z" />
		<path
			d="M87.375 274v-6.469l15.937-14.906c5.813-5.437 7.032-7.656 7.032-10.781v-.063c-.032-3.687-2.844-6.312-6.844-6.312-4.625 0-7.75 3.031-7.875 7.093l-.031.282h-8.75v-.25c0-8.5 7.062-14.469 16.594-14.469 9.531 0 16.093 5.437 16.093 13.125v.062c0 5.469-2.593 9.032-10.375 16.188l-9.437 8.625v.375h20.437v7.5H87.375Z"
			class="fill-game-hitori-initial-text"
		/>
		<path class="fill-game-hitori-eliminated-bg" d="M326 325h143v143H326z" />
		<path class="fill-game-hitori-initial-bg" d="M178 178h143v143H178z" />
		<path
			d="M249.594 274v-36.438h-.188l-11.5 7.813V237l11.656-8.094h9.376V274h-9.344Z"
			class="fill-game-hitori-initial-text"
		/>
		<path class="fill-game-hitori-initial-bg" d="M178 325h143v143H178z" />
		<path
			d="M234.375 421v-6.469l15.937-14.906c5.813-5.437 7.032-7.656 7.032-10.781v-.063c-.032-3.687-2.844-6.312-6.844-6.312-4.625 0-7.75 3.031-7.875 7.093l-.031.282h-8.75v-.25c0-8.5 7.062-14.469 16.594-14.469 9.531 0 16.093 5.437 16.093 13.125v.062c0 5.469-2.593 9.032-10.375 16.188l-9.437 8.625v.375h20.437v7.5h-32.781Z"
			class="fill-game-hitori-initial-text"
		/>
		<path class="fill-game-hitori-initial-bg" d="M325 178h143v143H325z" />
		<path
			d="M397.312 274.781c-10.218 0-16.906-5.375-17.531-13.156l-.031-.406h9l.062.343c.407 3.219 3.594 5.626 8.469 5.626 4.844 0 7.969-2.594 7.969-6.282v-.062c0-4.313-3.219-6.688-8.656-6.688h-5.156v-6.718h5.031c4.719 0 7.781-2.5 7.781-6.157v-.062c0-3.657-2.594-5.844-7.031-5.844-4.407 0-7.407 2.281-7.781 5.781l-.032.282h-8.656l.031-.376c.625-7.812 6.969-12.937 16.438-12.937 9.719 0 15.969 4.75 15.969 11.844v.062c0 5.625-4.126 9.281-9.407 10.375v.188c6.688.625 11.063 4.5 11.063 10.625v.062c0 8.063-7 13.5-17.532 13.5Z"
			class="fill-game-hitori-initial-text"
		/>
	</g>
	<defs>
		<clipPath id="hitori-cover__a">
			<path fill="#fff" d="M0 0h498v498H0z" />
		</clipPath>
	</defs>
</svg>
