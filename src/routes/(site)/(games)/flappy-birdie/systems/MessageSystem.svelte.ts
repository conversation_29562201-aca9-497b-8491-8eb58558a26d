import type { GameScreen } from '../scenes/GameScreen.svelte';
import { Floor } from '../actors/Floor';
import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import { FlappyBirdieDifficulty, settings } from '../settings.svelte';
import type { Stats } from '$lib/util/Stats.svelte';
import type { Timer } from '$lib/util/Timer.svelte';
import { theme } from '$lib/stores/theme.svelte';
import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
import { gameSoundManager } from '$lib/stores/gameSoundManager.svelte';
import { getRandomItemWithWeightsAt } from '$lib/functions/getRandomItemWithWeightsAt';

interface Props {
	scene: GameScreen;
	timer: Timer;
	getStats: () => Stats<'score' | 'messagesSeen', 'totalScore' | 'bestScore' | 'averageScore'>;
}

export class MessageSystem {
	message = $state('');
	seenMessages = $state(0);
	private getStats: () => Stats<
		'score' | 'messagesSeen',
		'totalScore' | 'bestScore' | 'averageScore'
	>;
	private hitRoof = false;
	private hitPipe = false;
	private diedOnFloor = false;
	private newRecord = false;
	private timerWasPaused = false;
	private currentThemeBrightness = theme.brightness;
	private currentMusicMuted = musicPlayer?.muted ?? false;
	private currentGameSoundMuted = gameSoundManager?.muted ?? false;
	private playAmount = 0;
	private scene: GameScreen;
	private timer: Timer;
	private cleanupTimerEffect: () => void;
	private cleanupThemeEffect: () => void;
	private cleanupMusicPlayerEffect: () => void;
	private cleanupGameSoundEffect: () => void;
	private messageGroups: MessageGroup[] = [
		// 3rd time
		// new MessageGroup({
		// 	canDisplay: () => this.playAmount === 3,
		// 	messages: [
		// 		new Message({
		// 			key: '3rd-0',
		// 			message: "3rd time's a charm",
		// 		}),
		// 	],
		// }),
		// Timer paused
		new MessageGroup({
			canDisplay: () => this.timerWasPaused,
			messages: [
				new Message({
					key: 'timer-0',
					message:
						'Did you see that? I stopped the time for a moment! I have time powers!',
				}),
			],
		}),
		// 30th time
		// new MessageGroup({
		// 	canDisplay: () =>
		// 		this.playAmount === 30 || this.getStats().fixedStats.totalGames.value === 30,
		// 	messages: [
		// 		new Message({
		// 			key: '30th-0',
		// 			message: "30th time's a charm...",
		// 		}),
		// 	],
		// }),
		// Invisible ceiling
		new MessageGroup({
			canDisplay: () => this.hitRoof,
			messages: [
				new Message({
					key: 'invisible-ceiling-0',
					message:
						'I believe I can fly, I believe I can touch the sky... Oh no, invisible ceiling!',
				}),
			],
		}),
		// Died on floor
		// new MessageGroup({
		// 	canDisplay: () => this.diedOnFloor,
		// 	messages: [
		// 		new Message({
		// 			key: 'died-on-floor-0',
		// 			message: 'The pipes are little bit higher',
		// 		}),
		// 		new Message({
		// 			key: 'died-on-floor-1',
		// 			message: "I can't walk, just fly (˶˃⤙˂˶)",
		// 		}),
		// 	],
		// }),
		// New record
		new MessageGroup({
			canDisplay: () => this.newRecord,
			messages: [
				new Message({
					key: 'new-record-0',
					message: 'New record!',
				}),
				new Message({
					key: 'new-record-1',
					message: 'New record! ヾ( ˃ᴗ˂ )◞ • *✰',
				}),
			],
		}),
		// Score is 0
		new MessageGroup({
			canDisplay: () => this.scene.score === 0,
			messages: [
				new Message({
					key: 'score-0-0',
					message: "You've got this! The sky's waiting for you!",
				}),
				new Message({
					key: 'score-0-1',
					message: 'The clouds are calling - give it another try!',
				}),
				new Message({
					key: 'score-0-2',
					message: "You're a true flapper at heart. Let's take off again!",
				}),
				new Message({
					key: 'score-0-3',
					message: 'Every fall is a chance to rise even higher!',
				}),
				new Message({
					key: 'score-0-4',
					message: 'The wind is perfect for flying. Tap to soar again!',
				}),
				new Message({
					key: 'score-0-5',
					message: "Every flight is a new story. Let's make this one epic!",
				}),
			],
		}),
		// Score is <= 1
		new MessageGroup({
			canDisplay: () => this.scene.score <= 1,
			messages: [
				new Message({
					key: 'score-1-0',
					message: "We'll do better next time!",
				}),
				new Message({
					key: 'score-<=1-1',
					message: "So close! Let's flap to new heights!",
				}),
				new Message({
					key: 'score-<=1-2',
					message: "Don't give up!",
				}),
				new Message({
					key: 'score-<=1-3',
					message: 'We can do it! ദ്ദി(˵ •̀ ᴗ - ˵ ) ✧',
				}),
				new Message({
					key: 'score-<=1-4',
					message: "You're a natural flapper! One more try?",
				}),
			],
		}),
		// Score is < 10 and Hit pipe
		new MessageGroup({
			canDisplay: () => this.scene.score < 10 && this.hitPipe,
			messages: [
				// new Message({
				// 	key: 'hit-pipe-0',
				// 	message: 'I was AFK when I hit that pipe...',
				// }),
				new Message({
					key: 'hit-pipe-1',
					message: "Don't worry, it didn't hurt (˶ᵔ ᵕ ᵔ˶)",
				}),
				// new Message({
				// 	key: 'hit-pipe-2',
				// 	message: "I didn't see that pipe comming...",
				// }),
			],
		}),
		// Score is < 10
		new MessageGroup({
			canDisplay: () => this.scene.score < 10,
			messages: [
				new Message({
					key: 'score-<10-0',
					message: "Little bird, big dreams! The sky's calling you back.",
				}),
				new Message({
					key: 'score-<10-1',
					message: 'Time to try again',
				}),
				new Message({
					key: 'score-<10-2',
					message: "Flapping is an art, and you're the artist. Try again!",
				}),
				new Message({
					key: 'score-<10-3',
					message: "Let's do this, human!",
				}),
				new Message({
					key: 'score-<10-4',
					message: '＼(＾∀＾)メ(＾∀＾)ノ',
				}),
				new Message({
					key: 'score-<10-5',
					message: "You were born to flap! Let's give it another shot!",
				}),
				new Message({
					key: 'score-<10-6',
					message: 'Every fall brings you closer to mastering the skies!',
				}),
				new Message({
					key: 'score-<10-7',
					message: 'Birdie back in action!',
				}),
				new Message({
					key: 'score-<10-8',
					message: "No bird succeeds without trying. Let's rise together!",
				}),
				new Message({
					key: 'score-<10-9',
					message: 'Go, go, go!',
				}),
				new Message({
					key: 'score-<10-10',
					message: 'We are improving!',
				}),
				new Message({
					key: 'score-<10-11',
					message: 'ヽ(・∀・)ﾉ',
				}),
				// new Message({
				// 	key: 'score-<10-12',
				// 	message: 'GG!',
				// }),
			],
		}),
		// Score is >= 10 and difficulty is easy
		new MessageGroup({
			canDisplay: () =>
				this.scene.score >= 10 && settings.difficulty === FlappyBirdieDifficulty.Easy,
			messages: [
				new Message({
					key: 'score->=10-easy-0',
					message: "Great! Let's play the Normal mode!",
				}),
			],
		}),
		// Score is >= 10 and difficulty is normal
		new MessageGroup({
			canDisplay: () =>
				this.scene.score >= 10 && settings.difficulty === FlappyBirdieDifficulty.Normal,
			messages: [
				new Message({
					key: 'score->=10-normal-0',
					message: 'Ready to play the Hard mode!',
				}),
			],
		}),
		// Score is >= 10 and difficulty is hard
		new MessageGroup({
			canDisplay: () =>
				this.scene.score >= 10 && settings.difficulty === FlappyBirdieDifficulty.Hard,
			messages: [
				// new Message({
				// 	key: 'score->=10-hard-0',
				// 	message: 'Yeah! Is this easy mode?',
				// }),
				new Message({
					key: 'score->=10-hard-1',
					message: "You've mastered it! How about that Very Hard mode?",
				}),
			],
		}),
		// Score is >= 10 and difficulty is very hard
		new MessageGroup({
			canDisplay: () =>
				this.scene.score >= 10 && settings.difficulty === FlappyBirdieDifficulty.VeryHard,
			messages: [
				new Message({
					key: 'score->=10-very-hard-0',
					message: 'I-M-P-R-E-S-S-I-V-E!',
				}),
			],
		}),
		// Score is >= 10
		new MessageGroup({
			canDisplay: () => this.scene.score >= 10,
			messages: [
				new Message({
					key: 'score->=10-0',
					message: "I'm just getting started!",
				}),
				new Message({
					key: 'score->=10-1',
					message: 'Pure skill!',
				}),
				new Message({
					key: 'score->=10-2',
					message: "I'm on the next level!",
				}),
				new Message({
					key: 'score->=10-3',
					message: "I'm on fire!",
				}),
				new Message({
					key: 'score->=10-4',
					message: 'GG WP!',
				}),
				new Message({
					key: 'score->=10-5',
					message: 'Well Played!',
				}),
				new Message({
					key: 'score->=10-6',
					message: 'No hacks required!',
				}),
				new Message({
					key: 'score->=10-7',
					message: "I'm a professional!",
				}),
				new Message({
					key: 'score->=10-8',
					message: "I'm the best!",
				}),
				new Message({
					key: 'score->=10-9',
					message:
						"Mirror, mirror on the Wall, who's the best bird of them all? Of course it's me!",
				}),
				new Message({
					key: 'score->=10-10',
					message: 'Those flying lessons paid off!',
				}),
				new Message({
					key: 'score->=10-11',
					message: "We're pretty good together",
				}),
				new Message({
					key: 'score->=10-12',
					message: '(˶˃ ᵕ ˂˶) .ᐟ.ᐟ',
				}),
			],
		}),
	];
	private siteMessageGroups = {
		theme: [
			// Light theme
			new MessageGroup({
				canDisplay: () => this.currentThemeBrightness === 'light',
				messages: [
					new Message({
						key: 'light-theme-0',
						message: 'Look, player. Everything the light touches is our kingdom',
					}),
				],
			}),
			// Dark theme
			new MessageGroup({
				canDisplay: () => this.currentThemeBrightness === 'dark',
				messages: [
					new Message({
						key: 'dark-theme-0',
						message:
							"♫ Hello darkness, my old friend. I've come to talk with you again ♫",
					}),
				],
			}),
		],
		music: {
			// Muted
			muted: [
				new MessageGroup({
					canDisplay: () => this.currentMusicMuted,
					messages: [
						new Message({
							key: 'music-muted',
							message: 'No music? No problem!',
						}),
					],
				}),
				// Unmuted
				new MessageGroup({
					canDisplay: () => !this.currentMusicMuted,
					messages: [
						new Message({
							key: 'music-unmuted',
							message: 'Yay, music! ( ˘ ɜ˘) ♬♪♫',
						}),
					],
				}),
			],
		},
		gameSound: [
			new MessageGroup({
				canDisplay: () => this.currentGameSoundMuted,
				messages: [
					new Message({
						key: 'game-sound-muted',
						message: '... ... ... ... ...',
					}),
				],
			}),
			new MessageGroup({
				canDisplay: () => !this.currentGameSoundMuted,
				messages: [
					new Message({
						key: 'game-sound-unmuted',
						message: 'Wow, I thought I was deaf for a moment',
					}),
				],
			}),
		],
	};
	private allMessagesReadGroup = new MessageGroup({
		canDisplay: () => {
			// Show it just once
			if (!this.allMessagesReadGroup.hasNewMessagesToDisplay) {
				return false;
			}

			const readAllMessages =
				this.messageGroups.every((group) => !group.hasNewMessagesToDisplay) &&
				[
					...this.siteMessageGroups.gameSound,
					...this.siteMessageGroups.music.muted,
					...this.siteMessageGroups.theme,
				].every((group) => !group.hasNewMessagesToDisplay);

			return readAllMessages;
		},
		messages: [
			new Message({
				key: 'messages-read-0',
				message:
					"Wow! You read all the messages I memorized. Congrats! For now on, I'll just repeat myself... unless you can teach me some funny quotes by sending a feedback",
			}),
		],
	});

	constructor({ scene, timer, getStats }: Props) {
		this.scene = scene;
		this.timer = timer;
		this.seenMessages = Message.storedMessagesAmount;
		this.getStats = getStats;

		this.scene.on('initialize', () => {
			this.scene.bird.on('hitRoof', () => {
				this.hitRoof = true;
			});

			this.scene.bird.on('hitPipe', () => {
				this.hitPipe = true;
			});

			this.scene.bird.on('dead', ({ collidedWith }) => {
				this.diedOnFloor = collidedWith instanceof Floor;

				if (this.getStats().liveStats.score.isNewBest) {
					this.newRecord = true;
				}

				this.generateMessage();
			});
		});

		this.scene.on('started', () => {
			this.playAmount += 1;
			this.resetState();
		});

		this.cleanupTimerEffect = $effect.root(() => {
			$effect(() => {
				if (this.timer.paused && this.timer.started && !this.timer.stopped) {
					this.timerWasPaused = true;
				}
			});
		});

		this.cleanupThemeEffect = $effect.root(() => {
			$effect(() => {
				if (
					this.currentThemeBrightness !== theme.brightness &&
					this.timer.paused &&
					!this.timer.started
				) {
					this.currentThemeBrightness = theme.brightness;
					this.generateMessage(this.siteMessageGroups.theme);
				}
			});
		});

		this.cleanupMusicPlayerEffect = $effect.root(() => {
			$effect(() => {
				if (this.timer.paused && !this.timer.started) {
					if (musicPlayer.muted !== this.currentMusicMuted) {
						this.currentMusicMuted = musicPlayer.muted;
						this.generateMessage(this.siteMessageGroups.music.muted);
					}
				}
			});
		});

		this.cleanupGameSoundEffect = $effect.root(() => {
			$effect(() => {
				if (gameSoundManager && this.timer.paused && !this.timer.started) {
					if (gameSoundManager.muted !== this.currentGameSoundMuted) {
						this.currentGameSoundMuted = gameSoundManager.muted;
						this.generateMessage(this.siteMessageGroups.gameSound);
					}
				}
			});
		});
	}

	generateMessage(messageGroups = this.messageGroups) {
		let messageGroup = messageGroups.find(
			(group) => group.canDisplay() && group.hasNewMessagesToDisplay,
		);

		if (!messageGroup) {
			if (
				this.allMessagesReadGroup.hasNewMessagesToDisplay &&
				this.allMessagesReadGroup.canDisplay()
			) {
				messageGroup = this.allMessagesReadGroup;
			} else {
				const items = messageGroups.filter((group) => group.canDisplay());
				const weights = items.map((item) => item.messagesAmount);

				messageGroup = getRandomItemWithWeightsAt(items, weights);
			}
		}

		this.message = messageGroup?.getNextMessage() ?? '';
		this.updateSeenMessagesStats();
	}

	private updateSeenMessagesStats() {
		const stats = this.getStats();

		this.seenMessages = Message.storedMessagesAmount;
		stats.save();
	}

	private resetState() {
		this.hitRoof = false;
		this.hitPipe = false;
		this.diedOnFloor = false;
		this.newRecord = false;
		this.timerWasPaused = false;
	}

	resetMessages() {
		Message.resetStoredMessages();
		this.seenMessages = 0;
	}

	dispose() {
		this.cleanupTimerEffect?.();
		this.cleanupThemeEffect?.();
		this.cleanupMusicPlayerEffect?.();
		this.cleanupGameSoundEffect?.();
	}
}

interface MessageProps {
	message: string;
	key: string;
}

class Message {
	message: string;
	key: string;

	constructor({ message, key }: MessageProps) {
		this.message = message;
		this.key = key;

		if (!Message.storedMessages) {
			Message.storedMessages = JSON.parse(
				localStorage.getItem('flappy-birdie-messages') ?? '{}',
			);
		}
	}

	get wasDisplayed() {
		return Message.storedMessages[this.key];
	}

	set wasDisplayed(value: boolean) {
		Message.storedMessages[this.key] = value;
		localStorage.setItem('flappy-birdie-messages', JSON.stringify(Message.storedMessages));
	}

	static storedMessages: Record<string, boolean>;

	static get storedMessagesAmount() {
		return Object.keys(Message.storedMessages).length;
	}

	static resetStoredMessages() {
		Message.storedMessages = {};
		localStorage.setItem('flappy-birdie-messages', JSON.stringify(Message.storedMessages));
	}
}

interface MessageGroupProps {
	messages: Message[];
	canDisplay: () => boolean;
}

class MessageGroup {
	private messages: Message[];
	canDisplay: () => boolean;

	constructor({ messages, canDisplay }: MessageGroupProps) {
		this.messages = messages;
		this.canDisplay = canDisplay;
	}

	get hasNewMessagesToDisplay() {
		return this.messages.some((message) => !message.wasDisplayed);
	}

	get messagesAmount() {
		return this.messages.length;
	}

	getNextMessage(): string {
		const firstMessageNotDisplayedYet = this.messages.find((message) => !message.wasDisplayed);

		if (firstMessageNotDisplayedYet) {
			firstMessageNotDisplayedYet.wasDisplayed = true;
			return firstMessageNotDisplayedYet.message;
		}

		return getRandomItemAt(this.messages).message;
	}
}
