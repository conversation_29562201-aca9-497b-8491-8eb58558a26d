<script lang="ts">
	import { theme } from '$lib/stores/theme.svelte';
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();

	let url = $derived.by(() => {
		if (theme.loaded) {
			if (theme.brightness === 'light') {
				return 'https://static.lofiandgames.com/covers/flappy-birdie-cover-light.svg';
			} else {
				return 'https://static.lofiandgames.com/covers/flappy-birdie-cover-dark.svg';
			}
		}
	});

	let style = $derived.by(() => {
		if (url) {
			return `background-image: url('${url}')`;
		}
	});
</script>

<div class={cn('bg-center bg-cover bg-no-repeat size-full', className)} {style} {...props}></div>
