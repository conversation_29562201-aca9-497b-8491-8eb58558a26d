<script lang="ts">
	import FlappyBirdie from './FlappyBirdie.svelte';
	import { MetaTags } from 'svelte-meta-tags';
	import PageTransition from '$lib/components/PageTransition.svelte';
</script>

<MetaTags
	title="Play Flappy Birdie Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play flappy birdie online for free. Beautiful flappy birdie game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/flappy-birdie"
	openGraph={{
		url: 'https://www.lofiandgames.com/flappy-birdie',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-flappy-birdie.png',
				width: 1200,
				height: 630,
				alt: 'Flappy Birdie Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Flappy Birdie on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-flappy-birdie.png',
		site: 'https://www.lofiandgames.com/flappy-birdie',
	}}
/>

<PageTransition>
	<FlappyBirdie />
</PageTransition>
