import {
	Animation,
	AnimationStrategy,
	ImageSource,
	ImageWrapping,
	range,
	SpriteSheet,
} from 'excalibur';
import { TiledSprite } from '../lib/TilesSprite';

const themeToImageMap = {
	light: 'https://static.lofiandgames.com/images/flappy-birdie/flappy-birdie-light.png',
	dark: 'https://static.lofiandgames.com/images/flappy-birdie/flappy-birdie-dark.png',
} as const;

export type FlappyBirdieSupportedThemes = keyof typeof themeToImageMap;

class FlappyBirdieSpriteSheet {
	private _theme: FlappyBirdieSupportedThemes = 'light';
	private spriteSheets: Partial<Record<FlappyBirdieSupportedThemes, SpriteSheet>> = {};

	constructor() {}

	get spriteSheet() {
		return this.spriteSheets[this._theme]!;
	}

	get theme() {
		return this._theme;
	}

	async load(theme: FlappyBirdieSupportedThemes) {
		this._theme = theme;

		if (this.spriteSheets[theme]) {
			return Promise.resolve();
		}

		const image = new ImageSource(themeToImageMap[theme]);

		await image.load();

		this.spriteSheets[theme] = SpriteSheet.fromImageSourceWithSourceViews({
			image,
			sourceViews: [
				// Bird fly
				{ x: 0, y: 0, width: 87, height: 67 },
				{ x: 92, y: 0, width: 87, height: 67 },
				{ x: 184, y: 0, width: 87, height: 67 },
				// Bird dead
				{ x: 276, y: 0, width: 87, height: 67 },
				// Floor
				{ x: 368, y: 0, width: 60, height: 90 },
				// Buildings
				{ x: 0, y: 72, width: 127, height: 69 },
				// Forest
				{ x: 132, y: 72, width: 106, height: 62 },
				// Clouds
				{ x: 0, y: 146, width: 350, height: 143 },
				// Pipe End
				{ x: 243, y: 72, width: 105, height: 44 },
				// Pipe
				{ x: 243, y: 121, width: 91, height: 12 },
				// 0
				{ x: 433, y: 0, width: 38, height: 46 },
				// 1
				{ x: 471, y: 0, width: 38, height: 46 },
				// 2
				{ x: 433, y: 46, width: 38, height: 46 },
				// 3
				{ x: 471, y: 46, width: 38, height: 46 },
				// 4
				{ x: 433, y: 92, width: 38, height: 46 },
				// 5
				{ x: 471, y: 92, width: 38, height: 46 },
				// 6
				{ x: 433, y: 138, width: 38, height: 46 },
				// 7
				{ x: 471, y: 138, width: 38, height: 46 },
				// 8
				{ x: 433, y: 184, width: 38, height: 46 },
				// 9
				{ x: 471, y: 184, width: 38, height: 46 },
			],
		});
	}

	birdFly() {
		return Animation.fromSpriteSheet(
			this.spriteSheet,
			range(0, 2),
			60,
			AnimationStrategy.PingPong,
		);
	}

	birdDead() {
		return this.spriteSheet.getSprite(3, 0);
	}

	floor({ width, height }: { width: number; height: number }) {
		return new TiledSprite({
			width,
			height,
			image: this.spriteSheet.getSprite(4, 0).image,
			sourceView: { x: 368, y: 0, width: 60, height: 90 },
			wrapping: {
				x: ImageWrapping.Repeat,
				y: ImageWrapping.Clamp,
			},
		});
	}

	buildings({ width, height }: { width: number; height: number }) {
		return new TiledSprite({
			width,
			height,
			image: this.spriteSheet.getSprite(5, 0).image,
			sourceView: { x: 0, y: 72, width: 127, height: 69 },
			wrapping: {
				x: ImageWrapping.Repeat,
				y: ImageWrapping.Clamp,
			},
		});
	}

	forest({ width, height }: { width: number; height: number }) {
		return new TiledSprite({
			width,
			height,
			image: this.spriteSheet.getSprite(6, 0).image,
			sourceView: { x: 132, y: 72, width: 106, height: 62 },
			wrapping: {
				x: ImageWrapping.Repeat,
				y: ImageWrapping.Clamp,
			},
		});
	}

	clouds({ width, height }: { width: number; height: number }) {
		return new TiledSprite({
			width,
			height,
			image: this.spriteSheet.getSprite(7, 0).image,
			sourceView: { x: 0, y: 146, width: 350, height: 143 },
			wrapping: {
				x: ImageWrapping.Repeat,
				y: ImageWrapping.Clamp,
			},
		});
	}

	pipe({ width, height }: { width: number; height: number }) {
		return new TiledSprite({
			width,
			height,
			image: this.spriteSheet.getSprite(9, 0).image,
			sourceView: { x: 243, y: 121, width: 91, height: 12 },
			wrapping: {
				x: ImageWrapping.Repeat,
				y: ImageWrapping.Clamp,
			},
		});
	}

	pipeEnd() {
		return this.spriteSheet.getSprite(8, 0);
	}

	score(number: number) {
		return this.spriteSheet.getSprite(10 + number, 0);
	}
}

export const flappyBirdieSpriteSheet = new FlappyBirdieSpriteSheet();
