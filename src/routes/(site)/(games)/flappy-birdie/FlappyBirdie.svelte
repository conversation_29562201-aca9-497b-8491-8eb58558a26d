<script lang="ts">
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import MouseLeftIcon from '$lib/components/Icons/MouseLeftIcon.svelte';
	import TouchIcon from '$lib/components/Icons/TouchIcon.svelte';
	import { fade } from 'svelte/transition';
	import type { FlappyBirdieGame } from './FlappyBirdieGame.svelte';
	import DirectionKeys from '$lib/components/DirectionKeys.svelte';
	import FlappyBirdieInfoModal from './FlappyBirdieInfoModal.svelte';
	import { theme } from '$lib/stores/theme.svelte';
	import FeedbackModal from '$lib/components/FeedbackModal.svelte';
	import { FlappyBirdieDifficulty, FlappyBirdieSpeed } from './settings.svelte';
	import { MetaTags } from 'svelte-meta-tags';
	import { gameDifficultyLabel, speedLabel } from './labels';
	import { toast } from 'svelte-sonner';
	import { cn } from '$lib/util/cn';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';

	let game = $state<FlappyBirdieGame>();
	let canvas = $state() as HTMLCanvasElement;
	let isInfoModalOpen = $state(false);
	let isFeedbackModalOpen = $state(false);
	let tab = $state('difficulty' as 'difficulty' | 'speed');

	async function createGame() {
		const { FlappyBirdieGame } = await import('./FlappyBirdieGame.svelte');

		game = new FlappyBirdieGame({ canvas });
	}

	$effect(() => {
		try {
			createGame();
		} catch (_error) {
			setTimeout(() => {
				toast.error(
					'Oops, there was an error loading the game. Please refresh the page to try again.',
					{
						duration: 10_000,
					},
				);
			}, 1000);
		}

		return () => {
			game?.dispose();
		};
	});

	function changeDifficulty(difficulty: FlappyBirdieDifficulty) {
		game?.changeDifficulty(difficulty);
		canvas.focus();
	}

	function changeSpeed(speed: FlappyBirdieSpeed) {
		game?.changeSpeed(speed);
		canvas.focus();
	}
</script>

{#if game?.skyColor}
	<MetaTags
		additionalMetaTags={[
			{
				name: 'theme-color',
				content: theme.brightness === 'dark' ? '#02253D' : game?.skyColor,
			},
		]}
	/>
{/if}

<FlappyBirdieInfoModal bind:isOpen={isInfoModalOpen} />

<FeedbackModal context="Flappy Birdie" bind:isOpen={isFeedbackModalOpen} />

<GameLayout
	noPadding
	navbarStyle="on-top"
	adsProps={{
		verticalAdSide: 'left',
		variant: 'on-top',
		horizontalAdSide: 'bottom',
		minHeightToShowHorizontalAd: 844,
	}}
	navbarProps={{
		profileButtonProps: {
			extraMenuItems: [
				{
					Icon: InfoSolidIcon,
					title: 'About this game',
					onclick: () => (isInfoModalOpen = true),
				},
			],
		},
	}}
	soundButtonAccentWhenPlaying={theme.brightness === 'dark'}
>
	{#snippet Island()}
		{#if game?.context}
			<GameIsland
				context={game.context}
				gameOver={game.timer?.stopped}
				gameOverStrategy="best-stats-update"
				gameOverIslandDelay={0}
			/>
		{/if}
	{/snippet}

	{#if !game || game?.isLoading}
		<span
			transition:fade={{ duration: 150 }}
			class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 loading loading-spinner loading-lg"
		></span>
	{/if}

	{#if game && !game?.isLoading && !game?.timer.started}
		{#if game.messageSystem?.message}
			<div
				transition:fade|global
				class="absolute top-1/2 left-1/2 -translate-x-1/2 translate-y-[calc(-100%-50px)] pointer-events-none flex w-full items-center justify-center"
			>
				<div
					class="py-3 px-6 rounded-3xl max-w-80 bg-white text-black message-bubble border-white text-center"
				>
					{game.messageSystem?.message}
				</div>
			</div>
		{/if}

		<!-- svelte-ignore a11y_click_events_have_key_events -->
		<!-- svelte-ignore a11y_no_static_element_interactions -->
		<div
			class="flex flex-col gap-4 absolute top-1/2 left-1/2 -translate-x-1/2 lg:translate-y-20 translate-y-10 z-10 bg-base-100 p-4 rounded-2xl"
			onclick={() => game?.gameScreen?.start()}
			transition:fade
		>
			<div role="tablist" class="tabs tabs-box mx-auto grid grid-cols-2 w-full">
				<button
					role="tab"
					class="tab"
					class:tab-active={tab === 'difficulty'}
					onclick={(e) => {
						e.stopPropagation();
						tab = 'difficulty';
					}}
				>
					Difficulty
				</button>

				<button
					role="tab"
					class="tab"
					class:tab-active={tab === 'speed'}
					onclick={(e) => {
						e.stopPropagation();
						tab = 'speed';
					}}
				>
					Speed
				</button>
			</div>

			<div onclick={(e) => e.stopPropagation()}>
				{#if tab === 'difficulty'}
					<div
						class="grid grid-cols-2 lg:grid-cols-4 gap-2 items-center justify-center w-52 lg:w-auto"
					>
						<button
							class={cn('btn btn-soft btn-sm whitespace-nowrap', {
								'btn-active btn-primary':
									game?.difficulty === FlappyBirdieDifficulty.Easy,
							})}
							onclick={(e) => {
								changeDifficulty(FlappyBirdieDifficulty.Easy);
								e.stopPropagation();
							}}
						>
							{gameDifficultyLabel[FlappyBirdieDifficulty.Easy]}
						</button>
						<button
							class={cn('btn btn-soft btn-sm whitespace-nowrap', {
								'btn-active btn-primary':
									game?.difficulty === FlappyBirdieDifficulty.Normal,
							})}
							onclick={(e) => {
								changeDifficulty(FlappyBirdieDifficulty.Normal);
								e.stopPropagation();
							}}
						>
							{gameDifficultyLabel[FlappyBirdieDifficulty.Normal]}
						</button>
						<button
							class={cn('btn btn-soft btn-sm whitespace-nowrap', {
								'btn-active btn-primary':
									game?.difficulty === FlappyBirdieDifficulty.Hard,
							})}
							onclick={(e) => {
								changeDifficulty(FlappyBirdieDifficulty.Hard);
								e.stopPropagation();
							}}
						>
							{gameDifficultyLabel[FlappyBirdieDifficulty.Hard]}
						</button>
						<button
							class={cn('btn btn-soft btn-sm btn-error whitespace-nowrap', {
								'btn-active': game?.difficulty === FlappyBirdieDifficulty.VeryHard,
							})}
							onclick={(e) => {
								changeDifficulty(FlappyBirdieDifficulty.VeryHard);
								e.stopPropagation();
							}}
						>
							{gameDifficultyLabel[FlappyBirdieDifficulty.VeryHard]}
						</button>
					</div>
				{:else if tab === 'speed'}
					<div
						class="grid grid-cols-2 lg:grid-cols-4 gap-2 items-center justify-center w-52 lg:w-auto"
					>
						<button
							class={cn('btn btn-soft btn-sm whitespace-nowrap lg:col-start-2', {
								'btn-active btn-primary': game?.speed === FlappyBirdieSpeed.Normal,
							})}
							onclick={(e) => {
								changeSpeed(FlappyBirdieSpeed.Normal);
								e.stopPropagation();
							}}
						>
							{speedLabel[FlappyBirdieSpeed.Normal]}
						</button>
						<button
							class={cn('btn btn-soft btn-sm whitespace-nowrap btn-error', {
								'btn-active': game?.speed === FlappyBirdieSpeed.Fast,
							})}
							onclick={(e) => {
								changeSpeed(FlappyBirdieSpeed.Fast);
								e.stopPropagation();
							}}
						>
							{speedLabel[FlappyBirdieSpeed.Fast]}
						</button>
					</div>
				{/if}
			</div>

			<div class="flex items-center justify-center">
				<div class="hidden items-center justify-center gap-2 text-center lg:flex">
					<div class="flex items-center justify-center flex-col gap-2">
						<MouseLeftIcon class="size-14" />
					</div>

					<div class="divider divider-horizontal">OR</div>

					<div class="flex items-center justify-center flex-col gap-2">
						<DirectionKeys variant="up" />
					</div>

					<div class="divider divider-horizontal">OR</div>

					<div class="flex items-center justify-center flex-col gap-2 self-end">
						<kbd class="kbd kbd-lg px-8">space</kbd>
					</div>
				</div>

				<div class="lg:hidden flex gap-2 flex-col items-center justify-center">
					<TouchIcon class="size-14" />
					Touch to start
				</div>
			</div>
		</div>
	{/if}

	<canvas bind:this={canvas} class="size-full"></canvas>
</GameLayout>

<style>
	.message-bubble:after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 50%;
		width: 0;
		height: 0;
		border: 8px solid transparent;
		border-top-color: inherit;
		border-bottom: 0;
		margin-left: -8px;
		margin-bottom: -8px;
	}
</style>
