/** TODO: Remove this file once TiledSprite is available on the library */

import {
	Future,
	ImageFiltering,
	ImageSourceAttributeConstants,
	ImageWrapping,
	Sprite,
	TextureLoader,
	ImageSource,
	type ImageSourceOptions,
	type ImageWrapConfiguration,
	type SourceView,
} from 'excalibur';

export interface TiledSpriteOptions {
	image: ImageSource;
	/**
	 * Source view into the {@link ImageSource image}
	 */
	sourceView?: SourceView;
	/**
	 * Optionally override {@link ImageFiltering filtering}
	 */
	filtering?: ImageFiltering;
	/**
	 * Optionally override {@link ImageWrapping wrapping} , default wrapping is Repeat for TiledSprite
	 */
	wrapping?: ImageWrapConfiguration | ImageWrapping;
	/**
	 * Total width in pixels for the tiling to take place over
	 */
	width: number;
	/**
	 * Total height in pixels for the tiling to take place over
	 */
	height: number;
}

export class TiledSprite extends Sprite {
	private static spriteCache = new Map<string, HTMLCanvasElement>();
	private _ready = new Future<void>();
	public ready = this._ready.promise;
	private _options: TiledSpriteOptions;
	constructor(options: TiledSpriteOptions) {
		super({
			image: options.image,
			sourceView: options.sourceView,
			destSize: { width: options.width, height: options.height },
		});
		this._options = options;

		if (this.image.isLoaded()) {
			this._applyTiling();
		} else {
			this.image.ready.then(() => this._applyTiling());
		}
	}

	public static fromSprite(
		sprite: Sprite,
		options?: Partial<Omit<TiledSpriteOptions, 'image'>>,
	): TiledSprite {
		return new TiledSprite({
			width: sprite.width,
			height: sprite.height,
			...options,
			image: sprite.image,
		});
	}

	private _applyTiling() {
		const { width, height, filtering, wrapping } = { ...this._options };
		const cacheKey = `${this.sourceView.width}-${this.sourceView.height}-${this.image.path}`;

		if (!TiledSprite.spriteCache.has(cacheKey)) {
			const spriteCanvas = document.createElement('canvas');
			spriteCanvas.width = this.sourceView.width;
			spriteCanvas.height = this.sourceView.height;
			const spriteCtx = spriteCanvas.getContext('2d')!;

			spriteCtx.drawImage(
				this.image.image,
				this.sourceView.x,
				this.sourceView.y,
				this.sourceView.width,
				this.sourceView.height,
				0,
				0,
				this.sourceView.width,
				this.sourceView.height,
			);

			TiledSprite.spriteCache.set(cacheKey, spriteCanvas);
		}

		const cachedCanvas = TiledSprite.spriteCache.get(cacheKey)!;
		// prettier-ignore
		const tiledImageSource = fromHtmlCanvasElement(cachedCanvas, {
			wrapping: wrapping ?? ImageWrapping.Repeat,
			filtering
		});
		if (width) {
			this.destSize.width = width;
			this.sourceView.width = width;
		}
		if (height) {
			this.destSize.height = height;
			this.sourceView.height = height;
		}
		this.sourceView.x = 0;
		this.sourceView.y = 0;
		this.image = tiledImageSource;
		// this._ready.resolve();
		this.image.ready.then(() => this._ready.resolve());
	}
}

function fromHtmlCanvasElement(
	image: HTMLCanvasElement,
	options?: ImageSourceOptions,
): ImageSource {
	const imageSource = new ImageSource('');
	(imageSource as any)._src = 'canvas-element-blob';
	imageSource.data.setAttribute('data-original-src', 'canvas-element-blob');

	if (options?.filtering) {
		imageSource.data.setAttribute(ImageSourceAttributeConstants.Filtering, options?.filtering);
	} else {
		imageSource.data.setAttribute(
			ImageSourceAttributeConstants.Filtering,
			ImageFiltering.Blended,
		);
	}

	if (options?.wrapping) {
		let wrapping: ImageWrapConfiguration;
		if (typeof options.wrapping === 'string') {
			wrapping = {
				x: options.wrapping,
				y: options.wrapping,
			};
		} else {
			wrapping = {
				x: options.wrapping.x,
				y: options.wrapping.y,
			};
		}
		imageSource.data.setAttribute(ImageSourceAttributeConstants.WrappingX, wrapping.x);
		imageSource.data.setAttribute(ImageSourceAttributeConstants.WrappingY, wrapping.y);
	} else {
		imageSource.data.setAttribute(ImageSourceAttributeConstants.WrappingX, ImageWrapping.Clamp);
		imageSource.data.setAttribute(ImageSourceAttributeConstants.WrappingY, ImageWrapping.Clamp);
	}

	TextureLoader.checkImageSizeSupportedAndLog(image);

	image.toBlob((blob) => {
		// TODO throw? if blob null?
		const url = URL.createObjectURL(blob!);
		imageSource.image.onload = () => {
			// no longer need to read the blob so it's revoked
			URL.revokeObjectURL(url);
			imageSource.data = imageSource.image;
			(imageSource as any)._readyFuture.resolve(imageSource.image);
		};
		imageSource.image.src = url;
	});

	return imageSource;
}
