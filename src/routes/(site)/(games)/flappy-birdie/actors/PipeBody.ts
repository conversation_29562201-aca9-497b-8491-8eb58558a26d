import { Actor, CollisionType, Engine, type ActorArgs } from 'excalibur';
import { settings } from '../settings.svelte';
import { flappyBirdieSpriteSheet } from '../assets/flappyBirdieSpriteSheet';
import type { TiledSprite } from '../lib/TilesSprite';
import { NotBirdCollisionGroup } from './collisionGroups';

export class PipeBody extends Actor {
	private sprite!: TiledSprite;

	constructor(config?: ActorArgs) {
		super({
			z: settings.z.pipe,
			collisionGroup: NotBirdCollisionGroup,
			collisionType: CollisionType.PreventCollision,
			...config,
		});

		this.body.useGravity = false;
	}

	reloadGraphics() {
		this.sprite = flappyBirdieSpriteSheet.pipe({
			height: this.height,
			width: this.width,
		});

		this.graphics.use(this.sprite);
	}

	onInitialize(_engine: Engine): void {
		this.reloadGraphics();
	}
}
