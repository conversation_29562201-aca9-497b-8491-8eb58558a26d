import { Actor, CollisionType, Color, Engine, type ActorArgs } from 'excalibur';
import { settings } from '../settings.svelte';

export class Impact extends Actor {
	private isBlinking = false;
	private time = 0;

	constructor(config?: ActorArgs) {
		super({
			color: Color.White,
			opacity: 0,
			z: settings.z.ui,
			collisionType: CollisionType.PreventCollision,
			...config,
		});
	}

	blink() {
		this.graphics.opacity = 0;
		this.time = 0;
		this.isBlinking = true;
	}

	reset() {
		this.isBlinking = false;
		this.graphics.opacity = 0;
		this.time = 0;
	}

	onInitialize(engine: Engine): void {
		engine.on('predraw', (event) => {
			if (this.isBlinking) {
				this.time += event.elapsed;

				if (this.time < 50) {
					this.graphics.opacity = Math.min(this.graphics.opacity + 0.2, 0.8);
				} else if (this.time > 70) {
					this.graphics.opacity = Math.max(0, this.graphics.opacity - 0.15);
				}
			}
		});
	}
}
