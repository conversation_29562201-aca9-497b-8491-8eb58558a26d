import { Actor, CollisionType, Engine, type ActorArgs } from 'excalibur';
import { settings } from '../settings.svelte';
import { PipeBody } from './PipeBody';
import { PipeEnd } from './PipeEnd';
import { NotBirdCollisionGroup } from './collisionGroups';

export class Pipe extends Actor {
	pipeBody?: PipeBody;
	pipeEnd?: PipeEnd;
	pipeEndPosition: 'top' | 'bottom';

	constructor({ pipeEndPosition, ...config }: ActorArgs & { pipeEndPosition: 'top' | 'bottom' }) {
		super({
			z: settings.z.pipe,
			collisionGroup: NotBirdCollisionGroup,
			collisionType: CollisionType.Passive,
			...config,
		});

		this.pipeEndPosition = pipeEndPosition;
	}

	reloadGraphics() {
		this.pipeBody?.reloadGraphics();
		this.pipeEnd?.reloadGraphics();
	}

	onInitialize(_engine: Engine): void {
		this.pipeBody = new PipeBody({
			width: this.width + 8,
			height: this.height,
			x: 0,
			y: 0,
		});

		this.pipeEnd = new PipeEnd({
			x: 0,
			y: this.pipeEndPosition === 'top' ? this.height / 2 : -this.height / 2,
			width: settings.pipeEnd.width - 8,
			height: settings.pipeEnd.height - 8,
		});

		this.addChild(this.pipeBody);
		this.addChild(this.pipeEnd);

		this.reloadGraphics();
	}
}
