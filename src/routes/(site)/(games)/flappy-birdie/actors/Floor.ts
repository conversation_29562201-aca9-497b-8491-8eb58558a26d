import { Actor, CollisionType, Engine, type ActorArgs } from 'excalibur';
import { settings } from '../settings.svelte';
import { flappyBirdieSpriteSheet } from '../assets/flappyBirdieSpriteSheet';
import type { TiledSprite } from '../lib/TilesSprite';
import { NotBirdCollisionGroup } from './collisionGroups';

export class Floor extends Actor {
	sprite!: TiledSprite;

	constructor(config?: ActorArgs) {
		super({
			z: settings.z.floor,
			collisionGroup: NotBirdCollisionGroup,
			collisionType: CollisionType.Fixed,
			...config,
		});

		this.body.useGravity = false;
		this.reloadGraphics();
	}

	reloadGraphics() {
		this.sprite = flappyBirdieSpriteSheet.floor({
			height: this.height,
			width: this.width,
		});

		this.graphics.use(this.sprite);
	}

	onInitialize(_engine: Engine): void {
		this.reloadGraphics();
	}
}
