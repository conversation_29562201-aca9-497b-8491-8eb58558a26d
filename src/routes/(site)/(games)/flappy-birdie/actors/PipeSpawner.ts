import { Actor, CollisionType, Engine, vec, type ActorArgs } from 'excalibur';
import { Pipe } from './Pipe';
import { settings } from '../settings.svelte';
import { PipeGap } from './PipeGap';

interface PipePair {
	top: Pipe;
	bottom: Pipe;
	gap: PipeGap;
}

export class PipeSpawner extends Actor {
	pipePairs?: PipePair[];
	private stopped = true;

	constructor(args?: ActorArgs) {
		super({
			...args,
			collisionType: CollisionType.PreventCollision,
		});
	}

	getAvailablePipeAmountToDeviate(deviateUp: boolean) {
		const pipeHeight = this.pipePairs?.[0].top.height ?? 0;
		const minVisiblePipeAmount = (pipeHeight / 2) * settings.pipe.minVisiblePipePercentage;
		const availablePipeAmountToDeviate = Math.min(
			settings.pipe.gap.maxDisplacement,
			pipeHeight / 2 -
				minVisiblePipeAmount -
				settings.pipe.gap.vertical / 2 +
				(deviateUp ? 0 : -settings.floor.height),
		);

		return availablePipeAmountToDeviate;
	}

	positionPair({ top, bottom, gap }: PipePair, x: number) {
		const deviateUp = Math.random() > 0.5;
		const deviation =
			this.getAvailablePipeAmountToDeviate(deviateUp) * (deviateUp ? -1 : 1) * Math.random();

		top.pos = vec(x, -settings.pipe.gap.vertical / 2 + deviation);
		bottom.pos = vec(
			x,
			(this.scene?.engine.drawHeight ?? 0) + settings.pipe.gap.vertical / 2 + deviation,
		);
		gap.pos = vec(
			x + top.width / 2 - gap.width / 2,
			top.pos.y + top.height / 2 + gap.height / 2,
		);
	}

	calcAmountOfPipesForScreenSize() {
		const pipeAndSpacing = settings.pipe.width + settings.pipe.gap.horizontalBase;

		return 1 + Math.ceil((this.scene?.engine.drawWidth ?? 0) / pipeAndSpacing);
	}

	onInitialize(engine: Engine): void {
		const pipeHeight = engine.drawHeight;

		this.pipePairs = Array.from({ length: this.calcAmountOfPipesForScreenSize() }).map((_) => {
			return {
				top: new Pipe({
					height: pipeHeight,
					width: settings.pipe.width - 8,
					pipeEndPosition: 'top',
				}),
				bottom: new Pipe({
					height: pipeHeight,
					width: settings.pipe.width - 8,
					pipeEndPosition: 'bottom',
				}),
				gap: new PipeGap({
					collisionType: CollisionType.Passive,
					width: settings.pipe.width / 4,
					height: settings.pipe.gap.vertical,
				}),
			};
		});

		this.pipePairs?.forEach((pair) => {
			pair.top.on('initialize', () => {
				pair.top.pipeEnd?.on('exitviewport', () => {
					if (pair.top.pos.x < 0) {
						let mostRightPipeX = -Infinity;

						this.pipePairs?.forEach(({ top }) => {
							mostRightPipeX = Math.max(mostRightPipeX, top.pos.x);
						});

						this.positionPair(
							pair,
							mostRightPipeX + settings.pipe.gap.horizontal + settings.pipe.width,
						);
					}
				});

				this.reset();
			});

			this.scene?.add(pair.top);
			this.scene?.add(pair.bottom);
			this.scene?.add(pair.gap);
		});
	}

	onPreUpdate(engine: Engine): void {
		const speedY = settings.pipe.speed.vertical;

		if (speedY === 0 || this.stopped) {
			return;
		}

		for (let i = 0; i < (this.pipePairs?.length ?? 0); i += 1) {
			const pair = this.pipePairs![i];
			const maxDeviation = this.getAvailablePipeAmountToDeviate(pair.top.body.vel.y < 0);
			const centerX = engine.drawHeight / 2;
			const deviation = centerX - pair.gap.pos.y;

			if (deviation > maxDeviation) {
				pair.top.body.vel.y = speedY;
				pair.bottom.body.vel.y = speedY;
				pair.gap.body.vel.y = speedY;
			} else if (-deviation > maxDeviation) {
				pair.top.body.vel.y = -speedY;
				pair.bottom.body.vel.y = -speedY;
				pair.gap.body.vel.y = -speedY;
			}
		}
	}

	reset() {
		const { gap, initialOffsetX, width } = settings.pipe;

		this.stopped = true;
		this.pipePairs?.forEach((pair, index) => {
			this.positionPair(
				pair,
				(this.scene?.engine.drawWidth ?? 0) +
					(gap.horizontal + width) * index +
					(settings.isDesktop() ? initialOffsetX.desktop : initialOffsetX.mobile) +
					(pair.top.pipeEnd?.width ?? 0) / 2,
			);
			pair.bottom.body.vel = vec(0, 0);
			pair.top.body.vel = vec(0, 0);
			pair.gap.body.vel = vec(0, 0);
		});
	}

	start() {
		this.stopped = false;
		this.pipePairs?.forEach((pair, index) => {
			const signal = (Math.random() > 0.5 ? 1 : -1) * (index % 2 === 0 ? 1 : -1);
			pair.top.body.vel = vec(
				settings.pipe.speed.horizontal,
				signal * settings.pipe.speed.vertical,
			);
			pair.bottom.body.vel = vec(
				settings.pipe.speed.horizontal,
				signal * settings.pipe.speed.vertical,
			);
			pair.gap.body.vel = vec(
				settings.pipe.speed.horizontal,
				signal * settings.pipe.speed.vertical,
			);
		});
	}

	stop() {
		this.stopped = true;
		this.pipePairs?.forEach((pair) => {
			pair.top.body.vel = vec(0, 0);
			pair.bottom.body.vel = vec(0, 0);
			pair.gap.body.vel = vec(0, 0);
		});
	}

	reloadGraphics() {
		this.pipePairs?.forEach((pair) => {
			pair.top.reloadGraphics();
			pair.bottom.reloadGraphics();
		});
	}
}
