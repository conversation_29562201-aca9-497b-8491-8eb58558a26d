import { Actor, CollisionType, GraphicsGroup, Sprite, vec, type ActorArgs } from 'excalibur';
import { flappyBirdieSpriteSheet } from '../assets/flappyBirdieSpriteSheet';
import { settings } from '../settings.svelte';

export class Score extends Actor {
	private _score: number = 0;
	private sprites: Record<string, Sprite> = {};

	constructor(config?: ActorArgs) {
		super({
			z: settings.z.ui,
			collisionType: CollisionType.PreventCollision,
			...config,
		});
	}

	reloadGraphics() {
		this.sprites = {
			'0': flappyBirdieSpriteSheet.score(0),
			'1': flappyBirdieSpriteSheet.score(1),
			'2': flappyBirdieSpriteSheet.score(2),
			'3': flappyBirdieSpriteSheet.score(3),
			'4': flappyBirdieSpriteSheet.score(4),
			'5': flappyBirdieSpriteSheet.score(5),
			'6': flappyBirdieSpriteSheet.score(6),
			'7': flappyBirdieSpriteSheet.score(7),
			'8': flappyBirdieSpriteSheet.score(8),
			'9': flappyBirdieSpriteSheet.score(9),
		};
	}

	onInitialize(): void {
		this.reloadGraphics();

		this.renderScore();
	}

	setScore = (score: number) => {
		this._score = score;

		if (this.isInitialized) {
			this.renderScore();
		}
	};

	private renderScore() {
		const scoreString = this._score.toString();
		const digits = scoreString.length;

		this.graphics.use(
			new GraphicsGroup({
				members: Array.from({ length: digits })
					.fill(0)
					.map((_, index) => {
						const sprite = this.sprites[scoreString[index]];

						return {
							graphic: sprite,
							offset: vec(index * sprite.width, 0),
						};
					}),
			}),
		);
	}
}
