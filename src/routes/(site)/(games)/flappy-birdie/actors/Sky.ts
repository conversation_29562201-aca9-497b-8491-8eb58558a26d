import {
	Actor,
	CollisionType,
	Color,
	EmitterType,
	Engine,
	type Graphic,
	ParticleEmitter,
	Raster,
	type ActorArgs,
	type GraphicOptions,
	type RasterOptions,
} from 'excalibur';
import {
	flappyBirdieSpriteSheet,
	type FlappyBirdieSupportedThemes,
} from '../assets/flappyBirdieSpriteSheet';
import { settings } from '../settings.svelte';

class SkyBg extends Raster {
	fromColor: string;
	toColor: string;

	constructor({
		fromColor,
		toColor,
		...options
	}: GraphicOptions & RasterOptions & { fromColor: string; toColor: string }) {
		super(options);
		this.fromColor = fromColor;
		this.toColor = toColor;
	}

	clone(): Graphic {
		throw new Error('Method not implemented.');
	}

	execute(ctx: CanvasRenderingContext2D): void {
		const gradient = ctx.createLinearGradient(0, 0, 0, this.height);

		gradient.addColorStop(0, this.fromColor);
		gradient.addColorStop(1, this.toColor);

		ctx.fillStyle = gradient;
		ctx.fillRect(0, 0, this.width, this.height);
	}
}

export const themeToSkyColors: Record<
	FlappyBirdieSupportedThemes,
	{ fromColor: string; toColor: string }
> = {
	light: {
		fromColor: '#7DD3FC',
		toColor: '#BAE6FD',
	},
	dark: {
		fromColor: '#082F49',
		toColor: '#075985',
	},
};

export class Sky extends Actor {
	bg?: SkyBg;
	stars?: ParticleEmitter;
	colors? = themeToSkyColors.light;

	constructor(config?: ActorArgs) {
		super({
			z: settings.z.sky,
			collisionType: CollisionType.PreventCollision,
			...config,
		});
	}

	reloadGraphics() {
		this.colors = themeToSkyColors[flappyBirdieSpriteSheet.theme];

		this.bg = new SkyBg({
			width: this.width,
			height: this.height,
			fromColor: this.colors.fromColor,
			toColor: this.colors.toColor,
		});
		this.graphics.use(this.bg);

		if (this.stars) {
			this.stars.clearParticles();

			if (flappyBirdieSpriteSheet.theme === 'dark') {
				this.stars.isEmitting = true;
				this.stars.emitParticles(200);
			} else {
				this.stars.isEmitting = false;
			}
		}
	}

	onInitialize(engine: Engine): void {
		this.body.collisionType = CollisionType.PreventCollision;
		const scale = settings.isDesktop() ? settings.desktopScale : 2;

		this.stars = new ParticleEmitter({
			x: -engine.drawWidth / 2,
			y: -engine.drawHeight / 2,
			height: engine.drawHeight,
			width: engine.drawWidth,
			emitterType: EmitterType.Rectangle,
			isEmitting: false,
			emitRate: 15,
			z: settings.z.stars,
			particle: {
				opacity: 1,
				life: 18000,
				fade: true,
				maxSpeed: 0,
				minSpeed: 0,
				minSize: 1 * scale,
				maxSize: 1 * scale,
				beginColor: Color.Transparent,
				endColor: Color.White,
			},
		});

		this.addChild(this.stars);

		this.reloadGraphics();
	}
}
