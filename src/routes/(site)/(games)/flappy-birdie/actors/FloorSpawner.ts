import { Actor, CollisionType, Engine, vec, type ActorArgs } from 'excalibur';
import { settings } from '../settings.svelte';
import { Floor } from './Floor';

export class FloorSpawner extends Actor {
	floorTiles?: Floor[];

	constructor(args?: ActorArgs) {
		super({
			...args,
			collisionType: CollisionType.PreventCollision,
		});
	}

	reloadGraphics() {
		this.floorTiles?.forEach((tile) => tile.reloadGraphics());
	}

	onInitialize(engine: Engine): void {
		const scale = settings.isDesktop() ? settings.desktopScale : settings.mobileScale;
		const floorWidth = settings.floor.width * scale;
		const width = Math.ceil(1 + engine.drawWidth / floorWidth) * floorWidth;

		this.floorTiles = Array.from({
			length: 2,
		}).map((_, index) => {
			return new Floor({
				width,
				height: settings.floor.height,
				pos: vec(index * width, engine.drawHeight - settings.floor.height / 2),
			});
		});

		this.floorTiles.forEach((tile) => this.scene!.add(tile));

		this.floorTiles.forEach((tile) => {
			tile.on('exitviewport', () => {
				if (tile.pos.x < 0) {
					let mostRightTile = -Infinity;

					this.floorTiles?.forEach((tile) => {
						mostRightTile = Math.max(mostRightTile, tile.pos.x);
					});

					tile.pos.x = mostRightTile + tile.width - 1;
				}
			});
		});

		this.reset();
		this.start();
	}

	reset() {
		this.floorTiles?.forEach((tile, index) => {
			tile.pos.x = index * tile.width;
			tile.body.vel.x = settings.floor.speed;
		});
	}

	start() {
		this.floorTiles?.forEach((tile) => {
			tile.body.vel.x = settings.floor.speed;
		});
	}

	stop() {
		this.floorTiles?.forEach((tile) => {
			tile.body.vel.x = 0;
		});
	}
}
