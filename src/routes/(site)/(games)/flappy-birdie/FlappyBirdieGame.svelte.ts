import { DisplayMode, Engine, FadeInOut, ImageFiltering, vec } from 'excalibur';
import { GameScreen } from './scenes/GameScreen.svelte';
import { Stats } from '$lib/util/Stats.svelte';
import { flappyBirdieSpriteSheet } from './assets/flappyBirdieSpriteSheet';
import { theme } from '$lib/stores/theme.svelte';
import { FlappyBirdieDifficulty, FlappyBirdieSpeed, settings } from './settings.svelte';
import { GameSoundRegistry } from '$lib/util/GameSoundRegistry.svelte';
import { flappyBirdieSoundResources } from './assets/flappyBirdieSoundResources';
import { MessageSystem } from './systems/MessageSystem.svelte';
import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
import { gameDifficultyLabel, speedLabel } from './labels';
import { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';

interface Props {
	canvas: HTMLCanvasElement;
}

export class FlappyBirdieGame {
	engine!: Engine;
	gameScreen!: GameScreen;
	canvas: HTMLCanvasElement;
	isLoading = $state(true);
	private sounds = GameSoundRegistry.register(flappyBirdieSoundResources);
	private _skyColor = $state('');
	messageSystem?: MessageSystem;
	cleanupThemeEffect?: () => void;
	cleanupTimerEffect?: () => void;
	context = new GameContext({
		gameName: 'Flappy Birdie',
		gameKey: 'flappy-birdie',
		settings: {
			defaultSettings: {
				difficulty: FlappyBirdieDifficulty.Normal,
				speed: FlappyBirdieSpeed.Normal,
			},
		},
		variants: {
			map: {
				difficulty: {
					allValues: [
						FlappyBirdieDifficulty.Easy,
						FlappyBirdieDifficulty.Normal,
						FlappyBirdieDifficulty.Hard,
						FlappyBirdieDifficulty.VeryHard,
					],
					format: (difficulty: FlappyBirdieDifficulty) => gameDifficultyLabel[difficulty],
				},
				speed: {
					allValues: [FlappyBirdieSpeed.Normal, FlappyBirdieSpeed.Fast],
					format: (speed: FlappyBirdieSpeed) => `${speedLabel[speed]} Speed`,
				},
			},
			fromGame: () => {
				return {
					difficulty: this?.difficulty ?? FlappyBirdieDifficulty.Normal,
					speed: this?.speed ?? FlappyBirdieSpeed.Normal,
				};
			},
			getStatsVariant(variants) {
				return `${gameDifficultyLabel[variants.difficulty ?? FlappyBirdieDifficulty.Normal]} - ${speedLabel[variants.speed ?? FlappyBirdieSpeed.Normal]} Speed`;
			},
			getLeaderboardVariant(variants) {
				return `${gameDifficultyLabel[variants.difficulty ?? FlappyBirdieDifficulty.Normal]} - ${speedLabel[variants.speed ?? FlappyBirdieSpeed.Normal]} Speed`;
			},
		},
		stats: ({ props, context }) => {
			const variants = context.variants!.fromGame(this as FlappyBirdieGame);
			const gameVariant = context.variants!.getStatsVariant(variants);

			return {
				stats: new Stats({
					...props,
					gameVariant,
					liveStats: {
						score: {
							name: 'Score',
							unit: 'plain',
							value: () => {
								return this.score;
							},
							metrics: {
								total: {
									key: 'totalScore',
									name: 'Total Score',
								},
								average: {
									key: 'averageScore',
									name: 'Average Score',
								},
								max: {
									key: 'bestScore',
									name: 'Best Score',
									useAsBest: true,
								},
								min: {
									key: 'worstScore',
									name: 'Worst Score',
								},
							},
						},
						messagesSeen: {
							name: 'Messages',
							description: "Amount of unique messages you've seen",
							value: () => this.messageSystem?.seenMessages ?? 0,
							unit: 'plain',
						},
					},
					initialPinnedStats: ['time', 'score'],
					beforeReset: () => {
						this.messageSystem?.resetMessages();
					},
				}),
				canUpdateWithGameLost() {
					return true;
				},
				visibleStats: ['bestScore', 'averageScore', 'totalGames'],
			};
		},
		leaderboard: ({ props }) => {
			return {
				leaderboard: new Leaderboard({
					...props,
					firstAvailableDate: new Date('2025/05/08'),
					order: 'higher-first',
				}),
				sendScoreOn: ['lost'],
				getScore: () => {
					return {
						score: this.score,
					};
				},
			};
		},
	});

	constructor({ canvas }: Props) {
		this.canvas = canvas;
		this.init();
	}

	get timer() {
		return this.context.timer;
	}

	async init() {
		this.context.load();

		const rect = { width: window.innerWidth, height: window.innerHeight };

		await Promise.all([
			flappyBirdieSpriteSheet.load(theme.brightness === 'light' ? 'light' : 'dark'),
			GameSoundRegistry.loadAll(this.sounds),
		]);

		// Mobile
		let resScale = settings.mobileScale;

		if (settings.isDesktop()) {
			resScale = settings.desktopScale;
		}

		this.engine = new Engine({
			canvasElement: this.canvas,
			width: rect.width,
			height: rect.height,
			displayMode: DisplayMode.FitContainer,
			antialiasing: settings.isDesktop()
				? {
						canvasImageRendering: 'auto',
						filtering: ImageFiltering.Blended,
						multiSampleAntialiasing: true,
						nativeContextAntialiasing: false,
						pixelArtSampler: false,
					}
				: false,
			snapToPixel: false,
			pixelArt: false,
			resolution: {
				width: rect.width * resScale,
				height: rect.height * resScale,
			},
		});

		this.gameScreen = new GameScreen({ sounds: this.sounds });
		this.gameScreen.on('started', () => {
			this.context.resetGameState();
			this.timer.start();
		});
		this.gameScreen.on('gameOver', () => {
			this.context.handleGameOver('lost');
		});

		this.gameScreen.on('hitFloor', () => {
			const hasNewBest =
				this.context.stats?.pinnedStats[0]?.isNewBest ||
				this.context.stats?.pinnedStats[1]?.isNewBest;

			setTimeout(
				() => {
					this.timer.reset();
					this.engine.goToScene('gameScreen', {
						destinationIn: new FadeInOut({
							duration: settings.durations.gameOverFade,
							direction: 'in',
							hideLoader: true,
						}),
					});
				},
				hasNewBest
					? settings.durations.gameOverWithNewBestStat
					: settings.durations.gameOver,
			);
		});

		this.engine.addScene('gameScreen', this.gameScreen);

		this.engine.start().then(() => {
			this.isLoading = false;
			this.engine.goToScene('gameScreen').then(() => {
				this._skyColor = this.gameScreen?.sky?.colors?.toColor ?? '';
			});
		});

		this.cleanupThemeEffect = $effect.root(() => {
			$effect(() => {
				flappyBirdieSpriteSheet
					.load(theme.brightness === 'light' ? 'light' : 'dark')
					.then(() => {
						this.gameScreen.reloadGraphics();
						this._skyColor = this.gameScreen?.sky?.colors?.toColor ?? '';
					});
			});
		});

		this.cleanupTimerEffect = $effect.root(() => {
			$effect(() => {
				if (this.timer.paused && this.timer.started && !this.timer.stopped) {
					this.engine.stop();
				} else if (!this.engine.isRunning() && !this.timer.stopped) {
					this.engine.start();
				}
			});
		});

		this.changeDifficulty(this.context.settingsManager.settings.difficulty);
		this.changeSpeed(this.context.settingsManager.settings.speed);
		this.messageSystem = new MessageSystem({
			scene: this.gameScreen,
			getStats: () => this.context.stats as any,
			timer: this.timer,
		});
	}

	get skyColor() {
		return this._skyColor;
	}

	get score() {
		return this.gameScreen?.score;
	}

	get difficulty() {
		return settings.difficulty;
	}

	get speed() {
		return settings.speed;
	}

	changeDifficulty(difficulty: FlappyBirdieDifficulty) {
		this.context.settingsManager.settings.difficulty = difficulty;
		settings.difficulty = difficulty;
		this.gameScreen.physics.config.gravity = vec(settings.gravity.x, settings.gravity.y);
		this.gameScreen.pipeSpawner?.reset();
		this.context.createGame({ updateStatsIfNeeded: false });
	}

	changeSpeed(speed: FlappyBirdieSpeed) {
		this.context.settingsManager.settings.speed = speed;
		settings.speed = speed;
		this.gameScreen.pipeSpawner?.reset();
		this.gameScreen.floorSpawner?.reset();
		this.context.createGame({ updateStatsIfNeeded: false });
	}

	dispose() {
		this.context.dispose();
		this.messageSystem?.dispose();
		this.cleanupThemeEffect?.();
		this.cleanupTimerEffect?.();
		GameSoundRegistry.unloadAll(this.sounds);
		this.engine?.dispose();
	}
}
