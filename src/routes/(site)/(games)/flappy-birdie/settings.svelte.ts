import { islandSettings } from '$lib/stores/islandSettings.svelte';
import { untrack } from 'svelte';

export enum FlappyBirdieDifficulty {
	Easy = 'Easy',
	Normal = 'Normal',
	Hard = 'Hard',
	VeryHard = 'VeryHard',
}

export enum FlappyBirdieSpeed {
	Normal = 'Normal',
	Fast = 'Fast',
}

const birdWidth = 87;
const birdHeight = 67;
const pipeWidth = 91;
const desktopFloorSpeed: Record<FlappyBirdieSpeed, number> = {
	[FlappyBirdieSpeed.Normal]: -180,
	[FlappyBirdieSpeed.Fast]: -600,
};
const mobileFloorSpeed: Record<FlappyBirdieSpeed, number> = {
	[FlappyBirdieSpeed.Normal]: -180,
	[FlappyBirdieSpeed.Fast]: -300,
};
const pipeHorizontalGapFactorBySpeed: Record<FlappyBirdieSpeed, number> = {
	[FlappyBirdieSpeed.Normal]: 1,
	[FlappyBirdieSpeed.Fast]: 2,
};
const gapMaxDisplacementBySpeed: Record<FlappyBirdieSpeed, number> = {
	[FlappyBirdieSpeed.Normal]: birdHeight * 4,
	[FlappyBirdieSpeed.Fast]: birdHeight * 1.5,
};
const flySpeedByDifficulty: Record<FlappyBirdieDifficulty, number> = {
	[FlappyBirdieDifficulty.Easy]: 740,
	[FlappyBirdieDifficulty.Normal]: 760,
	[FlappyBirdieDifficulty.Hard]: 780,
	[FlappyBirdieDifficulty.VeryHard]: 780,
};
const maxDownSpeedByDifficulty: Record<FlappyBirdieDifficulty, number> = {
	[FlappyBirdieDifficulty.Easy]: 580,
	[FlappyBirdieDifficulty.Normal]: 620,
	[FlappyBirdieDifficulty.Hard]: 670,
	[FlappyBirdieDifficulty.VeryHard]: 670,
};
const pipeVerticalGapFactorByDifficultyNormalSpeed: Record<FlappyBirdieDifficulty, number> = {
	[FlappyBirdieDifficulty.Easy]: 5,
	[FlappyBirdieDifficulty.Normal]: 4.5,
	[FlappyBirdieDifficulty.Hard]: 3.8,
	[FlappyBirdieDifficulty.VeryHard]: 4,
};
const pipeVerticalGapFactorByDifficultyFastSpeed: Record<FlappyBirdieDifficulty, number> = {
	[FlappyBirdieDifficulty.Easy]: 4.6,
	[FlappyBirdieDifficulty.Normal]: 4.1,
	[FlappyBirdieDifficulty.Hard]: 3.4,
	[FlappyBirdieDifficulty.VeryHard]: 3.5,
};
const pipeVerticalSpeedByDifficulty: Record<FlappyBirdieDifficulty, number> = {
	[FlappyBirdieDifficulty.Easy]: 0,
	[FlappyBirdieDifficulty.Normal]: 0,
	[FlappyBirdieDifficulty.Hard]: 0,
	[FlappyBirdieDifficulty.VeryHard]: 80,
};
const gravityYByDifficulty: Record<FlappyBirdieDifficulty, number> = {
	[FlappyBirdieDifficulty.Easy]: 2100,
	[FlappyBirdieDifficulty.Normal]: 2200,
	[FlappyBirdieDifficulty.Hard]: 2400,
	[FlappyBirdieDifficulty.VeryHard]: 2400,
};

let difficulty = $state(FlappyBirdieDifficulty.Normal);
let speed = $state(FlappyBirdieSpeed.Normal);
const gameOverWithNewBestStatDuration = $derived(
	islandSettings.settings.leaderboards && islandSettings.settings.showLeaderboardsOnGameOver
		? 4000
		: 2000,
);

export const settings = {
	isDesktop: () => window.matchMedia('(min-width: 768px)').matches,
	get difficulty() {
		return difficulty;
	},
	set difficulty(newDifficulty: FlappyBirdieDifficulty) {
		difficulty = newDifficulty;
		settings.gravity.y = gravityYByDifficulty[newDifficulty];
		settings.bird.flySpeed = flySpeedByDifficulty[newDifficulty];
		settings.bird.maxUpSpeed = flySpeedByDifficulty[newDifficulty];
		settings.bird.maxDownSpeed = maxDownSpeedByDifficulty[newDifficulty];
		settings.pipe.speed.vertical = pipeVerticalSpeedByDifficulty[newDifficulty];
		settings.pipe.gap.vertical =
			birdHeight *
			(speed === FlappyBirdieSpeed.Normal
				? pipeVerticalGapFactorByDifficultyNormalSpeed[newDifficulty]
				: pipeVerticalGapFactorByDifficultyFastSpeed[newDifficulty]);
	},
	get speed() {
		return speed;
	},
	set speed(newSpeed: FlappyBirdieSpeed) {
		speed = newSpeed;
		const isDesktop = settings.isDesktop();
		settings.floor.speed = isDesktop ? desktopFloorSpeed[newSpeed] : mobileFloorSpeed[newSpeed];
		settings.pipe.speed.horizontal = isDesktop
			? desktopFloorSpeed[newSpeed]
			: mobileFloorSpeed[newSpeed];
		settings.pipe.gap.horizontal =
			settings.pipe.gap.horizontalBase * pipeHorizontalGapFactorBySpeed[newSpeed];
		settings.pipe.gap.maxDisplacement = gapMaxDisplacementBySpeed[newSpeed];
		settings.pipe.gap.vertical =
			birdHeight *
			(newSpeed === FlappyBirdieSpeed.Normal
				? pipeVerticalGapFactorByDifficultyNormalSpeed[untrack(() => difficulty)]
				: pipeVerticalGapFactorByDifficultyFastSpeed[untrack(() => difficulty)]);
	},
	mobileScale: 1.3,
	desktopScale: 1,
	gravity: {
		x: 0,
		y: gravityYByDifficulty[untrack(() => difficulty)],
	},
	durations: {
		gameOver: 1000,
		get gameOverWithNewBestStat() {
			return gameOverWithNewBestStatDuration;
		},
		gameOverFade: 400,
	},
	score: {
		y: 150,
	},
	forest: {
		height: 62,
	},
	buildings: {
		height: 69,
	},
	clouds: {
		height: 143,
	},
	floor: {
		speed: desktopFloorSpeed[untrack(() => speed)],
		height: 90,
		width: 60,
	},
	bird: {
		width: birdWidth,
		height: birdHeight,
		flySpeed: flySpeedByDifficulty[untrack(() => difficulty)],
		maxUpSpeed: flySpeedByDifficulty[untrack(() => difficulty)],
		maxDownSpeed: maxDownSpeedByDifficulty[untrack(() => difficulty)],
		flyAngularSpeed: -15,
		flyMaxFallAngularSpeed: 6,
		flyAngularSpeedPositiveDecay: 0.15,
		flyAngularSpeedNegativeDecay: 0.5,
	},
	pipe: {
		minVisiblePipePercentage: 0.2,
		initialOffsetX: {
			desktop: birdWidth,
			mobile: birdWidth * 4,
		},
		speed: {
			vertical: pipeVerticalSpeedByDifficulty[untrack(() => difficulty)],
			horizontal: desktopFloorSpeed[untrack(() => speed)],
		},
		width: pipeWidth,
		gap: {
			maxDisplacement: gapMaxDisplacementBySpeed[untrack(() => speed)],
			horizontalBase: pipeWidth * 2.5,
			horizontal: pipeHorizontalGapFactorBySpeed[untrack(() => speed)] * pipeWidth * 2.5,
			vertical:
				birdHeight *
				pipeVerticalGapFactorByDifficultyNormalSpeed[untrack(() => difficulty)],
		},
	},
	pipeEnd: {
		width: 105,
		height: 44,
	},
	z: {
		sky: 0,
		stars: 1,
		clouds: 2,
		buildings: 3,
		forest: 4,
		pipe: 5,
		pipeEnd: 6,
		floor: 700,
		bird: 800,
		ui: 900,
	},
};
