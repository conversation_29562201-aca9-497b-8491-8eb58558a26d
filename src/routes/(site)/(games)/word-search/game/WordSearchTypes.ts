import type { Point2D } from '$lib/models/Point2D';

export interface WordLocation {
	word: string;
	positions: Point2D[];
}

export type WordSearchHints = Record<string, { startX: number; startY: number }>;

export interface ContinueSelectionOptions {
	currentSelection: WordLocation;
	x: number;
	y: number;
}

export interface PlaceWordsResult {
	words: string[];
	hints: WordSearchHints;
}

export interface WordSearchGameStrategy {
	/**
	 * Place words on a grid by mutating it and return the placed words and hints */
	placeWords: (words: string[], maxWords: number) => PlaceWordsResult;
	continueSelection: (options: ContinueSelectionOptions) => WordLocation;
}
