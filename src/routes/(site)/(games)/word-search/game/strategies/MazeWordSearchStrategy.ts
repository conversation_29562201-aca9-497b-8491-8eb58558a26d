import { getGridItemsAround } from '$lib/functions/getGridItemsAround';
import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import { isGridItemAround } from '$lib/functions/isGridItemAround';
import { shuffleMutate } from '$lib/functions/shuffle';
import type { GridItem } from '$lib/models/GridItem';
import type { GridSize } from '$lib/models/GridSize';
import type { Point2D } from '$lib/models/Point2D';
import type {
	ContinueSelectionOptions,
	PlaceWordsResult,
	WordLocation,
	WordSearchGameStrategy,
	WordSearchHints,
} from '../WordSearchTypes';

export class MazeWordSearchStrategy implements WordSearchGameStrategy {
	private maxPlaceWordAttempts = 25;
	private gridSize: GridSize;

	constructor(private grid: string[][]) {
		this.gridSize = {
			rows: grid.length - 1,
			columns: grid[0].length - 1,
		};
	}

	placeWords(words: string[], maxWords: number): PlaceWordsResult {
		const placedWords: string[] = [];
		const hints: WordSearchHints = {};

		for (const word of words) {
			let placed = false;
			let attempts = 0;

			while (!placed && attempts < this.maxPlaceWordAttempts) {
				const positions = this.getPositionsToPlaceWord(word);
				if (positions.length > 0) {
					placed = true;
					placedWords.push(word);
					hints[word] = {
						startX: positions[0].x,
						startY: positions[0].y,
					};
					// Place on grid
					positions.forEach((position, index) => {
						this.grid[position.y][position.x] = word[index];
					});
				}

				attempts += 1;
			}

			if (placedWords.length === maxWords) {
				break;
			}
		}

		return {
			words: placedWords.sort((a, b) => a.localeCompare(b)),
			hints,
		};
	}

	private getPositionsToPlaceWord(word: string): Point2D[] {
		const positions: Point2D[] = [];
		const maxAttempts = 20;
		let attempts = 0;

		while (positions.length < word.length && attempts < maxAttempts) {
			// Pick first character
			if (positions.length === 0) {
				const availablePositions = this.grid.flatMap<GridItem>((row, rowIndex) => {
					return row
						.map((character, columnIndex) => {
							if (character === '') {
								return {
									row: rowIndex,
									column: columnIndex,
								};
							}
						})
						.filter(Boolean) as GridItem[];
				});

				if (availablePositions.length > 0) {
					const pickedPosition = getRandomItemAt(availablePositions);
					positions.push({ x: pickedPosition.column, y: pickedPosition.row });
				} else {
					attempts += 1;
				}

				continue;
			}

			const character = word[positions.length];
			const previousPosition = positions[positions.length - 1];
			const nearestPositions = getGridItemsAround(
				{
					row: previousPosition.y,
					column: previousPosition.x,
				},
				this.gridSize,
				{ diagonal: false },
			);
			const possiblePositions = nearestPositions.filter((nearestPosition) => {
				const nearestPositionWasAlreadyPicked = positions.some(
					(position) =>
						position.x === nearestPosition.column && position.y === nearestPosition.row,
				);

				return !nearestPositionWasAlreadyPicked;
			});

			if (possiblePositions.length === 0) {
				attempts += 1;
				positions.pop();

				continue;
			}

			shuffleMutate(possiblePositions);

			for (const possiblePosition of possiblePositions) {
				const characterOnPossiblePosition =
					this.grid[possiblePosition.row][possiblePosition.column];

				if (
					character === characterOnPossiblePosition ||
					characterOnPossiblePosition === ''
				) {
					positions.push({
						x: possiblePosition.column,
						y: possiblePosition.row,
					});

					break;
				}
			}

			attempts += 1;
		}

		if (positions.length !== word.length) {
			return [];
		}

		return positions;
	}

	continueSelection({ currentSelection, x, y }: ContinueSelectionOptions): WordLocation {
		if (
			currentSelection.positions.length === 1 &&
			x === currentSelection.positions[0].x &&
			y === currentSelection.positions[0].y
		) {
			return currentSelection;
		}

		let newPositions: Point2D[] = currentSelection.positions;
		const newPositionIndexOnCurrentSelection = currentSelection.positions.findIndex(
			(position) => position.x === x && position.y === y,
		);

		const isNewPositionRepeated = newPositionIndexOnCurrentSelection !== -1;

		if (isNewPositionRepeated) {
			if (newPositionIndexOnCurrentSelection === currentSelection.positions.length - 2) {
				newPositions = currentSelection.positions.slice(
					0,
					newPositionIndexOnCurrentSelection + 1,
				);
			}
		} else {
			const lastItem = currentSelection.positions[currentSelection.positions.length - 1];
			const isNearby = isGridItemAround(
				{
					row: y,
					column: x,
				},
				{
					row: lastItem.y,
					column: lastItem.x,
				},
				{
					diagonal: false,
				},
			);

			if (isNearby) {
				newPositions = [
					...currentSelection.positions,
					{
						x: x,
						y: y,
					},
				];
			}
		}

		const location: WordLocation = {
			word: this.getWordFrom(newPositions),
			positions: newPositions,
		};
		return location;
	}

	private getWordFrom(positions: Point2D[]): string {
		return positions
			.map(({ x, y }) => {
				return this.grid[y][x];
			})
			.join('');
	}
}
