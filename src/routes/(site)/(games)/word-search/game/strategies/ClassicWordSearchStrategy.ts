import type { GridSize } from '$lib/models/GridSize';
import type {
	ContinueSelectionOptions,
	PlaceWordsResult,
	WordLocation,
	WordSearchGameStrategy,
	WordSearchHints,
} from '../WordSearchTypes';

type Direction =
	| 'horizontal'
	| 'vertical'
	| 'diagonal'
	| 'horizontal-reverse'
	| 'vertical-reverse'
	| 'diagonal-reverse';

export class ClassicWordSearchStrategy implements WordSearchGameStrategy {
	private maxPlaceWordAttempts = 100;
	private gridSize: GridSize;

	constructor(private grid: string[][]) {
		this.gridSize = {
			rows: grid.length - 1,
			columns: (grid[0]?.length ?? 1) - 1,
		};
	}

	continueSelection({ currentSelection, x, y }: ContinueSelectionOptions): WordLocation {
		try {
			const nearest = this.getNearestWordFrom({
				...currentSelection,
				positions: [
					...(currentSelection?.positions ?? []),
					{
						x: x,
						y: y,
					},
				],
			});

			if (nearest) {
				return nearest;
			}
		} catch {
			// Ignore
		}

		return currentSelection;
	}

	placeWords(words: string[], maxWords: number): PlaceWordsResult {
		const successfullyPlacedWords: string[] = [];
		const wordHints: WordSearchHints = {};

		for (const word of words) {
			let placed = false;
			let attempts = 0;
			while (!placed && attempts < this.maxPlaceWordAttempts) {
				const direction: Direction = this.getRandomDirection();
				const startX = Math.floor(Math.random() * this.gridSize.columns);
				const startY = Math.floor(Math.random() * this.gridSize.rows);

				if (this.canPlaceWord(word, startX, startY, direction)) {
					this.placeWord(word, startX, startY, direction);
					placed = true;
					successfullyPlacedWords.push(word);
					wordHints[word] = { startX, startY };
				}
				attempts++;
			}

			if (successfullyPlacedWords.length === maxWords) {
				break;
			}
		}
		return {
			words: successfullyPlacedWords.sort((a, b) => a.localeCompare(b)),
			hints: wordHints,
		};
	}

	private getNearestWordFrom(location: Omit<WordLocation, 'word'>): WordLocation | null {
		const { x: startX, y: startY } = location.positions[0];

		if (startX === undefined || startY === undefined) {
			return null;
		}

		const { x: initialEndX, y: initialEndY } =
			location.positions[location.positions.length - 1];
		let endX = initialEndX;
		let endY = initialEndY;

		if (startX !== initialEndX || startY !== initialEndY) {
			const angle = -Math.atan((initialEndY - startY) / (initialEndX - startX));
			const degrees = (angle * 180) / Math.PI;
			const distanceX = Math.abs(endX - startX);
			const distanceY = Math.abs(endY - startY);
			const biggestDistance = Math.max(distanceX, distanceY);

			if (degrees > -22.5 && degrees < 22.5) {
				// Horizontal line
				endY = startY;
			} else if (degrees >= 22.5 && degrees < 67.5) {
				if (initialEndY > startY) {
					// Diagonal bottom left
					endX = Math.max(0, startX - biggestDistance);
					endY = Math.max(0, startY + biggestDistance);
				} else {
					// Diagonal top right
					endX = Math.min(startX + biggestDistance, this.gridSize.columns);
					endY = Math.min(startY - biggestDistance, this.gridSize.rows);
				}
			} else if (degrees >= 67.5) {
				// Vertical line
				endX = startX;
			} else if (degrees < -67.5) {
				// Vertical line
				endX = startX;
			} else if (degrees >= -67.5 && degrees < -22.5) {
				if (initialEndX > startX) {
					// Diagonal bottom right
					endX = Math.min(startX + biggestDistance, this.gridSize.columns);
					endY = Math.min(startY + biggestDistance, this.gridSize.rows);
				} else {
					// Diagonal top left
					endX = Math.max(0, startX - biggestDistance);
					endY = Math.max(0, startY - biggestDistance);
				}
			}
		}

		const word: string[] = [];
		const deltaX = Math.sign(endX - startX);
		const deltaY = Math.sign(endY - startY);
		let x = startX;
		let y = startY;

		do {
			word.push(this.grid[y][x]);
			x += deltaX;
			y += deltaY;
		} while (x !== endX + deltaX || y !== endY + deltaY);

		return {
			word: word.join(''),
			positions: [
				{
					x: startX,
					y: startY,
				},
				{
					x: endX,
					y: endY,
				},
			],
		};
	}

	private getRandomDirection(): Direction {
		const directions: Direction[] = [
			'horizontal',
			'vertical',
			'diagonal',
			'horizontal-reverse',
			'vertical-reverse',
			'diagonal-reverse',
		];
		return directions[Math.floor(Math.random() * directions.length)];
	}

	private canPlaceWord(word: string, x: number, y: number, direction: Direction): boolean {
		switch (direction) {
			case 'horizontal':
				if (x + word.length > this.gridSize.columns) return false;
				for (let i = 0; i < word.length; i++) {
					if (this.grid[y][x + i] !== '' && this.grid[y][x + i] !== word[i]) return false;
				}
				break;
			case 'vertical':
				if (y + word.length > this.gridSize.rows) return false;
				for (let i = 0; i < word.length; i++) {
					if (this.grid[y + i][x] !== '' && this.grid[y + i][x] !== word[i]) return false;
				}
				break;
			case 'diagonal':
				if (x + word.length > this.gridSize.columns || y + word.length > this.gridSize.rows)
					return false;
				for (let i = 0; i < word.length; i++) {
					if (this.grid[y + i][x + i] !== '' && this.grid[y + i][x + i] !== word[i])
						return false;
				}
				break;
			case 'horizontal-reverse':
				if (x - word.length < 0) return false;
				for (let i = 0; i < word.length; i++) {
					if (this.grid[y][x - i] !== '' && this.grid[y][x - i] !== word[i]) return false;
				}
				break;
			case 'vertical-reverse':
				if (y - word.length < 0) return false;
				for (let i = 0; i < word.length; i++) {
					if (this.grid[y - i][x] !== '' && this.grid[y - i][x] !== word[i]) return false;
				}
				break;
			case 'diagonal-reverse':
				if (x - word.length < 0 || y - word.length < 0) return false;
				for (let i = 0; i < word.length; i++) {
					if (this.grid[y - i][x - i] !== '' && this.grid[y - i][x - i] !== word[i])
						return false;
				}
				break;
		}
		return true;
	}

	private placeWord(word: string, x: number, y: number, direction: Direction): void {
		switch (direction) {
			case 'horizontal':
				for (let i = 0; i < word.length; i++) {
					this.grid[y][x + i] = word[i];
				}
				break;
			case 'vertical':
				for (let i = 0; i < word.length; i++) {
					this.grid[y + i][x] = word[i];
				}
				break;
			case 'diagonal':
				for (let i = 0; i < word.length; i++) {
					this.grid[y + i][x + i] = word[i];
				}
				break;
			case 'horizontal-reverse':
				for (let i = 0; i < word.length; i++) {
					this.grid[y][x - i] = word[i];
				}
				break;
			case 'vertical-reverse':
				for (let i = 0; i < word.length; i++) {
					this.grid[y - i][x] = word[i];
				}
				break;
			case 'diagonal-reverse':
				for (let i = 0; i < word.length; i++) {
					this.grid[y - i][x - i] = word[i];
				}
				break;
		}
	}
}
