<script lang="ts">
	interface Props {
		selected?: boolean;
	}

	let { selected = false }: Props = $props();
</script>

<svg
	class="size-full"
	class:selected
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 196 196"
>
	<g clip-path="url(#maze-mode__a)">
		<path
			d="M74 27H27v47h94v47"
			class="text-blue-200 opacity-60 dark:text-blue-700 dark:opacity-40"
			stroke="currentColor"
			stroke-width="32"
			stroke-linecap="round"
			stroke-linejoin="round"
			style="--index:0;--offset:240"
		/>
		<path
			d="M74 168v-47H27v47"
			class="text-red-200 opacity-60 dark:text-red-700 dark:opacity-40"
			stroke="currentColor"
			stroke-width="32"
			stroke-linecap="round"
			stroke-linejoin="round"
			style="--index:1;--offset:150"
		/>
		<path
			d="M121 168h47V74"
			class="text-green-200 opacity-60 dark:text-green-700 dark:opacity-40"
			stroke="currentColor"
			stroke-width="32"
			stroke-linecap="round"
			stroke-linejoin="round"
			style="--index:2;--offset:150"
		/>
		<circle cx="27" cy="27" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="74" cy="27" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="121" cy="27" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="168" cy="27" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="27" cy="74" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="74" cy="74" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="121" cy="74" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="168" cy="74" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="27" cy="121" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="74" cy="121" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="121" cy="121" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="168" cy="121" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="27" cy="168" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="74" cy="168" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="121" cy="168" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="168" cy="168" r="5" stroke="currentColor" stroke-width="2" />
	</g>
	<defs>
		<clipPath id="maze-mode__a">
			<path fill="#fff" d="M0 0h196v196H0z" />
		</clipPath>
	</defs>
</svg>

<style>
	@keyframes dash {
		to {
			stroke-dashoffset: 0;
		}
	}

	.selected path {
		--duration: 500ms;
		--offset: 141;
		--index: 0;

		stroke-dasharray: var(--offset);
		stroke-dashoffset: var(--offset);
		animation: dash var(--duration) ease-in-out forwards;
		animation-delay: calc(var(--duration) * (var(--index)));
	}
</style>
