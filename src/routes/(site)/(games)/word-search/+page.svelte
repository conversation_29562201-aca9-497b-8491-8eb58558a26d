<script lang="ts">
	import PageTransition from '$lib/components/PageTransition.svelte';
	import WordSearch from './WordSearch.svelte';
	import { MetaTags } from 'svelte-meta-tags';
</script>

<MetaTags
	title="Play Word Search Game Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play word search game online for free. Beautiful word search game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/word-search"
	openGraph={{
		url: 'https://www.lofiandgames.com/word-search',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-word-search.png',
				width: 1200,
				height: 630,
				alt: 'Word Search Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Word Search Game on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-word-search.png',
		site: 'https://www.lofiandgames.com/word-search',
	}}
/>

<PageTransition>
	<WordSearch />
</PageTransition>
