<script>
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { MetaTags } from 'svelte-meta-tags';
	import AuthContainer from '../components/AuthContainer.svelte';
	import ForgotPassword from '../components/ForgotPassword.svelte';

	let email = $state('');
</script>

<MetaTags
	title="Forgot Password"
	titleTemplate="%s | Lofi and Games"
	description="Forgot your password? No problem. Just enter your email address and we'll send you a link to reset your password."
	canonical="https://www.lofiandgames.com/forgot-password"
	openGraph={{
		url: 'https://www.lofiandgames.com/forgot-password',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'website',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Forgot Password on Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com/forgot-password',
	}}
/>

<PageTransition>
	<Navbar variant="transparent" />

	<AuthContainer>
		<ForgotPassword bind:email />
	</AuthContainer>
</PageTransition>
