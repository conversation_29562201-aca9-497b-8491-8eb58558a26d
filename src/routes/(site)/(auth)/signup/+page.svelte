<script>
	import { goto } from '$app/navigation';
	import { authClient } from '$lib/auth/client';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { theme } from '$lib/stores/theme.svelte';
	import AuthContainer from '../components/AuthContainer.svelte';
	import SignUp from '../components/SignUp.svelte';
	import { MetaTags } from 'svelte-meta-tags';

	let email = $state('');

	const session = authClient.useSession();

	$effect(() => {
		if ($session.data?.user) {
			goto('/');
		}
	});
</script>

<MetaTags
	title="Sign Up"
	titleTemplate="%s | Lofi and Games"
	description="Create your account on Lofi and Games to save your progress and sync across devices."
	canonical="https://www.lofiandgames.com/signup"
	openGraph={{
		url: 'https://www.lofiandgames.com/signup',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'website',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Sign Up for Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com/signup',
	}}
/>

<PageTransition>
	<Navbar variant="transparent" />

	<AuthContainer
		image={theme.brightness === 'light'
			? 'https://static.lofiandgames.com/images/site/logo-3d-white.avif'
			: 'https://static.lofiandgames.com/images/site/logo-3d.avif'}
	>
		<SignUp bind:email />
	</AuthContainer>
</PageTransition>
