<script>
	import { goto } from '$app/navigation';
	import { authClient } from '$lib/auth/client';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { toast } from 'svelte-sonner';
	import { MetaTags } from 'svelte-meta-tags';
	import AuthBackground from '../components/AuthBackground.svelte';

	const session = authClient.useSession();

	$effect(() => {
		if (!$session.isPending) {
			if ($session.data?.user) {
				authClient
					.signOut()
					.then(() => {
						goto('/');
					})
					.catch(() => {
						toast.error('Oops, could not sign out. Please try again later.');
						goto('/');
					});
			} else {
				goto('/');
			}
		}
	});
</script>

<MetaTags
	title="Sign Out"
	titleTemplate="%s | Lofi and Games"
	description="Sign out from your Lofi and Games account."
	canonical="https://www.lofiandgames.com/signout"
	openGraph={{
		url: 'https://www.lofiandgames.com/signout',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'website',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Sign Out from Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com/signout',
	}}
/>

<PageTransition>
	<Navbar variant="transparent" />

	<div class="relative flex items-center justify-center min-h-screen-no-navbar p-4 pb-10">
		<AuthBackground />

		<div class="absolute inset-0 flex items-center justify-center">
			<div class="loading loading-spinner size-10"></div>
		</div>
	</div>
</PageTransition>
