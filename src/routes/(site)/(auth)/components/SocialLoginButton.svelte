<script lang="ts">
	import { cn } from '$lib/util/cn';
	import GoogleIcon from '$lib/components/Icons/logos/GoogleIcon.svelte';
	import capitalize from 'lodash/capitalize';
	import EmailOutlineIcon from '$lib/components/Icons/EmailOutlineIcon.svelte';

	interface Props {
		provider: 'google' | 'email';
		variant: 'signIn' | 'signUp';
		class?: string;
		onclick?: () => void;
		disabled?: boolean;
		loading?: boolean;
	}

	let {
		provider = 'google',
		variant,
		class: className,
		disabled,
		onclick,
		loading,
	}: Props = $props();
</script>

<button
	class={cn('btn btn-outline flex flex-row gap-3 w-full', className)}
	{onclick}
	disabled={disabled || loading}
>
	{#if loading}
		<span class="loading loading-spinner size-5"></span>
	{:else if provider === 'google'}
		<GoogleIcon class="size-5" />
	{:else if provider === 'email'}
		<EmailOutlineIcon class="size-7" />
	{/if}

	{#if provider === 'email'}
		Continue with Email
	{:else}
		{#if variant === 'signIn'}Sign in with {capitalize(provider)}{/if}
		{#if variant === 'signUp'}Sign up with {capitalize(provider)}{/if}
	{/if}
</button>
