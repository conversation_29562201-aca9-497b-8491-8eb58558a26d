<script lang="ts">
	import { page } from '$app/state';
	import { authClient } from '$lib/auth/client';
	import { cn } from '$lib/util/cn';
	import capitalize from 'lodash/capitalize';
	import SocialLoginButton from './SocialLoginButton.svelte';
	import Turnstile, { type TurnstileHandler } from './Turnstile.svelte';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';
	import EmailIcon from '$lib/components/Icons/EmailIcon.svelte';
	import { fly } from 'svelte/transition';
	import { quadInOut } from 'svelte/easing';
	import { toast } from 'svelte-sonner';

	interface Props {
		email: string;
		class?: string;
	}

	let { email = $bindable(), class: className }: Props = $props();

	const session = authClient.useSession();
	let name = $state('');
	let username = $state('');
	let password = $state('');
	let turnstileToken = $state('');
	let turnstileHandler = $state() as TurnstileHandler;
	let isLoading = $state(false);
	let error = $state(null) as { code?: string; message?: string } | null;
	let step = $state<'method' | 'email' | 'success'>('method');

	$effect(() => {
		if (step) {
			error = null;
		}
	});

	async function signInWithGoogle() {
		if (!turnstileToken) {
			return;
		}

		error = null;
		isLoading = true;

		const { error: apiError } = await authClient.signIn.social({
			provider: 'google',
			callbackURL: `${page.url.origin}`,
			fetchOptions: {
				credentials: 'include',
				headers: {
					'x-captcha-response': turnstileToken,
				},
			},
		});

		error = apiError;
		isLoading = false;

		if (error) {
			turnstileHandler.reset();

			toast.error(
				capitalize(error?.message ?? 'Oops, something went wrong. Please try again later'),
				{ duration: 5_000 },
			);
		}
	}

	async function signUp() {
		if (!turnstileToken || isLoading) {
			return;
		}

		error = null;
		isLoading = true;

		const { error: apiError } = await authClient.signUp.email({
			email,
			username,
			password,
			name,
			callbackURL: `${page.url.origin}`,
			fetchOptions: {
				credentials: 'include',
				headers: {
					'x-captcha-response': turnstileToken,
				},
			},
		});

		error = apiError;
		isLoading = false;

		if (error) {
			turnstileHandler.reset();

			toast.error(
				capitalize(error?.message ?? 'Oops, something went wrong. Please try again later'),
				{
					duration: 5_000,
				},
			);
		} else {
			step = 'success';
		}
	}

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			signUp();
		}
	}
</script>

<div
	transition:fly={{ y: 25, duration: 300, easing: quadInOut }}
	class={cn('size-full relative flex items-center justify-center min-h-[300px]', className)}
>
	{#if step === 'method'}
		<div
			transition:fly={{ y: -25, duration: 300, easing: quadInOut }}
			class="absolute inset-0 w-full flex flex-col items-center justify-center gap-2"
		>
			<h2 class="text-3xl mb-4">Sign Up</h2>

			<SocialLoginButton
				provider="email"
				variant="signUp"
				loading={isLoading || $session.isPending}
				onclick={() => (step = 'email')}
			/>

			<div class="divider">or</div>

			<SocialLoginButton
				provider="google"
				variant="signUp"
				loading={!turnstileToken || isLoading || $session.isPending}
				onclick={signInWithGoogle}
			/>

			<Turnstile bind:token={turnstileToken} bind:this={turnstileHandler} />

			<div class="pt-4">
				Already a member? <a class="link" href="/signin">Sign in</a>
			</div>
		</div>
	{:else if step === 'email'}
		<div
			transition:fly={{ y: 25, duration: 300, easing: quadInOut }}
			class="w-full flex flex-col items-center justify-center gap-2"
		>
			<div class="w-full flex items-center justify-between gap-4 mb-4">
				<button
					class="btn btn-outline btn-sm btn-circle"
					aria-label="Back to sign up method"
					onclick={() => (step = 'method')}
				>
					<ChevronLeftIcon />
				</button>

				<h2 class="text-3xl">Sign Up</h2>

				<div class="btn btn-circle btn-sm btn-outline invisible"></div>
			</div>

			<div class="flex items-center justify-between gap-4">
				<label class="label flex flex-col w-full">
					<span class="text-sm self-start">Username</span>

					<input
						class={cn('input input-bordered w-full', {
							'input-error': [
								'USER_ALREADY_EXISTS',
								'USERNAME_IS_TOO_SHORT',
								'USERNAME_IS_TOO_LONG',
								'USERNAME_IS_INVALID',
								'INVALID_USERNAME_OR_PASSWORD',
								'USERNAME_IS_ALREADY_TAKEN_PLEASE_TRY_ANOTHER',
							].includes($state.snapshot(error?.code) as any),
						})}
						type="text"
						placeholder="Username"
						bind:value={username}
						disabled={isLoading || $session.isPending}
						maxlength="20"
						onkeydown={handleKeyDown}
						autocomplete="username"
					/>
				</label>

				<label class="label flex flex-col w-full">
					<span class="text-sm self-start">Name</span>

					<input
						class="input input-bordered w-full"
						type="text"
						placeholder="Name"
						bind:value={name}
						disabled={isLoading || $session.isPending}
						onkeydown={handleKeyDown}
						autocomplete="name"
					/>
				</label>
			</div>

			<label class="label flex flex-col w-full">
				<span class="text-sm self-start">Email</span>

				<input
					class={cn('input input-bordered w-full validator', {
						'input-error': [
							'USER_ALREADY_EXISTS',
							'INVALID_EMAIL',
							'INVALID_EMAIL_OR_PASSWORD',
						].includes($state.snapshot(error?.code) as any),
					})}
					type="Email"
					placeholder="Email"
					bind:value={email}
					disabled={isLoading || $session.isPending}
					onkeydown={handleKeyDown}
					autocomplete="email"
				/>

				<div class="validator-hint self-start hidden mt-0">Enter a valid email address</div>
			</label>

			<label class="label flex flex-col w-full">
				<span class="text-sm self-start">Password</span>

				<input
					class={cn('input input-bordered w-full', {
						'input-error': [
							'PASSWORD_TOO_LONG',
							'PASSWORD_TOO_SHORT',
							'INVALID_PASSWORD',
							'INVALID_EMAIL_OR_PASSWORD',
						].includes($state.snapshot(error?.code) as any),
					})}
					type="password"
					placeholder="Password"
					bind:value={password}
					disabled={isLoading || $session.isPending}
					onkeydown={handleKeyDown}
					autocomplete="new-password"
				/>
			</label>

			<span class="text-sm self-start text-wrap">
				Password should be at least 8 characters
			</span>

			<Turnstile bind:token={turnstileToken} bind:this={turnstileHandler} />

			<button
				class="btn btn-primary w-full"
				disabled={!turnstileToken || isLoading || $session.isPending}
				onclick={signUp}
			>
				{#if !turnstileToken || isLoading}
					<span class="loading loading-spinner"></span>
				{/if}

				Sign Up
			</button>

			<div class="pt-4">
				Already a member? <a class="link" href="/signin">Sign in</a>
			</div>
		</div>
	{:else if step === 'success'}
		<div
			transition:fly={{ y: 25, duration: 300, delay: 400, easing: quadInOut }}
			class="size-full flex flex-col items-center justify-center gap-1 absolute inset-0 text-center"
		>
			<EmailIcon class="size-14 mb-2" />

			<h2 class="text-3xl">All Done!</h2>

			<p class="text-lg">Check your email for a verification link</p>
		</div>
	{/if}
</div>
