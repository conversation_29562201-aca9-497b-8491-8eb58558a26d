<script lang="ts">
	import { page } from '$app/state';
	import { authClient } from '$lib/auth/client';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { cn } from '$lib/util/cn';
	import capitalize from 'lodash/capitalize';
	import { onDestroy, onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { toast } from 'svelte-sonner';
	import Turnstile, { type TurnstileHandler } from '../components/Turnstile.svelte';
	import AuthContainer from '../components/AuthContainer.svelte';
	import { MetaTags } from 'svelte-meta-tags';

	let newPassword = $state('');
	let turnstileToken = $state('');
	let turnstileHandler = $state() as TurnstileHandler;
	let isLoading = $state(false);
	let error = $state(null) as { code?: string; message?: string } | null;
	let token = $state<string | null>(null);
	const errorToastId = 'invalid-reset-password-link';

	onMount(() => {
		token = page.url.searchParams.get('token');

		if (!token) {
			setTimeout(() => {
				toast.error(
					'Oops, this link is invalid, please try to reset your password again.',
					{
						duration: Number.POSITIVE_INFINITY,
						dismissable: false,
						id: errorToastId,
					},
				);
			}, 1000);
		}
	});

	onDestroy(() => {
		toast.dismiss(errorToastId);
	});

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			resetPassword();
		}
	}

	async function resetPassword() {
		if (!turnstileToken || isLoading || !token) {
			return;
		}

		isLoading = true;
		error = null;

		const { error: apiError } = await authClient.resetPassword({
			newPassword,
			token,
			fetchOptions: {
				headers: {
					'x-captcha-response': turnstileToken,
				},
			},
		});

		isLoading = false;
		error = apiError;

		if (error) {
			turnstileHandler.reset();

			toast.error(
				capitalize(error?.message ?? 'Oops, something went wrong. Please try again later'),
				{
					duration: 5_000,
				},
			);
		} else {
			toast.success('Password reset successful!', {
				description: 'You can now sign in with your new password.',
				duration: 5_000,
			});

			goto('/signin');
		}
	}
</script>

<MetaTags
	title="Reset Password"
	titleTemplate="%s | Lofi and Games"
	description="Reset your password for your Lofi and Games account."
	canonical="https://www.lofiandgames.com/reset-password"
	openGraph={{
		url: 'https://www.lofiandgames.com/reset-password',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'website',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Reset Password - Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com/reset-password',
	}}
/>

<PageTransition>
	<Navbar variant="transparent" />

	<AuthContainer>
		<div class="text-center flex flex-col items-center justify-center gap-2 size-full">
			<h1 class="text-3xl mb-4">Reset Password</h1>

			<label class="label flex flex-col items-start w-full">
				<span class="text-sm">New Password</span>

				<input
					class={cn('input input-bordered w-full', {
						'input-error': [
							'INVALID_USERNAME_OR_PASSWORD',
							'INVALID_EMAIL_OR_PASSWORD',
							'INVALID_PASSWORD',
							'PASSWORD_TOO_LONG',
							'PASSWORD_TOO_SHORT',
						].includes($state.snapshot(error?.code) as any),
					})}
					disabled={isLoading || !token}
					type="password"
					placeholder="New password"
					tabindex={2}
					bind:value={newPassword}
					onkeydown={handleKeyDown}
					autocomplete="new-password"
				/>
			</label>

			{#if token}
				<Turnstile bind:token={turnstileToken} bind:this={turnstileHandler} />
			{/if}

			<button
				class="btn btn-primary w-full"
				disabled={!turnstileToken || isLoading || !token}
				onclick={resetPassword}
				tabindex={4}
			>
				{#if token}
					{#if !turnstileToken || isLoading}
						<span class="loading loading-spinner"></span>
					{/if}
				{/if}

				Reset Password
			</button>
		</div>
	</AuthContainer>
</PageTransition>
