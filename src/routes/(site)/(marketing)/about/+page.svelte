<script>
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { MetaTags } from 'svelte-meta-tags';
</script>

<MetaTags
	title="About"
	titleTemplate="%s | Lofi and Games"
	description="Lofi and Games is a website that aims to provide the best experience in playing casual games"
	canonical="https://www.lofiandgames.com/about"
	openGraph={{
		url: 'https://www.lofiandgames.com/about',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'article',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'About Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com/about',
	}}
/>

<PageTransition>
	<article class="px-4 py-8 mx-auto">
		<h1 class="text-center">ABOUT US</h1>

		<p>
			Lofi and Games is a website that aims to provide the best experience in playing casual
			games.
		</p>

		<p>
			We want to rebuild many classic games, like Solitaire, Snake, Tic <PERSON> Toe, Chess, and
			many more. All the games we make should look modern, cozy, and relaxing, so you can have
			the best experience while playing them.
		</p>

		<p>
			The Lofi and Games site is at the very beginning, as you can see, but we're working hard
			to improve it.
		</p>
	</article>
</PageTransition>
