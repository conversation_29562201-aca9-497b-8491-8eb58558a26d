import { darkWallpapers, lightWallpapers, wallpapers, type Wallpaper } from '$lib/data/wallpapers';
import { shuffle } from '$lib/functions/shuffle';
import { theme } from '$lib/stores/theme.svelte';

const darkImages = darkWallpapers.map((wallpaper) => wallpaper.url);
const lightImages = lightWallpapers.map((wallpaper) => wallpaper.url);
let current = $state<Wallpaper | null>(null);

const images = $derived.by(() => {
	// In the future, when there are more themes, change this logic
	// to include the new theme wallpapers
	if (theme.brightness === 'dark') {
		return shuffle(darkImages);
	} else {
		return shuffle(lightImages);
	}
});

export const wallpaper = {
	get images() {
		return images;
	},
	get current() {
		return current;
	},
	changeByUrl(url: string) {
		const wallpaper = wallpapers.find((wallpaper) => wallpaper.url === url);

		if (wallpaper) {
			current = wallpaper;
		}
	},
};
