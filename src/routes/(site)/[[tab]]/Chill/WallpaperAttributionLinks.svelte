<script lang="ts">
	import type { Attribution, AttributionResource } from '$lib/models/Attribution';
	import { cn } from '$lib/util/cn';

	interface Props {
		attribution: Attribution;
		class?: string;
	}

	let { attribution, class: className }: Props = $props();
</script>

<div
	class={cn(
		'flex flex-row gap-2 glass px-4 py-3 rounded-full items-center justify-center',
		className,
	)}
>
	<a
		class="text-base font-semibold leading-5 text-center"
		target="_blank"
		rel="noreferrer noopener"
		href={attribution.work.url}
	>
		{attribution.work.name ?? ''}
	</a>

	{#if attribution.creator}
		{@const attr = attribution}
		{@const creators = (Array.isArray(attr.creator) ? attr.creator : [attr.creator]).filter(
			Boolean,
		) as AttributionResource[]}

		<span class="text-sm font-normal">by</span>

		{#each creators as creator, index}
			<a
				class="text-base font-semibold text-center"
				target="_blank"
				rel="noreferrer noopener"
				href={creator.url}
			>
				{creator.name}{#if index !== creators.length - 1 && creators.length > 1},&nbsp;
				{/if}
			</a>
		{/each}
	{/if}

	{#if attribution.license}
		<span class="text-sm font-normal">•</span>

		<a
			class="text-sm font-light text-center"
			target="_blank"
			rel="noreferrer noopener"
			href={attribution.license.url}
		>
			{attribution.license.name ?? ''}
		</a>
	{/if}
</div>
