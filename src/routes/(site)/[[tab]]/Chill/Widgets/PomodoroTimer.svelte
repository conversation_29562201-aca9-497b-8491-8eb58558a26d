<script lang="ts">
	import TimerComponent from '$lib/components/Timer.svelte';
	import PlayIcon from '$lib/components/Icons/PlayIcon.svelte';
	import UndoIcon from '$lib/components/Icons/UndoIcon.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Dialog from '$lib/components/Dialog.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import { pomodoroTimer } from '$lib/stores/pomodoroTimer.svelte';
	import Range from '$lib/components/Range/Range.svelte';
	import Widget from './Widget.svelte';
	import { animate } from 'motion';
	import Collapse from '$lib/components/Collapse/Collapse.svelte';
	import CollapseContent from '$lib/components/Collapse/CollapseContent.svelte';

	type Tab = 'pomodoro' | 'break';

	const maxTimeInMinutes = 24 * 60 - 1;
	let tab = $state<Tab>('pomodoro');
	let isSettingsOpen = $state(false);
	let timer = $state(pomodoroTimer.effectiveTimer);
	let root = $state() as HTMLElement;
	let lastTimerEndedId = $state(pomodoroTimer.timerEndedId);

	$effect(() => {
		if (pomodoroTimer.effectiveTimer === pomodoroTimer.timer) {
			tab = 'pomodoro';
		} else {
			tab = 'break';
		}
		timer = pomodoroTimer.effectiveTimer;
	});

	$effect(() => {
		if (pomodoroTimer.timerEndedId && pomodoroTimer.timerEndedId !== lastTimerEndedId) {
			lastTimerEndedId = pomodoroTimer.timerEndedId;

			if (pomodoroTimer.settings.settings.finishedAnimations) {
				animate([
					[root, { rotateZ: 5, scale: 1.1 }, { duration: 0.2, ease: 'easeInOut' }],
					[root, { rotateZ: -5 }, { duration: 0.2, ease: 'easeInOut' }],
					[root, { rotateZ: 5 }, { duration: 0.2, ease: 'easeInOut' }],
					[root, { rotateZ: -5 }, { duration: 0.2, ease: 'easeInOut' }],
					[root, { rotateZ: 0 }, { duration: 0.2, ease: 'easeInOut' }],
					[root, { scale: 0.85 }, { at: '<', duration: 0.3, ease: 'easeInOut' }],
					[root, { scale: 1 }, { type: 'spring', bounce: 0.4, duration: 0.8 }],
				]);
			}
		}
	});

	function changeTab(newTab: Tab) {
		if (tab !== newTab) {
			pomodoroTimer.timer.reset();
			pomodoroTimer.breakTimer.reset();
		}

		tab = newTab;

		if (tab === 'pomodoro') {
			pomodoroTimer.effectiveTimer = pomodoroTimer.timer;
		} else {
			pomodoroTimer.effectiveTimer = pomodoroTimer.breakTimer;
		}
	}

	function changePomodoroTimerAmount(e: Event) {
		let newAmount = +(e.target as HTMLInputElement).value;
		newAmount = Math.max(0, Math.min(maxTimeInMinutes, newAmount));

		(e.target as HTMLInputElement).value = newAmount.toString();
		pomodoroTimer.settings.settings.timer = newAmount;
		pomodoroTimer.timer.countdownDuration = 1000 * 60 * newAmount;
	}

	function changeBreakTimerAmount(e: Event) {
		let newAmount = +(e.target as HTMLInputElement).value;
		newAmount = Math.max(0, Math.min(maxTimeInMinutes, newAmount));

		(e.target as HTMLInputElement).value = newAmount.toString();
		pomodoroTimer.settings.settings.break = newAmount;
		pomodoroTimer.breakTimer.countdownDuration = 1000 * 60 * newAmount;
	}
</script>

<Widget class="flex flex-col" bind:element={root}>
	<div role="tablist" class="tabs tabs-border grid grid-cols-2">
		<button
			role="tab"
			class="tab"
			class:tab-active={tab === 'pomodoro'}
			onclick={() => changeTab('pomodoro')}>Focus</button
		>
		<button
			role="tab"
			class="tab"
			class:tab-active={tab === 'break'}
			onclick={() => changeTab('break')}>Break</button
		>
	</div>

	<TimerComponent
		animated={pomodoroTimer.settings.settings.numberAnimations}
		class="text-5xl grow -z-10"
		{timer}
	/>

	<div class="flex gap-4 items-center justify-center">
		<button
			class="btn btn-circle btn-md btn-ghost"
			aria-label="Open pomodoro timer settings"
			onclick={() => (isSettingsOpen = true)}
		>
			<SettingsIcon class="size-6" />
		</button>

		<button
			class="btn btn-circle btn-md btn-ghost"
			onclick={() => timer.toggle()}
			aria-label={timer.running ? 'Pause timer' : 'Start timer'}
		>
			<PlayIcon variant={timer.running ? 'pause' : 'play'} />
		</button>
		<button
			class="btn btn-circle btn-md btn-ghost"
			onclick={() => timer.reset()}
			aria-label="Reset timer"
		>
			<UndoIcon />
		</button>
	</div>
</Widget>

<Dialog bind:isOpen={isSettingsOpen} modalBoxClass="sm:w-96!" centered={true}>
	<div class="h-6"></div>

	<Toggle
		aria-label={pomodoroTimer.settings.settings.numberAnimations
			? 'Turn off number animations'
			: 'Turn on number animations'}
		bind:checked={pomodoroTimer.settings.settings.numberAnimations}
	>
		Number Animations
	</Toggle>

	<Toggle
		aria-label={pomodoroTimer.settings.settings.finishedAnimations
			? 'Turn off finished animations'
			: 'Turn on finished animations'}
		bind:checked={pomodoroTimer.settings.settings.finishedAnimations}
	>
		Finished Animations
	</Toggle>

	<Collapse open={pomodoroTimer.settings.settings.switchTimersOnEnd}>
		<Toggle
			aria-label={pomodoroTimer.settings.settings.switchTimersOnEnd
				? 'Turn off switch timers on end'
				: 'Turn on switch timers on end'}
			bind:checked={pomodoroTimer.settings.settings.switchTimersOnEnd}
		>
			Switch Timers on End
		</Toggle>

		<CollapseContent asSettings>
			<Toggle
				aria-label={pomodoroTimer.settings.settings.autoStartNextTimer
					? 'Turn off auto start next timer'
					: 'Turn on auto start next timer'}
				bind:checked={pomodoroTimer.settings.settings.autoStartNextTimer}
			>
				Auto Start Next Timer
			</Toggle>
		</CollapseContent>
	</Collapse>

	<div class="flex gap-4 justify-between items-center pb-2">
		<fieldset class="fieldset grow">
			<legend class="fieldset-legend">Focus</legend>

			<div class="input input-bordered flex items-center gap-2">
				<input
					type="number"
					aria-label="Focus time in minutes"
					placeholder="15"
					class="grow"
					value={pomodoroTimer.settings.settings.timer}
					oninput={(e) => changePomodoroTimerAmount(e)}
					min={0}
					max={maxTimeInMinutes}
				/>
				<span aria-hidden="true">min</span>
			</div>
		</fieldset>

		<fieldset class="fieldset grow">
			<legend class="fieldset-legend">Break</legend>
			<div class="input input-bordered flex items-center gap-2">
				<input
					type="number"
					aria-label="Break time in minutes"
					placeholder="15"
					class="grow"
					value={pomodoroTimer.settings.settings.break}
					oninput={(e) => changeBreakTimerAmount(e)}
					min={0}
					max={maxTimeInMinutes}
				/>
				<span aria-hidden="true">min</span>
			</div>
		</fieldset>
	</div>

	<div class="flex flex-col items-start gap-4">
		<label for="timer-volume" class="label">Volume</label>

		<Range
			id="timer-volume"
			class="range-xs"
			min={0}
			max={1}
			step={0.01}
			bind:value={
				() => pomodoroTimer.settings.settings.volume,
				(newVolume: number) => {
					pomodoroTimer.sound.volume(newVolume);
					pomodoroTimer.settings.settings.volume = newVolume;
				}
			}
			onpointerup={() => {
				pomodoroTimer.sound.play();
			}}
		/>
	</div>
</Dialog>
