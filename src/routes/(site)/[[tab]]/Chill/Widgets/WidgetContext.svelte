<script lang="ts" module>
	import { getContext, setContext } from 'svelte';
	import type { Component } from 'svelte';
	import PomodoroTimerComponent from './PomodoroTimer.svelte';
	import { browser } from '$app/environment';
	import { pomodoroTimer } from '$lib/stores/pomodoroTimer.svelte';

	export interface Widget {
		row: number;
		column: number;
		rowSpan: number;
		colSpan: number;
		id: WidgetId;
		Component: Component;
		closed: boolean;
		onclose?: () => void;
	}

	type StoredWidget = Omit<Widget, 'Component'>;

	const widgetsStorageKey = 'widgets';

	export type WidgetId = 'pomodoro-timer';

	export function getWidgetFor(widgetId: WidgetId): Widget {
		switch (widgetId) {
			case 'pomodoro-timer':
				return {
					row: 0,
					column: 0,
					rowSpan: 1,
					colSpan: 1,
					id: 'pomodoro-timer',
					closed: false,
					Component: PomodoroTimerComponent,
					onclose() {
						pomodoroTimer.timer.reset();
						pomodoroTimer.breakTimer.reset();
					},
				};
		}
	}

	let widgets: Widget[] = $state([]);

	function getWidget(id: WidgetId) {
		return widgets.find((widget) => widget.id === id);
	}

	function closeWidget(id: WidgetId) {
		const widget = getWidget(id);

		if (widget) {
			widget.closed = true;
			widget.onclose?.();
		}
	}

	function openWidget(id: WidgetId) {
		const widget = getWidget(id);

		if (widget) {
			widget.closed = false;
			return;
		}

		widgets = [...widgets, getWidgetFor(id)];
	}

	function isOpen(id: WidgetId) {
		return getWidget(id)?.closed === false;
	}

	function toggleWidget(id: WidgetId) {
		if (isOpen(id)) {
			closeWidget(id);
		} else {
			openWidget(id);
		}
	}

	$effect.root(() => {
		$effect(() => {
			if (browser) {
				const loadedWidgets = localStorage.getItem(widgetsStorageKey);

				if (loadedWidgets) {
					widgets = JSON.parse(loadedWidgets).map((widget: StoredWidget) => {
						return {
							...getWidgetFor(widget.id),
							...widget,
						};
					});
				}
			}
		});

		$effect(function saveWidgets() {
			if (browser) {
				localStorage.setItem(widgetsStorageKey, JSON.stringify(widgets));
			}
		});
	});

	let context = {
		get widgets() {
			return widgets;
		},
		openWidget,
		closeWidget,
		toggleWidget,
		isOpen,
	};

	export function setWidgetContext() {
		setContext('widget-context', context);
	}

	export function getWidgetContext(): typeof context {
		return getContext('widget-context');
	}
</script>
