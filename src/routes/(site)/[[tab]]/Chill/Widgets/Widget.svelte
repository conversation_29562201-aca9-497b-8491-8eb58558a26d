<script lang="ts">
	import type { Snippet } from 'svelte';
	import { cn } from '$lib/util/cn';
	import { fly } from 'svelte/transition';

	interface Props {
		children: Snippet;
		class?: string;
		element?: HTMLElement;
	}

	let { children, class: className, element = $bindable() }: Props = $props();
</script>

<div
	class={cn('glass p-4 z-0 rounded-2xl size-full', className)}
	transition:fly|global={{ y: 20, duration: 500 }}
	bind:this={element}
>
	{@render children()}
</div>
