<script lang="ts">
	import { cn } from '$lib/util/cn';
	import { fade } from 'svelte/transition';
	import { getWidgetContext, type Widget } from './WidgetContext.svelte';
	import WidgetPreview from './WidgetPreview.svelte';
	import { MediaQuery } from '$lib/util/MediaQuery.svelte';

	const widgetContext = getWidgetContext();
	const mediaQueries = {
		md: new MediaQuery('min-width: 768px'),
		lg: new MediaQuery('min-width: 1024px'),
		xl: new MediaQuery('min-width: 1280px'),
		xl2: new MediaQuery('min-width: 1536px'),
	};
	let gridSizeClasses = `
		grid-cols-2 grid-rows-4
		md:grid-cols-3 md:grid-rows-4
		lg:grid-cols-4 lg:grid-rows-4
		xl:grid-cols-5 xl:grid-rows-4
		2xl:grid-cols-6 2xl:grid-rows-4
	`;
	let gridSize = $derived.by(() => {
		if (mediaQueries.xl2.current) {
			return {
				cols: 6,
				rows: 4,
			};
		}
		if (mediaQueries.xl.current) {
			return {
				cols: 5,
				rows: 4,
			};
		}

		if (mediaQueries.lg.current) {
			return {
				cols: 4,
				rows: 4,
			};
		}

		if (mediaQueries.md.current) {
			return {
				cols: 3,
				rows: 4,
			};
		}

		return {
			cols: 2,
			rows: 4,
		};
	});

	let dragStartPosition: { x: number; y: number } | null = null;
	let isDragging = $state(false);
	let dragElement: HTMLElement | null = null;
	let dragWidget: Widget | null = $state(null);
	let grid = $state() as HTMLElement;
	let previewWidgetPosition: { row: number; col: number } | null = $state(null);

	function getWidgetPosition(element: HTMLElement) {
		const gridRect = grid.getBoundingClientRect();
		const widgetRect = element.getBoundingClientRect();
		const rowSpanSize = gridRect.height / gridSize.rows;
		const colSpanSize = gridRect.width / gridSize.cols;

		const x = Math.min(
			gridRect.width - 1,
			Math.max(0, widgetRect.x + widgetRect.width / 2 - gridRect.x),
		);

		const y = Math.min(
			gridRect.height - 1,
			Math.max(0, widgetRect.y + widgetRect.height / 2 - gridRect.y),
		);

		const col = Math.floor(x / colSpanSize);
		const row = Math.floor(y / rowSpanSize);

		return { row, col };
	}

	function endDrag(moveWidget: boolean) {
		isDragging = false;
		dragStartPosition = null;

		if (moveWidget && dragElement && dragWidget) {
			const { row, col } = getWidgetPosition(dragElement);

			dragWidget.row = row;
			dragWidget.column = col;
		}

		dragElement?.style.removeProperty('transform');
		dragElement = null;
		dragWidget = null;
		previewWidgetPosition = null;
	}

	function getWidgetGridClass(widget: Widget) {
		return cn('min-h-32', {
			'col-span-4': widget.colSpan === 4,
			'row-span-4': widget.rowSpan === 4,
			'col-span-3': widget.colSpan === 3,
			'row-span-3': widget.rowSpan === 3,
			'col-span-2': widget.colSpan === 2,
			'row-span-2': widget.rowSpan === 2,
			'col-span-1': widget.colSpan === 1,
			'row-span-1': widget.rowSpan === 1,
			'col-start-1': widget.column === 0,
			'row-start-1': widget.row === 0,
			'col-start-2': widget.column === 1,
			'row-start-2': widget.row === 1,
			'col-start-3': widget.column === 2,
			'row-start-3': widget.row === 2,
			'col-start-4': widget.column === 3,
			'row-start-4': widget.row === 3,
			'col-start-5': widget.column === 4,
			'row-start-5': widget.row === 4,
			'col-start-6': widget.column === 5,
			'row-start-6': widget.row === 5,
			'col-start-7': widget.column === 6,
			'row-start-7': widget.row === 6,
			'col-start-8': widget.column === 7,
			'row-start-8': widget.row === 7,
			'col-start-9': widget.column === 8,
			'row-start-9': widget.row === 8,
			'col-start-10': widget.column === 9,
			'row-start-10': widget.row === 9,
			'col-start-11': widget.column === 10,
			'row-start-11': widget.row === 10,
			'col-start-12': widget.column === 11,
			'row-start-12': widget.row === 11,
		});
	}
</script>

<svelte:window
	onpointermove={(e) => {
		if (e.buttons !== 1) {
			if (isDragging) {
				endDrag(true);
			}

			return;
		}

		const x = e.clientX;
		const y = e.clientY;

		if (!isDragging && dragStartPosition) {
			if (Math.abs(x - dragStartPosition.x) > 10 || Math.abs(y - dragStartPosition.y) > 10) {
				isDragging = true;
			}

			return;
		}

		if (isDragging && dragStartPosition && dragElement && dragWidget) {
			dragElement.style.transform = `translate(${-(dragStartPosition.x - x)}px, ${-(
				dragStartPosition.y - y
			)}px)`;

			if (previewWidgetPosition) {
				previewWidgetPosition = getWidgetPosition(dragElement);
			}

			if (x > window.innerWidth || y > window.innerHeight || x < 0 || y < 0) {
				endDrag(true);
			}
		}
	}}
	onpointerup={() => endDrag(true)}
/>

<div
	class="fixed overflow-auto inset-0 pt-16 px-2 pb-44 hidden sm:block z-0 touch-none"
	transition:fade|global={{ duration: 300 }}
>
	<div class="grid {gridSizeClasses} gap-4" bind:this={grid}>
		{#if dragWidget && previewWidgetPosition && isDragging}
			{#key `${previewWidgetPosition.row}-${previewWidgetPosition.col}`}
				<div
					class={getWidgetGridClass({
						...dragWidget,
						row: previewWidgetPosition.row,
						column: previewWidgetPosition.col,
					})}
					transition:fade|global={{ duration: 200 }}
				>
					<WidgetPreview />
				</div>
			{/key}
		{/if}

		{#each widgetContext.widgets as widget}
			{#if !widget.closed}
				<div
					class={getWidgetGridClass(widget)}
					onpointerdown={(e) => {
						if (!dragStartPosition) {
							dragStartPosition = {
								x: e.clientX,
								y: e.clientY,
							};
							dragElement = e.currentTarget as HTMLElement;
							dragWidget = widget;
							previewWidgetPosition = getWidgetPosition(dragElement);
						}
					}}
					onpointerup={(e) => {
						endDrag(isDragging && !!dragElement && dragElement === e.currentTarget);
					}}
				>
					<widget.Component />
				</div>
			{/if}
		{/each}
	</div>
</div>
