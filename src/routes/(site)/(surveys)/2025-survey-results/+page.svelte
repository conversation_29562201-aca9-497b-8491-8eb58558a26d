<script lang="ts">
	import { supabase } from '$lib/api/supabase';
	import Footer from '$lib/components/Footer.svelte';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { onMount } from 'svelte';

	let data: Array<{
		id: string;
		created_at: string;
		survey: Record<string, string | boolean>;
	}> = [];

	let results: Array<{ name: string; value: number }> = $state([]);
	let comments: string[] = $state([]);
	let participants = $state(0);

	onMount(async () => {
		try {
			const select = await supabase.from('2025_survey').select();
			data = select.data as [];
			participants = data.length;

			let resultsCounter: Record<string, number> = {};

			data.forEach((record) => {
				Object.entries(record.survey).forEach(([field, value]) => {
					if (field === 'comments') {
						comments.unshift(value as string);
					} else {
						if (!resultsCounter[field]) {
							resultsCounter[field] = 0;
						}
						resultsCounter[field] += 1;
					}
				});
			});

			results = Object.entries(resultsCounter)
				.map(([name, value]) => ({
					name,
					value,
				}))
				.sort((a, b) => b.value - a.value);

			comments = comments;
		} catch (error) {
			console.error(error);
		}
	});
</script>

<PageTransition>
	<Navbar />

	<article class="px-4 py-8 mx-auto">
		<h1>2025 Survey</h1>

		<h2>Participants: {participants}</h2>

		<h2>Results</h2>

		<table class="table">
			<thead>
				<tr>
					<th class="text-start">Option</th>
					<th class="text-start">Count</th>
				</tr>
			</thead>
			<tbody>
				{#each results as result}
					<tr>
						<td>{result.name}</td>
						<td>{result.value}</td>
					</tr>
				{/each}
			</tbody>
		</table>

		<h2>Comments</h2>

		{#each comments as comment}
			<p>{comment}</p>
		{/each}
	</article>

	<Footer />
</PageTransition>
