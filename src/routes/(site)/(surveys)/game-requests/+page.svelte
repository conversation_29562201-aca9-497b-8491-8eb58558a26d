<script lang="ts">
	import { supabase } from '$lib/api/supabase';
	import Footer from '$lib/components/Footer.svelte';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { onMount } from 'svelte';

	let data: Array<{
		game: string;
		created_at: string;
	}> = $state([]);

	onMount(async () => {
		try {
			const select = await supabase.from('game_request').select('game,created_at');
			data = select.data as [];
			data.reverse();
		} catch (error) {
			console.error(error);
		}
	});
</script>

<PageTransition>
	<Navbar />

	<article class="px-4 py-8 mx-auto">
		<h1>Game Requests</h1>

		<h2>Total: {data?.length}</h2>

		{#each data as entry}
			<p>{entry.game} at {Intl.DateTimeFormat('').format(new Date(entry.created_at))}</p>
		{/each}
	</article>

	<Footer />
</PageTransition>
