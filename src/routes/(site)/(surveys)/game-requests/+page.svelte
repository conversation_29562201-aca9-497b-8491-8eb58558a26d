<script lang="ts">
	import { supabase } from '$lib/api/supabase';
	import Footer from '$lib/components/Footer.svelte';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { atMidnight } from '$lib/functions/date/atMidnight';
	import { onMount } from 'svelte';

	let data: Array<{
		game: string;
		createdAt: Date;
	}> = $state([]);

	let groups = $state<{ createdAt: Date; games: string[] }[]>([]);

	onMount(async () => {
		try {
			const select = await supabase.from('game_request').select('game,created_at');

			data = select.data?.map((request) => {
				return {
					game: request.game,
					createdAt: new Date(request.created_at),
				};
			})!;

			// Build groups and sort by createdAt
			const groupMap = new Map<string, { createdAt: Date; games: string[] }>();

			// Group games by date
			for (const entry of data) {
				const dateKey = atMidnight(entry.createdAt).toISOString();

				if (!groupMap.has(dateKey)) {
					groupMap.set(dateKey, {
						createdAt: atMidnight(entry.createdAt),
						games: [],
					});
				}

				groupMap.get(dateKey)!.games.push(entry.game);
			}

			// Convert to array and sort by date (newest first)
			groups = Array.from(groupMap.values()).sort(
				(a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
			);
		} catch (error) {
			console.error(error);
		}
	});
</script>

<PageTransition>
	<Navbar />

	<article class="px-4 py-8 mx-auto">
		<h1>Game Requests</h1>

		<h2>Total: {data?.length}</h2>

		{#each groups as group}
			<h3>
				{Intl.DateTimeFormat('en-US', {
					month: 'short',
					day: 'numeric',
					year: 'numeric',
				}).format(group.createdAt)}
			</h3>

			{#each group.games as game}
				<p>
					{game}
				</p>
			{/each}
		{/each}
	</article>

	<Footer />
</PageTransition>
