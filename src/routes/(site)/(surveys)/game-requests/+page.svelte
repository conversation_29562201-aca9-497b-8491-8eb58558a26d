<script lang="ts">
	import { supabase } from '$lib/api/supabase';
	import Footer from '$lib/components/Footer.svelte';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { atMidnight } from '$lib/functions/date/atMidnight';
	import { isSameDay } from '$lib/functions/date/isSameDay';
	import { onMount } from 'svelte';
	import { SvelteMap } from 'svelte/reactivity';

	let data: Array<{
		game: string;
		createdAt: Date;
	}> = $state([]);

	let groups = new SvelteMap<Date, string[]>();
	let dates = $derived(
		data
			.map((d) => atMidnight d.createdAt)
			.sort((a, b) => {
				return a.getDate() - b.getDate();
			}),
	);

	onMount(async () => {
		try {
			const select = await supabase.from('game_request').select('game,created_at');

			data = select.data?.map((request) => {
				return {
					game: request.game,
					createdAt: new Date(request.created_at),
				};
			})!;

			dates.forEach((date) => {
				groups.set(
					date,
					data.filter((d) => isSameDay(d.createdAt, date)).map((d) => d.game),
				);
			});
		} catch (error) {
			console.error(error);
		}
	});
</script>

<PageTransition>
	<Navbar />

	<article class="px-4 py-8 mx-auto">
		<h1>Game Requests</h1>

		<h2>Total: {data?.length}</h2>

		{#each dates as date}
			<h3>
				{Intl.DateTimeFormat('en-US', {
					month: 'short',
					day: 'numeric',
					year: 'numeric',
				}).format(date)}
			</h3>

			{#each groups.get(date) as game}
				<p>
					{game}
				</p>
			{/each}
		{/each}
	</article>

	<Footer />
</PageTransition>
