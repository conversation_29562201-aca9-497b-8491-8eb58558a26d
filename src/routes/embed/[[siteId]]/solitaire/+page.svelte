<script lang="ts">
	import { untrack } from 'svelte';
	import Solitaire, {
		type SolitaireProps,
	} from '../../../(site)/(games)/solitaire/Solitaire.svelte';
	import {
		createSolitaireMessageBroker,
		type SolitaireMessageBroker,
	} from '../../../(site)/(games)/solitaire/systems/createSolitaireMessageBroker';
	import { page } from '$app/state';
	import { stripTrailingSlash } from '$lib/functions/stripTrailingSlash';
	import { embedSiteParamToUrl } from './embedSiteParamToUrl';

	let messageBroker = $state<SolitaireMessageBroker>();
	let solitaireProps = $state<DeepPartial<SolitaireProps>>();

	$effect.pre(() => {
		untrack(() => {
			const port = page.url.searchParams.get('port');
			const siteId = page.params.siteId ?? '';
			const site = embedSiteParamToUrl[siteId];

			if (!site) {
				throw new Error('Invalid site');
			}

			const url = new URL(site);

			if (port) {
				url.port = port;
			}

			messageBroker = createSolitaireMessageBroker({
				targetOrigin: stripTrailingSlash(url.toString()),
			});

			messageBroker.on('setup-game', (data: DeepPartial<SolitaireProps>) => {
				solitaireProps = {
					ui: data.ui === 'actions' ? 'actions' : 'none',
					backgroundColor: data.backgroundColor ? data.backgroundColor : undefined,
					layoutConfig: data.layoutConfig ? data.layoutConfig : undefined,
					assets: data.assets ? data.assets : undefined,
				};
			});

			messageBroker.start();
			messageBroker.send('ready');
		});

		return () => {
			untrack(() => {
				messageBroker?.dispose();
			});
		};
	});
</script>

{#if solitaireProps && messageBroker}
	<Solitaire
		ui="actions"
		{...solitaireProps}
		targetOrigin={messageBroker.targetOrigin}
		disableLeaderboard
		noNavbar
	/>
{/if}
