<script lang="ts">
	import type { Snippet } from 'svelte';
	import { ads } from '$lib/stores/ads.svelte';
	import { notifications } from '$lib/stores/notifications.svelte';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';

	interface Props {
		children: Snippet;
	}

	let { children }: Props = $props();

	$effect.pre(() => {
		ads.canShowGameAds = false;
		notifications.disabled = true;
		// TODO: Override settings instead
		islandSettings.settings.autoPauseOnWindowBlur = false;
		islandSettings.settings.showExitIntent = false;
	});
</script>

{@render children()}
