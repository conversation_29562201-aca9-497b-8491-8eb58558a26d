<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" type="image/png" href="/favicon-96x96.png" sizes="96x96" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<link rel="shortcut icon" href="/favicon.ico" />
		<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
		<meta name="apple-mobile-web-app-title" content="Lofi and Games" />
		<link rel="manifest" href="/site.webmanifest" />
		<link rel="manifest" href="/site.webmanifest" />
		<link rel="mask-icon" href="/safari-pinned-tab.svg" color="#252a35" />
		<meta name="apple-mobile-web-app-title" content="Lofi And Games" />
		<meta name="application-name" content="Lofi And Games" />
		<meta name="msapplication-TileColor" content="#252a35" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<script>
			const updateVh = () => {
				const doc = document.documentElement;
				let newVh = 0;

				newVh = window.innerHeight;
				doc.style.setProperty('--vh', `${newVh}px`);
			};

			window.addEventListener('orientationchange', function () {
				// After orientationchange, add a one-time resize event
				const afterOrientationChange = function () {
					updateVh();
					// Remove the resize event listener after it has executed
					window.removeEventListener('resize', afterOrientationChange);
				};
				window.addEventListener('resize', afterOrientationChange);
			});

			// Update vh size on desktop, so entering and leaving fullscreen works correctly
			window.addEventListener('resize', function () {
				if (window.matchMedia('(min-width: 1024px)').matches) {
					updateVh();
				}
			});

			updateVh();
		</script>

		<!-- Google tag (gtag.js) -->
		<script defer src="https://www.googletagmanager.com/gtag/js?id=G-M7M1CJNQZP"></script>
		<script>
			window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag('js', new Date());

			gtag('config', 'G-M7M1CJNQZP');
		</script>
		<!-- Playlight -->
		<link rel="stylesheet" href="https://sdk.playlight.dev/playlight-sdk.css" defer />

		%sveltekit.head%
	</head>
	<body>
		<div style="display: contents">%sveltekit.body%</div>

		<script
			async
			src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-9459941752955255"
			crossorigin="anonymous"
		></script>
	</body>
</html>
