import { wait } from '$lib/functions/wait';
import type { Attachment } from 'svelte/attachments';

export const switchSoundAttachment = ({
	action,
	disabled = false,
	onPlayOnSound,
	onPlayOffSound,
}: {
	action: 'on' | 'off';
	disabled?: boolean;
	onPlayOnSound: () => void;
	onPlayOffSound: () => void;
}): Attachment => {
	return (element) => {
		if (disabled) {
			return;
		}

		const maxWaitTime = 80;
		let pointerDownAt = 0;

		const onPointerDown = () => {
			pointerDownAt = performance.now();

			if (action === 'on') {
				onPlayOffSound();
			} else {
				onPlayOnSound();
			}
		};

		const onPointerUp = async () => {
			const timeDiff = performance.now() - pointerDownAt;
			const waitTime = timeDiff > maxWaitTime ? 0 : maxWaitTime - timeDiff;

			if (waitTime > 0) {
				await wait(waitTime);
			}

			if (action === 'on') {
				onPlayOnSound();
			} else {
				onPlayOffSound();
			}
		};

		element.addEventListener('pointerdown', onPointerDown, true);
		element.addEventListener('pointerup', onPointerUp, true);

		return () => {
			element.removeEventListener('pointerdown', onPointerDown, true);
			element.removeEventListener('pointerup', onPointerUp, true);
		};
	};
};
