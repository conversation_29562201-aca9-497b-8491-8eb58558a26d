export class Timer {
	private _startedAt: number | null = $state(null);
	private _stopped = $state(false);
	private cumulativeTime = $state(0);
	private _countdownDuration: number | null = $state(null);

	constructor(options?: { countdownDuration?: number }) {
		if (options?.countdownDuration !== undefined) {
			this.countdownDuration = options.countdownDuration;
		}
	}

	get startedAt() {
		return this._startedAt;
	}

	get started(): boolean {
		if (this.isCountdown) {
			return this.time !== this.countdownDuration;
		}
		return this._startedAt !== null || this.cumulativeTime !== 0;
	}

	set startedAt(startedAt: number | null) {
		this._startedAt = startedAt;
	}

	get countdownDuration(): number | null {
		return this._countdownDuration;
	}

	set countdownDuration(milliseconds: number | null) {
		if (milliseconds === null) {
			this._countdownDuration = null;
			this.cumulativeTime = 0;
		} else {
			this._countdownDuration = milliseconds;
			this.cumulativeTime = this._countdownDuration;
		}
		// Reset timer state when changing mode
		this._startedAt = null;
		this._stopped = false;
	}

	get time() {
		if (this._countdownDuration === null) {
			if (!this.startedAt) {
				return this.cumulativeTime;
			}
			return performance.now() - this.startedAt + this.cumulativeTime;
		}

		if (!this.startedAt) {
			return this.cumulativeTime;
		}

		const time =
			this._countdownDuration -
			(performance.now() - this.startedAt + (this._countdownDuration - this.cumulativeTime));

		if (time <= 0) {
			return 0;
		}

		return time;
	}

	/**
	 * Get the true elapsed time.
	 * When timer is not countdown, it's the same as time.
	 * When it's countdown, it's the true elapsed time.
	 */
	get elapsedTime() {
		if (this._countdownDuration !== null) {
			return this._countdownDuration - this.time;
		}

		return this.time;
	}

	get running() {
		return !!this.startedAt;
	}

	get paused() {
		return !this.startedAt;
	}

	get stopped() {
		if (this.isCountdown) {
			return this.time === 0 || this._stopped;
		}

		return this._stopped;
	}

	get isCountdown() {
		return this._countdownDuration !== null;
	}

	start() {
		if (this.stopped) {
			return;
		}

		if (this.startedAt) {
			return;
		}

		this.startedAt = performance.now();
	}

	pause() {
		if (this.stopped) {
			return;
		}

		if (this.startedAt) {
			if (this._countdownDuration === null) {
				this.cumulativeTime += performance.now() - this.startedAt;
			} else {
				this.cumulativeTime = this.time;
			}
		}
		this.startedAt = null;
	}

	stop() {
		this.pause();
		this._stopped = true;
	}

	toggle() {
		if (this.paused) {
			this.start();
		} else {
			this.pause();
		}
	}

	reset() {
		this.cumulativeTime = this._countdownDuration ?? 0;
		this.startedAt = null;
		this._stopped = false;
	}
}
