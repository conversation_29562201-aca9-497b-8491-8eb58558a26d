type CacheRecord<T> = {
	value: T;
	createdAt: number;
	duration: number; // ms
};

export class Cache<T> {
	private _cache = new Map<string, CacheRecord<T>>();
	private _defaultDuration: number;

	constructor(props?: { defaultDuration: number }) {
		this._defaultDuration = props?.defaultDuration || 60_000; // 1min
	}

	set({ key, value, duration }: { key: string; value: T; duration?: number }) {
		this._cache.set(key, {
			value,
			createdAt: performance.now(),
			duration: duration ?? this._defaultDuration,
		});
	}

	get(key: string) {
		const record = this._cache.get(key);

		if (!record) {
			return null;
		}

		if (performance.now() - record.createdAt > record.duration) {
			this._cache.delete(key);
			return null;
		}

		return record;
	}

	invalidate(key: string | ((key: string) => boolean)) {
		if (typeof key === 'function') {
			const keysToDelete = Array.from(this._cache.keys().filter(key));

			keysToDelete.forEach((keyToDelete) => {
				this._cache.delete(keyToDelete);
			});

			return keysToDelete.length > 0;
		} else {
			return this._cache.delete(key);
		}
	}
}
