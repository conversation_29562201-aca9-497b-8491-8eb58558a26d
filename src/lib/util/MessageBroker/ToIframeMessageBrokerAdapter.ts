import type { MessageBrokerAdapter, MessageBrokerReceiveEvent } from './MessageBrokerAdapter';

export class ToIframeMessageBrokerAdapter implements MessageBrokerAdapter {
	#abortController = new AbortController();
	#iframe: HTMLIFrameElement;

	constructor(iframe: HTMLIFrameElement) {
		this.#iframe = iframe;
	}

	send(message: string, targetOrigin: string) {
		this.#iframe.contentWindow?.postMessage(message, targetOrigin);
	}

	listen = (callback: (event: MessageBrokerReceiveEvent) => void) => {
		window.addEventListener(
			'message',
			(event) => {
				if (event.type === 'message') {
					callback(event);
				}
			},
			{ signal: this.#abortController.signal },
		);
	};

	dispose() {
		this.#abortController.abort();
	}
}
