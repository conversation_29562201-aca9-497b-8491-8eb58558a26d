import type { MessageBrokerAdapter } from './MessageBrokerAdapter';

class Message {
	name: string;
	data: any;

	constructor({ name, data }: { name: string; data: any }) {
		this.name = name;
		this.data = data;
	}

	toString() {
		return JSON.stringify({
			name: this.name,
			data: this.data,
		});
	}

	static fromString(text: string): Message {
		const { name, data } = JSON.parse(text);

		return new Message({ name, data });
	}
}

interface MessageBrokerProps<SendEvent extends string, ReceiveEvent extends string> {
	targetOrigin: string;
	readonly receive: ReceiveEvent[];
	readonly send: SendEvent[];
	adapter?: MessageBrokerAdapter;
}

export class MessageBroker<SendEvent extends string, ReceiveEvent extends string> {
	#props: MessageBrokerProps<SendEvent, ReceiveEvent>;
	#adapter?: MessageBrokerAdapter;
	#eventTarget = new EventTarget();
	#abortController = new AbortController();

	constructor(props: MessageBrokerProps<SendEvent, ReceiveEvent>) {
		this.#props = props;
		this.#adapter = props.adapter;
	}

	get targetOrigin() {
		return this.#props.targetOrigin;
	}

	get adapter(): MessageBrokerAdapter | undefined {
		return this.#adapter;
	}

	set adapter(adapter: MessageBrokerAdapter) {
		this.#adapter?.dispose();
		this.#adapter = adapter;
	}

	start() {
		if (!this.adapter) {
			throw new Error('MessageBroker adapter is not set');
		}

		this.adapter.listen((event) => {
			if (this.#props.targetOrigin !== event.origin) {
				console.debug('ignoring event', this.#props.targetOrigin, event.origin);
				return;
			}

			const message = Message.fromString(event.data);

			if (this.#props.receive.includes(message.name as ReceiveEvent)) {
				this.#eventTarget.dispatchEvent(
					new CustomEvent(message.name, { detail: message.data }),
				);
			} else {
				console.debug('event not found', message.name);
			}
		});
	}

	on(event: ReceiveEvent, callback: (data?: any) => void) {
		this.#eventTarget.addEventListener(
			event,
			(e) => {
				console.debug('on', event);
				if (e instanceof CustomEvent) {
					callback(e.detail);
				}
			},
			{
				signal: this.#abortController.signal,
			},
		);

		return this;
	}

	send(event: SendEvent, data?: any) {
		if (!this.adapter) {
			throw new Error('MessageBroker adapter is not set');
		}

		this.adapter.send(new Message({ name: event, data }).toString(), this.#props.targetOrigin);
	}

	dispose() {
		this.#abortController.abort();
		this.adapter?.dispose();
	}
}
