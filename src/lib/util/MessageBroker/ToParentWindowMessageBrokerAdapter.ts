import type { MessageBrokerAdapter, MessageBrokerReceiveEvent } from './MessageBrokerAdapter';

export class ToParentWindowMessageBrokerAdapter implements MessageBrokerAdapter {
	#abortController = new AbortController();

	send(message: string, targetOrigin: string) {
		window.parent.postMessage(message, targetOrigin);
	}

	listen = (callback: (event: MessageBrokerReceiveEvent) => void) => {
		window.addEventListener(
			'message',
			(event) => {
				if (event.type === 'message') {
					callback(event);
				}
			},
			{ signal: this.#abortController.signal },
		);
	};

	dispose() {
		this.#abortController.abort();
	}
}
