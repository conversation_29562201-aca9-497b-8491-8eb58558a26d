import { authClient } from '$lib/auth/client';
import { getLeaderboardDays } from '$lib/util/Leaderboard/getLeaderboardDays';
import { getLeaderboardMonths } from '$lib/util/Leaderboard/getLeaderboardMonths';
import { getLeaderboardWeeklyRanges } from '$lib/util/Leaderboard/getLeaderboardWeeklyRanges';
import { getLeaderboardYears } from '$lib/util/Leaderboard/getLeaderboardYears';
import { formatDateToYearMonthDay } from '$lib/functions/date/formatDateToYearMonthDay';
import { Cache } from '../Cache';
import { isSameDay } from '$lib/functions/date/isSameDay';

export interface LeaderboardRecord {
	name: string;
	image: string;
	score: number;
	time: number;
	moves?: number;
	level?: number;
	rank: number;
}

export interface LeaderboardData {
	records: LeaderboardRecord[];
	user: LeaderboardRecord | null;
}

export interface LeaderboardNewScoreDataV2 {
	records: LeaderboardRecord[];
	user: LeaderboardRecord | null;
	frequency: LeaderboardFrequency;
}

export const leaderboardRanges = ['top-20', 'top-50', 'top-100', 'around-player'] as const;

export type LeaderboardRange = (typeof leaderboardRanges)[number];

export const leaderboardOrders = ['lower-first', 'higher-first'] as const;

export type LeaderboardOrder = (typeof leaderboardOrders)[number];

export const leaderboardFrequencies = ['daily', 'weekly', 'monthly', 'yearly', 'all-time'] as const;

export type LeaderboardFrequency = (typeof leaderboardFrequencies)[number];

const maxRetry = 3;

export interface LeaderboardProps {
	game: string;
	gameVariant?: string;
	range?: LeaderboardRange;
	firstAvailableDate: Date;
	/** @default false */
	hasMoves?: boolean;
	/** @default false */
	hasLevel?: boolean;
	/** @default true */
	hasTime?: boolean;
	order: LeaderboardOrder;
	frequency?: LeaderboardFrequency;
	disabled?: boolean;
}

const leaderboardCache = new Cache<LeaderboardData>({ defaultDuration: 60_000 });

export const leaderboardExpiryDaysByFrequency: Record<LeaderboardFrequency, number> = {
	daily: 30,
	monthly: 365,
	weekly: 7 * 8,
	yearly: 365 * 5,
	'all-time': Infinity,
};

interface SendNewPlayerScoreProps {
	score?: number | null;
	moves?: number;
	/** time in ms */
	time: number;
	level?: number;
	retryCount?: number;
}

export class Leaderboard {
	private _game: string;
	private _gameVariant: string;
	private _board: LeaderboardData = $state({ records: [], user: null });
	private _range: LeaderboardRange;
	private _loadState: 'loading' | 'success' | 'error' | 'idle' = $state('idle');
	private _sendScoreState: 'loading' | 'success' | 'error' | 'idle' = $state('idle');
	private _date = $state(new Date());
	private _firstAvailableDate: Date;
	private _abortController: AbortController | null = null;
	private _hasMoves = false;
	private _hasLevel = false;
	private _hasTime = true;
	private _order: LeaderboardOrder;
	private _lastSendSuccessAt = $state<Date>();
	private _lastPlayerRank = $state<number | null>(null);
	private _frequency: LeaderboardFrequency;
	private _failedSendNewPlayerScoreProps: SendNewPlayerScoreProps | null = null;
	private _disabled = $state(false);

	constructor({
		game,
		range = 'around-player',
		gameVariant = 'default',
		firstAvailableDate,
		hasMoves = false,
		hasLevel = false,
		hasTime = true,
		order,
		frequency = 'daily',
		disabled = false,
	}: LeaderboardProps) {
		this._game = game;
		this._range = range;
		this._firstAvailableDate = firstAvailableDate;
		this._hasMoves = hasMoves;
		this._hasLevel = hasLevel;
		this._hasTime = hasTime;
		this._gameVariant = gameVariant;
		this._order = order;
		this._frequency = frequency;
		this._disabled = disabled;

		const url = this.getUrl({ date: new Date(), range: this._range });

		const cached = leaderboardCache.get(url);

		if (cached) {
			const rank = cached.value.user?.rank;

			if (rank !== undefined && rank !== null) {
				this._lastPlayerRank = rank;
			}
		}
	}

	get disabled() {
		return this._disabled;
	}

	set disabled(disabled: boolean) {
		this._disabled = disabled;
	}

	get lastSendSuccessAt() {
		return this._lastSendSuccessAt;
	}

	get order() {
		return this._order;
	}

	get gameVariant() {
		return this._gameVariant;
	}

	get game() {
		return this._game;
	}

	get hasLevel() {
		return this._hasLevel;
	}

	get hasMoves() {
		return this._hasMoves;
	}

	get hasTime() {
		return this._hasTime;
	}

	get range() {
		return this._range;
	}

	get board() {
		return this._board;
	}

	get loadState() {
		return this._loadState;
	}

	get sendScoreState() {
		return this._sendScoreState;
	}

	get date() {
		return this._date;
	}

	private get _isLoggedIn() {
		const session = authClient.useSession();

		return !!session.get().data?.user;
	}

	set date(newDate: Date) {
		this._date = newDate;
		this.load();
	}

	get firstAvailableDate() {
		return this._firstAvailableDate;
	}

	get wasPlayerRankUpdated() {
		const user = this._board.user;

		if (user) {
			return user.rank !== this._lastPlayerRank;
		}

		return false;
	}

	mockBoard() {
		const playerPlace = Math.floor(0 + Math.random() * 40);
		const playerRecord = {
			rank: playerPlace,
			score: 12312,
			time: 1230312,
			moves: 123,
			level: 1,
			name: 'Bruno Alves',
			image: '',
		};
		this._board = {
			user: playerRecord,
			records:
				this._range === 'around-player'
					? [
							{
								rank: playerPlace - 1,
								score: 34534,
								time: 34511,
								moves: 123,
								level: 1,
								name: 'player-above',
								image: '',
							},
							playerRecord,
							{
								rank: playerPlace + 1,
								score: 5674,
								time: 567,
								moves: 123,
								level: 1,
								name: 'player-below',
								image: '',
							},
						]
					: Array.from({ length: 20 })
							.fill(0)
							.map((_, rank) => {
								return {
									rank: rank,
									score: rank * 10,
									time: rank * 1e5,
									moves: rank * 10,
									level: rank * 10,
									name: `Player ${rank} with a very large name`,
									image: '',
								};
							}),
		};
		this._loadState = 'success';
	}

	get url() {
		return this.getUrl({
			date: this._date,
			range: this.range,
			order: this.order,
			frequency: this._frequency,
		});
	}

	private getUrl({
		date,
		range,
		order = this._order,
		frequency = this._frequency,
		v2 = false,
	}: {
		date: Date;
		range?: LeaderboardRange;
		order?: LeaderboardOrder;
		frequency?: LeaderboardFrequency;
		v2?: boolean;
	}): string {
		const day = formatDateToYearMonthDay(date, '-');

		const url = v2
			? new URL(
					`${import.meta.env.VITE_API_URL}/leaderboard/v2/game/${this._game}/variant/${this._gameVariant}/day/${day}`,
				)
			: new URL(
					`${import.meta.env.VITE_API_URL}/leaderboard/game/${this._game}/variant/${this._gameVariant}/day/${day}`,
				);

		url.searchParams.append('order', order);

		if (range) {
			url.searchParams.append('range', range);
		}

		if (frequency) {
			url.searchParams.append('frequency', frequency);
		}

		return url.toString();
	}

	async load(date = this._date) {
		if (this.disabled) {
			return;
		}

		if (!this._isLoggedIn) {
			return;
		}

		if (this._board.user) {
			this._lastPlayerRank = this._board.user.rank;
		}

		if (!isSameDay(date, this.date)) {
			this._failedSendNewPlayerScoreProps = null;
			this._sendScoreState = 'idle';
		}

		this._abortController?.abort();
		this._abortController = new AbortController();
		this._date = date;

		const url = this.getUrl({ date, range: this._range });

		const cachedBoard = leaderboardCache.get(url);

		if (cachedBoard) {
			this._board = cachedBoard.value;
			this._loadState = 'success';
			return cachedBoard;
		}

		this._loadState = 'loading';

		try {
			const res = await fetch(url, {
				credentials: 'include',
				signal: this._abortController.signal,
			});

			if (res.status >= 300) {
				throw new Error('Error loading leaderboard');
			}

			const board = (await res.json()) as LeaderboardData;

			leaderboardCache.set({
				key: url,
				value: board,
			});

			this._board = board;

			this._loadState = 'success';
		} catch (_) {
			this._loadState = 'error';
		} finally {
			this._abortController = null;
		}
	}

	async sendNewPlayerScore({
		score,
		moves,
		level,
		time,
		retryCount = 0,
	}: SendNewPlayerScoreProps): Promise<boolean> {
		if (this._disabled) {
			return false;
		}

		if (!this._isLoggedIn) {
			return false;
		}

		if ([undefined, null, Infinity, -Infinity].includes(score)) {
			return false;
		}

		if (this._board.user) {
			this._lastPlayerRank = this._board.user.rank;
		}

		this._sendScoreState = 'loading';

		if (this._range === 'around-player') {
			this._loadState = 'loading';
		}

		const date = new Date();
		this._date = date;

		try {
			const res = await fetch(this.getUrl({ date, v2: true }), {
				method: 'PUT',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ score, moves, time, level }),
			});

			if (res.status >= 300 || !res.ok) {
				throw new Error('Failed to send player score');
			}

			const aroundPlayerResponse = (await res.json()) as LeaderboardNewScoreDataV2[];

			const baseUrl = new URL(
				`${import.meta.env.VITE_API_URL}/leaderboard/game/${this._game}/variant/${this._gameVariant}`,
			).toString();

			leaderboardCache.invalidate((key) => {
				return key.includes(baseUrl);
			});

			aroundPlayerResponse.forEach((board) => {
				let day = date;

				switch (board.frequency) {
					case 'daily':
						day = getLeaderboardDays({ leaderboard: this, amountOfDays: 1 })[0];
						break;
					case 'weekly':
						day = getLeaderboardWeeklyRanges({
							leaderboard: this,
							amountOfWeeks: 1,
						})[0].from;
						break;
					case 'monthly':
						day = getLeaderboardMonths({ leaderboard: this, amountOfMonths: 1 })[0];
						break;
					case 'yearly':
						day = getLeaderboardYears({ leaderboard: this, amountOfYears: 1 })[0];
						break;
					case 'all-time':
						day = date;
						break;
				}

				const aroundPlayerUrl = this.getUrl({
					date: day,
					range: 'around-player',
					order: this._order,
					frequency: board.frequency,
				});

				leaderboardCache.set({
					key: aroundPlayerUrl,
					value: board,
				});
			});

			if (this._range === 'around-player') {
				const board = aroundPlayerResponse.find(
					(board) => board.frequency === this._frequency,
				);

				if (board) {
					this._board = board;
				}

				this._loadState = 'success';
			}

			this._sendScoreState = 'success';
			this._lastSendSuccessAt = new Date();
			this._failedSendNewPlayerScoreProps = null;
		} catch (_error) {
			this._failedSendNewPlayerScoreProps = {
				score,
				moves,
				time,
				level,
				retryCount: 0,
			};

			this._sendScoreState = 'error';

			if (retryCount < maxRetry) {
				return await this.sendNewPlayerScore({
					score,
					moves,
					time,
					level,
					retryCount: retryCount + 1,
				});
			}

			return false;
		}

		return true;
	}

	retrySendingNewPlayerScore() {
		if (this._failedSendNewPlayerScoreProps) {
			this.sendNewPlayerScore(this._failedSendNewPlayerScoreProps);
		}
	}

	clone(props?: {
		range?: LeaderboardRange;
		frequency?: LeaderboardFrequency;
		gameVariant?: string;
	}) {
		const variant = props?.range ?? this.range;

		const leaderboard = new Leaderboard({
			game: this._game,
			firstAvailableDate: this.firstAvailableDate,
			gameVariant: props?.gameVariant ?? this.gameVariant,
			hasMoves: this.hasMoves,
			hasLevel: this.hasLevel,
			hasTime: this.hasTime,
			range: variant,
			order: this._order,
			frequency: props?.frequency ?? this._frequency,
			disabled: this.disabled,
		});

		leaderboard._loadState = this._loadState;

		return leaderboard;
	}
}
