import { subtract } from '$lib/functions/date/subtract';
import type { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';

export function getLeaderboardDays({
	leaderboard,
	amountOfDays,
}: {
	leaderboard: Leaderboard;
	amountOfDays: number;
}) {
	const today = new Date();
	const firstAvailableDate = leaderboard.firstAvailableDate;
	const daysBetween = Math.floor(
		(today.getTime() - firstAvailableDate.getTime()) / (1000 * 60 * 60 * 24),
	);
	const actualAmountOfDays = Math.min(amountOfDays, daysBetween + 1);

	return Array.from({ length: actualAmountOfDays }).map((_, i) => {
		return subtract(today, actualAmountOfDays - 1 - i);
	});
}
