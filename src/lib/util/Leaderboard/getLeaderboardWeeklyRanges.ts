import { add } from '$lib/functions/date/add';
import type { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';

export function getLeaderboardWeeklyRanges({
	leaderboard,
	amountOfWeeks,
}: {
	leaderboard: Leaderboard;
	amountOfWeeks: number;
}) {
	const firstDayOfMonth = new Date(
		leaderboard.date.getFullYear(),
		leaderboard.date.getMonth(),
		1,
	);
	let firstSunday: Date | null = null;
	let date = firstDayOfMonth;

	while (!firstSunday) {
		if (date.getDay() === 0) {
			firstSunday = date;
		}

		date = add(date, 1);
	}

	const nearRanges = [];

	for (let i = -amountOfWeeks; i < 5; i += 1) {
		const from = add(firstSunday, i * 7);
		const to = new Date(add(from, 6).getTime() + 24 * 60 * 60 * 1000 - 1);

		nearRanges.push({ from, to });
	}

	const leaderboardRangeIndex = nearRanges.findIndex((range) => {
		return range.from <= leaderboard.date && range.to >= leaderboard.date;
	});

	const ranges = nearRanges.slice(
		leaderboardRangeIndex - amountOfWeeks + 1,
		leaderboardRangeIndex + 1,
	);

	// Filter out ranges that start before firstAvailableDate
	const firstAvailableDate = leaderboard.firstAvailableDate;

	return ranges.filter((range) => range.to >= firstAvailableDate);
}
