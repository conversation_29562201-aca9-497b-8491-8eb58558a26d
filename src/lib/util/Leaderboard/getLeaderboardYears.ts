import type { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';

export function getLeaderboardYears({
	leaderboard,
	amountOfYears,
}: {
	leaderboard: Leaderboard;
	amountOfYears: number;
}) {
	const today = new Date();
	const firstAvailableDate = leaderboard.firstAvailableDate;
	const firstAvailableYear = firstAvailableDate.getFullYear();
	const currentYear = today.getFullYear();

	// Calculate years between first available and current year
	const yearsBetween = currentYear - firstAvailableYear;
	const actualAmountOfYears = Math.min(amountOfYears, yearsBetween + 1);

	return Array.from({ length: actualAmountOfYears }).map((_, i) => {
		return new Date(currentYear - actualAmountOfYears + 1 + i, 0, 1);
	});
}
