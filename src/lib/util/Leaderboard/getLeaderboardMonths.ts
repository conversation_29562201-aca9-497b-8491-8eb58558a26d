import type { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';

export function getLeaderboardMonths({
	leaderboard,
	amountOfMonths,
}: {
	leaderboard: Leaderboard;
	amountOfMonths: number;
}) {
	const today = new Date();
	const firstAvailableDate = leaderboard.firstAvailableDate;
	const firstAvailableMonth = new Date(
		firstAvailableDate.getFullYear(),
		firstAvailableDate.getMonth(),
		1,
	);
	const currentMonth = new Date(today.getFullYear(), today.getMonth(), 1);

	// Calculate months between first available and current month
	const monthsBetween =
		(currentMonth.getFullYear() - firstAvailableMonth.getFullYear()) * 12 +
		(currentMonth.getMonth() - firstAvailableMonth.getMonth());

	const actualAmountOfMonths = Math.min(amountOfMonths, monthsBetween + 1);

	return Array.from({ length: actualAmountOfMonths }).map((_, i) => {
		return new Date(
			currentMonth.getFullYear(),
			currentMonth.getMonth() - actualAmountOfMonths + 1 + i,
			1,
		);
	});
}
