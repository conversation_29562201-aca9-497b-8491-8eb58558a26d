import { goto } from '$app/navigation';
import { page } from '$app/state';

export interface SpecificGameProps {
	onError: () => void;
	onPlay: (encodedGame: string) => void;
}

export class SpecificGame {
	error: Error | undefined = $state();
	private onPlay: (encoded: string) => void;
	private onError: () => void;

	constructor(props: SpecificGameProps) {
		this.onPlay = props.onPlay;
		this.onError = props.onError;
	}

	private async play(game: string): Promise<void> {
		try {
			this.updateUrlWithSpecificGame(game);

			return this.onPlay(game as any);
		} catch (error: any) {
			this.error = error;
			this.onError();
		}
	}

	private updateUrlWithSpecificGame(game: string) {
		page.url.searchParams.set('game', encodeURIComponent(game));

		goto(page.url.toString().replace(/=(?=&|$)/gm, ''), { replaceState: true });
	}

	playSpecificGameFromUrl() {
		const game = SpecificGame.getGameFromUrl();

		if (game !== null) {
			this.play(game);
		}
	}

	urlHasSpecificGame() {
		try {
			const game = SpecificGame.getGameFromUrl();

			return !!game;
		} catch (_) {
			return false;
		}
	}

	private static getGameFromUrl() {
		const gameParam = page.url.searchParams.get('game');

		if (gameParam !== null) {
			return decodeURIComponent(gameParam);
		}

		return null;
	}
}
