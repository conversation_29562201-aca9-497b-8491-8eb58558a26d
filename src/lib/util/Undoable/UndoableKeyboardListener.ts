import { isIOS } from '$lib/functions/isIOS';
import { isMacLike } from '$lib/functions/isMacLike';

type Callback = () => void;

export type ShortcutsTags = 'INPUT' | 'SELECT' | 'TEXTAREA';

export const elementTagsToIgnoreEvents: ShortcutsTags[] = ['INPUT', 'TEXTAREA', 'SELECT'];

export class UndoableKeyboardListener {
	onUndo: Callback;
	onRedo: Callback;

	constructor(onUndo: Callback, onRedo: Callback) {
		this.onUndo = onUndo;
		this.onRedo = onRedo;
	}

	listen() {
		if (document) {
			document?.addEventListener('keydown', this.listener);
		}
	}

	dispose() {
		if (document) {
			document?.removeEventListener('keydown', this.listener);
		}
	}

	private listener = (event: KeyboardEvent) => {
		if (
			elementTagsToIgnoreEvents.includes(
				(event.target as HTMLElement).tagName as ShortcutsTags,
			)
		) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (isMacLike() || isIOS()) {
			if (event.metaKey && key === 'z') {
				if (event.shiftKey) {
					this.onRedo();
					event.preventDefault();
				} else {
					this.onUndo();
					event.preventDefault();
				}
			}
		} else {
			if (event.ctrlKey && key === 'z') {
				if (event.shiftKey) {
					this.onRedo();
					event.preventDefault();
				} else {
					this.onUndo();
					event.preventDefault();
				}
			}
		}
	};
}
