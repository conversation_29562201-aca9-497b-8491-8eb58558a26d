import { createSubscriber } from 'svelte/reactivity';
import { on } from 'svelte/events';
import { Playlight } from '$lib/stores/playlightSdk.svelte';

export class MediaQuery {
	#queryString: string;
	#query?: MediaQueryList;
	#subscribe?: () => void;

	constructor(query: string) {
		this.#queryString = query;

		this.#init();
	}

	get current() {
		// This makes the getter reactive, if read in an effect
		this.#subscribe?.();

		// Return the current state of the query, whether or not we're in an effect
		return this.#query?.matches;
	}

	#init() {
		this.#subscribe = createSubscriber((update) => {
			this.#query = window.matchMedia(`(${this.#queryString})`);

			// when the `change` event occurs, re-run any effects that read `this.current`
			let off = on(this.#query!, 'change', update);

			void Playlight.waitUntilReady().then(() => {
				off();

				this.#query = window.matchMedia(`(${this.#queryString})`);

				off = on(this.#query!, 'change', update);

				update();
			});

			// stop listening when all the effects are destroyed
			return () => off();
		});
	}
}
