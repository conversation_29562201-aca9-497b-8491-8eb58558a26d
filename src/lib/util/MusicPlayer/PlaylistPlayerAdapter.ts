import type { PlaylistPlayer } from './PlaylistPlayer.svelte';
import type { MusicPlayerAdapter } from './MusicPlayerAdapter';

export class PlaylistPlayerAdapter implements MusicPlayerAdapter {
	private _player: PlaylistPlayer;

	constructor(player: PlaylistPlayer) {
		this._player = player;
	}

	play() {
		this._player.play();
	}

	pause() {
		this._player.pause();
	}

	stop() {
		this._player.pause();
	}

	next() {
		this._player.next();
	}

	previous() {
		this._player.previous();
	}

	resetVolume() {
		this._player.resetVolume();
	}

	restore() {
		// Ignore
	}

	dispose() {
		this._player.dispose();
	}

	get volume() {
		return this._player.volume;
	}

	set volume(newVolume: number) {
		this._player.volume = newVolume;
	}

	get isPlaying() {
		return this._player.isPlaying;
	}

	get isReady() {
		return true;
	}

	get currentSong() {
		return this._player.currentSong;
	}

	get muted() {
		return this._player.muted;
	}

	set muted(muted: boolean) {
		this._player.muted = muted;
	}
}
