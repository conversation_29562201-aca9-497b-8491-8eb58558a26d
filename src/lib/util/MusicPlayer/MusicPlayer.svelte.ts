import { browser } from '$app/environment';
import { canUseHTML5Audio } from '$lib/functions/canUseHTML5Audio';
import type { MusicPlayerAdapter } from './MusicPlayerAdapter';

export interface MusicPlayerOptions {
	playlistAdapter: MusicPlayerAdapter;
	youtubeAdapter: MusicPlayerAdapter;
}

type Player = 'playlist' | 'youtube';

const mutedStorageKey = 'music-muted';
const playerStorageKey = 'music-player';

export class MusicPlayer implements MusicPlayerAdapter {
	private _playlistAdapter: MusicPlayerAdapter;
	private _youtubeAdapter: MusicPlayerAdapter;
	private _currentAdapter = $state() as MusicPlayerAdapter;
	private _muted = $state(false);

	constructor(options: MusicPlayerOptions) {
		this._playlistAdapter = options.playlistAdapter;
		this._youtubeAdapter = options.youtubeAdapter;
		this._currentAdapter = this._playlistAdapter;
		this._loadSettings();

		if (browser && 'mediaSession' in navigator && canUseHTML5Audio()) {
			navigator.mediaSession.metadata = new MediaMetadata();

			navigator.mediaSession.setActionHandler('previoustrack', () => {
				this.previous();
			});
			navigator.mediaSession.setActionHandler('nexttrack', () => {
				this.next();
			});
			navigator.mediaSession.setActionHandler('play', () => {
				this.play();
			});
			navigator.mediaSession.setActionHandler('pause', () => {
				this.pause();
			});
			// navigator.mediaSession.setActionHandler('stop', () => {
			// 	this.pause();
			// });
			// navigator.mediaSession.setActionHandler('seekbackward', () => {
			// 	this.previous();
			// });
			// navigator.mediaSession.setActionHandler('seekforward', () => {
			// 	this.next();
			// });
			// navigator.mediaSession.setActionHandler('seekto', () => {
			// 	this.next();
			// });
			// navigator.mediaSession.setActionHandler('skipad', () => {
			// 	this.next();
			// });
		}
	}

	get player() {
		return this._currentAdapter === this._playlistAdapter ? 'playlist' : 'youtube';
	}

	set player(player: Player) {
		if (this.player !== player) {
			this._currentAdapter.dispose();
		}

		if (player === 'playlist') {
			this._youtubeAdapter.pause();
			this._playlistAdapter.restore();
			this._playlistAdapter.play();
			this._currentAdapter = this._playlistAdapter;
		} else {
			this._playlistAdapter.pause();
			this._youtubeAdapter.restore();
			this._youtubeAdapter.play();
			this._currentAdapter = this._youtubeAdapter;
		}

		this._updateMediaSession();

		getStorage()?.setItem(playerStorageKey, player);
	}

	togglePlayer() {
		if (this._currentAdapter === this._playlistAdapter) {
			this.player = 'youtube';
		} else {
			this.player = 'playlist';
		}
	}

	togglePlay() {
		if (this.isPlaying) {
			this.pause();
		} else {
			this.play();
		}
	}

	get volume() {
		return this._currentAdapter.volume;
	}

	set volume(newVolume: number) {
		this._currentAdapter.volume = newVolume;
	}

	get muted() {
		return this._muted;
	}

	set muted(muted: boolean) {
		this._muted = muted;

		getStorage()?.setItem(mutedStorageKey, `${muted}`);

		this._playlistAdapter.muted = muted;
		this._youtubeAdapter.muted = muted;

		if (muted) {
			this.stop();
		}
	}

	get unmuted() {
		return !this.muted;
	}

	set unmuted(unmuted: boolean) {
		this.muted = !unmuted;
	}

	get isPlaying() {
		return this._currentAdapter.isPlaying;
	}

	get currentSong() {
		return this._currentAdapter.currentSong;
	}

	get isReady() {
		return this._currentAdapter.isReady;
	}

	play() {
		if (this._muted) {
			return;
		}

		this._currentAdapter.play();
		this._updateMediaSession();
	}

	pause() {
		this._currentAdapter.pause();
		this._updateMediaSession();
	}

	stop() {
		this._currentAdapter.stop();
		this._updateMediaSession();
	}

	next() {
		this._currentAdapter.next();
		this._updateMediaSession();
	}

	previous() {
		this._currentAdapter.previous();
		this._updateMediaSession();
	}

	resetVolume() {
		this._currentAdapter.resetVolume();
	}

	restore() {
		this._currentAdapter.restore();
	}

	dispose() {
		this._currentAdapter.dispose();
	}

	private _updateMediaSession() {
		if (
			!browser ||
			!('mediaSession' in navigator) ||
			!this.currentSong ||
			!navigator.mediaSession.metadata ||
			canUseHTML5Audio()
		) {
			return;
		}

		const creators = this.currentSong.attribution?.creator
			? (Array.isArray(this.currentSong.attribution.creator)
					? this.currentSong.attribution.creator
					: [this.currentSong.attribution.creator]
				).filter(Boolean)
			: [];

		navigator.mediaSession.metadata.title = this.currentSong.attribution.work.name;
		navigator.mediaSession.metadata.artist = creators.map((creator) => creator.name).join(', ');
		navigator.mediaSession.metadata.album = this.currentSong.attribution.album.name ?? '';
		navigator.mediaSession.metadata.artwork = [
			{
				src: this.currentSong.attribution.album.picture,
				sizes: '96x96',
				type: 'image/png',
			},
			{
				src: this.currentSong.attribution.album.picture,
				sizes: '128x128',
				type: 'image/png',
			},
		];
	}

	private _loadSettings() {
		const storageValue = getStorage()?.getItem(mutedStorageKey);
		const booleanValue = storageValue === 'true';

		this.muted = booleanValue;

		if (getStorage()?.getItem(playerStorageKey) === 'youtube') {
			this._currentAdapter = this._youtubeAdapter;
		}
	}
}

const getStorage = () => {
	if (browser) {
		return localStorage;
	}

	return null;
};
