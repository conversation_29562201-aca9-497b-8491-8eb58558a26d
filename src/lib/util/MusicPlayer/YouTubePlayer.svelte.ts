import { browser } from '$app/environment';
import type { YouTubeSong } from '$lib/data/youtubePlaylist';
import { siteSounds } from '$lib/stores/siteSounds.svelte';

export enum PlayerState {
	UNSTARTED = -1,
	ENDED = 0,
	PLAYING = 1,
	PAUSED = 2,
	BUFFERING = 3,
	CUED = 5,
}

const volumeStorageKey = 'youtube-volume';

export class YouTubePlayer {
	private _player: YT.Player | null = $state(null);
	private _playlist: YouTubeSong[] = [];
	private _currentSongIndex = $state(0);
	private _isReady = $state(false);
	private _playerState = $state(-1);
	private _failedToLoad = $state(false);
	private _volume = $state(100);
	private _youTubeIframeAPIReady = false;
	private _inited = false;
	private _playWhenReady = false;
	private _muted = $state(false);

	constructor(playlist: YouTubeSong[]) {
		this._playlist = playlist;
		this._loadSettings();
	}

	get muted() {
		return this._muted;
	}

	set muted(muted: boolean) {
		this._muted = muted;

		if (muted) {
			this.stop();
		}
	}

	get isReady() {
		return this._isReady;
	}

	get player() {
		return this._player;
	}

	set player(player: YT.Player | null) {
		this._player = player;
	}

	get playlist() {
		return this._playlist;
	}

	get currentSong(): YouTubeSong | undefined {
		return this._playlist[this.currentSongIndex];
	}

	get currentSongIndex() {
		return this._currentSongIndex;
	}

	set currentSongIndex(index: number) {
		if (index < 0) {
			index = this._playlist.length - 1;
		} else if (index >= this._playlist.length) {
			index = 0;
		}
		this._currentSongIndex = index;
	}

	get isPlaying() {
		return this._playerState === PlayerState.PLAYING;
	}

	get isPaused() {
		return this._playerState === PlayerState.PAUSED;
	}

	get isStopped() {
		return this._playerState === PlayerState.ENDED;
	}

	get isBuffering() {
		return this._playerState === PlayerState.BUFFERING;
	}

	get isCued() {
		return this._playerState === PlayerState.CUED;
	}

	get isUnstarted() {
		return this._playerState === PlayerState.UNSTARTED;
	}

	get volume() {
		return this._volume;
	}

	set volume(volume: number) {
		this._volume = volume;
		this._player?.setVolume?.(volume);
		siteSounds.changeRadioStation.volume(volume / 100);
		getStorage()?.setItem(volumeStorageKey, `${volume}`);
	}

	get failedToLoad() {
		return this._failedToLoad;
	}

	private _createPlayer() {
		if (this._player) {
			return;
		}

		this._player = new YT.Player('player', {
			height: '200',
			width: '200',
			videoId: this.currentSong?.id,
			playerVars: {
				enablejsapi: 1,
				playsinline: 1,
				disablekb: 1,
				fs: 0,
				controls: 0,
				origin: location.origin,
				rel: 0,
				showinfo: 0,
			},
			events: {
				onReady: () => {
					this._isReady = true;
					this._player?.setVolume?.(this._volume);

					if (this._playWhenReady) {
						this.play();
					}

					this._playWhenReady = false;
				},
				onStateChange: (event) => {
					this._playerState = event.data;

					if (event.data === PlayerState.ENDED) {
						this.next();
					}
				},
				onError: () => {
					this._failedToLoad = true;
					this.next();
				},
			},
		});
	}

	private _init() {
		if (!browser || this._player || this._youTubeIframeAPIReady || this._inited || this.muted) {
			return;
		}

		(window as any).onYouTubeIframeAPIReady = () => {
			this._youTubeIframeAPIReady = true;
			this._createPlayer();
		};

		const container = document.createElement('div');
		container.className = 'fixed -top-[9999px] -left-[9999px]';
		document.body.append(container);

		const playerContainer = document.createElement('div');
		playerContainer.id = 'player';
		container.append(playerContainer);

		const tag = document.createElement('script');
		tag.src = 'https://www.youtube.com/iframe_api';

		document.body.append(tag);

		this._inited = true;
	}

	play() {
		if (this.muted) {
			return;
		}

		if (!this._inited) {
			this._init();
		}

		if (!this.isReady) {
			this._playWhenReady = true;
			return;
		}

		if (this._playerState === PlayerState.UNSTARTED) {
			this.load();
		}

		this._player?.playVideo?.();
	}

	pause() {
		this._playWhenReady = false;
		this._player?.pauseVideo?.();
	}

	stop() {
		this._playWhenReady = false;
		this._player?.stopVideo?.();
	}

	private loadSong(song: string) {
		if (!this._isReady) {
			return;
		}

		this._player?.loadVideoById?.(song);
	}

	load() {
		this.loadSong(this._playlist[this.currentSongIndex].id);
	}

	next() {
		if (this.muted) {
			return;
		}

		siteSounds.changeRadioStation.play();

		setTimeout(() => {
			this.currentSongIndex += 1;
			this.loadSong(this._playlist[this.currentSongIndex].id);
		}, 10);
	}

	previous() {
		if (this.muted) {
			return;
		}

		siteSounds.changeRadioStation.play();

		setTimeout(() => {
			this.currentSongIndex -= 1;
			this.loadSong(this._playlist[this.currentSongIndex].id);
		}, 10);
	}

	restore() {
		if (this._youTubeIframeAPIReady) {
			this._createPlayer();
		} else {
			this._init();
		}
	}

	dispose() {
		this._player?.destroy?.();
		this._player = null;
		this._isReady = false;
		this._playerState = -1;
	}

	private _loadSettings() {
		this.volume = +(getStorage()?.getItem(volumeStorageKey) ?? 100);
	}
}

const getStorage = () => {
	if (browser) {
		return localStorage;
	}

	return null;
};
