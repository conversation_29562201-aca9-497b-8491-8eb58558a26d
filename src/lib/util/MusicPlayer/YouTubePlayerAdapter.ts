import type { YouTubePlayer } from './YouTubePlayer.svelte';
import type { MusicPlayerAdapter } from './MusicPlayerAdapter';

export class YouTubePlayerAdapter implements MusicPlayerAdapter {
	private _player: YouTubePlayer;

	constructor(player: YouTubePlayer) {
		this._player = player;
	}

	play() {
		this._player.play();
	}

	pause() {
		this._player.pause();
	}

	stop() {
		this._player.stop();
	}

	next() {
		this._player.next();
	}

	previous() {
		this._player.previous();
	}

	resetVolume() {
		this._player.volume = 100;
	}

	restore() {
		this._player.restore();
	}

	dispose() {
		this._player.dispose();
	}

	get volume() {
		return this._player.volume / 100;
	}

	set volume(newVolume: number) {
		this._player.volume = newVolume * 100;

		if (newVolume === 0) {
			this.pause();
		}
	}

	get isPlaying() {
		return this._player.isPlaying;
	}

	get isReady() {
		return this._player.isReady;
	}

	get currentSong() {
		return this._player.currentSong;
	}

	get muted() {
		return this._player.muted;
	}

	set muted(muted: boolean) {
		this._player.muted = muted;
	}
}
