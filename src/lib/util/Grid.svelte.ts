import { clone2DGrid } from '$lib/functions/clone2DGrid';
import { get2DGrid } from '$lib/functions/get2DGrid';
import { getAllGridItemsOnDirection } from '$lib/functions/getAllGridItemsOnDirection';
import { getGridItemsAround, type GridItemsAroundOptions } from '$lib/functions/getGridItemsAround';
import {
	getNextGridItemOnDirection,
	type AnyDirection,
} from '$lib/functions/getNextGridItemOnDirection';
import { isGridItemAround } from '$lib/functions/isGridItemAround';
import type { GridItem } from '$lib/models/GridItem';
import type { GridSize } from '$lib/models/GridSize';

interface Props extends GridSize {
	reactive?: boolean;
}

export class Grid<T> {
	rows: number;
	columns: number;
	private _grid: Array<Array<T | null>> | null = null;
	private _reactiveGrid = $state(null) as Array<Array<T | null>> | null;

	constructor(
		{ rows, columns, reactive = true }: Props,
		fill?: (row: number, column: number) => T,
	) {
		this.rows = rows;
		this.columns = columns;

		if (reactive) {
			this._reactiveGrid = get2DGrid(rows, columns, fill);
		} else {
			this._grid = get2DGrid(rows, columns, fill);
		}
	}

	get isReactive() {
		return !this._grid;
	}

	get grid() {
		return this._grid ?? (this._reactiveGrid as Array<Array<T | null>>);
	}

	set grid(newGrid: Array<Array<T | null>>) {
		if (this.isReactive) {
			this._reactiveGrid = newGrid;
		} else {
			this._grid = newGrid;
		}
	}

	get size(): GridSize {
		return this;
	}

	print(properties?: string[]) {
		console.table($state.snapshot(this.grid), properties);
	}

	clone(transform: (item: T | null) => T | null = (_) => _): Grid<T> {
		const grid = clone2DGrid(this.grid, transform);
		const instance = new Grid<T>({ rows: 0, columns: 0 });

		if (this.isReactive) {
			instance._reactiveGrid = grid;
		} else {
			instance._grid = grid;
		}

		instance.rows = grid.length ?? 0;
		instance.columns = grid[0]?.length ?? 0;

		return instance;
	}

	getItemsAround(gridItem: GridItem, options?: GridItemsAroundOptions) {
		return getGridItemsAround(gridItem, this.size, options);
	}

	getItemValuesAround(gridItem: GridItem, options?: GridItemsAroundOptions) {
		return getGridItemsAround(gridItem, this.size, options).map((item) => this.at(item)!);
	}

	isItemAround(item1: GridItem, item2: GridItem, options?: GridItemsAroundOptions) {
		return isGridItemAround(item1, item2, options);
	}

	getNextItemOnDirection(gridItem: GridItem, direction: AnyDirection) {
		return getNextGridItemOnDirection(gridItem, direction, {
			row: this.rows,
			column: this.columns,
		});
	}

	getAllGridItemsOnDirection(
		gridItem: GridItem,
		direction: AnyDirection,
		until: (untilProps: {
			item: T | null;
			position: GridItem;
			distance: number;
			previousItem: T | null;
			previousPosition: GridItem | null;
		}) => boolean,
	) {
		return getAllGridItemsOnDirection(
			gridItem,
			direction,
			{
				row: this.rows,
				column: this.columns,
			},
			({ gridItem: position, distance, previousGridItem: previousPosition }) => {
				return until({
					item: this.at(position),
					position,
					distance,
					previousItem: previousPosition ? this.at(previousPosition) : null,
					previousPosition: previousPosition,
				});
			},
		);
	}

	isOutOfBounds(gridItem?: Partial<GridItem>): boolean {
		if (!gridItem) {
			return true;
		}

		let isOutOfBounds = false;

		if (gridItem.row !== undefined) {
			isOutOfBounds = isOutOfBounds || gridItem.row >= this.size.rows || gridItem.row < 0;
		}

		if (gridItem.column !== undefined) {
			isOutOfBounds =
				isOutOfBounds || gridItem.column >= this.size.columns || gridItem.column < 0;
		}

		return isOutOfBounds;
	}

	at(gridItem: GridItem): T | null {
		if (this.isOutOfBounds(gridItem)) {
			return null;
		}

		return this.grid[gridItem.row][gridItem.column];
	}

	set(gridItem: GridItem, value: T | null): boolean {
		if (this.isOutOfBounds(gridItem)) {
			return false;
		}

		this.grid[gridItem.row][gridItem.column] = value;
		return true;
	}

	random(randomFunc = Math.random): { value: T | null; at: GridItem } {
		const at = {
			row: Math.floor(randomFunc() * this.rows),
			column: Math.floor(randomFunc() * this.columns),
		};

		return {
			value: this.at(at),
			at,
		};
	}

	reset(value?: () => T | null) {
		this.grid.forEach((rowItem) => {
			rowItem.forEach((_, column) => {
				rowItem[column] = value ? value() : null;
			});
		});
	}

	find(predicate: (value: T | null, gridItem: GridItem) => boolean): {
		gridItem: GridItem | null;
		item: T | null;
	} {
		let position: GridItem | null = null;
		let itemOnPosition: T | null = null;

		this.grid.find((rowItem, row) => {
			return rowItem.find((item, column) => {
				if (predicate(item, { row, column })) {
					position = { row, column };
					itemOnPosition = item;
					return true;
				}
			});
		});

		return { gridItem: position, item: itemOnPosition };
	}

	// Get all items on a single array
	flatten(): Array<T | null> {
		const items: Array<T | null> = [];

		this.grid.forEach((row) => {
			row.forEach((item) => {
				items.push(item);
			});
		});

		return items;
	}

	getAllItems(): GridItem[] {
		const items: GridItem[] = [];
		for (let row = 0; row < this.rows; row++) {
			for (let column = 0; column < this.columns; column++) {
				items.push({ row, column });
			}
		}
		return items;
	}
}
