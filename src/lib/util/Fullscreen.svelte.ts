export class Fullscreen {
	#isOnFullscreen = $state(false);
	#element: HTMLElement;

	constructor({ element }: { element: HTMLElement }) {
		this.#element = element;
	}

	get isOnFullscreen() {
		return this.#isOnFullscreen;
	}

	hasSupport() {
		if (typeof document === 'undefined') {
			return false;
		}

		return document.fullscreenEnabled;
	}

	toggle() {
		if (this.isOnFullscreen) {
			this.exit();
		} else {
			this.request();
		}
	}

	async request() {
		if (!this.hasSupport()) {
			return;
		}

		await this.#element.requestFullscreen();
		this.#isOnFullscreen = true;

		const controller = new AbortController();

		this.#element.addEventListener(
			'fullscreenchange',
			() => {
				if (document.fullscreenElement !== this.#element) {
					this.#isOnFullscreen = false;
					controller.abort();
				}
			},
			controller,
		);
	}

	exit() {
		if (!this.hasSupport()) {
			return;
		}

		if (!this.isOnFullscreen) {
			return;
		}

		document.exitFullscreen();
		this.#isOnFullscreen = false;
	}
}
