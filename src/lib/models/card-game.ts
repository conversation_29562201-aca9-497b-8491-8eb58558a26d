export type CardSuit = 'heart' | 'diamond' | 'club' | 'spade';

export type CardValue = 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 'A' | 'J' | 'Q' | 'K';

export type CardFace = 'up' | 'down';

export type Card = {
	suit: CardSuit;
	value: CardValue;
	face: CardFace;
};

export const suits: CardSuit[] = ['club', 'diamond', 'heart', 'spade'];

export const values: CardValue[] = [2, 3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K', 'A'];

export type SuitColor = 'red' | 'black';

export const suitToColor: Record<CardSuit, SuitColor> = {
	club: 'black',
	diamond: 'red',
	heart: 'red',
	spade: 'black',
};

export const cardValueNameMap: Record<CardValue, string> = {
	A: 'Ace',
	'2': 'Two',
	'3': 'Three',
	'4': 'Four',
	'5': 'Five',
	'6': 'Six',
	'7': 'Seven',
	'8': 'Eight',
	'9': 'Nine',
	'10': 'Ten',
	J: 'Jack',
	Q: 'Queen',
	K: 'King',
};

export const cardSuitNameMap: Record<CardSuit, string> = {
	club: 'Club',
	diamond: 'Diamond',
	heart: 'Heart',
	spade: 'Spade',
};

export const cardSuitNamePluralMap: Record<CardSuit, string> = {
	club: 'Clubs',
	diamond: 'Diamonds',
	heart: 'Hearts',
	spade: 'Spades',
};

export function getCardName(card: Omit<Card, 'face'>) {
	return `${cardValueNameMap[card.value]} of ${cardSuitNamePluralMap[card.suit]}`;
}
