import type { GridItem } from '$lib/models/GridItem';
import { getNextGridItemOnDirection, type AnyDirection } from './getNextGridItemOnDirection';

export const getAllGridItemsOnDirection = (
	position: GridItem,
	direction: AnyDirection,
	gridSize: GridItem,
	until: (untilProps: {
		gridItem: GridItem;
		distance: number;
		previousGridItem: GridItem | null;
	}) => boolean,
): GridItem[] => {
	let canKeepMovingOnDirection = true;
	let nextGridItem: GridItem | null = position;
	const positions: GridItem[] = [];
	let distance = 0;
	let previousGridItem: GridItem | null = null;

	do {
		nextGridItem = getNextGridItemOnDirection(nextGridItem!, direction, gridSize);
		distance += 1;

		if (nextGridItem) {
			if (until({ gridItem: nextGridItem, distance, previousGridItem })) {
				positions.push(nextGridItem);
			} else {
				canKeepMovingOnDirection = false;
			}
		} else {
			canKeepMovingOnDirection = false;
		}

		previousGridItem = nextGridItem;
	} while (canKeepMovingOnDirection);

	return positions;
};
