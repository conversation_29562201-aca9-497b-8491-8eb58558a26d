<script lang="ts">
	import { getTimeUnits } from '$lib/functions/date/getTimeUnits';
	import { cn } from '$lib/util/cn';
	import NumberFlow, { type Trend } from '@number-flow/svelte';

	interface Props {
		time: number;
		class?: string;
		animated?: boolean;
		trend?: Trend;
		variant?: 'number-flow' | 'static';
	}

	let {
		time,
		class: classFromProps = '',
		animated,
		variant = 'number-flow',
		trend,
	}: Props = $props();

	let years = $state(0);
	let days = $state(0);
	let hours = $state(0);
	let minutes = $state(0);
	let seconds = $state(0);

	let firstNumber = $state(0);
	let secondNumber = $state(0);
	let firstUnit = $state(':');
	let secondUnit = $state('');
	let firstMinimumIntegerDigits = $state(2);
	let secondMinimumIntegerDigits = $state(2);

	let firstNumberFormat = $derived(
		Intl.NumberFormat(undefined, {
			minimumIntegerDigits: firstMinimumIntegerDigits,
		}),
	);

	let secondNumberFormat = $derived(
		Intl.NumberFormat(undefined, {
			minimumIntegerDigits: secondMinimumIntegerDigits,
		}),
	);

	function update() {
		const units = getTimeUnits(time);

		hours = units.hours;
		minutes = units.minutes;
		seconds = units.seconds;
		days = Math.floor(hours / 24);
		years = Math.floor(days / 365);

		if (years > 0) {
			const remainingDays = days % 365;

			firstNumber = years;
			firstUnit = 'y ';
			firstMinimumIntegerDigits = 1;
			secondNumber = remainingDays;
			secondUnit = 'd';
			secondMinimumIntegerDigits = 1;
		} else if (days > 0) {
			const remainingHours = hours % 24;

			firstNumber = days;
			firstUnit = 'd ';
			firstMinimumIntegerDigits = 1;
			secondNumber = remainingHours;
			secondUnit = 'h';
			secondMinimumIntegerDigits = 1;
		} else if (hours > 0) {
			firstNumber = hours;
			firstUnit = 'h ';
			firstMinimumIntegerDigits = 1;
			secondNumber = minutes;
			secondUnit = 'm';
			secondMinimumIntegerDigits = 1;
		} else {
			firstNumber = minutes;
			firstUnit = ':';
			firstMinimumIntegerDigits = 2;
			secondNumber = seconds;
			secondUnit = '';
			secondMinimumIntegerDigits = 2;
		}
	}

	$effect(() => {
		update();
	});
</script>

<div
	class={cn(
		'flex gap-0 items-center justify-center [font-variant-numeric:tabular-nums]',
		classFromProps,
	)}
>
	{#if variant === 'number-flow'}
		<NumberFlow
			value={firstNumber}
			format={{
				minimumIntegerDigits: firstMinimumIntegerDigits,
			}}
			{animated}
			{trend}
		/>
		<span>{firstUnit}</span>
		<NumberFlow
			value={secondNumber}
			format={{
				minimumIntegerDigits: secondMinimumIntegerDigits,
			}}
			{animated}
			{trend}
		/>
		<span>{secondUnit}</span>
	{/if}

	<span
		class={cn({
			'sr-only': variant === 'number-flow',
		})}
	>
		{firstNumberFormat.format(firstNumber)}{firstUnit}{secondNumberFormat.format(
			secondNumber,
		)}{secondUnit}
	</span>
</div>
