<script lang="ts">
	import { siteSounds } from '$lib/stores/siteSounds.svelte';
	import type { Snippet } from 'svelte';
	import type { ChangeEventHandler } from 'svelte/elements';
	import { twMerge } from 'tailwind-merge';

	interface Props {
		class?: string;
		checked: boolean;
		disabled?: boolean;
		'aria-label': string;
		onchange?: ChangeEventHandler<HTMLInputElement>;
		children?: Snippet;
		description?: Snippet;
	}

	let {
		class: classFromProps = '',
		checked = $bindable(),
		disabled = false,
		'aria-label': ariaLabel,
		onchange = () => {},
		children,
		description,
	}: Props = $props();
</script>

<label
	class={twMerge('label cursor-pointer py-2 gap-2 flex-col items-start w-full', classFromProps)}
>
	<div class="flex gap-4 justify-between items-center w-full">
		<span class="text-sm text-base-content">{@render children?.()}</span>
		<input
			type="checkbox"
			class="toggle"
			aria-label={ariaLabel}
			bind:checked
			onchange={(e) => {
				onchange?.(e);
				if (checked) {
					siteSounds.toggleOn.play();
				} else {
					siteSounds.toggleOff.play();
				}
			}}
			{disabled}
		/>
	</div>

	{#if description}
		<span class="text-sm text-wrap">
			{@render description?.()}
		</span>
	{/if}
</label>
