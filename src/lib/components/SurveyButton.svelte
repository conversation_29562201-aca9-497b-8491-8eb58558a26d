<script lang="ts">
	import CloseIcon from './Icons/CloseIcon.svelte';
	import { supabase } from '$lib/api/supabase';
	import { browser } from '$app/environment';
	import { onMount } from 'svelte';

	let classFromProps = '';
	export { classFromProps as class };

	let dialog: HTMLDialogElement;
	let sendState: 'sending' | 'error' | 'success' | 'none' = 'none';
	let form: HTMLFormElement;
	let hasSentSurveyKey = 'has-sent-2025-survey';
	let hasSentSurvey = true;

	const fields = [
		{
			groupName: 'New Games',
			options: [
				'3D games',
				'Board games',
				'Card games',
				'Classic games',
				'Drawing games',
				'Fighting games',
				'Music games',
				'Physics games',
				'Platform games',
				'Puzzle games',
				'Racing games',
				'Shooter games',
				'Solitaire variants',
				'Tower defense games',
				'Other (please leave a comment)',
			],
		},
		{
			groupName: 'Game features',
			options: [
				'Achievements',
				'Customizations',
				'Leaderboards',
				'More daily challenges',
				'More game statistics',
				'Play game where you left off',
				'Share exact same game using a link',
			],
		},
		{
			groupName: 'Site',
			options: [
				'Account (save stats, settings, stats, daily games, etc.)',
				'Add game to favorites for quick access',
				'Global settings (hide daily games, hide all timers, hide dynamic island, hide notifications, etc.)',
				'More themes (light, dark, etc.)',
				'More images on the home page',
				'Productivity tools on home page (pomodoro, todo list, notes, etc.)',
			],
		},
		{
			groupName: 'Music and sounds',
			options: [
				'More lofi music',
				'More music genres (classic, piano, synthwave, etc.)',
				'More game sound effects',
				'White noise sounds (rain, forest, beach, etc.)',
			],
		},
	];

	async function send(event: Event) {
		event.preventDefault();
		const formData = new FormData(form);
		const survey: Record<string, string | boolean> = {};
		formData.forEach((value, key) => {
			if (value !== '') {
				survey[key] = value === 'on' ? true : value.toString();
			}
		});

		if (!Object.keys(survey).length) {
			return;
		}

		if (['sending', 'success'].includes(sendState)) {
			return;
		}

		sendState = 'sending';

		try {
			await supabase.from('2025_survey').insert({ survey }).throwOnError();
			sendState = 'success';
			localStorage.setItem(hasSentSurveyKey, 'true');
			hasSentSurvey = true;
		} catch (_error) {
			sendState = 'error';
		}
	}

	function handleStorageChange(event: StorageEvent) {
		if (event.key === hasSentSurveyKey) {
			hasSentSurvey = event.newValue === 'true';
		}
	}

	onMount(() => {
		if (browser) {
			hasSentSurvey = localStorage.getItem(hasSentSurveyKey) === 'true';
		}
	});
</script>

<svelte:window on:storage={handleStorageChange} />

{#if !hasSentSurvey}
	<button
		onclick={() => dialog?.showModal()}
		class="btn btn-primary btn-block btn-md {classFromProps}"
	>
		2025 Survey
	</button>
	<!-- {:else} -->
	<!-- <p class="text-center font-bold text-sm">Thanks for your participation!</p> -->
{/if}

<dialog bind:this={dialog} class="modal modal-bottom sm:modal-middle z-30">
	<form bind:this={form} onsubmit={send} class="modal-box text-center">
		<button
			class="btn btn-sm btn-circle absolute top-2 right-2"
			onclick={() => dialog?.close()}
		>
			<CloseIcon />
		</button>

		{#if sendState !== 'success'}
			<div>
				<h3 class="text-3xl mt-8 mb-2 font-bold text-center">
					What would you like to see next?
				</h3>
				<p class="text-lg text-center mb-8">Help us build the best experience for you</p>

				<div class="form-control my-4 mx-auto">
					{#each fields as field}
						<h4 class="text-start my-4">{field.groupName}</h4>

						{#each field.options as option}
							<label
								class="label cursor-pointer justify-start flex gap-4 items-center"
							>
								<input
									type="checkbox"
									class="checkbox checkbox-primary"
									name={option}
								/>
								<span class="label-text text-start">{option}</span>
							</label>
						{/each}
					{/each}

					<label class="label mt-4" for="comments">
						<span class="label-text">Additional Comments</span>
					</label>
					<textarea
						onkeydown={(e) => e.stopPropagation()}
						id="comments"
						name="comments"
						placeholder="Share your thoughts and ideas with us!"
						class="textarea textarea-bordered"
					></textarea>

					{#if sendState === 'error'}
						<p class="mt-4 text-error">There was an error. Please, try again later.</p>
					{/if}
				</div>

				<div class="modal-action">
					<button
						disabled={sendState === 'sending'}
						class="btn btn-ghost"
						onclick={() => dialog?.close()}>Not now</button
					>

					<button
						type="submit"
						class:loading={sendState === 'sending'}
						class="btn btn-primary"
					>
						Send
					</button>
				</div>
			</div>
		{/if}

		{#if sendState === 'success'}
			<div>
				<p class="text-3xl mt-8 mb-2 font-bold text-center">Thank you!</p>
				<p class="text-center">Your opinion is very important</p>
				<p class="my-8 text-5xl">🥳</p>

				<div class="card-actions justify-center">
					<button class="btn btn-primary" onclick={() => dialog?.close()}>Awesome</button>
				</div>
			</div>
		{/if}
	</form>
</dialog>
