<script lang="ts">
	import type { Attribution } from '../../models/Attribution';

	interface Props {
		attributions: Attribution[];
	}

	let { attributions }: Props = $props();
</script>

<ul>
	{#each attributions as attr}
		<li>
			<a href={attr.work.url} target="_blank" rel="noreferrer noopener">"{attr.work.name}"</a>
			{#if attr.creator}
				by
				{@const creators = (
					Array.isArray(attr.creator) ? attr.creator : [attr.creator]
				).filter(Boolean)}
				{#each creators as creator, index}
					<a href={creator.url} target="_blank" rel="noreferrer noopener">
						{creator.name}{#if index !== creators.length - 1 && creators.length > 1},&nbsp;
						{/if}
					</a>
				{/each}
			{/if}
			{#if attr.license}
				used under
				<a href={attr.license.url} target="_blank" rel="noreferrer noopener"
					>{attr.license.name}</a
				>
			{/if}
			{#if attr.modification}
				/ {attr.modification}
			{/if}
		</li>
	{/each}
</ul>
