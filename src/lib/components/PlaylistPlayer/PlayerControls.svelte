<script lang="ts">
	import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
	import PlayIcon from '$lib/components/Icons/PlayIcon.svelte';
	import SkipPreviousIcon from '$lib/components/Icons/SkipPreviousIcon.svelte';
	import SkipNextIcon from '$lib/components/Icons/SkipNextIcon.svelte';
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';

	interface Props {
		class?: string;
		unmuteOnInteraction?: boolean;
		before?: Snippet;
		after?: Snippet;
	}

	let {
		class: classFromProps = '',
		unmuteOnInteraction = false,
		before,
		after,
	}: Props = $props();

	const unmuteIfNeeded = () => {
		if (unmuteOnInteraction) {
			musicPlayer.muted = false;
		}
	};

	const previous = () => {
		unmuteIfNeeded();
		musicPlayer.previous();
	};

	const togglePlay = () => {
		unmuteIfNeeded();
		musicPlayer.togglePlay();
	};

	const next = () => {
		unmuteIfNeeded();
		musicPlayer.next();
	};
</script>

<div class={cn('flex justify-center gap-2 py-2 px-1', classFromProps)}>
	{@render before?.()}

	<button aria-label="Previous song" class="btn btn-ghost btn-circle" onclick={previous}>
		<SkipPreviousIcon />
	</button>

	<button aria-label="Play / Pause song" class="btn btn-ghost btn-circle" onclick={togglePlay}>
		<PlayIcon variant={musicPlayer?.isPlaying ? 'pause' : 'play'} />
	</button>

	<button aria-label="Next song" class="btn btn-ghost btn-circle" onclick={next}>
		<SkipNextIcon />
	</button>

	{@render after?.()}
</div>
