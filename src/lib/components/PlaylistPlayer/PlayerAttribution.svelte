<script lang="ts">
	import type { Attribution, AttributionResource } from '$lib/models/Attribution';
	import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
	import { siteSounds } from '$lib/stores/siteSounds.svelte';
	import MusicOptionsButton from './MusicOptionsButton.svelte';

	interface Props {
		class?: string;
		attribution: Attribution | null | undefined;
		noPlaylistButton?: boolean;
	}

	let { class: classFromProps = '', attribution, noPlaylistButton = false }: Props = $props();

	let creators = $derived(
		attribution?.creator
			? (Array.isArray(attribution.creator)
					? attribution.creator
					: [attribution.creator]
				).filter(Boolean)
			: [],
	) as AttributionResource[];
</script>

<div class="flex w-full flex-col {classFromProps}">
	<a
		class="text-lg font-semibold leading-5 line-clamp-3"
		target="_blank"
		rel="noreferrer noopener"
		href={attribution?.work.url}
	>
		{siteSounds.changeRadioStation.playing()
			? 'Tuning to lofi radio station...'
			: (attribution?.work.name ?? 'No music')}
	</a>
	{#if siteSounds.changeRadioStation.playing()}
		<span class="text-sm font-normal line-clamp-2">...</span>
	{:else if creators.length === 0}
		<span class="text-sm font-normal line-clamp-2">No artist</span>
	{:else}
		<div>
			{#each creators as creator, index}
				<a
					class="text-sm font-normal line-clamp-2 inline"
					target="_blank"
					rel="noreferrer noopener"
					href={creator.url}
				>
					{creator.name}{#if index !== creators.length - 1 && creators.length > 1},&nbsp;
					{/if}
				</a>
			{/each}
		</div>
	{/if}

	{#if musicPlayer.player === 'playlist'}
		<div class="flex w-full gap-1 justify-between items-center">
			<a
				class="text-xs font-light line-clamp-1"
				target="_blank"
				rel="noreferrer noopener"
				href={attribution?.license?.url}
			>
				{attribution?.license?.name ?? ''}
			</a>

			{#if !noPlaylistButton}
				<MusicOptionsButton />
			{/if}
		</div>
	{/if}
</div>
