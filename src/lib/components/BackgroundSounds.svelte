<script lang="ts">
	import { backgroundSoundInstances } from '$lib/stores/backgroundSoundManager.svelte';
	import { cn } from '$lib/util/cn';
	import { fly } from 'svelte/transition';
	import Arc from './Arc.svelte';

	interface Props {
		volumeSliderVariant?: 'glass' | 'opaque';
	}

	let { volumeSliderVariant = 'glass' }: Props = $props();

	let isShowingVolumeFor = $state('');
	let canClearIsShowingVolumeFor = $state(false);
	let timeoutId = -1;
	const clearAfterSeconds = 2_000;

	$effect(function clearIsShowingVolumeFor() {
		if (isShowingVolumeFor && canClearIsShowingVolumeFor) {
			timeoutId = setTimeout(() => {
				isShowingVolumeFor = '';
				canClearIsShowingVolumeFor = false;
				timeoutId = -1;
			}, clearAfterSeconds) as any as number;
		}

		return () => {
			clearTimeout(timeoutId);
		};
	});
</script>

<svelte:window
	onclick={isShowingVolumeFor
		? () => {
				isShowingVolumeFor = '';
			}
		: undefined}
/>

<div class="grid grid-cols-3 gap-x-2 gap-y-2 items-start py-2">
	{#each backgroundSoundInstances as sound, i}
		{@const column = i % 3}
		<div class="flex flex-col gap-2 items-center justify-start text-center relative">
			<button
				class="btn btn-circle btn-md btn-ghost relative"
				aria-label={sound.volume === 0
					? `Play ${sound.name} sound`
					: `Mute ${sound.name} sound`}
				onclick={(e) => {
					if (sound.volume === 0) {
						sound.volume = 0.5;
						isShowingVolumeFor = sound.name;
						canClearIsShowingVolumeFor = true;
					} else {
						sound.volume = 0;
						isShowingVolumeFor = '';
					}

					e.stopPropagation();
				}}
			>
				<Arc
					angle={359.99 * sound.volume}
					strokeWidth={4}
					class="absolute -top-[5px] -left-[5px] size-[calc(100%_+_10px)]"
				/>
				<sound.resource.icon class="size-8" />
			</button>

			<span class="label-text text-xs">
				{sound.name}
			</span>

			<input
				aria-label="{sound.name} volume"
				type="range"
				bind:value={sound.volume}
				min="0"
				max="1"
				step="0.01"
				class="sr-only"
			/>

			{#if isShowingVolumeFor === sound.name}
				<!-- svelte-ignore a11y_click_events_have_key_events -->
				<!-- svelte-ignore a11y_no_static_element_interactions -->
				<div
					transition:fly={{ y: 20 }}
					class={cn(
						'absolute -top-2 -translate-y-full w-56 flex items-center justify-center p-4 rounded-full z-10',
						{
							'left-0': column === 0,
							'translate-x-1/2 right-1/2': column === 1,
							'right-0': column === 2,
							glass: volumeSliderVariant === 'glass',
							'bg-base-100': volumeSliderVariant === 'opaque',
						},
					)}
					onclick={(e) => e.stopPropagation()}
					onpointermove={() => (canClearIsShowingVolumeFor = false)}
					onpointerleave={() => (canClearIsShowingVolumeFor = true)}
				>
					<input
						aria-label="{sound.name} background sound volume"
						aria-live="off"
						aria-hidden="true"
						type="range"
						bind:value={sound.volume}
						min="0"
						max="1"
						step="0.01"
						class="range range-xs w-full"
					/>
				</div>
			{/if}
		</div>
	{/each}
</div>
