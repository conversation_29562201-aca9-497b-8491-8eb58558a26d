<script lang="ts">
	import { theme } from '$lib/stores/theme.svelte';
	import { Toaster } from 'svelte-sonner';
	import CheckIcon from './Icons/CheckIcon.svelte';
	import InfoSolidIcon from './Icons/InfoSolidIcon.svelte';
	import WarningSolidIcon from './Icons/WarningSolidIcon.svelte';
</script>

<Toaster
	position="bottom-right"
	gap={16}
	theme={theme.brightness}
	toastOptions={{
		unstyled: true,
		classes: {
			toast: 'alert shadow-lg overflow-hidden flex flex-row items-center justify-stretch text-start w-full',
			success: 'alert-success',
			error: 'alert-error',
			warning: 'alert-warning',
			info: 'alert-info',
			actionButton: 'btn btn-sm btn-primary',
			cancelButton: 'btn btn-sm',
			description: 'text-sm',
			title: 'text-base font-medium',
			default: 'bg-base-100 dark:bg-base-300',
		},
	}}
>
	<span class="loading loading-spinner size-6" slot="loading-icon"></span>
	<WarningSolidIcon class="size-6" slot="error-icon" />
	<InfoSolidIcon class="size-6" slot="info-icon" />
	<CheckIcon class="size-6" slot="success-icon" />
	<WarningSolidIcon class="size-6" slot="warning-icon" />
</Toaster>
