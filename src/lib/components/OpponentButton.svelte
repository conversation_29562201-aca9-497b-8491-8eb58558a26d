<script lang="ts" generics="Difficulty">
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import PeopleIcon from '$lib/components/Icons/PeopleIcon.svelte';
	import { cn } from '$lib/util/cn';

	export type Opponent = 'cpu' | 'local-multiplayer';

	export interface DifficultyOption<T> {
		value: T;
		label: string;
	}

	interface Props<T> {
		opponent: Opponent;
		difficulty?: T;
		difficulties: DifficultyOption<T>[];
		onOpponentChange: (opponent: Opponent, difficulty?: T) => void;
		disabled?: boolean;
		class?: string;
		dropdownButtonClass?: string;
	}

	let open = $state(false);

	let {
		opponent,
		difficulty,
		difficulties,
		onOpponentChange,
		disabled = false,
		class: className,
		dropdownButtonClass,
	}: Props<Difficulty> = $props();

	function change(newOpponent: Opponent, newDifficulty?: Difficulty) {
		onOpponentChange(newOpponent, newDifficulty);
		open = false;
	}
</script>

<Dropdown bind:open class={className}>
	<DropdownButton class={cn(dropdownButtonClass)} {disabled} aria-label="Change opponent">
		<PeopleIcon class="size-4" variant={opponent === 'cpu' ? 'single' : 'multiple'} />
		<span>
			{opponent === 'cpu'
				? (difficulties.find((d) => d.value === difficulty)?.label ?? 'CPU')
				: 'Player 2'}
		</span>
	</DropdownButton>

	{#key `${opponent}-${difficulty}`}
		<DropdownContent menu class="w-48">
			<DropdownItem>
				<button
					class={cn({
						'menu-active': opponent === 'cpu',
					})}
					onclick={() => {
						change('cpu');
					}}
				>
					<PeopleIcon variant="single" class="size-4" />
					Single Player
				</button>
			</DropdownItem>

			{#if difficulties.length > 0}
				<div
					class="pl-6 before:absolute before:top-2 before:left-3 before:w-[1px] before:bottom-2 before:bg-base-content/60 before:z-10 relative"
				>
					<div class="py-2">
						{#each difficulties as item (item.value)}
							<DropdownItem>
								<button
									class={cn({
										'menu-active':
											opponent === 'cpu' && difficulty === item.value,
									})}
									onclick={() => change('cpu', item.value)}
								>
									{item.label}
								</button>
							</DropdownItem>
						{/each}
					</div>
				</div>
			{/if}

			<DropdownItem>
				<button
					class={cn({
						'menu-active': opponent === 'local-multiplayer',
					})}
					onclick={() => {
						change('local-multiplayer');
					}}
				>
					<PeopleIcon variant="multiple" class="size-4" />
					Local Multiplayer
				</button>
			</DropdownItem>
		</DropdownContent>
	{/key}
</Dropdown>
