<script lang="ts">
	import { scale } from 'svelte/transition';
	import QuestionIcon from '../Icons/QuestionIcon.svelte';
	import AdsInfoDialog from './AdsInfoDialog.svelte';

	let showAdsInfoDialog = $state(false);
</script>

<AdsInfoDialog bind:isOpen={showAdsInfoDialog} />

<div
	class="tooltip before:hidden after:hidden lg:before:block lg:after:block tooltip-left"
	data-tip="Why am I seeing ads?"
>
	<button
		out:scale
		class="btn btn-sm btn-circle"
		onclick={() => {
			showAdsInfoDialog = true;
		}}
		aria-label="Show why am I seeing ads dialog"
	>
		<QuestionIcon class="size-6" />
	</button>
</div>
