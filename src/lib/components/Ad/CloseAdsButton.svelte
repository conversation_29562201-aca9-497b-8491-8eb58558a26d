<script lang="ts">
	import { siteSounds } from '$lib/stores/siteSounds.svelte';
	import { cn } from '$lib/util/cn';
	import { scale } from 'svelte/transition';
	import CloseIcon from '../Icons/CloseIcon.svelte';
	import { ads } from '$lib/stores/ads.svelte';
	import { tick } from 'svelte';

	interface Props {
		class?: string;
		onclick?: () => void;
	}

	let canShowTooltip = $state(true);
	let { class: className, onclick }: Props = $props();
</script>

<div
	class={cn(
		'tooltip before:hidden after:hidden lg:before:block lg:after:block tooltip-left',
		{
			'lg:before:hidden lg:after:hidden': !canShowTooltip,
		},
		className,
	)}
	data-tip="Close ads"
>
	<button
		out:scale
		class="btn btn-sm btn-circle"
		onclick={async () => {
			canShowTooltip = false;

			await tick();

			ads.canShowGameAds = false;
			siteSounds.adPop.play();
			onclick?.();
		}}
		aria-label="Close ads"
	>
		<CloseIcon class="size-6" />
	</button>
</div>
