<script lang="ts">
	import Dialog from '../Dialog.svelte';

	interface Props {
		isOpen?: boolean;
		onClose?: () => void;
	}

	let { isOpen = $bindable(false), onClose }: Props = $props();
</script>

<Dialog bind:isOpen {onClose}>
	<article>
		<h2>Why am I seeing ads?</h2>

		<p>
			We know ads can be annoying, and honestly, we're not huge fans of them either! But
			here's the thing - running this website and keeping all the games free requires covering
			server costs and compensating the hard work that goes into creating and maintaining
			these games.
		</p>

		<p>
			Ads provide a more steady stream of income compared to donations or one-time purchases,
			which helps us keep the lights on and continue developing new games for everyone to
			enjoy.
		</p>

		<p>
			The good news? You can close the ads anytime by clicking the close button, and they
			won't show up again until your next visit to the site. We try to keep them as
			unobtrusive as possible while still being able to support the project.
		</p>

		<p>Thanks for understanding and for playing our games!</p>

		<p class="text-center">{'ദ്ദി(˵ •̀ ᴗ - ˵ ) ✧'}</p>
	</article>
</Dialog>
