<script lang="ts">
	import { Ads } from '$lib/stores/ads.svelte';
	import { fade } from 'svelte/transition';
	import BuyMeACoffeeButton from '../BuyMeACoffeeButton.svelte';
	import { cn } from '$lib/util/cn';
	import { untrack } from 'svelte';

	type Variant = 'game-desktop' | 'game-mobile' | 'game-tablet' | 'game-paused';

	interface Props {
		variant: Variant;
	}

	let { variant }: Props = $props();
	let canShowAdsFallback = $state<boolean>();
	let insElement = $state<HTMLElement>();

	$effect(function checkAdBlock() {
		if (insElement) {
			function assignShowAdsFallback() {
				untrack(() => {
					canShowAdsFallback = (insElement?.children?.length ?? 0) === 0;
				});
			}

			assignShowAdsFallback();

			const observer = new MutationObserver(assignShowAdsFallback);

			observer.observe(insElement, {
				childList: true,
				subtree: true,
			});

			return () => {
				observer.disconnect();
			};
		}
	});

	$effect(() => {
		// Track variant
		variant;

		untrack(() => {
			try {
				((window as any).adsbygoogle = (window as any).adsbygoogle || []).push({});
			} catch (_) {
				// Ignore
			}
		});
	});
</script>

<div class="relative size-full">
	{#if canShowAdsFallback}
		<a
			href={variant === 'game-paused'
				? undefined
				: 'https://donate.stripe.com/aEU7t72mG6Cke1a000'}
			target="_blank"
			rel="noreferrer noopener"
			transition:fade
			class={cn('absolute inset-0 p-4 flex gap-1 items-center justify-between bg-base-300', {
				'gap-12 text-center flex-col justify-center': [
					'game-desktop',
					'game-paused',
				].includes(variant),
				'px-4 py-2': variant === 'game-mobile',
			})}
		>
			<div
				class={cn('flex flex-col gap-1 xl:gap-6', {
					'gap-0': variant === 'game-mobile',
					'gap-6': ['game-desktop', 'game-paused'].includes(variant),
				})}
			>
				<h2
					class={cn('text-lg xl:text-4xl font-bold', {
						'text-4xl': variant === 'game-desktop',
						'text-4xl xl:text-5xl': variant === 'game-paused',
						'text-sm': variant === 'game-mobile',
					})}
				>
					No ads? That's ok!
				</h2>

				<p
					class={cn('text-md xl:text-xl', {
						'text-xl': variant === 'game-desktop',
						'text-xl xl:text-2xl': variant === 'game-paused',
						hidden: variant === 'game-mobile',
					})}
				>
					Support the site directly with a coffee instead
				</p>

				<p
					class={cn('text-xs hidden', {
						block: variant === 'game-mobile',
					})}
				>
					Buy me a coffee instead
				</p>
			</div>

			<div>
				<BuyMeACoffeeButton
					variant="icon"
					class={cn('hidden', {
						'size-8 block': variant === 'game-mobile',
					})}
				/>
				<BuyMeACoffeeButton
					class={cn({
						hidden: variant === 'game-mobile',
					})}
				/>
			</div>
		</a>
	{/if}

	<div class="size-full flex items-center justify-center">
		{#if ['game-desktop', 'game-paused'].includes(variant)}
			<ins
				bind:this={insElement}
				class="adsbygoogle"
				style="display:inline-block;width:{Ads.desktopGameAdSize.width}px;height:{Ads
					.desktopGameAdSize.height}px"
				data-ad-client="ca-pub-9459941752955255"
				data-ad-slot="7918725640"
			></ins>
		{:else if variant === 'game-mobile'}
			<ins
				bind:this={insElement}
				class="adsbygoogle"
				style="display:inline-block;width:{Ads.mobileGameAdSize.width}px;height:{Ads
					.mobileGameAdSize.height}px"
				data-ad-client="ca-pub-9459941752955255"
				data-ad-slot="1048172291"
			></ins>
		{:else if variant === 'game-tablet'}
			<ins
				bind:this={insElement}
				class="adsbygoogle"
				style="display:inline-block;width:{Ads.tabletGameAdSize.width}px;height:{Ads
					.tabletGameAdSize.height}px"
				data-ad-client="ca-pub-9459941752955255"
				data-ad-slot="7543892915"
			></ins>
		{:else if variant === 'game-paused'}
			<ins
				bind:this={insElement}
				class="adsbygoogle size-full"
				style="display:block"
				data-ad-client="ca-pub-9459941752955255"
				data-ad-slot="6922992102"
				data-ad-format="auto"
				data-full-width-responsive="true"
			></ins>
		{/if}
	</div>
</div>
