<script lang="ts">
	import type { <PERSON><PERSON>ventHandler } from 'svelte/elements';
	import { cn } from '$lib/util/cn';
	import InfoSolidIcon from './Icons/InfoSolidIcon.svelte';

	interface Props {
		class?: string;
		iconClass?: string;
		onclick: MouseEventHandler<HTMLButtonElement>;
	}

	let { class: classFromProps = '', iconClass, onclick }: Props = $props();
</script>

<button {onclick} class={cn('btn btn-sm', classFromProps)} aria-label="Open info dialog">
	<InfoSolidIcon class={cn('size-5', iconClass)} />
</button>
