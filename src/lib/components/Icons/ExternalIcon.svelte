<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M11 6H6a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-5"
		stroke="currentColor"
		stroke-width="2"
	/>
	<path
		d="M14 4h5a1 1 0 0 1 1 1v5m-.5-5.5L14 10"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
</svg>
