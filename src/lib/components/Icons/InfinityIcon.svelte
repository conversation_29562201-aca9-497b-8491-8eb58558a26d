<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M7.44434 8.5C8.81847 8.5 9.95759 9.28237 10.8066 10.2432C11.2239 10.7153 11.543 11.2017 11.7549 11.5879C11.8457 11.7534 11.9111 11.8944 11.9561 12C11.9111 12.1056 11.8457 12.2466 11.7549 12.4121C11.543 12.7983 11.2239 13.2847 10.8066 13.7568C9.95759 14.7176 8.81847 15.5 7.44434 15.5C5.55375 15.4999 4 13.9447 4 12C4 10.0553 5.55375 8.50006 7.44434 8.5Z"
		stroke="currentColor"
		stroke-width="2"
	/>
	<path
		d="M16.5557 8.5C18.4463 8.50006 20 10.0553 20 12C20 13.9447 18.4463 15.4999 16.5557 15.5C15.1815 15.5 14.0424 14.7176 13.1934 13.7568C12.7761 13.2847 12.457 12.7983 12.2451 12.4121C12.1542 12.2464 12.0879 12.1057 12.043 12C12.0879 11.8943 12.1542 11.7536 12.2451 11.5879C12.457 11.2017 12.7761 10.7153 13.1934 10.2432C14.0424 9.28237 15.1815 8.5 16.5557 8.5Z"
		stroke="currentColor"
		stroke-width="2"
	/>
</svg>
