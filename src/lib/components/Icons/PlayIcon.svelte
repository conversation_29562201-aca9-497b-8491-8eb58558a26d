<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		variant: 'play' | 'pause';
		class?: string;
	}

	let { variant, class: className, ...props }: Props = $props();
</script>

{#if variant === 'pause'}
	<svg
		class={cn('size-full', className)}
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		{...props}
	>
		<path
			d="M8 8C8 7.44772 8.44772 7 9 7C9.55228 7 10 7.44772 10 8V16C10 16.5523 9.55228 17 9 17C8.44772 17 8 16.5523 8 16V8Z"
			fill="currentColor"
		/>
		<path
			d="M14 8C14 7.44772 14.4477 7 15 7V7C15.5523 7 16 7.44772 16 8V16C16 16.5523 15.5523 17 15 17V17C14.4477 17 14 16.5523 14 16V8Z"
			fill="currentColor"
		/>
	</svg>
{/if}

{#if variant === 'play'}
	<svg
		class={cn('size-full', className)}
		viewBox="0 0 24 24"
		fill="none"
		xmlns="http://www.w3.org/2000/svg"
		{...props}
	>
		<path
			d="M16.5 11.134C17.1667 11.5189 17.1667 12.4811 16.5 12.866L10.5 16.3301C9.83333 16.715 9 16.2339 9 15.4641L9 8.5359C9 7.7661 9.83333 7.28497 10.5 7.66987L16.5 11.134Z"
			fill="currentColor"
		/>
	</svg>
{/if}
