<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	const { class: classFromProps, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', classFromProps)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
	<circle cx="12" cy="17" r="1" fill="currentColor" />
</svg>
