<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<circle cx="11" cy="11" r="6" stroke="currentColor" stroke-width="2" />
	<path d="m20 20-4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
</svg>
