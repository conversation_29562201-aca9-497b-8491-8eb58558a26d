<script lang="ts">
	interface Props {
		variant?: 'touch' | 'hold';
		class?: string;
		width?: string;
		height?: string;
		style?: string;
	}

	let { variant = 'touch', ...props }: Props = $props();
</script>

<svg
	width="24"
	height="24"
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	class:touch={variant === 'touch'}
	class:hold={variant === 'hold'}
	{...props}
>
	<g class="circle">
		<circle cx="10" cy="7" r="3.5" stroke="currentColor" />
	</g>
	<g class="hand">
		<mask
			id="path-2-outside-1_253_1294"
			maskUnits="userSpaceOnUse"
			x="4"
			y="5"
			width="16"
			height="17"
			fill="black"
		>
			<rect fill="white" x="4" y="5" width="16" height="17" />
			<path
				fill-rule="evenodd"
				clip-rule="evenodd"
				d="M9.99999 6C9.4477 6 8.99999 6.44772 8.99999 7V17.1323L7.86619 15.1685C7.59005 14.6902 6.97846 14.5263 6.50017 14.8025C6.02187 15.0786 5.858 15.6902 6.13414 16.1685L8.63317 20.497C8.81498 20.8119 9.14222 20.9905 9.48108 20.9969C9.49935 20.999 9.51792 21 9.53673 21H10.0367H10.5367H16.0367C17.1413 21 18.0367 20.1046 18.0367 19V16.4415C18.0367 15.5807 17.4859 14.8164 16.6692 14.5442L11.6692 12.8775C11.445 12.8028 11.2195 12.7699 11 12.7736V7C11 6.44772 10.5523 6 9.99999 6Z"
			/>
		</mask>
		<path
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M9.99999 6C9.4477 6 8.99999 6.44772 8.99999 7V17.1323L7.86619 15.1685C7.59005 14.6902 6.97846 14.5263 6.50017 14.8025C6.02187 15.0786 5.858 15.6902 6.13414 16.1685L8.63317 20.497C8.81498 20.8119 9.14222 20.9905 9.48108 20.9969C9.49935 20.999 9.51792 21 9.53673 21H10.0367H10.5367H16.0367C17.1413 21 18.0367 20.1046 18.0367 19V16.4415C18.0367 15.5807 17.4859 14.8164 16.6692 14.5442L11.6692 12.8775C11.445 12.8028 11.2195 12.7699 11 12.7736V7C11 6.44772 10.5523 6 9.99999 6Z"
			fill="currentColor"
		/>
	</g>
</svg>

<style>
	.hand {
		transform-origin: center;
		animation-iteration-count: infinite;
	}

	.circle {
		transform-origin: 41% 32%;
		animation-iteration-count: infinite;
	}

	/* Touch */
	@keyframes handTouch {
		20% {
			transform: scale(1);
		}
		50% {
			transform: scale(0.8);
		}
		70% {
			transform: scale(1);
		}
		100% {
			transform: scale(1);
		}
	}

	@keyframes circleTouch {
		0% {
			transform: scale(0);
			opacity: 0;
		}
		50% {
			transform: scale(0);
			opacity: 1;
		}
		100% {
			transform: scale(1.5);
			opacity: 0;
		}
	}

	.touch .hand {
		animation: handTouch 1000ms ease-in-out forwards infinite;
	}

	.touch .circle {
		animation: circleTouch 1000ms ease-in-out forwards infinite;
	}

	/* Hold */
	@keyframes handHold {
		20% {
			transform: scale(1);
		}
		30% {
			transform: scale(0.8);
		}
		90% {
			transform: scale(0.8);
		}
		100% {
			transform: scale(1);
		}
	}

	@keyframes circleHold {
		0% {
			transform: scale(0);
			opacity: 0;
		}
		30% {
			transform: scale(0);
			opacity: 1;
		}
		90% {
			transform: scale(1.5);
			opacity: 0;
		}
		100% {
			transform: scale(1.5);
			opacity: 0;
		}
	}

	.hold .hand {
		animation: handHold 1500ms ease-in-out forwards infinite;
	}

	.hold .circle {
		animation: circleHold 1500ms ease-in-out forwards infinite;
	}
</style>
