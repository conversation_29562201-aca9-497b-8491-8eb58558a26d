<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M16.111 16.111V19a1 1 0 1 1-2 0v-2.889a2 2 0 0 1 2-2H19a1 1 0 1 1 0 2h-2.889Zm0-8.222V5a1 1 0 1 0-2 0v2.889a2 2 0 0 0 2 2H19a1 1 0 1 0 0-2h-2.889Zm-6.222 0V5a1 1 0 1 0-2 0v2.889H5a1 1 0 0 0 0 2h2.889a2 2 0 0 0 2-2Zm0 11.111v-2.889a2 2 0 0 0-2-2H5a1 1 0 1 0 0 2h2.889V19a1 1 0 1 0 2 0Z"
		fill="currentColor"
	/>
</svg>
