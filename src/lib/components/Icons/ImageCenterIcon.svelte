<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M15.125 8.25h-6.25c-.69 0-1.25.56-1.25 1.25v5c0 .69.56 1.25 1.25 1.25h6.25c.69 0 1.25-.56 1.25-1.25v-5c0-.69-.56-1.25-1.25-1.25ZM8.875 7a2.5 2.5 0 0 0-2.5 2.5v5a2.5 2.5 0 0 0 2.5 2.5h6.25a2.5 2.5 0 0 0 2.5-2.5v-5a2.5 2.5 0 0 0-2.5-2.5h-6.25Z"
		fill="currentColor"
	/>
	<path
		d="M6.688 14.5v-.107c0-.415.164-.812.457-1.105l.938-.938c.61-.61 1.6-.61 2.21 0l1.357 1.358a.938.938 0 0 0 1.325 0l.42-.42c.61-.61 1.6-.61 2.21 0l1.11 1.11a.975.975 0 0 1-.69 1.665H8.25c-.863 0-1.562-.7-1.562-1.563Zm9.062-4.687a.938.938 0 1 1-1.875 0 .938.938 0 0 1 1.875 0Zm5.139 8.076V15a1 1 0 1 0-2 0v2.889H16a1 1 0 1 0 0 2h2.889a2 2 0 0 0 2-2ZM5 19.889h2.889a1 1 0 1 0 0-2H5V15a1 1 0 1 0-2 0v2.889a2 2 0 0 0 2 2ZM3 6v2.889a1 1 0 1 0 2 0V6h2.889a1 1 0 1 0 0-2H5a2 2 0 0 0-2 2Zm15.889-2H16a1 1 0 1 0 0 2h2.889v2.889a1 1 0 1 0 2 0V6a2 2 0 0 0-2-2Z"
		fill="currentColor"
	/>
</svg>
