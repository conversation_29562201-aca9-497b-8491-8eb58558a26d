<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 24 24"
	fill="none"
	{...props}
>
	<path
		d="M16.2 13.588A6.248 6.248 0 0 0 18 9.176C18 5.766 15.314 3 12 3S6 5.765 6 9.176c0 1.729.69 3.291 1.8 4.412.***************.337.328.612.577.863.815.863 3.084a1 1 0 0 0 1 1h4a1 1 0 0 0 1-1c0-2.27.251-2.507.863-3.084.102-.096.214-.202.336-.328Z"
		class="fill-current"
	/>
	<rect x="10" y="19" width="4" height="2" rx="1" class="fill-current" />
</svg>
