<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M10.229 5.187c-.085-.072-.434-.463-3.572.16-.678.135-1.211 1.006-1.121 1.692.141 1.077.258 2.43.214 2.744-.072.517-.193.598-.506.734s-.506-.244-.626-.38-.602-.463-.867-.408c-.265.054-.41.326-.554.625-.144.3-.217 1.251-.192 2.312.019.85.168 1.223.53 1.495.36.272.818-.108.963-.217.144-.109.327-.416.578-.462.234-.044.325.014.505.19.361.353.072 2.366.072 2.366s-.433 2.366-.385 2.366c.039 0 .225.036.313.054 3.155.626 3.66.49 4.094.517.433.027.554-.163.626-.245.072-.081.337-.516 0-.816-.337-.299-.65-.815-.65-.87 0-.054-.313-1.06 1.445-1.17 1.406-.086 2.287.073 2.552.164.385.136.458.19.674.544.184.299.049.598-.144.87-.204.287-.53.49-.602.653-.123.277.09.712.337.788.53.164.814.103 1.397.055.954-.08 1.668-.19 2.27-.309.614-.12 1.064-.829.955-1.444-.08-.46-.156-.89-.167-.94-.024-.108-.217-1.876-.12-2.284.06-.259.168-.326.337-.462.144-.117.457-.055.53.027.072.082.36.408.65.571.288.163.65.109.77 0s.24-.326.385-.843c.145-.517.048-1.85.048-1.985 0-.136-.144-1.496-.746-1.523-.443-.022-.819.362-.987.57-.308.327-.658.191-.795.055-.212-.174-.193-.625-.193-.979 0-.946.205-2.246.355-3.06.082-.443-.151-.826-.594-.913a79.17 79.17 0 0 0-.989-.188c-.794-.108-2.769-.408-3.202-.081-.361.272-.29.707-.241.761l.578.653c.27.348.313.752.096.979-.27.283-.819.504-1.806.571-.795.054-1.83-.027-2.288-.272-.38-.203-.577-.517-.457-.897.09-.282.461-.742.675-.981a.383.383 0 0 0 .08-.116c.094-.246-.137-.596-.225-.671Z"
		fill="currentColor"
	/>
</svg>
