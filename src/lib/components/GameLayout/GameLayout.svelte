<script lang="ts">
	import PhoneRotateLandscape from '../Icons/PhoneRotateLandscape.svelte';
	import { onMount, tick, untrack, type Snippet } from 'svelte';
	import { cn } from '$lib/util/cn';
	import Navbar, { type NavbarProps } from '../Navbar/Navbar.svelte';
	import Ad from '../Ad/Ad.svelte';
	import { Ads, ads } from '$lib/stores/ads.svelte';
	import CloseAdsButton from '../Ad/CloseAdsButton.svelte';
	import { scale } from 'svelte/transition';
	import WhyAdsButton from '../Ad/WhyAdsButton.svelte';
	import { MediaQuery } from '$lib/util/MediaQuery.svelte';

	type Orientation = 'landscape' | 'portrait' | 'all';

	type AdVariant = 'default' | 'on-top';
	type VerticalAdSide = 'right' | 'left';
	type HorizontalAdSide = 'top' | 'bottom';

	type AdsProps = {
		desktopVariant?: AdVariant;
		mobileVariant?: AdVariant;
		variant?: AdVariant;
		verticalAdSide?: VerticalAdSide;
		horizontalAdSide?: HorizontalAdSide;
		class?: string;
		canShow?: boolean;
		minHeightToShowHorizontalAd?: number;
		onClose?: () => void;
	};

	interface Props {
		class?: string;
		style?: string;
		mobileOrientation?: Orientation;
		noPadding?: boolean;
		navbarStyle?: 'default' | 'on-top' | 'on-top-as-glass' | 'on-top-opaque' | 'gone';
		adsProps?: AdsProps;
		soundButtonAccentWhenPlaying?: boolean;
		navbarProps?: Pick<NavbarProps, 'profileButtonProps'>;
		Island: Snippet;
		children: Snippet;
	}

	let {
		class: classFromProps = '',
		mobileOrientation = 'portrait',
		noPadding = false,
		navbarStyle = 'default',
		adsProps: _adsProps,
		soundButtonAccentWhenPlaying,
		navbarProps,
		style,
		Island,
		children,
	}: Props = $props();

	let adsProps = $derived.by(() => {
		return {
			desktopVariant: 'on-top',
			mobileVariant: 'default',
			..._adsProps,
		} as AdsProps;
	});

	let isNavbarOnTop = $derived(
		['on-top', 'on-top-as-glass', 'on-top-opaque'].includes(navbarStyle),
	);
	let hasMinHeightToShowHorizontalAd = $state(false);
	const adsPopDuration = 200;
	let xlMediaQuery = new MediaQuery('min-width: 1280px');

	function onresize() {
		hasMinHeightToShowHorizontalAd =
			window.innerHeight >= (adsProps?.minHeightToShowHorizontalAd ?? 0);
	}

	$effect(() => {
		// Track
		adsProps?.minHeightToShowHorizontalAd;

		untrack(() => {
			onresize();
		});
	});

	onMount(() => {
		onresize();
	});
</script>

<svelte:window {onresize} />

{#if navbarStyle !== 'gone'}
	<Navbar
		variant={navbarStyle === 'on-top'
			? 'transparent'
			: navbarStyle === 'on-top-as-glass'
				? 'glass'
				: 'normal'}
		{soundButtonAccentWhenPlaying}
		class={cn({
			'absolute top-0 left-0 right-0 z-20 grow-0': isNavbarOnTop,
		})}
		{...navbarProps}
	>
		{@render Island()}
	</Navbar>
{/if}

<section
	class={cn(
		'touch-none flex justify-between size-full select-none relative flex-col xl:flex-row max-w-screen',
		{
			'flex-row-reverse': adsProps?.verticalAdSide === 'left',
			'min-h-screen-no-navbar': navbarStyle === 'default',
			'min-h-screen': isNavbarOnTop,
		},
		classFromProps,
	)}
	{style}
>
	<main
		class={cn('p-4 grow shrink flex items-stretch justify-center relative', {
			'p-0': noPadding,
			'order-2 xl:-order-1': adsProps?.horizontalAdSide !== 'bottom',
		})}
	>
		<div
			class={cn('grow flex items-center justify-center relative max-w-[100vw]', {
				'portrait:invisible portrait:pointer-events-none lg:portrait:visible lg:portrait:pointer-events-auto':
					mobileOrientation === 'landscape',
				'landscape:invisible landscape:pointer-events-none lg:landscape:visible lg:landscape:pointer-events-auto':
					mobileOrientation === 'portrait',
			})}
		>
			{@render children()}
		</div>

		<div
			class={cn(
				'flex justify-center items-center absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex-col gap-4 text-lg text-center p-4',
				{
					'lg:hidden portrait:hidden': mobileOrientation === 'portrait',
					'lg:hidden landscape:hidden': mobileOrientation === 'landscape',
					hidden: mobileOrientation === 'all',
				},
			)}
		>
			<PhoneRotateLandscape width="64" height="64" />

			<span>Rotate your device </span>
		</div>
	</main>

	<!-- Ads -->
	{#if ads.canShowGameAds && adsProps?.canShow !== false && (ads.variant === 'desktop' ? true : hasMinHeightToShowHorizontalAd)}
		{@const isAdOnTop =
			isNavbarOnTop ||
			adsProps?.variant === 'on-top' ||
			(xlMediaQuery.current && adsProps?.desktopVariant === 'on-top') ||
			(!xlMediaQuery.current && adsProps?.mobileVariant === 'on-top')}
		<aside
			class={cn(
				'max-w-2xl w-full xl:w-auto mx-auto xl:max-w-72 xl:h-[572px] shrink-0 flex xl:flex-col gap-2 items-center justify-center xl:justify-start xl:items-end px-4 xl:p-4 xl:pb-[var(--navbar-height)] sticky bottom-4 xl:top-0 xl:bottom-auto z-10 pb-1 pt-2',
				{
					'absolute bottom-0 right-1/2 left-1/2 -translate-x-1/2 xl:right-0 xl:translate-0 xl:left-auto xl:top-[calc(var(--spacing)_*_16_+_var(--navbar-height))] xl:bottom-[var(--navbar-height)] xl:pb-4':
						isAdOnTop,
					'xl:top-8': isAdOnTop && navbarStyle === 'default',
					'xl:right-auto xl:left-0 xl:pr-4': adsProps?.verticalAdSide === 'left',
					'bottom-auto top-16': adsProps?.horizontalAdSide !== 'bottom' && isAdOnTop,
				},
				adsProps?.class,
			)}
		>
			<!-- Spacer -->
			<div class="invisible xl:hidden size-8 shrink"></div>

			<!-- Mobile and Tablet ads -->

			<div out:scale class="xl:hidden">
				<div
					class="hidden sm:flex size-full items-center justify-center"
					style="height: {Ads.tabletGameAdSize.height}px; width: {Ads.tabletGameAdSize
						.width}px"
				>
					<Ad variant="game-tablet" />
				</div>

				<div
					class="sm:hidden size-full flex items-center justify-center"
					style="height: {Ads.mobileGameAdSize.height}px; width: {Ads.mobileGameAdSize
						.width}px"
				>
					<Ad variant="game-mobile" />
				</div>
			</div>

			<!-- Buttons -->

			<div class="flex flex-col xl:flex-row-reverse items-center gap-1">
				<CloseAdsButton
					onclick={() => {
						setTimeout(async () => {
							await tick();

							adsProps?.onClose?.();
						}, adsPopDuration);
					}}
				/>

				<WhyAdsButton />
			</div>

			<!-- Desktop ad -->

			<div
				out:scale
				class="hidden xl:block size-full"
				style="height: {Ads.desktopGameAdSize.height}px; width: {Ads.desktopGameAdSize
					.width}px"
			>
				<Ad variant="game-desktop" />
			</div>
		</aside>
	{/if}
</section>
