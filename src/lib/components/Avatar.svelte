<script lang="ts">
	import type { User } from '$lib/auth/client';
	import { cn } from '$lib/util/cn';
	import PeopleIcon from './Icons/PeopleIcon.svelte';

	interface Props {
		class?: string;
		user?: Pick<User, 'displayUsername' | 'name' | 'image'>;
	}

	let { user, class: className = '' }: Props = $props();
	let imageLoaded = $state(false);

	let initials = $derived(
		(user?.displayUsername || user?.name)
			?.split(' ')
			.map((name) => name[0])
			.slice(0, 2)
			.join(''),
	);
</script>

<div class="relative">
	{#if !user}
		<PeopleIcon class={cn('size-8 shrink-0', className)} />
	{:else if user.image}
		<img
			src={user.image}
			alt={initials}
			class={cn('rounded-full size-8 shrink-0', className)}
			onload={() => {
				imageLoaded = true;
			}}
			aria-label="Avatar image of {user.name}"
		/>

		<div
			class={cn(
				'absolute inset-0 rounded-full size-8 bg-base-100 transition-opacity duration-300',
				{
					'opacity-0': imageLoaded,
				},
				className,
			)}
		></div>
	{:else}
		<div
			class={cn(
				'rounded-full size-8 flex items-center justify-center text-center text-sm font-medium from-primary/20 to-secondary/20 bg-linear-to-tr dark:bg-linear-to-bl dark:from-primary/30 dark:to-secondary/30 relative after:absolute after:bg-base-100! after:inset-0 after:rounded-full after:-z-10 shrink-0',
				className,
			)}
			aria-hidden="true"
		>
			{initials}
		</div>
		<div class="sr-only">Avatar of {user.name} with initials {initials}</div>
	{/if}
</div>
