<script lang="ts">
	import { daysInMonth } from '$lib/functions/date/daysInMonth';
	import { startOfMonth } from '$lib/functions/date/startOfMonth';
	import { cn } from '$lib/util/cn';
	import ChevronLeftIcon from './Icons/ChevronLeftIcon.svelte';
	import ChevronRightIcon from './Icons/ChevronRightIcon.svelte';

	interface Props {
		disabled?: boolean;
		currentMonth?: Date;
		selectedDate: Date;
		fromDate: Date;
		toDate?: Date;
		class?: string;
		isMarked?: (date: Date) => void;
	}

	let {
		disabled,
		selectedDate = $bindable(),
		currentMonth = $bindable(startOfMonth(selectedDate)),
		fromDate,
		toDate = new Date(),
		class: className,
		isMarked = () => false,
	}: Props = $props();

	let daysInCurrentMonth = $derived(
		daysInMonth(currentMonth.getMonth() + 1, currentMonth.getFullYear()),
	);

	function isBeforeFirstDate(day: number) {
		if (currentMonth.getFullYear() === fromDate.getFullYear()) {
			if (currentMonth.getMonth() === fromDate.getMonth()) {
				return day < fromDate.getDate();
			}

			return currentMonth.getMonth() < fromDate.getMonth();
		}

		return currentMonth.getFullYear() < fromDate.getFullYear();
	}

	function isAfterLastDate(day: number) {
		if (currentMonth.getFullYear() === toDate.getFullYear()) {
			if (currentMonth.getMonth() === toDate.getMonth()) {
				return day > toDate.getDate();
			}

			return currentMonth.getMonth() > toDate.getMonth();
		}

		return currentMonth.getFullYear() > toDate.getFullYear();
	}

	const colStartClasses = [
		'col-start-1',
		'col-start-2',
		'col-start-3',
		'col-start-4',
		'col-start-5',
		'col-start-6',
		'col-start-7',
	];
	let colStart = $derived(currentMonth.getDay());
</script>

<div class={cn('w-full', className)}>
	<div class="flex justify-center items-center w-full gap-8 my-2">
		<button
			class="btn btn-circle btn-sm btn-ghost"
			aria-label="Previous month"
			style="--tw-text-opacity:0.5"
			disabled={disabled ||
				(currentMonth.getMonth() === fromDate.getMonth() &&
					currentMonth.getFullYear() === fromDate.getFullYear())}
			onclick={() =>
				(currentMonth = startOfMonth(
					new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1),
				))}
		>
			<ChevronLeftIcon class="size-full" />
		</button>
		<h4 class="font-bold w-36 text-center">
			{Intl.DateTimeFormat('en', {
				year: 'numeric',
				month: 'long',
			}).format(currentMonth)}
		</h4>
		<button
			class="btn btn-circle btn-sm btn-ghost"
			style="--tw-text-opacity:0.5"
			aria-label="Next month"
			disabled={disabled ||
				(currentMonth.getMonth() === toDate.getMonth() &&
					currentMonth.getFullYear() === toDate.getFullYear())}
			onclick={() =>
				(currentMonth = startOfMonth(
					new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1),
				))}
		>
			<ChevronRightIcon class="size-full" />
		</button>
	</div>

	<div class="grid grid-cols-7 grid-rows-7 gap-2 content-center">
		<div class="text-center font-bold text-sm py-2">Sun</div>
		<div class="text-center font-bold text-sm py-2">Mon</div>
		<div class="text-center font-bold text-sm py-2">Tue</div>
		<div class="text-center font-bold text-sm py-2">Wed</div>
		<div class="text-center font-bold text-sm py-2">Thu</div>
		<div class="text-center font-bold text-sm py-2">Fri</div>
		<div class="text-center font-bold text-sm py-2">Sat</div>

		{#each { length: daysInCurrentMonth } as _, index (`${index}-${currentMonth.getMonth()}-${currentMonth.getFullYear()}`)}
			{@const day = index + 1}
			{@const isDisabled = isBeforeFirstDate(day) || isAfterLastDate(day) || disabled}
			{@const selected =
				selectedDate.getDate() === day &&
				selectedDate.getMonth() === currentMonth.getMonth() &&
				selectedDate.getFullYear() === currentMonth.getFullYear()}
			<button
				class={cn('btn btn-circle btn-sm self-center justify-self-center relative', {
					'btn-ghost': !selected,
					'btn-primary': selected,
					'btn-disabled bg-transparent!': isDisabled,
					[colStartClasses[colStart]]: day === 1,
					'after:absolute after:top-1/2 after:-translate-y-1/2 after:-rotate-45 after:w-[80%] after:h-[2px] after:bg-current':
						isMarked(
							new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day),
						),
				})}
				style={isDisabled ? '--tw-text-opacity:0.5' : undefined}
				disabled={isDisabled}
				onclick={() => {
					selectedDate = new Date(
						currentMonth.getFullYear(),
						currentMonth.getMonth(),
						day,
					);
				}}
			>
				{day}
			</button>
		{/each}
	</div>
</div>
