/** TextShuffler V2 - Mathematical approach for deterministic text obfuscation */
export class POC {
	private static readonly SEED = 0x2f6e2b1; // Prime number seed for deterministic results
	private static readonly MULTIPLIER = 0x41c64e6d; // LCG multiplier
	private static readonly INCREMENT = 0x3039; // LCG increment
	private static readonly MASK = 0xffffffff; // 32-bit mask

	/**
	 * Simple Linear Congruential Generator for deterministic pseudo-random numbers
	 */
	private static lcg(seed: number): number {
		return ((seed * this.MULTIPLIER + this.INCREMENT) & this.MASK) >>> 0;
	}

	/**
	 * Generate a deterministic offset for a character based on its position only
	 */
	private static getOffset(position: number): number {
		const combinedSeed = (this.SEED ^ (position * 0x9e3779b9)) >>> 0;
		const random = this.lcg(combinedSeed);
		return (random % 94) + 1; // Offset range: 1-94 (printable ASCII range)
	}

	/**
	 * Shuffle text using mathematical transformation
	 */
	// static shuffle(text: string): string {
	// 	let result = '';

	// 	for (let i = 0; i < text.length; i++) {
	// 		const char = text[i];
	// 		const charCode = char.charCodeAt(0);

	// 		// Only transform printable ASCII characters (32-126)
	// 		if (charCode >= 32 && charCode <= 126) {
	// 			const offset = this.getOffset(i);
	// 			const newCharCode = ((charCode - 32 + offset) % 95) + 32;
	// 			result += String.fromCharCode(newCharCode);
	// 		} else {
	// 			// Keep non-printable characters unchanged
	// 			result += char;
	// 		}
	// 	}

	// 	return result;
	// }

	/**
	 * Unshuffle text by reversing the mathematical transformation
	 */
	static u(text: string): string {
		let result = '';

		for (let i = 0; i < text.length; i++) {
			const char = text[i];
			const charCode = char.charCodeAt(0);

			// Only transform printable ASCII characters (32-126)
			if (charCode >= 32 && charCode <= 126) {
				// Reverse the transformation directly
				const offset = this.getOffset(i);
				let originalCharCode = (charCode - 32 - offset) % 95;
				if (originalCharCode < 0) originalCharCode += 95; // Handle negative modulo
				originalCharCode += 32;

				result += String.fromCharCode(originalCharCode);
			} else {
				// Keep non-printable characters unchanged
				result += char;
			}
		}

		return result;
	}
}

// console.log(TSV2.shuffle('lofiandgames.com'));
