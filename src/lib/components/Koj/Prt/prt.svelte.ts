import { browser } from '$app/environment';
import { page } from '$app/state';
import { POC } from './POC';

/** DMCAV2 */
class Prt {
	// Allowed sites
	static #allowedSites = [
		// lofiandgames.com
		'`YJUS`u;_=[Iq?gE',
		// www.lofiandgames.com
		'ka[y^aw=_>Z=EI]K,xMp',
		// localhost
		'`YGM^Z!Gr',
	];

	/** fraud */
	d = $derived.by(() => {
		if (import.meta.env.VITE_DMCA_CHECK_DISABLED) {
			return false;
		}

		if ((page as any)[POC.u('i\\P')][POC.u('\\YW``S~9')]) {
			// this.fraud = DMCAV2.#allowedSites.every((u) => page.url.hostname !== TSV2.u(u));
			return Prt.#allowedSites.every(
				(u) => (page as any)[POC.u('i\\P')][POC.u('\\YW``S~9')] !== POC.u(u),
			);
		}
	});
}

/** dmcaV2 */
export const prt = browser ? new Prt() : null;
