<script lang="ts">
	import { jhx } from '$lib/components/Koj/JhxB/jhx.svelte';
	import { TS } from '$lib/components/Koj/JhxB/TS';

	let opacity = 1;
	let interval = 1000;
	let si = setInterval;
	let ci = clearInterval;

	$effect(() => {
		if (jhx?.f) {
			const id = si(() => {
				if (opacity > 0) {
					opacity -= 0.01;
				}
				// document.body.style.opacity = ...
				(document as any)[TS.u('foet')][TS.u('0ntmd')][TS.u('o3zq5nt')] = `${opacity}`;
			}, interval);

			return () => ci(id);
		}
	});
</script>
