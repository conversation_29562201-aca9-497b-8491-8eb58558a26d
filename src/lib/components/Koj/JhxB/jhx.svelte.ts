import { browser } from '$app/environment';
import { page } from '$app/state';
import { TS } from './TS';

/** DMCA */
class Jhx {
	/** fraud */
	f = $state(false);
	cleanUpEffect?: () => void;

	constructor() {
		if (import.meta.env.PROD && !import.meta.env.VITE_DMCA_CHECK_DISABLED) {
			this.cleanUpEffect = $effect.root(() => {
				$effect(() => {
					// page.url
					if ((page as any)[TS.u('ygm')][TS.u('lo0n')]) {
						// this.fraud = !page.url.host.includes('lofiandgames.com');
						this.f = !(page as any)[TS.u('ygm')][TS.u('lo0n')][TS.u('5sqmyed0')](
							TS.u('moc5zseuz4d0.qo4'),
						);
					}
				});
			});
		}
	}
}

/** DMCA */
export const jhx = browser ? new Jhx() : null;
