const fromText = 'qwertyuiopasdfghjklzxcvbnm1234567890';
const toText = 'jkdgnty5o3z0eculvxmb1qrfs469iphw782a';

// let fromDict = new Map(fromText.split('').map((f, i) => [f, toText[i]]));
const toDict = new Map(toText.split('').map((f, i) => [f, fromText[i]]));

/** TextShuffler */
export class TS {
	// static shuffle(text: string): string {
	// 	let newText: string[] = [];

	// 	for (const char of text) {
	// 		newText.push(fromDict.get(char) ?? char);
	// 	}

	// 	return newText.join('');
	// }

	/** unshuffle */
	static u(text: string): string {
		const newText: string[] = [];

		for (const char of text) {
			newText.push(toDict.get(char) ?? char);
		}

		return newText.join('');
	}
}
