<script lang="ts">
	import { goto } from '$app/navigation';
	import { favoriteGames } from '$lib/stores/games.svelte';
	import { gameSearch, type SearchableGame } from '$lib/stores/gameSearch.svelte';
	import { Playlight } from '$lib/stores/playlightSdk.svelte';
	import { cn } from '$lib/util/cn';
	import {
		elementTagsToIgnoreEvents,
		type ShortcutsTags,
	} from '$lib/util/Undoable/UndoableKeyboardListener';
	import { supabase } from '$lib/api/supabase';
	import Dialog from '../Dialog.svelte';
	import ExternalIcon from '../Icons/ExternalIcon.svelte';
	import SearchIcon from '../Icons/SearchIcon.svelte';
	import StarSolidIcon from '../Icons/StarSolidIcon.svelte';
	import { handleError } from '../../../hooks.client';
	import Alert from '../Alert.svelte';
	import { fly } from 'svelte/transition';

	let input: HTMLInputElement;
	let gamesContainer = $state<HTMLUListElement | null>(null);
	let selectedGame = $state<SearchableGame | null>(null);
	let requestState = $state<'idle' | 'loading' | 'success' | 'error'>('idle');
	let requestedGame = $state('');

	function handleIsOpen(e: KeyboardEvent) {
		const shouldToggle = e.code === 'KeyK' && (e.metaKey || e.ctrlKey);

		if (gameSearch.isOpen && shouldToggle) {
			gameSearch.isOpen = false;
			e.stopPropagation();
			return;
		}

		const tagName = (e.target as HTMLElement)?.tagName as unknown;

		if (elementTagsToIgnoreEvents.includes(tagName as ShortcutsTags)) {
			return;
		}

		if (shouldToggle) {
			gameSearch.isOpen = true;
			e.stopPropagation();
			return;
		}
	}

	function handleArrowKeys(e: KeyboardEvent) {
		if (gameSearch.isOpen) {
			if (e.code !== 'ArrowUp' && e.code !== 'ArrowDown') {
				return;
			}

			e.preventDefault();
			e.stopPropagation();

			const currentGameIndex = gameSearch.games.findIndex(
				(game) => game.key === selectedGame?.key,
			);
			let nextGame: SearchableGame | null = null;

			if (e.code === 'ArrowUp') {
				if (currentGameIndex === -1) {
					nextGame = gameSearch.games[0];
				} else {
					nextGame = gameSearch.games[currentGameIndex - 1];
				}
			} else if (e.code === 'ArrowDown') {
				nextGame = gameSearch.games[currentGameIndex + 1];
			}

			if (nextGame) {
				selectedGame = nextGame;
				const element = gamesContainer?.querySelector(`[data-game=game-${nextGame.key}]`);
				(element as HTMLAnchorElement)?.scrollIntoView({ behavior: 'instant' });
				return;
			}
		}
	}

	$effect(() => {
		if (gameSearch.isOpen) {
			input.focus();
		} else {
			setTimeout(() => {
				selectedGame = null;
				gameSearch.search = '';
				gamesContainer?.scrollTo({
					top: 0,
					behavior: 'instant',
				});
			}, 400);
		}
	});

	$effect(() => {
		if (gameSearch.games.length === 0) {
			selectedGame = null;
		} else if (!gameSearch.games.some((game) => game.key === selectedGame?.key)) {
			selectedGame = gameSearch.games[0];
		}
	});

	$effect(() => {
		// Reset request state when search changes or dialog closes
		if (!gameSearch.isOpen || gameSearch.games.length > 0) {
			requestState = 'idle';
			requestedGame = '';
		}
	});

	function navigateToGame(game: SearchableGame) {
		if (game.type === 'local') {
			gameSearch.isOpen = false;
			goto(game.url, { noScroll: true });
		} else {
			// open on new tab
			void Playlight.registerGameClick(game.id);
			window.open(game.url, '_blank');
		}
	}

	async function requestGame() {
		if (requestState === 'loading' || !gameSearch.search.trim()) {
			return;
		}

		requestState = 'loading';
		requestedGame = gameSearch.search.trim();

		try {
			await supabase
				.from('game_request')
				.insert({
					game: gameSearch.search.trim(),
				})
				.throwOnError();

			requestState = 'success';
		} catch (error: any) {
			requestState = 'error';
			handleError(error);

			setTimeout(() => {
				if (requestState === 'error') {
					requestState = 'idle';
				}
			}, 3000);
		}
	}
</script>

<svelte:window onkeydown={handleIsOpen} />

<Dialog
	centered
	noCloseIcon
	fullScreenOnMobile
	bind:isOpen={gameSearch.isOpen}
	modalBoxClass="p-0 flex flex-col lg:h-100"
>
	<label
		class="input input-lg w-full outline-none input-ghost my-2 shrink-0"
		aria-label="Search game"
	>
		<SearchIcon class="size-6 opacity-50 shrink-0" />

		<input
			name="search game"
			type="search"
			class="grow [&::-webkit-search-cancel-button]:hidden"
			placeholder="Search games"
			bind:this={input}
			bind:value={gameSearch.search}
			onfocus={() => (selectedGame = null)}
			onkeydown={(e) => {
				handleArrowKeys(e);
				handleIsOpen(e);

				if (e.code === 'Enter') {
					if (selectedGame) {
						navigateToGame(selectedGame);
					}
				}
			}}
		/>

		<div class="size-9 shrink-0">
			{#if selectedGame}
				{#if selectedGame.cover}
					<selectedGame.cover class="p-0 size-full aspect-square rounded-sm" />
				{:else if selectedGame.imgUrl}
					<img
						class="size-full rounded-sm"
						src={selectedGame.imgUrl}
						alt="{selectedGame.name} cover image"
					/>
				{/if}
			{/if}
		</div>
	</label>

	<ul
		class={cn('menu menu-lg w-full overflow-y-scroll flex flex-col flex-nowrap grow', {
			'max-lg:pb-[70vh]': gameSearch.games.length > 0,
		})}
		bind:this={gamesContainer}
	>
		{#if gameSearch.games.length > 0}
			<div class="flex flex-col">
				{#each gameSearch.games as game}
					<li>
						<a
							data-sveltekit-noscroll
							href={game.url}
							target={game.type === 'playlight' ? '_blank' : undefined}
							onfocus={() => {
								selectedGame = game;
							}}
							data-game="game-{game.key}"
							class={cn({
								'menu-active': game.key === selectedGame?.key,
							})}
							onclick={() => {
								if (game.type === 'local') {
									gameSearch.isOpen = false;
								} else {
									void Playlight.registerGameClick(game.id);
								}
							}}
							onkeydown={(e) => {
								if (e.code === 'Tab' || e.code === 'Escape') {
									return;
								}

								handleArrowKeys(e);
								handleIsOpen(e);

								e.stopPropagation();
								e.preventDefault();

								if (e.code === 'Enter' || e.code === 'Space') {
									if (selectedGame) {
										navigateToGame(selectedGame);
									}
								}
							}}
							onkeyup={(e) => {
								if (e.code === 'Tab' || e.code === 'Escape') {
									return;
								}

								e.stopPropagation();
							}}
						>
							{#if game.cover}
								<game.cover class="size-10 p-0 shrink-0 rounded-sm" />
							{:else if game.imgUrl}
								<img
									class="size-10 shrink-0 rounded-sm"
									src={game.imgUrl}
									alt="{game.name} cover image"
								/>
							{/if}

							{game.name}

							{#if favoriteGames.isFavorite(game.key as any)}
								<StarSolidIcon class="size-6 text-warning shrink-0" />
							{/if}

							{#if game.type === 'playlight'}
								<ExternalIcon class="size-5 shrink-0" />
							{/if}
						</a>
					</li>
				{/each}
			</div>
		{:else}
			<div class="relative flex flex-col items-center justify-center text-center p-2 grow">
				{#if requestState === 'success'}
					<div class="text-center">
						<div class="text-6xl mb-6">🥳</div>
						<h3 class="text-3xl font-semibold mb-2">Request submitted!</h3>
						<p class="text-base-content/70 max-w-sm text-lg mb-8">
							Thanks for the suggestion! We'll consider adding "{requestedGame}" to
							our games collection.
						</p>

						<button
							class="btn btn-primary"
							onclick={() => {
								gameSearch.search = '';
							}}
						>
							Got it
						</button>
					</div>
				{:else}
					{#if requestState === 'error'}
						<div class="absolute top-8" transition:fly={{ y: 20 }}>
							<Alert
								sentiment="error"
								title="Oops, something went wrong"
								description="Please try again later"
							/>
						</div>
					{/if}

					<div class="text-6xl mb-6">🎮</div>
					<h3 class="text-3xl font-semibold mb-2">Game not found</h3>
					<p class="text-base-content/70 max-w-sm text-lg mb-8">
						Can't find "{gameSearch.search}"? Let us know and we might build it for you!
					</p>
					<button
						class="btn btn-primary"
						disabled={requestState === 'loading'}
						onclick={requestGame}
					>
						{#if requestState === 'loading'}
							<span class="loading loading-spinner loading-sm"></span>
							Submitting...
						{:else}
							Request this game
						{/if}
					</button>
				{/if}
			</div>
		{/if}
	</ul>
</Dialog>
