<script lang="ts">
	import { onMount } from 'svelte';
	import type { Timer } from '$lib/util/Timer.svelte';
	import { onDestroy } from 'svelte';
	import Time from './Time.svelte';
	import type { Trend } from '@number-flow/svelte';

	interface Props {
		timer: Timer;
		class?: string;
		trend?: Trend;
		animated?: boolean;
	}

	let { timer = $bindable(), trend, animated, ...props }: Props = $props();

	let time = $state(timer.time);
	let intervalId = -1;

	function update() {
		time = timer.time;
	}

	update();

	onMount(() => {
		clearInterval(intervalId);

		intervalId = setInterval(update, 500) as unknown as number;
	});

	onDestroy(() => {
		clearInterval(intervalId);
	});
</script>

<Time {time} {trend} {animated} {...props} />
