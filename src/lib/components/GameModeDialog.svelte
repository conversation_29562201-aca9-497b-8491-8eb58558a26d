<script lang="ts" generics="Mode">
	import { cn } from '$lib/util/cn';
	import { fly } from 'svelte/transition';
	import Dialog from './Dialog.svelte';
	import CheckIcon from './Icons/CheckIcon.svelte';
	import CloseIcon from './Icons/CloseIcon.svelte';
	import { quadInOut } from 'svelte/easing';
	import type { Snippet } from 'svelte';

	interface Props<Mode> {
		isOpen?: boolean;
		mode: Mode;
		gameModes: GameMode[];
		onSave: (mode: Mode) => void;
	}

	interface GameMode {
		mode: Mode;
		image: Snippet<[{ selected: boolean }]>;
		title: string;
		description: string;
	}

	let { isOpen = $bindable(false), onSave, mode, gameModes }: Props<Mode> = $props();
	let selectedMode = $state(mode);

	$effect(function resetSelectedMode() {
		if (!isOpen) {
			selectedMode = mode;
		}
	});
</script>

<Dialog bind:isOpen>
	<form method="dialog">
		<button class="btn btn-sm btn-circle btn-ghost absolute right-4 top-4" aria-label="close">
			<CloseIcon />
		</button>
	</form>

	<h3 class="mb-4 text-lg font-bold">Game Mode</h3>

	<div class="grid w-full grid-cols-2 gap-2 sm:gap-4">
		{#each gameModes as { mode, image, title, description }}
			<div class="indicator flex w-auto">
				<button
					aria-label={title}
					class={cn(
						'cursor-pointer h-auto w-full flex flex-col items-center rounded-xl border-2 p-2 outline-hidden duration-300 transition-all sm:p-4',
						{
							'btn-outline border-primary': selectedMode === mode,
						},
					)}
					onclick={() => (selectedMode = mode)}
				>
					{#if selectedMode === mode}
						<div
							transition:fly={{ y: -10, duration: 300, easing: quadInOut }}
							class="indicator-item badge badge-primary pointer-events-none p-0"
						>
							<CheckIcon class="size-5" />
						</div>
					{/if}

					<figure class="p-2 pb-0 sm:p-0 grow">
						{@render image({ selected: selectedMode === mode })}
					</figure>
					<span class="mb-1 mt-2 text-lg font-semibold sm:text-xl">{title}</span>
					<span class="text-sm sm:text-sm">{description}</span>
				</button>
			</div>
		{/each}
	</div>

	<form method="dialog" class="modal-action">
		<button class="btn-ghost btn">Cancel</button>

		<button class="btn-primary btn" onclick={() => onSave(selectedMode)}>
			Save and play a new game
		</button>
	</form>
</Dialog>
