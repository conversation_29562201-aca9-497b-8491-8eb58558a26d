<script lang="ts">
	import CloseIcon from '$lib/components/Icons/CloseIcon.svelte';
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';
	import { portal } from 'svelte-portal';

	let dialog = $state<HTMLDialogElement>();

	interface Props {
		class?: string;
		modalBoxClass?: string;
		isOpen?: boolean;
		centered?: boolean;
		noCloseIcon?: boolean;
		fullScreenOnMobile?: boolean;
		children: Snippet;
		onClose?: () => void;
	}

	let {
		class: classFromProps = '',
		modalBoxClass = '',
		isOpen = $bindable(false),
		centered = false,
		noCloseIcon,
		fullScreenOnMobile,
		onClose,
		children,
	}: Props = $props();

	export function open() {
		if (dialog?.open) {
			return;
		}
		isOpen = true;
		dialog?.showModal?.();
	}

	export function close() {
		if (!dialog?.open) {
			return;
		}
		isOpen = false;
		dialog?.close?.();
		onClose?.();
	}

	$effect(() => {
		if (isOpen) {
			open();
		} else {
			close();
		}
	});
</script>

<dialog
	use:portal
	bind:this={dialog}
	role="dialog"
	class={cn(
		'modal select-none',
		centered ? 'modal-middle' : 'modal-bottom sm:modal-middle',
		classFromProps,
	)}
	onsubmit={(event) => {
		close();
		event.preventDefault();
	}}
	onclose={() => {
		isOpen = false;
		onClose?.();
	}}
	onpointerup={(event) => {
		event.stopPropagation();
	}}
	onpointermove={(event) => {
		event.stopPropagation();
	}}
	onpointerdown={(event) => {
		event.stopPropagation();
	}}
	onpointercancel={(event) => {
		event.stopPropagation();
	}}
>
	<div
		class={cn(
			'modal-box',
			{
				'max-lg:h-screen max-lg:max-h-screen max-lg:w-screen max-lg:max-w-screen max-lg:rounded-none':
					fullScreenOnMobile,
			},
			modalBoxClass,
		)}
	>
		{#if fullScreenOnMobile}
			<form method="dialog" class="flex p-4 items-center justify-end lg:hidden">
				<button
					class="btn btn-sm btn-circle btn-ghost outline-hidden"
					aria-label="Close dialog"
				>
					<CloseIcon />
				</button>
			</form>
		{/if}

		{#if !noCloseIcon}
			<form method="dialog">
				<button
					class="btn btn-sm btn-circle btn-ghost absolute right-4 top-4 outline-hidden"
					aria-label="Close dialog"
				>
					<CloseIcon />
				</button>
			</form>
		{/if}

		{@render children()}
	</div>

	<form method="dialog" class="modal-backdrop">
		<button>Close dialog</button>
	</form>
</dialog>
