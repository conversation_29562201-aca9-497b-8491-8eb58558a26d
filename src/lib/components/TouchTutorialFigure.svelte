<script lang="ts">
	import TouchIcon from '$lib/components/Icons/TouchIcon.svelte';
	import type { GridItem } from '$lib/models/GridItem';
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';

	interface Props {
		gameBeforeTouch: Snippet;
		gameAfterTouch: Snippet;
		touchContainerClass?: string;
		figcaption: string;
		touchIconPosition: GridItem;
		rows?: number;
		columns?: number;
		fadeTouch?: boolean;
		noTouch?: boolean;
	}

	let {
		gameBeforeTouch,
		gameAfterTouch,
		touchContainerClass,
		fadeTouch = false,
		noTouch = false,
		figcaption,
		touchIconPosition,
		rows = 3,
		columns = 3,
	}: Props = $props();
</script>

<figure class="flex flex-col items-center text-center w-full mx-auto z-0">
	<div class="size-full relative max-w-48">
		<div
			class={cn('absolute inset-0 grid z-10', touchContainerClass)}
			style="
		 grid-template-rows: repeat({rows}, minmax(0, 1fr)); grid-template-columns: repeat({columns}, minmax(0, 1fr));
		 "
		>
			<div
				class={cn('p-1 rounded-full bg-base-300 self-center justify-self-center', {
					hidden: noTouch,
					'fade-touch': fadeTouch,
				})}
				style="grid-column-start: {touchIconPosition.column +
					1}; grid-row-start: {touchIconPosition.row + 1};"
			>
				<TouchIcon class="size-8 text-base-content dark:text-white" />
			</div>
		</div>

		<div class="relative z-0 max-w-48 mx-auto flex items-center justify-center">
			{@render gameBeforeTouch()}

			<div class="absolute bg-base-100 inset-0 z-10 game-after">
				{@render gameAfterTouch()}
			</div>
		</div>
	</div>

	<figcaption>{figcaption}</figcaption>
</figure>

<style>
	@keyframes gameAfter {
		/* Finger up */
		0% {
			opacity: 0;
		}

		/* Finger down */
		25% {
			opacity: 0;
		}

		/* Finger up */
		50% {
			opacity: 1;
		}

		/* Finger down */
		75% {
			opacity: 1;
		}

		/* Finger up */
		100% {
			opacity: 0;
		}
	}

	.game-after {
		animation: gameAfter 2s ease-in-out infinite;
	}

	@keyframes fadeTouch {
		/* Finger up */
		0% {
			opacity: 1;
		}

		/* Finger down */
		25% {
			opacity: 1;
		}

		/* Finger up */
		50% {
			opacity: 0;
		}

		/* Finger down */
		75% {
			opacity: 0;
		}

		/* Finger up */
		90% {
			opacity: 0;
		}

		100% {
			opacity: 1;
		}
	}

	.fade-touch {
		animation: fadeTouch 2s ease-in-out infinite;
	}
</style>
