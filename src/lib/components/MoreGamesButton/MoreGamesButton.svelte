<script lang="ts">
	import { Playlight } from '$lib/stores/playlightSdk.svelte';
	import { cn } from '$lib/util/cn';
	import { SettingsManager } from '$lib/util/SettingsManager.svelte';
	import { onDestroy, onMount } from 'svelte';
	import JoystickIcon from '../Icons/JoystickIcon.svelte';

	interface Props {
		class?: string;
	}

	let { class: className }: Props = $props();

	let moreGamesSettings = new SettingsManager({
		key: 'more-games-button',
		defaultSettings: {
			clickCount: 0,
		},
	});

	onMount(() => {
		moreGamesSettings.load();
	});

	onDestroy(() => {
		moreGamesSettings.dispose();
	});
</script>

<button
	class={cn('btn relative', className)}
	onclick={() => {
		Playlight.sdk?.setDiscovery(true);
		moreGamesSettings.settings.clickCount += 1;
	}}
	aria-label="More Games"
>
	<JoystickIcon class="size-6" />
</button>
