<script lang="ts" module>
	import { getContext, setContext, type Component, type Snippet } from 'svelte';

	export interface Tab {
		icon?: Component;
		name: string;
		key: string;
	}

	export interface TabsContext {
		active: string;
	}

	export let tabsContext = 'tabs';
</script>

<script lang="ts">
	import { animate } from 'motion';
	import { cn } from '$lib/util/cn';

	let isFirstAnimation = true;
	let root = $state<HTMLElement>();
	let activeArea = $state<HTMLDivElement>();

	interface Props {
		active: string;
		bounce?: number;
		animationDuration?: number;
		class?: string;
		children: Snippet;
	}

	let {
		children,
		class: className,
		active,
		bounce = 0.5,
		animationDuration = 0.6,
	}: Props = $props();

	let contextValue = $state({ active });
	setContext(tabsContext, contextValue);

	let context = getContext<TabsContext>(tabsContext);

	function animateTab() {
		if (activeArea && active) {
			const activeTab = root?.querySelector(`[data-key=${active}]`) as HTMLElement;

			if (activeTab) {
				if (context) {
					context.active = active;
				}

				animate([
					[
						activeArea,
						{ x: activeTab.offsetLeft, width: activeTab.offsetWidth },
						{
							type: 'spring',
							duration: isFirstAnimation ? 0 : animationDuration,
							bounce,
						},
					],
				]);

				isFirstAnimation = false;
			}
		}
	}

	$effect(() => {
		animateTab();
	});
</script>

<svelte:window onresize={animateTab} />

<div class={cn('flex relative z-0', className)} bind:this={root}>
	<div
		class="absolute left-0 top-0 bottom-0 h-full bg-neutral dark:bg-base-300 z-0 rounded-full"
		bind:this={activeArea}
	></div>

	{@render children()}
</div>
