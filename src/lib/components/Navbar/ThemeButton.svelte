<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';
	import Dropdown from '../Dropdown/Dropdown.svelte';
	import DropdownButton from '../Dropdown/DropdownButton.svelte';
	import ThemeIcon from '../Icons/ThemeIcon/ThemeIcon.svelte';
	import ThemeContent from './ThemeContent.svelte';
	import DropdownContent from '../Dropdown/DropdownContent.svelte';

	let open = $state(false);

	interface Props {
		children?: Snippet;
		class?: string;
	}

	let { children, class: className }: Props = $props();
</script>

<Dropdown
	class={cn(
		'dropdown-end',
		{
			'z-30': open,
		},
		className,
	)}
	bind:open
>
	<DropdownButton
		class="btn-ghost btn-lg btn-circle"
		aria-label={open ? 'Close theme settings' : 'Open theme settings'}
	>
		{@render children?.()}
		<ThemeIcon class="size-8" />
	</DropdownButton>

	<DropdownContent class="w-60 navbar-dropdown-content">
		<ThemeContent />
	</DropdownContent>
</Dropdown>
