<script lang="ts">
	import { onMount } from 'svelte';
	import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
	import SoundIcon from '../Icons/SoundIcon.svelte';
	import PlayerControls from '../PlaylistPlayer/PlayerControls.svelte';
	import PlayerInfo from '../PlaylistPlayer/PlayerInfo.svelte';
	import { gameSoundManager } from '$lib/stores/gameSoundManager.svelte';
	import Dropdown from '../Dropdown/Dropdown.svelte';
	import { cn } from '$lib/util/cn';
	import DropdownButton from '../Dropdown/DropdownButton.svelte';
	import DropdownContent from '../Dropdown/DropdownContent.svelte';
	import DropdownItem from '../Dropdown/DropdownItem.svelte';
	import Toggle from '../Toggle/Toggle.svelte';
	import {
		backgroundSoundInstances,
		backgroundSoundManager,
	} from '$lib/stores/backgroundSoundManager.svelte';
	import BackgroundSounds from '../BackgroundSounds.svelte';
	import RadioIcon from '../Icons/RadioIcon.svelte';
	import PlaylistIcon from '../Icons/PlaylistIcon.svelte';

	interface Props {
		accentWhenPlaying?: boolean;
	}

	let { accentWhenPlaying = true }: Props = $props();

	let open = $state(false);

	function resetOrSetPlayerVolumeToZero() {
		if (musicPlayer.volume === 0) {
			musicPlayer.resetVolume();
		} else {
			musicPlayer.volume = 0;
		}
	}

	function resetOrSetGameSoundVolumeToZero() {
		if (gameSoundManager.volume === 0) {
			gameSoundManager.volume = gameSoundManager.defaultVolume;
		} else {
			gameSoundManager.volume = 0;
		}
	}

	function resetOrSetBackgroundSoundVolumeToZero() {
		if (backgroundSoundManager.volume === 0) {
			backgroundSoundManager.volume = backgroundSoundManager.defaultVolume;
		} else {
			backgroundSoundManager.volume = 0;
		}
	}

	onMount(() => {
		gameSoundManager.loadSettings();
	});
</script>

<Dropdown
	bind:open
	closeStrategy="backdrop-click"
	class={cn('dropdown-end', {
		'z-30': open,
	})}
>
	<DropdownButton
		class={cn('btn-circle btn-lg h-full transition-colors', {
			'btn-ghost':
				!musicPlayer.isPlaying &&
				backgroundSoundInstances.every((instance) => !instance.isPlaying),
			'btn-outline':
				musicPlayer.isPlaying ||
				backgroundSoundInstances.some((instance) => instance.isPlaying),
			'btn-accent':
				(musicPlayer.isPlaying ||
					backgroundSoundInstances.some((instance) => instance.isPlaying)) &&
				accentWhenPlaying,
		})}
		aria-label="Open sound settings"
	>
		<SoundIcon muted={gameSoundManager.muted && musicPlayer.muted} />
	</DropdownButton>

	<DropdownContent
		class="w-80 touch-pinch-zoom navbar-dropdown-content"
		ontouchmove={(e) => e.stopPropagation()}
		mobileVariant="screen-centered"
	>
		<DropdownItem>
			<Toggle
				aria-label={musicPlayer.muted ? 'Turn on music' : 'Turn off music'}
				bind:checked={musicPlayer.unmuted}
				onchange={(e) => {
					if ((e.target as HTMLInputElement).checked) {
						musicPlayer.play();
					}
				}}
			>
				Music
			</Toggle>
		</DropdownItem>

		<DropdownItem>
			<div
				class={cn('flex flex-col overflow-hidden transition-all duration-500', {
					'max-h-0 opacity-0 pointer-events-none': musicPlayer.muted,
					'max-h-72': !musicPlayer.muted,
				})}
			>
				<div class="flex items-center justify-between gap-2 py-2">
					<button
						class={cn('btn btn-md grow', {
							'btn-accent btn-outline': musicPlayer.player === 'playlist',
						})}
						aria-label="Play Lofi and Games playlist"
						onclick={() => (musicPlayer.player = 'playlist')}
					>
						<PlaylistIcon class="size-5" />
						Playlist
					</button>
					<button
						class={cn('btn btn-md grow', {
							'btn-accent btn-outline': musicPlayer.player === 'youtube',
						})}
						aria-label="Play radio"
						onclick={() => (musicPlayer.player = 'youtube')}
					>
						<RadioIcon class="size-6" />
						Radio
					</button>
				</div>

				<PlayerInfo />
				<PlayerControls />
				<div class="flex-center gap-2">
					<button
						class="btn-ghost btn-sm btn-circle btn"
						onclick={resetOrSetPlayerVolumeToZero}
						aria-label={musicPlayer.volume === 0 ? 'Unmute music' : 'Mute music'}
					>
						<SoundIcon muted={musicPlayer.volume === 0} />
					</button>
					<input
						aria-label="Music volume"
						type="range"
						bind:value={musicPlayer.volume}
						min="0"
						max="1"
						step="0.01"
						class="range range-xs"
					/>
				</div>
			</div>
		</DropdownItem>

		<li class="divider m-1"></li>

		<DropdownItem>
			<Toggle
				aria-label={gameSoundManager.unmuted
					? 'Turn off sound effects'
					: 'Turn on sound effects'}
				bind:checked={gameSoundManager.unmuted}
			>
				Sound Effects
			</Toggle>

			<div
				class={cn('overflow-hidden transition-all duration-500', {
					'max-h-0 opacity-0 pointer-events-none': gameSoundManager.muted,
					'max-h-12 py-2': !gameSoundManager.muted,
				})}
			>
				<div class="flex-center gap-2">
					<button
						class="btn-ghost btn-sm btn-circle btn"
						onclick={resetOrSetGameSoundVolumeToZero}
						aria-label={gameSoundManager.volume === 0
							? 'Unmute sound effects'
							: 'Mute sound effects'}
					>
						<SoundIcon muted={gameSoundManager.volume === 0} />
					</button>
					<input
						aria-label="Sound effects volume"
						type="range"
						bind:value={gameSoundManager.volume}
						min="0"
						max="1"
						step="0.01"
						class="range range-xs"
					/>
				</div>
			</div>
		</DropdownItem>

		<li class="divider m-1"></li>

		<DropdownItem>
			<Toggle
				aria-label={backgroundSoundManager.unmuted
					? 'Turn off background sounds'
					: 'Turn on background sounds'}
				bind:checked={backgroundSoundManager.unmuted}
			>
				Background Sounds
			</Toggle>

			<div
				class={cn('overflow-hidden transition-all duration-500', {
					'max-h-0 opacity-0 pointer-events-none': backgroundSoundManager.muted,
					'max-h-[750px] py-2': !backgroundSoundManager.muted,
				})}
			>
				<div class="flex-center gap-2 pb-2">
					<button
						class="btn-ghost btn-sm btn-circle btn"
						onclick={resetOrSetBackgroundSoundVolumeToZero}
						aria-label={backgroundSoundManager.volume === 0
							? 'Unmute background sounds'
							: 'Mute background sounds'}
					>
						<SoundIcon muted={backgroundSoundManager.volume === 0} />
					</button>
					<input
						type="range"
						bind:value={backgroundSoundManager.volume}
						min="0"
						max="1"
						step="0.01"
						class="range range-xs"
					/>
				</div>

				<BackgroundSounds />
			</div>
		</DropdownItem>
	</DropdownContent>
</Dropdown>
