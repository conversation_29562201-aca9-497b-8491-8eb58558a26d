<script lang="ts">
	import { fade } from 'svelte/transition';
	import { cn } from '$lib/util/cn';
	import DropdownItem from '../Dropdown/DropdownItem.svelte';
	import FeedbackModal from '../FeedbackModal.svelte';
	import FeedbackIcon from '../Icons/FeedbackIcon.svelte';
	import { notifications } from '$lib/stores/notifications.svelte';
	import StackItem from '../Stack/StackItem.svelte';
	import StackHeader from './StackHeader.svelte';

	interface Props {
		class?: string;
		onClose: () => void;
	}

	let { class: className, onClose }: Props = $props();

	let isFeedbackOpen = $state(false);
	let feedbackContext = $state('Notification');
</script>

<FeedbackModal context={feedbackContext} bind:isOpen={isFeedbackOpen} />

<StackItem class={cn('w-[calc(100vw-16px)] xs:w-96 pb-16 sm:pb-4', className)} alignment="end">
	<StackHeader title="Notifications" onBack={onClose}>
		{#snippet trailing()}
			<button
				class="btn btn-primary btn-sm"
				onclick={notifications.readAllNotifications}
				disabled={!notifications.hasUnreadNotifications}
			>
				Read all
			</button>
		{/snippet}
	</StackHeader>

	<div class="menu gap-2 w-full p-0">
		{#each notifications.all as notification}
			<DropdownItem>
				<div
					role="checkbox"
					tabindex="0"
					aria-checked={notification.read}
					class={cn(
						'w-full indicator p-4 pr-8 flex flex-col items-start cursor-pointer border-base-300 dark:border-base-content/10 border',
						{
							'bg-base-200 dark:bg-base-300 dark:hover:bg-base-content/10 dark:active:bg-base-content/15!':
								!notification.read,
						},
					)}
					onclick={() => notifications.readNotification(notification)}
					oncontextmenu={(event) => event.preventDefault()}
					onkeydown={(event) => {
						if (
							['INPUT', 'TEXTAREA', 'SELECT'].includes(
								(event.target as HTMLElement).tagName,
							)
						) {
							return;
						}

						const key = event.key.toLocaleLowerCase();

						if (key === 'enter' || key === ' ') {
							notifications.readNotification(notification);
						}
					}}
				>
					<h4 class="text-base font-bold">
						{notification.title}
					</h4>

					<p class="m-0 text-sm text-pretty">
						{notification.description}
					</p>

					<p class="text-xs text-pretty opacity-80">
						{Intl.DateTimeFormat('en', {
							month: 'long',
							day: 'numeric',
						}).format(new Date(notification.date + 'T00:00:00'))}
					</p>
					{#if notification.link}
						<a
							class="btn-block btn btn-outline btn-primary btn-sm mt-4"
							href={notification.link.url}
						>
							{notification.link.name}
						</a>
					{/if}

					{#if notification.feedback}
						<button
							class="btn btn-outline btn-sm btn-primary w-full mt-4"
							onclick={() => {
								isFeedbackOpen = true;
								feedbackContext = notification.feedbackContext ?? 'Notification';
							}}
						>
							<FeedbackIcon class="size-6" />
							Give Feedback
						</button>
					{/if}

					{#if notification.cta}
						<div class="mt-4 w-full">
							<notification.cta onclick={onClose} />
						</div>
					{/if}

					{#if !notification.read}
						<div
							class="inline-grid *:[grid-area:1/1] indicator-item indicator-top indicator-end top-4 right-4"
							transition:fade={{ duration: 300 }}
						>
							<div class="status status-primary animate-ping"></div>
							<div class="status status-primary"></div>
						</div>
					{/if}
				</div>
			</DropdownItem>
		{/each}
	</div>
</StackItem>
