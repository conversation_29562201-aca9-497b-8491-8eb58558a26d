<script lang="ts" module>
	export type NavbarVariant = 'normal' | 'transparent' | 'glass';

	export interface NavbarProps {
		class?: string;
		variant?: NavbarVariant;
		soundButtonAccentWhenPlaying?: boolean;
		profileButtonProps?: ProfileButtonProps;
		children?: Snippet;
	}
</script>

<script lang="ts">
	import Logo from '$lib/components/Logo.svelte';
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';
	import ProfileButton, { type ProfileButtonProps } from './ProfileButton.svelte';
	import SoundButton from './SoundButton.svelte';
	import ThemeButton from './ThemeButton.svelte';

	let {
		class: classFromProps = '',
		variant = 'normal',
		soundButtonAccentWhenPlaying = true,
		profileButtonProps,
		children,
	}: NavbarProps = $props();

	const menuItems = [
		{
			text: 'Solitaire',
			href: '/solitaire',
		},
		{
			text: 'Tents',
			href: '/tents',
		},
		{
			text: 'Breakout',
			href: '/breakout',
		},
		{
			text: '2048',
			href: '/2048',
		},
		{
			text: 'Minesweeper',
			href: '/minesweeper',
		},
		{
			text: 'All Games',
			href: '/',
		},
	];
</script>

<nav
	class={cn(
		'navbar relative flex flex-row justify-between items-center',
		{
			'bg-base-100': variant === 'normal',
			'before:glass before:inset-0 before:absolute before:shadow-none before:-z-10':
				variant === 'glass',
		},
		classFromProps,
	)}
>
	<div class="navbar-start gap-2">
		<a aria-label="Home" href="/" class="btn-ghost btn btn-lg gap-2 py-1 text-xl px-2">
			<Logo class="h-8 md:h-full" />
		</a>
	</div>

	<div class="navbar-center flex justify-center relative h-10">
		{#if children}
			{@render children()}
		{:else}
			{#each menuItems as item}
				<a class="btn-ghost btn hidden lg:flex" href={item.href}>{item.text}</a>
			{/each}
		{/if}
	</div>

	<div class="navbar-end gap-2">
		<SoundButton accentWhenPlaying={soundButtonAccentWhenPlaying} />

		<ThemeButton class="hidden sm:block" />

		<ProfileButton {...profileButtonProps} />
	</div>
</nav>
