<script lang="ts">
	import { theme, type Theme, type ThemeBrightness } from '$lib/stores/theme.svelte';
	import { cn } from '$lib/util/cn';
	import type { Component } from 'svelte';
	import DropdownItem from '../Dropdown/DropdownItem.svelte';
	import Sun from '../Icons/ThemeIcon/Sun.svelte';
	import Toggle from '../Toggle/Toggle.svelte';
	import Moon from '../Icons/ThemeIcon/Moon.svelte';
	import Collapse from '../Collapse/Collapse.svelte';
	import CollapseContent from '../Collapse/CollapseContent.svelte';
	import { siteSounds } from '$lib/stores/siteSounds.svelte';
	import { switchSoundAttachment } from '$lib/attachments/switchSoundAttachment.svelte';

	interface Props {
		class?: string;
	}

	let { class: className }: Props = $props();

	const sounds = {
		onPlayOnSound: () => siteSounds.lightTheme.play(),
		onPlayOffSound: () => siteSounds.darkTheme.play(),
	};

	interface ThemeItem {
		name: string;
		icon: Component;
		theme: Theme;
		brightness: ThemeBrightness;
		active: boolean;
	}
</script>

<DropdownItem class={cn('mb-2', className)}>
	<Toggle
		aria-label={theme.isColorBlind ? 'Turn off color blind' : 'Turn on color blind'}
		bind:checked={theme.isColorBlind}>Color blind</Toggle
	>
</DropdownItem>

<ul class="menu p-0 w-full">
	<Collapse open={['light', 'light-classic'].includes(theme.value)}>
		{@render DropdownThemeItem({
			name: 'Light',
			icon: Sun,
			theme: 'light',
			brightness: 'light',
			active: ['light', 'light-classic'].includes(theme.value),
		})}

		<CollapseContent asSettings>
			<div class="join grid grid-cols-2 py-2">
				<button
					class={cn('join-item btn', {
						'btn-active btn-neutral': theme.value === 'light',
					})}
					onclick={() => (theme.value = 'light')}
					aria-label="Switch to default light theme"
					{@attach switchSoundAttachment({ action: 'on', ...sounds })}
				>
					Default
				</button>
				<button
					class={cn('join-item btn', {
						'btn-active btn-neutral': theme.value === 'light-classic',
					})}
					onclick={() => (theme.value = 'light-classic')}
					aria-label="Switch to classic light theme"
					{@attach switchSoundAttachment({ action: 'on', ...sounds })}
				>
					Classic
				</button>
			</div>
		</CollapseContent>
	</Collapse>

	<Collapse open={['dark', 'dark-classic'].includes(theme.value)}>
		{@render DropdownThemeItem({
			name: 'Dark',
			icon: Moon,
			theme: 'dark',
			brightness: 'dark',
			active: ['dark', 'dark-classic'].includes(theme.value),
		})}

		<CollapseContent asSettings>
			<div class="join grid grid-cols-2 py-2">
				<button
					class={cn('join-item btn', {
						'btn-active btn-neutral': theme.value === 'dark',
					})}
					onclick={() => (theme.value = 'dark')}
					aria-label="Switch to default dark theme"
					{@attach switchSoundAttachment({ action: 'off', ...sounds })}
				>
					Default
				</button>
				<button
					class={cn('join-item btn', {
						'btn-active btn-primary': theme.value === 'dark-classic',
					})}
					onclick={() => (theme.value = 'dark-classic')}
					aria-label="Switch to classic dark theme"
					{@attach switchSoundAttachment({ action: 'off', ...sounds })}
				>
					Classic
				</button>
			</div>
		</CollapseContent>
	</Collapse>
</ul>

{#snippet DropdownThemeItem(themeItem: ThemeItem)}
	<DropdownItem>
		<button
			class="flex flex-row gap-4 items-center justify-start relative py-3"
			class:menu-active={themeItem.active}
			{@attach switchSoundAttachment({
				action: themeItem.brightness === 'light' ? 'on' : 'off',
				...sounds,
			})}
			onclick={() => {
				theme.value = themeItem.theme;
			}}
		>
			<div class="size-7 flex items-center justify-center">
				<themeItem.icon class="size-6" />
			</div>
			<span>{themeItem.name}</span>
		</button>
	</DropdownItem>
{/snippet}
