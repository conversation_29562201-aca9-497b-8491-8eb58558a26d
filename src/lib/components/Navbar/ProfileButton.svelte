<script lang="ts" module>
	export type MenuItem = {
		Icon: Component;
		title: string;
		iconClass?: string;
		link?: string;
		hasIndicator?: boolean;
		onclick?: () => void;
		tag?: string;
		extra?: string;
	};

	export interface ProfileButtonProps {
		extraMenuItems?: MenuItem[];
	}
</script>

<script lang="ts">
	import { cn } from '$lib/util/cn';
	import { onMount, type Component } from 'svelte';
	import Dropdown from '../Dropdown/Dropdown.svelte';
	import DropdownButton from '../Dropdown/DropdownButton.svelte';
	import DropdownContent from '../Dropdown/DropdownContent.svelte';
	import DropdownItem from '../Dropdown/DropdownItem.svelte';
	import BellIcon from '../Icons/BellIcon.svelte';
	import PeopleIcon from '../Icons/PeopleIcon.svelte';
	import ThemeIcon from '../Icons/ThemeIcon/ThemeIcon.svelte';
	import StackComponent from '../Stack/Stack.svelte';
	import StackItem from '../Stack/StackItem.svelte';
	import { authClient } from '$lib/auth/client';
	import { goto } from '$app/navigation';
	import NotificationsStack from './NotificationsStack.svelte';
	import ThemeStack from './ThemeStack.svelte';
	import { notifications } from '$lib/stores/notifications.svelte';
	import { fade } from 'svelte/transition';
	import SignOutIcon from '../Icons/SignOutIcon.svelte';
	import LoggedUserAvatar from '../LoggedUserAvatar.svelte';
	import AccountSettingsIcon from '../Icons/AccountSettingsIcon.svelte';
	import EditAccountIcon from '../Icons/EditAccountIcon.svelte';
	import FeedbackIcon from '../Icons/FeedbackIcon.svelte';
	import FeedbackModal from '../FeedbackModal.svelte';
	import JoystickIcon from '../Icons/JoystickIcon.svelte';
	import { Playlight } from '$lib/stores/playlightSdk.svelte';
	import { gameSearch } from '$lib/stores/gameSearch.svelte';
	import SearchIcon from '../Icons/SearchIcon.svelte';
	import { isMacLike } from '$lib/functions/isMacLike';

	type TabName = 'home' | 'notifications' | 'theme' | 'preferences';

	type Stack = Omit<MenuItem, 'link'> & {
		name: TabName;
		Content: Component<{
			onClose: () => void;
		}>;
	};

	function changeTab(tabName: TabName | null) {
		activeTab = stacks.find((tab) => tab.name === tabName) ?? null;
	}

	const stacks: Stack[] = $derived.by(() => {
		const siteStacks: Stack[] = [
			{
				Icon: BellIcon,
				title: 'Notifications',
				name: 'notifications',
				Content: NotificationsStack,
				hasIndicator: notifications.hasUnreadNotifications,
			},
			{
				Icon: ThemeIcon,
				title: 'Theme',
				name: 'theme',
				Content: ThemeStack,
				iconClass: 'size-6',
			},
			// {
			// 	Icon: SettingsIcon,
			// 	title: 'Preferences',
			// 	name: 'preferences',
			// 	Content: UserPreferencesStack,
			// },
		];

		return siteStacks;
	});

	let open = $state(false);
	let activeTab = $state<Stack | null>(null);
	let isFeedbackOpen = $state(false);
	let Content = $derived(activeTab?.Content);
	let isMac = $state(false);

	const session = authClient.useSession();

	$effect.pre(() => {
		if (!open) {
			activeTab = null;
		}
	});

	onMount(() => {
		isMac = isMacLike();
	});
	let { extraMenuItems }: ProfileButtonProps = $props();
</script>

<FeedbackModal bind:isOpen={isFeedbackOpen} />

{#snippet Item({ Icon, title, iconClass, link, hasIndicator, onclick, tag, extra }: MenuItem)}
	<DropdownItem>
		<button
			class="flex flex-row gap-4 items-center justify-start py-3 indicator w-full"
			onclick={() => {
				if (link) {
					open = false;
					setTimeout(() => {
						goto(link);
					}, 300);
				} else {
					onclick?.();
				}
			}}
		>
			<div class="size-7 flex items-center justify-center">
				<Icon class={cn('size-5', iconClass)} />
			</div>

			<span class="grow">
				{title}
			</span>

			{#if hasIndicator}
				<div class="inline-grid *:[grid-area:1/1]" transition:fade={{ duration: 300 }}>
					<div class="status status-primary animate-ping"></div>
					<div class="status status-primary"></div>
				</div>
			{/if}

			{#if tag}
				<div class="badge badge-sm badge-primary justify-self-end">
					{tag}
				</div>
			{/if}

			{#if extra}
				<div class="text-sm opacity-50">{extra}</div>
			{/if}
		</button>
	</DropdownItem>
{/snippet}

{#snippet StackButton(props: Stack)}
	{@render Item({
		...props,
		onclick: () => changeTab(props.name),
	})}
{/snippet}

<Dropdown
	class={cn('dropdown-end', {
		'z-30': open,
	})}
	bind:open
>
	<DropdownButton class="btn-ghost btn-circle btn-lg" aria-label="Open menu">
		<div class="indicator">
			{#if notifications.hasUnreadNotifications}
				<div
					class="inline-grid *:[grid-area:1/1] indicator-item indicator-top inticator-end"
					transition:fade={{ duration: 300 }}
				>
					<div class="status status-primary animate-ping"></div>
					<div class="status status-primary"></div>
				</div>
			{/if}

			<LoggedUserAvatar
				class={cn({
					'size-8': $session.data?.user,
					'size-6': !$session.data?.user,
				})}
			/>
		</div>
	</DropdownButton>

	<DropdownContent class="min-w-72 bg-transparent! shadow-none! p-0! m-0!">
		<StackComponent>
			<StackItem
				class="menu w-full navbar-dropdown-content"
				collapsedHeight={$session.data?.user ? 64 : undefined}
			>
				<!-- User is signed in -->
				{#if $session.data?.user}
					<div class="flex gap-4 items-center py-3 px-4">
						<LoggedUserAvatar class="size-12 text-lg" />

						<div class="flex flex-col">
							<div class="text-lg font-medium line-clamp-1 break-all">
								{$session.data.user.name}
							</div>
							<div class="text-base line-clamp-1 break-all">
								{#if $session.data.user.displayUsername}
									@{$session.data.user.displayUsername}
								{:else}
									No username
								{/if}
							</div>
						</div>
					</div>

					<div class="divider m-0"></div>
				{/if}

				{#if extraMenuItems}
					{#each extraMenuItems as extraItem}
						{@render Item({
							...extraItem,
							onclick: () => {
								open = false;
								extraItem.onclick?.();
							},
						})}
					{/each}
				{/if}

				{@render StackButton(stacks.find((stack) => stack.name === 'notifications')!)}

				<!-- User is not signed in -->
				{#if !$session.data?.user}
					{@render Item({
						Icon: PeopleIcon,
						title: 'Sign in',
						link: '/signin',
					})}
				{/if}

				<!-- User is signed in -->
				{#if $session.data?.user}
					<!-- TODO: Create profile page -->
					<!-- {@render Item({
						Icon: PeopleIcon,
						title: 'Profile',
						link: '/profile',
					})} -->

					{@render Item({
						Icon: AccountSettingsIcon,
						title: 'Account Settings',
						link: '/account',
					})}

					{#if $session.data?.user.role === 'admin'}
						{@render Item({
							Icon: EditAccountIcon,
							title: 'Admin Panel',
							link: '/admin',
						})}
					{/if}
				{/if}

				{@render StackButton(stacks.find((stack) => stack.name === 'theme')!)}

				<!-- {@render StackButton(stacks.find((stack) => stack.name === 'preferences')!)} -->

				{@render Item({
					Icon: FeedbackIcon,
					title: 'Send Feedback',
					onclick() {
						isFeedbackOpen = true;
						open = false;
					},
				})}

				{@render Item({
					Icon: SearchIcon,
					title: 'Search Games',
					extra: `(${isMac ? '⌘' : 'Ctrl'} + K)`,
					onclick() {
						gameSearch.isOpen = true;
						open = false;
					},
				})}

				{@render Item({
					Icon: JoystickIcon,
					title: 'More Games',
					onclick() {
						Playlight.sdk?.setDiscovery(true);
						open = false;
					},
				})}

				<!-- User is signed in -->
				{#if $session.data?.user}
					<div class="divider m-0"></div>

					{@render Item({
						Icon: SignOutIcon,
						title: 'Sign out',
						onclick: () => {
							authClient.signOut();
						},
					})}
				{/if}
			</StackItem>

			{#if Content}
				<Content onClose={() => changeTab(null)} />
			{/if}
		</StackComponent>
	</DropdownContent>
</Dropdown>
