<script lang="ts">
	import { createEventDispatcher } from 'svelte';

	type Key =
		| 'enter'
		| 'backspace'
		| 'q'
		| 'w'
		| 'e'
		| 'r'
		| 't'
		| 'y'
		| 'u'
		| 'i'
		| 'o'
		| 'p'
		| 'a'
		| 's'
		| 'd'
		| 'f'
		| 'g'
		| 'h'
		| 'j'
		| 'k'
		| 'l'
		| 'z'
		| 'x'
		| 'c'
		| 'v'
		| 'b'
		| 'n'
		| 'm';

	interface Props {
		class?: string;
		keysClasses?: Partial<Record<Key, string>>;
	}

	let { class: classFromProps = '', keysClasses = {} }: Props = $props();

	const onlyLettersRegex = /[a-zA-Z]/;
	const dispatch = createEventDispatcher();

	function handleKey(key: string) {
		key = key.toLocaleLowerCase();

		if (
			(key.length === 1 && onlyLettersRegex.test(key)) ||
			key === 'enter' ||
			key === 'backspace'
		) {
			dispatch('keypress', key);
		}

		if (key === '⌫') {
			dispatch('keypress', 'backspace');
		}
	}

	function handleClick(event: MouseEvent) {
		const key = (event.target as HTMLElement)?.innerText?.toLocaleLowerCase();

		if (key) {
			handleKey(key);
		}
	}

	function handleKeyDown(event: KeyboardEvent) {
		const key = event.key;

		handleKey(key);
	}
</script>

<svelte:window onkeydown={handleKeyDown} />

<div class="grid w-full grid-rows-3 gap-1 {classFromProps}">
	<div class="grid grid-cols-10 gap-1">
		<button
			onclick={handleClick}
			class="btn-md btn border-0 transition-colors xs:btn-md sm:btn-md {keysClasses.q}"
		>
			Q
		</button>
		<button
			onclick={handleClick}
			class="btn-md btn border-0 transition-colors xs:btn-md sm:btn-md {keysClasses.w}"
		>
			W
		</button>
		<button
			onclick={handleClick}
			class="btn-md btn border-0 transition-colors xs:btn-md sm:btn-md {keysClasses.e}"
		>
			E
		</button>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.r}">R</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.t}">T</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.y}">Y</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.u}">U</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.i}">I</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.o}">O</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.p}">P</button
		>
	</div>
	<div class="grid grid-cols-10 gap-1">
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.a}">A</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.s}">S</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.d}">D</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.f}">F</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.g}">G</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.h}">H</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.j}">J</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.k}">K</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.l}">L</button
		>
		<button
			onclick={handleClick}
			aria-label="delete"
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.backspace}"
		>
			⌫
		</button>
	</div>
	<div class="grid grid-cols-9 gap-1">
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.z}">Z</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.x}">X</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.c}">C</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.v}">V</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.b}">B</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.n}">N</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn transition-colors xs:btn-md sm:btn-md {keysClasses.m}">M</button
		>
		<button
			onclick={handleClick}
			class="btn-md btn col-span-2 transition-colors xs:btn-md sm:btn-md {keysClasses.enter}"
		>
			Enter
		</button>
	</div>
</div>
