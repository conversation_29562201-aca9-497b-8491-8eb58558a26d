<script lang="ts">
	import PlayerControls from '$lib/components/PlaylistPlayer/PlayerControls.svelte';
	import PlayerInfo from '$lib/components/PlaylistPlayer/PlayerInfo.svelte';
	import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
	import PlayIcon from '../../Icons/PlayIcon.svelte';

	interface Props {
		onPlay: () => void;
		pauseKey: string | null;
	}

	let { onPlay, pauseKey }: Props = $props();
</script>

<div class="flex items-center justify-center flex-col w-full p-6 xs:px-10">
	<div class="flex items-center justify-between w-full">
		<div>
			<p class="text-lg">Game Paused</p>
			{#if pauseKey !== null}
				<p class="text-xs mt-1 opacity-80 hidden lg:block">
					Press <kbd class="kbd kbd-sm mx-1">{pauseKey}</kbd> to toggle pause
				</p>
			{/if}
		</div>

		<button
			aria-label="Play game"
			class="btn btn-circle btn-primary"
			onclick={(event) => {
				event.stopPropagation();
				onPlay();
			}}
		>
			<PlayIcon variant="play" />
		</button>
	</div>

	{#if musicPlayer.unmuted}
		<div class="divider"></div>

		<div class="w-full flex flex-col">
			<PlayerInfo class="pt-0 px-0" noPlaylistButton />
			<PlayerControls class="pb-0 px-0" />
		</div>
	{/if}
</div>
