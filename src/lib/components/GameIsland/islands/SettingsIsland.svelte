<script>
	import Collapse from '$lib/components/Collapse/Collapse.svelte';
	import CollapseContent from '$lib/components/Collapse/CollapseContent.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
</script>

<div class="pb-8 pt-12">
	<div class="text-center text-2xl font-semibold mb-4 px-8">Settings</div>

	<Collapse class="px-8" open={islandSettings.settings.leaderboards}>
		<Toggle bind:checked={islandSettings.settings.leaderboards} aria-label="Leaderboards">
			Leaderboards
		</Toggle>

		<CollapseContent asSettings>
			<Toggle
				bind:checked={islandSettings.settings.showLeaderboardsOnGameOver}
				aria-label="Show on game over"
			>
				Show on game over
			</Toggle>
		</CollapseContent>
	</Collapse>

	<Collapse class="px-8" open={islandSettings.settings.stats}>
		<Toggle bind:checked={islandSettings.settings.stats} aria-label="Game stats"
			>Game Stats</Toggle
		>

		<CollapseContent asSettings>
			<Toggle
				bind:checked={islandSettings.settings.animatedStats}
				aria-label="Number animations"
			>
				Number Animations
			</Toggle>
		</CollapseContent>
	</Collapse>

	<Toggle bind:checked={islandSettings.settings.dailyGames} aria-label="Daily Games" class="px-8">
		Daily Games
	</Toggle>

	<Collapse open class="py-2 flex flex-col gap-2 px-8">
		<span class="text-sm">When opening, what should show up?</span>

		<CollapseContent asSettings>
			<label class="cursor-pointer gap-4 w-full flex justify-between items-center py-2">
				<span class="text-sm">Stats</span>
				<input
					type="radio"
					name="promote-strategy"
					class="radio"
					checked={islandSettings.settings.initialTab === 'stats' ||
						!islandSettings.settings.leaderboards}
					onchange={() => {
						islandSettings.settings.initialTab = 'stats';
					}}
				/>
			</label>

			<label class="cursor-pointer gap-4 w-full flex justify-between items-center py-2">
				<span class="text-sm">Leaderboards</span>
				<input
					type="radio"
					name="promote-strategy"
					class="radio"
					checked={islandSettings.settings.initialTab === 'leaderboard' &&
						islandSettings.settings.leaderboards}
					onchange={() => {
						islandSettings.settings.initialTab = 'leaderboard';
					}}
					disabled={!islandSettings.settings.leaderboards}
				/>
			</label>
		</CollapseContent>
	</Collapse>

	<Toggle
		bind:checked={islandSettings.settings.showExitIntent}
		aria-label="Show leaving already message"
		class="px-8"
	>
		Show "Leaving already?" message
	</Toggle>

	<Toggle
		bind:checked={islandSettings.settings.autoPauseOnWindowBlur}
		aria-label="Pause game when switching tabs or apps"
		class="px-8"
	>
		Pause game when switching tabs or apps
	</Toggle>
</div>
