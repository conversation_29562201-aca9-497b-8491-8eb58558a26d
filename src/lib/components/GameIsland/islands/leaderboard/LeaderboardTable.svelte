<script lang="ts">
	import { cn } from '$lib/util/cn';
	import PlayerRow from './PlayerRow.svelte';
	import type { Leaderboard, LeaderboardRecord } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import Avatar from '$lib/components/Avatar.svelte';
	import Time from '$lib/components/Time.svelte';
	import { fade, fly, slide } from 'svelte/transition';
	import { quadInOut } from 'svelte/easing';
	import Alert from '$lib/components/Alert.svelte';
	import { authClient } from '$lib/auth/client';
	import { isToday } from '$lib/functions/date/isToday';
	import EmptyLeaderboard from './EmptyLeaderboard.svelte';
	import LeaderboardLoading from './LeaderboardLoading.svelte';

	interface Props {
		leaderboard: Leaderboard;
		noFloatingPlayer?: boolean;
		noTopPlayers?: boolean;
		noScoreAlert?: boolean;
		noAnimations?: boolean;
		limit?: number;
		class?: string;
	}

	let {
		leaderboard,
		noFloatingPlayer = false,
		noTopPlayers = false,
		noScoreAlert = false,
		noAnimations = false,
		limit = -1,
		class: className,
	}: Props = $props();

	const session = authClient.useSession();

	const numberFormat = Intl.NumberFormat();
	const topPlayerAnimationDelay = 180;
	const topPlayerAnimationDuration = 600;

	let shouldShowFloatingPlayer = $derived.by(() => {
		if (noFloatingPlayer || !leaderboard.board.user) {
			return false;
		}

		if (leaderboard.board.user.rank < 5) {
			return false;
		}

		return true;
	});
</script>

{#snippet TopPlayer({ rank, score, moves, time, name, image }: LeaderboardRecord)}
	<div class="flex flex-col gap-2 items-center justify-end min-h-56">
		<div
			in:fade|global={{
				duration: noAnimations
					? 0
					: topPlayerAnimationDelay + topPlayerAnimationDuration * 0.8,
				easing: quadInOut,
			}}
			class="flex flex-col gap-1 items-center"
		>
			<Avatar user={{ name, image }} class="size-12" />
			<div class="text-sm line-clamp-1 wrap-anywhere">
				{name}
			</div>
			<div class="flex items-center gap-1 text-sm">
				<div>{numberFormat.format(score)}</div>

				{#if leaderboard.hasMoves}
					<div>{numberFormat.format(moves || 0)}</div>
				{:else}
					<Time variant="static" {time} animated={false} />
				{/if}
			</div>
		</div>

		<div
			class={cn(
				'bg-linear-180 to-transparent w-full flex items-end justify-center p-2 rounded-tr-2xl rounded-tl-2xl',
				{
					'from-yellow-400 h-28': rank === 0,
					'from-gray-500 h-20': rank === 1,
					'from-orange-500 h-12': rank === 2,
				},
			)}
			in:slide|global={{
				axis: 'y',
				delay: noAnimations ? 0 : topPlayerAnimationDelay * (rank + 1) * 0.9,
				duration: noAnimations ? 0 : topPlayerAnimationDuration,
				easing: quadInOut,
			}}
		>
			<div
				in:fly|global={{
					y: 10,
					delay: noAnimations ? 0 : topPlayerAnimationDelay * (rank + 1) * 1.5 + 100,
					duration: noAnimations ? 0 : topPlayerAnimationDuration,
					easing: quadInOut,
				}}
				class="text-2xl font-black text-base-content"
			>
				{rank + 1}
			</div>
		</div>
	</div>
{/snippet}

{#snippet loading()}
	<LeaderboardLoading amountOfSkeletons={leaderboard.range === 'around-player' ? 3 : 12} />
{/snippet}

<div class={className}>
	{#if $session.isPending}
		{@render loading()}
	{:else if !$session.data?.user}
		<div class="relative w-full">
			<div class="blur-sm opacity-40">
				{#if noTopPlayers === false}
					<div class="px-8 grid grid-cols-3 gap-2">
						{@render TopPlayer({
							rank: 1,
							score: 22222,
							time: 20000,
							moves: 100,
							name: 'User 2',
							image: '',
						})}
						{@render TopPlayer({
							rank: 0,
							score: 111111,
							time: 10000,
							moves: 99,
							name: 'User 1',
							image: '',
						})}
						{@render TopPlayer({
							rank: 2,
							score: 3333,
							time: 30000,
							moves: 100,
							name: 'User 3',
							image: '',
						})}
					</div>
				{/if}

				<div
					in:fly|global={{
						y: 20,
						duration: noAnimations ? 0 : 300,
						delay: noAnimations
							? 0
							: noTopPlayers
								? 300
								: topPlayerAnimationDuration * 1.6,
						easing: quadInOut,
					}}
					class="overflow-x-auto"
				>
					<table class="table table-zebra">
						<thead>
							<tr>
								<th>#</th>
								<th>Player</th>
								<th>Score</th>
								{#if leaderboard.hasMoves}
									<th>Moves</th>
								{/if}
								{#if leaderboard.hasLevel}
									<th>Level</th>
								{/if}
								{#if leaderboard.hasTime}
									<th>Time</th>
								{/if}
							</tr>
						</thead>
						<tbody class="relative">
							{#each { length: leaderboard.range === 'top-20' ? 5 : 2 } as _, rank}
								<PlayerRow
									{rank}
									score={rank * 10}
									moves={rank * 10}
									level={rank * 10}
									time={rank * 1e5}
									hasMoves
									hasTime
									username={`Player ${rank + 1}`}
								/>
							{/each}
						</tbody>
					</table>
				</div>
			</div>

			<div
				class="sticky bottom-[50vh] justify-center z-10 flex flex-col items-center text-center px-8 gap-8 translate-y-1/2"
			>
				<div>
					<p class="mb-1 text-2xl font-medium">Sign in to join the leaderboards</p>
					<p class="text-sm font-normal">Compete for a top 3 spot!</p>
				</div>

				<a href="/signin" class="btn btn-secondary btn-outline grow rounded-full w-full">
					Join the leaderboards
				</a>
			</div>
		</div>
	{:else}
		{#if noTopPlayers === false}
			{#if leaderboard.loadState === 'success' && leaderboard.board.records.length > 0}
				{@const topPlayers = leaderboard.board.records.slice(0, 3)}

				<div class="px-8 grid grid-cols-3 gap-2">
					{#if topPlayers[1]}
						{@render TopPlayer(topPlayers[1])}
					{:else}
						<div></div>
					{/if}

					{#if topPlayers[0]}
						{@render TopPlayer(topPlayers[0])}
					{:else}
						<div></div>
					{/if}

					{#if topPlayers[2]}
						{@render TopPlayer(topPlayers[2])}
					{:else}
						<div></div>
					{/if}
				</div>
			{/if}
		{/if}

		{#if leaderboard.loadState === 'loading' || leaderboard.loadState === 'idle'}
			{@render loading()}
		{:else if leaderboard.loadState === 'error'}
			<Alert sentiment="error" title="Failed to get the leaderboard" class="mx-8" />
		{:else if leaderboard.loadState === 'success'}
			{#if leaderboard.board.records.length > 0}
				<div class="relative w-full">
					<div
						in:fly|global={{
							y: 20,
							duration: noAnimations ? 0 : 300,
							delay: noAnimations
								? 0
								: noTopPlayers
									? 300
									: topPlayerAnimationDuration * 1.6,
							easing: quadInOut,
						}}
						class="overflow-x-auto"
					>
						<table class="table table-zebra">
							<thead>
								<tr>
									<th>#</th>
									<th>Player</th>
									<th>Score</th>
									{#if leaderboard.hasMoves}
										<th>Moves</th>
									{/if}
									{#if leaderboard.hasLevel}
										<th>Level</th>
									{/if}
									{#if leaderboard.hasTime}
										<th>Time</th>
									{/if}
								</tr>
							</thead>
							<tbody class="relative">
								{#each leaderboard.board.records.filter(Boolean) as record, index}
									{@const isPlayer = record.rank === leaderboard.board.user?.rank}

									{#if limit === -1 || index < limit}
										<PlayerRow
											rank={record.rank}
											score={record.score}
											time={record.time}
											moves={record.moves}
											level={record.level}
											hasLevel={leaderboard.hasLevel}
											hasMoves={leaderboard.hasMoves}
											hasTime={leaderboard.hasTime}
											username={record.name}
											{isPlayer}
										/>
									{/if}
								{/each}
							</tbody>
						</table>
					</div>

					{#if shouldShowFloatingPlayer && leaderboard.board.user}
						{@const playerRecord = leaderboard.board.user}

						<div
							in:fly|global={{
								y: 20,
								duration: noAnimations ? 0 : 300,
								delay: noAnimations
									? 0
									: noTopPlayers
										? 300
										: topPlayerAnimationDuration * 1.6 + 200,
								easing: quadInOut,
							}}
							class={cn(
								'sticky bottom-8 mx-4 mt-4 rounded-2xl flex shadow-lg justify-between font-bold border border-base-300 overflow-hidden',
								{
									'bg-secondary text-base-300': playerRecord.rank > 2,
									'bg-yellow-400 text-base-300': playerRecord.rank === 0,
									'bg-gray-500': playerRecord.rank === 1,
									'bg-orange-500 text-base-300': playerRecord.rank === 2,
								},
							)}
						>
							<table class="table">
								<tbody>
									<PlayerRow
										rank={playerRecord.rank}
										score={playerRecord.score}
										moves={playerRecord.moves}
										time={playerRecord.time}
										username={playerRecord.name}
										level={playerRecord.level}
										hasMoves={leaderboard.hasMoves}
										hasLevel={leaderboard.hasLevel}
										hasTime={leaderboard.hasTime}
										isPlayer
									/>
								</tbody>
							</table>
						</div>
					{/if}

					{#if noScoreAlert === false}
						<div class="px-8 pt-4">
							<Alert
								description={leaderboard.order === 'lower-first'
									? 'Lower scores rank higher'
									: 'Higher scores rank better'}
							/>
						</div>
					{/if}
				</div>
			{:else}
				{@const isLeaderboardForToday = isToday(leaderboard.date)}

				<EmptyLeaderboard {isLeaderboardForToday} />
			{/if}
		{/if}
	{/if}
</div>
