<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { EmblaCarouselType } from 'embla-carousel';
	import emblaCarouselSvelte from 'embla-carousel-svelte';
	import WheelGesturesPlugin from 'embla-carousel-wheel-gestures';
	import type { Snippet } from 'svelte';
	// import type { Action } from 'svelte/action';

	interface Props {
		children: Snippet;
		class?: string;
		alignment: 'left' | 'right';
	}

	let { children, class: className, alignment }: Props = $props();

	let emblaApi = $state<EmblaCarouselType>();

	function handleScroll() {
		if (emblaApi) {
			if (alignment === 'right') {
				emblaApi.scrollTo(Math.max(0, emblaApi.slideNodes().length - 1), false);
			} else {
				emblaApi.scrollTo(0, false);
			}
		}
	}

	$effect.pre(() => {
		handleScroll();
	});
	// const scrollRightAction: Action<HTMLElement, undefined> = () => {
	// 	$effect.pre(() => {
	// 		// Track tab change
	// 		tab;

	// 		if (emblaApi) {
	// 			tick().then(() => {
	// 				if (emblaApi) {
	// 					emblaApi.scrollTo(Math.max(0, emblaApi.slideNodes().length - 1), true);
	// 				}
	// 			});
	// 		}
	// 	});
	// };
</script>

<div
	class={cn(
		'embla overflow-hidden relative w-full',
		{
			// Left
			'before:w-8 before:h-full before:bg-linear-to-r before:from-black before:to-black/0 before:absolute before:left-0 before:top-0 before:bottom-0 before:z-10 before:pointer-events-none': true,
			// Right
			'after:w-8 after:h-full after:bg-linear-to-l after:from-black after:to-black/0 after:absolute after:right-0 after:top-0 after:bottom-0 after:z-10 after:pointer-events-none': true,
		},
		className,
	)}
	use:emblaCarouselSvelte={{
		options: {
			align: 'center',
			axis: 'x',
			dragFree: true,
		},
		plugins: [WheelGesturesPlugin()],
	}}
	onemblaInit={(event) => {
		emblaApi = event.detail;
	}}
>
	<!-- use:scrollRightAction -->
	<div class="embla__container flex gap-2 [&_>*]:first-of-type:ml-8 [&_>*]:last-of-type:mr-8">
		{@render children()}
	</div>
</div>
