<script lang="ts">
	import { cn } from '$lib/util/cn';
	import { quadInOut } from 'svelte/easing';
	import { fly } from 'svelte/transition';

	interface Props {
		amountOfSkeletons: number;
		class?: string;
	}

	let { amountOfSkeletons, class: className }: Props = $props();
</script>

<div
	in:fly|global={{
		y: 20,
		duration: 300,
		delay: 0,
		easing: quadInOut,
	}}
	class={cn('flex flex-col gap-2 px-8', className)}
>
	{#each { length: amountOfSkeletons }}
		<div class="skeleton h-11 w-full"></div>
	{/each}
</div>
