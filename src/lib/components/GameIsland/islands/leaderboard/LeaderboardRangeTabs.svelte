<script lang="ts" module>
	export let tabs = ['daily', 'weekly', 'monthly', 'yearly', 'all-time'] as const;

	export type LeaderboardRangeTab = (typeof tabs)[number];
</script>

<script lang="ts">
	import { cn } from '$lib/util/cn';
	import { canShowLeaderboardYears } from './canShowLeaderboardYears';

	interface Props {
		tab: LeaderboardRangeTab;
		disabled?: boolean;
		onTabChange?: (tab: LeaderboardRangeTab) => void;
	}

	let { tab = $bindable(), onTabChange, disabled }: Props = $props();

	function changeTab(newTab: LeaderboardRangeTab) {
		tab = newTab;
		onTabChange?.(tab);
	}
</script>

<div
	class={cn('tabs tabs-box tabs-sm bg-transparent grid grid-cols-4 gap-2', {
		'grid-cols-5': canShowLeaderboardYears(),
	})}
>
	<button
		class={cn('tab', {
			'tab-active': tab === 'daily',
		})}
		{disabled}
		onclick={() => changeTab('daily')}
	>
		Day
	</button>
	<button
		class={cn('tab', {
			'tab-active': tab === 'weekly',
		})}
		{disabled}
		onclick={() => changeTab('weekly')}
	>
		Week
	</button>
	<button
		class={cn('tab', {
			'tab-active': tab === 'monthly',
		})}
		{disabled}
		onclick={() => changeTab('monthly')}
	>
		Month
	</button>
	{#if canShowLeaderboardYears()}
		<button
			class={cn('tab', {
				'tab-active': tab === 'yearly',
			})}
			{disabled}
			onclick={() => changeTab('yearly')}
		>
			Year
		</button>
	{/if}
	<button
		class={cn('tab', {
			'tab-active': tab === 'all-time',
		})}
		{disabled}
		onclick={() => changeTab('all-time')}
	>
		All <span class="hidden sm:inline">&nbsp;Time </span>
	</button>
</div>
