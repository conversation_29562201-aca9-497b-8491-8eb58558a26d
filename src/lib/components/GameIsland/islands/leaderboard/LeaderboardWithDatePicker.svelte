<script lang="ts">
	import type { Leaderboard as LeaderboardModel } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import { tick, untrack } from 'svelte';
	import LeaderboardTable from './LeaderboardTable.svelte';
	import { startOfMonth } from '$lib/functions/date/startOfMonth';
	import { authClient } from '$lib/auth/client';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DotsHorizontalIcon from '$lib/components/Icons/DotsHorizontalIcon.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';
	import FeedbackModal from '$lib/components/FeedbackModal.svelte';
	import { isSameDay } from '$lib/functions/date/isSameDay';
	import { cn } from '$lib/util/cn';
	import { getLeaderboardDays } from '../../../../util/Leaderboard/getLeaderboardDays';
	import { minLeaderboardYear } from './canShowLeaderboardYears';
	import LeaderboardRangeTabs, { type LeaderboardRangeTab } from './LeaderboardRangeTabs.svelte';
	import { getLeaderboardWeeklyRanges } from '$lib/util/Leaderboard/getLeaderboardWeeklyRanges';
	import { getLeaderboardMonths } from '$lib/util/Leaderboard/getLeaderboardMonths';
	import { getLeaderboardYears } from '$lib/util/Leaderboard/getLeaderboardYears';
	import DayButton from './DayButton.svelte';
	import ScrollableContainer from './ScrollableContainer.svelte';

	interface Props {
		leaderboard: LeaderboardModel;
	}

	let { leaderboard: leaderboardFromProps }: Props = $props();

	const today = new Date();

	let tab = $state<LeaderboardRangeTab>('daily');

	let leaderboard = $derived.by(() => {
		if (tab === 'daily') {
			return leaderboardFromProps.clone({
				range: 'top-50',
				frequency: 'daily',
			});
		}

		if (tab === 'weekly') {
			return leaderboardFromProps.clone({
				range: 'top-50',
				frequency: 'weekly',
			});
		}

		if (tab === 'monthly') {
			return leaderboardFromProps.clone({
				range: 'top-100',
				frequency: 'monthly',
			});
		}

		if (tab === 'yearly') {
			return leaderboardFromProps.clone({
				range: 'top-100',
				frequency: 'yearly',
			});
		}

		return leaderboardFromProps.clone({
			range: 'top-100',
			frequency: 'all-time',
		});
	});

	// Daily
	const amountOfDays = 14;
	const days = getLeaderboardDays({ leaderboard: leaderboardFromProps, amountOfDays });
	let selectedDate = $state(untrack(() => leaderboardFromProps.date));

	// Weekly
	const amountOfWeeks = 5;
	const weeklyItems = getLeaderboardWeeklyRanges({
		leaderboard: leaderboardFromProps,
		amountOfWeeks,
	});
	let selectedWeek = $state(weeklyItems[weeklyItems.length - 1]);

	// Monthly
	const amountOfMonths = 12;
	const months = getLeaderboardMonths({ leaderboard: leaderboardFromProps, amountOfMonths });
	let selectedMonth = $state(untrack(() => startOfMonth(leaderboardFromProps.date)));

	// Yearly
	const amountOfYears = Math.min(5, new Date().getFullYear() - minLeaderboardYear + 1);
	const years = getLeaderboardYears({ leaderboard: leaderboardFromProps, amountOfYears });
	let selectedYear = $state(untrack(() => startOfMonth(leaderboardFromProps.date)));

	// All Time
	let isReportModalOpen = $state(false);
	let session = authClient.useSession();

	$effect(function loadLeaderboardAccordingToTab() {
		// Track
		tab;
		selectedDate;
		selectedMonth;
		selectedWeek;
		selectedYear;
		leaderboard;

		untrack(async () => {
			/** Prevent Svelte from throwing an error when trying to render the leaderboard */
			await tick();

			if (tab === 'daily') {
				leaderboard.load(selectedDate);
			} else if (tab === 'weekly') {
				leaderboard.load(selectedWeek.from);
			} else if (tab === 'monthly') {
				leaderboard.load(selectedMonth);
			} else if (tab === 'yearly') {
				leaderboard.load(selectedYear);
			} else if (tab === 'all-time') {
				leaderboard.load();
			}
		});
	});
</script>

{#if $session.data?.user}
	<div>
		<div class="px-4 pb-4">
			<LeaderboardRangeTabs bind:tab />
		</div>

		{#if tab === 'daily'}
			<ScrollableContainer alignment="right">
				{#each days as date}
					<DayButton
						{date}
						isSelected={isSameDay(date, selectedDate)}
						onclick={() => {
							selectedDate = date;
						}}
					/>
				{/each}
			</ScrollableContainer>
		{:else if tab === 'weekly'}
			<ScrollableContainer alignment="right">
				{#each weeklyItems as range}
					<button
						class={cn(
							'btn flex flex-col items-center justify-center gap-0 btn-square h-auto w-24 embla__slide',
							{
								'btn-primary': selectedWeek.from === range.from,
							},
						)}
						onclick={() => {
							selectedWeek = range;
						}}
					>
						<div
							class="text-xs leading-snug font-normal border-b border-b-base-content/15 w-full py-0.5 flex items-center justify-center gap-1"
						>
							{Intl.DateTimeFormat('en', {
								month: 'short',
							}).format(range.from)}

							{#if range.from.getMonth() !== range.to.getMonth()}
								<span class="text-xs">{'/'}</span>

								{Intl.DateTimeFormat('en', {
									month: 'short',
								}).format(range.to)}
							{/if}
						</div>

						<div class="text-lg px-2 pt-1 flex items-center justify-center gap-1">
							{#if today >= range.from && today <= range.to}
								<span class="text-sm"> This week </span>
							{:else}
								{range.from.getDate()} <span class="text-xs">{'→'}</span>
								{range.to.getDate()}
							{/if}
						</div>
					</button>
				{/each}
			</ScrollableContainer>
		{:else if tab === 'monthly'}
			<ScrollableContainer alignment="right">
				{#each months as month}
					<button
						class={cn('btn w-24 embla__slide', {
							'btn-primary': selectedMonth.getMonth() === month.getMonth(),
						})}
						onclick={() => {
							selectedMonth = month;
						}}
					>
						{Intl.DateTimeFormat('en', {
							month: 'long',
						}).format(month)}
					</button>
				{/each}
			</ScrollableContainer>
		{:else if tab === 'yearly'}
			<ScrollableContainer alignment="right">
				{#each years as year}
					<button
						class={cn('btn w-24 embla__slide', {
							'btn-primary': selectedYear.getFullYear() === year.getFullYear(),
						})}
						onclick={() => {
							selectedYear = year;
						}}
					>
						{Intl.DateTimeFormat('en', {
							year: 'numeric',
						}).format(year)}
					</button>
				{/each}
			</ScrollableContainer>
		{/if}
	</div>
{/if}

<div class="relative">
	{#if $session.data?.user && leaderboard.loadState === 'success' && leaderboard.board.records.length > 0}
		<Dropdown class="dropdown-left absolute top-0 right-8">
			<DropdownButton
				aria-label={'Show or hide leaderboard options'}
				class="btn-ghost btn-sm btn-circle"
			>
				<DotsHorizontalIcon class="size-5" />
			</DropdownButton>

			<DropdownContent menu class="w-52">
				<DropdownItem>
					<button
						class="flex flex-row gap-2 items-center justify-start py-3 indicator w-full"
						onclick={() => (isReportModalOpen = true)}
					>
						<WarningSolidIcon class="size-5" />
						Report leaderboard
					</button>
				</DropdownItem>
			</DropdownContent>
		</Dropdown>

		<FeedbackModal
			bind:isOpen={isReportModalOpen}
			context="Report Leaderboard"
			extra={leaderboard.url}
			title="Report Leaderboard"
			description="If you think there is something wrong with this leaderboard, let us know!"
			feedbackLabel="Why are you reporting this leaderboard?"
			feedbackPlaceholder="e.g. Someone hacked it! Please ban @sombra"
			feedbackErrorLabel="Please enter a reason for reporting this leaderboard"
		/>
	{/if}

	<LeaderboardTable {leaderboard} />
</div>
