<script lang="ts">
	import CheckIcon from '$lib/components/Icons/CheckIcon.svelte';
	import { isToday } from '$lib/functions/date/isToday';
	import { isYesterday } from '$lib/functions/date/isYesterday';
	import { cn } from '$lib/util/cn';

	interface Props {
		date: Date;
		isSelected?: boolean;
		onclick?: () => void;
		class?: string;
		disabled?: boolean;
		isMarked?: boolean;
	}

	let { date, isSelected, onclick, class: className, disabled, isMarked }: Props = $props();
</script>

<div class={cn('indicator', className)}>
	<button
		class={cn(
			'btn flex flex-col items-center justify-center gap-0 btn-square h-auto w-16 grow embla__slide relative',
			{
				'btn-primary': isSelected,
			},
		)}
		{onclick}
		{disabled}
	>
		<div
			class="text-xs leading-snug font-normal border-b border-b-base-content/15 w-full py-0.5"
		>
			{Intl.DateTimeFormat('en', {
				month: 'short',
			}).format(date)}
		</div>

		<div class="text-xl px-2 pt-1 relative">
			{date.getDate()}
		</div>

		<div class="text-[0.69rem] leading-snug font-normal px-2 pb-1">
			{#if isToday(date)}
				Today
			{:else if isYesterday(date)}
				Yesterday
			{:else}
				{Intl.DateTimeFormat('en', {
					weekday: 'short',
				}).format(date)}
			{/if}
		</div>
	</button>

	{#if isMarked}
		<div class="indicator-item rounded-full bg-success">
			<CheckIcon class="size-6" />
		</div>
	{/if}
</div>
