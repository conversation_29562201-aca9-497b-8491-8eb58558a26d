<script lang="ts">
	import ScrollableContainer from './leaderboard/ScrollableContainer.svelte';
	import type { Stats } from '$lib/util/Stats.svelte';
	import Stat from './Stat.svelte';
	import { Leaderboard as LeaderboardModel } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import { authClient } from '$lib/auth/client';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import DotsHorizontalIcon from '$lib/components/Icons/DotsHorizontalIcon.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import EyeIcon from '$lib/components/Icons/EyeIcon.svelte';
	import { SettingsManager } from '$lib/util/SettingsManager.svelte';
	import LeaderboardRangeTabs, {
		type LeaderboardRangeTab,
	} from './leaderboard/LeaderboardRangeTabs.svelte';
	import { untrack } from 'svelte';
	import { getLeaderboardDays } from '../../../util/Leaderboard/getLeaderboardDays';
	import { getLeaderboardWeeklyRanges } from '$lib/util/Leaderboard/getLeaderboardWeeklyRanges';
	import { getLeaderboardMonths } from '$lib/util/Leaderboard/getLeaderboardMonths';
	import { getLeaderboardYears } from '$lib/util/Leaderboard/getLeaderboardYears';
	import PlayerRow from './leaderboard/PlayerRow.svelte';
	import { fly } from 'svelte/transition';
	import { quadInOut } from 'svelte/easing';
	import EmptyLeaderboard from './leaderboard/EmptyLeaderboard.svelte';
	import LeaderboardLoading from './leaderboard/LeaderboardLoading.svelte';
	import { DailyGame } from '$lib/util/DailyGame.svelte';
	import DayButton from './leaderboard/DayButton.svelte';
	import { subtract } from '$lib/functions/date/subtract';
	import { add } from '$lib/functions/date/add';
	import { atMidnight } from '$lib/functions/date/atMidnight';
	import { isSameDay } from '$lib/functions/date/isSameDay';
	import WarningSolidIcon from '$lib/components/Icons/WarningSolidIcon.svelte';

	interface Props {
		stats: Stats<any, any>;
		leaderboard?: LeaderboardModel;
		noButton?: boolean;
		isDailyGame?: boolean;
		dailyGame?: DailyGame;
		onPlayNewGame: () => void;
		onPlayDailyGame: (date: Date) => void;
	}

	let {
		stats,
		leaderboard: leaderboardFromProps,
		noButton,
		isDailyGame,
		dailyGame,
		onPlayNewGame,
		onPlayDailyGame,
	}: Props = $props();

	let visibleStats = $derived.by(() => {
		const statsToShow = [...stats.pinnedStats].filter(Boolean);

		if (statsToShow.length >= 2) {
			return statsToShow;
		}

		const otherStats = [...Object.values(stats.liveStats), ...Object.values(stats.fixedStats)];

		let index = 0;

		while (statsToShow.length !== 2 && index < otherStats.length) {
			const stat = otherStats[index];

			if (!statsToShow.find((pinned) => pinned?.name === stat.name)) {
				statsToShow.push(stat);
			}

			index += 1;
		}

		return statsToShow;
	});

	const session = authClient.useSession();
	let newGameIslandSettings = new SettingsManager({
		key: 'new-game-island',
		defaultSettings: {
			canShowJoinLeaderboardCTA: true,
		},
	});

	let tab = $state<LeaderboardRangeTab>('daily');

	$effect.pre(() => {
		newGameIslandSettings.load();
	});

	let leaderboard = $state(untrack(() => leaderboardFromProps));

	function onTabChange(newTab: LeaderboardRangeTab) {
		tab = newTab;

		if (tab === 'daily') {
			leaderboard = leaderboardFromProps?.clone({
				range: 'around-player',
				frequency: 'daily',
			});
			leaderboard?.load(getLeaderboardDays({ leaderboard, amountOfDays: 1 })[0]);
		} else if (tab === 'weekly') {
			leaderboard = leaderboardFromProps?.clone({
				range: 'around-player',
				frequency: 'weekly',
			});
			leaderboard?.load(
				getLeaderboardWeeklyRanges({ leaderboard, amountOfWeeks: 1 })[0].from,
			);
		} else if (tab === 'monthly') {
			leaderboard = leaderboardFromProps?.clone({
				range: 'around-player',
				frequency: 'monthly',
			});
			leaderboard?.load(getLeaderboardMonths({ leaderboard, amountOfMonths: 1 })[0]);
		} else if (tab === 'yearly') {
			leaderboard = leaderboardFromProps?.clone({
				range: 'around-player',
				frequency: 'yearly',
			});
			leaderboard?.load(getLeaderboardYears({ leaderboard, amountOfYears: 1 })[0]);
		} else if (tab === 'all-time') {
			leaderboard = leaderboardFromProps?.clone({
				range: 'around-player',
				frequency: 'all-time',
			});
			leaderboard?.load();
		}
	}

	function hideJoinLeaderboardCTA(e: MouseEvent) {
		newGameIslandSettings.settings.canShowJoinLeaderboardCTA = false;
		e.stopPropagation();
	}

	let maxDailyGamesToShow = 10;
	let selectedNextDailyGame = $state<Date>(new Date());

	// Daily game
	let isLoadingDailyGame = $derived(dailyGame?.isFetching);
	let availableDates = $derived.by(() => {
		if (isDailyGame && dailyGame && dailyGame.dateOfLastSuccessfullyPlayedGame) {
			const availableDates: Date[] = [];
			const date = atMidnight(dailyGame.dateOfLastSuccessfullyPlayedGame);
			const today = atMidnight(new Date());
			const tomorrow = add(today, 1);
			const firstAvailableGameDate = atMidnight(dailyGame.firstAvailableGameDate);
			const playedDays = dailyGame.playedDays;

			let previousDate: Date | null = subtract(date, 1);

			let nextDate: Date | null = add(date, 1);

			while (nextDate < tomorrow && availableDates.length < maxDailyGamesToShow) {
				if (!playedDays.includes(DailyGame.getFormattedDate(nextDate))) {
					availableDates.push(nextDate);
				}

				nextDate = add(nextDate, 1);
			}

			while (
				previousDate > firstAvailableGameDate &&
				availableDates.length < maxDailyGamesToShow
			) {
				if (!playedDays.includes(DailyGame.getFormattedDate(previousDate))) {
					availableDates.unshift(previousDate);
				}

				previousDate = subtract(previousDate, 1);
			}

			return availableDates;
		}

		return [];
	});

	$effect(() => {
		// Track
		availableDates;

		untrack(() => {
			selectedNextDailyGame = availableDates[availableDates.length - 1];
		});
	});
</script>

<div class="grid grid-cols-1 py-8">
	{#if islandSettings.settings.stats}
		<div class="grid grid-cols-2 gap-2 px-8 mb-4">
			{#each visibleStats as stat}
				{#if stat}
					<Stat
						{stat}
						firstAnimationDelay={550}
						newBest={stat.isNewBest}
						animated={islandSettings.settings.animatedStats}
					/>
				{/if}
			{/each}
		</div>
	{/if}

	{#if leaderboard && islandSettings.settings.leaderboards && islandSettings.settings.showLeaderboardsOnGameOver}
		{#if $session.data?.user}
			<div class="px-4 pb-2">
				<LeaderboardRangeTabs
					{tab}
					{onTabChange}
					disabled={leaderboard.sendScoreState === 'error' ||
						leaderboard.sendScoreState === 'loading'}
				/>
			</div>

			<div class="overflow-clip min-h-[192px] relative">
				{#if leaderboard.sendScoreState === 'success' || leaderboard.sendScoreState === 'idle'}
					{#if leaderboard.loadState === 'success' || leaderboard.loadState === 'idle'}
						{#if leaderboard.board.records.length === 0}
							<div
								class="size-full absolute inset-0 flex items-center justify-center"
							>
								<EmptyLeaderboard isLeaderboardForToday />
							</div>
						{:else}
							<table class="table table-zebra absolute inset-0">
								<thead>
									<tr>
										<th>#</th>
										<th>Player</th>
										<th>Score</th>
										{#if leaderboard.hasMoves}
											<th>Moves</th>
										{/if}
										{#if leaderboard.hasLevel}
											<th>Level</th>
										{/if}
										{#if leaderboard.hasTime}
											<th>Time</th>
										{/if}
									</tr>
								</thead>
								<tbody class="relative">
									{#each leaderboard.board.records as record}
										{@const isPlayer =
											record.rank === leaderboard.board.user?.rank}

										<PlayerRow
											rank={record.rank}
											score={record.score}
											time={record.time}
											moves={record.moves}
											level={record.level}
											hasMoves={leaderboard.hasMoves}
											hasLevel={leaderboard.hasLevel}
											hasTime={leaderboard.hasTime}
											username={record.name}
											{isPlayer}
										/>
									{/each}
								</tbody>
							</table>
						{/if}
					{:else if leaderboard.loadState === 'error'}
						<div
							transition:fly={{ y: -20, duration: 300, easing: quadInOut }}
							class="flex flex-col size-full items-center px-8 pt-2"
						>
							<div class="flex flex-col w-full items-center justify-center grow">
								<WarningSolidIcon class="size-12 text-error mb-2" />

								<h2 class="text-xl font-bold text-center max-w-64">
									Couldn't get the leaderboard
								</h2>
							</div>

							<button
								class="btn w-full rounded-full mt-6"
								onclick={() => leaderboard?.load()}
							>
								Try again
							</button>
						</div>
					{:else if leaderboard.loadState === 'loading'}
						<div
							transition:fly={{ y: 20, duration: 300, easing: quadInOut }}
							class="size-full flex items-center justify-center absolute inset-0"
						>
							<LeaderboardLoading amountOfSkeletons={3} class="grow" />
						</div>
					{/if}
				{:else if leaderboard.sendScoreState === 'error'}
					<div
						transition:fly={{ y: -20, duration: 300, easing: quadInOut }}
						class="flex flex-col size-full items-center px-8 pt-2"
					>
						<div class="flex flex-col w-full items-center justify-center grow">
							<WarningSolidIcon class="size-12 text-error mb-2" />

							<h2 class="text-xl font-bold text-center max-w-60">
								Couldn't save your score
							</h2>
						</div>

						<button
							class="btn w-full rounded-full mt-6"
							onclick={() => leaderboard?.retrySendingNewPlayerScore()}
						>
							Try saving again
						</button>
					</div>
				{:else if leaderboard.sendScoreState === 'loading'}
					<div
						transition:fly={{ y: 20, duration: 300, easing: quadInOut }}
						class="size-full flex items-center justify-center absolute inset-0"
					>
						<LeaderboardLoading amountOfSkeletons={3} class="grow" />
					</div>
				{/if}
			</div>
		{:else if newGameIslandSettings.settings.canShowJoinLeaderboardCTA}
			<div class="justify-center z-10 flex flex-col items-center text-center px-8 gap-4">
				<div class="relative w-full">
					<Dropdown class="absolute -top-7 -right-4 dropdown-left">
						<DropdownButton
							class="btn btn-sm btn-ghost btn-circle"
							aria-label="open leaderboard menu"
						>
							<DotsHorizontalIcon class="size-5" />
						</DropdownButton>

						<DropdownContent menu class="w-52">
							<DropdownItem>
								<button onclick={hideJoinLeaderboardCTA}>
									<EyeIcon variant="invisible" class="size-5" />
									Hide this message
								</button>
							</DropdownItem>
						</DropdownContent>
					</Dropdown>

					<p class="mb-1 text-2xl font-medium">Sign in to join the leaderboards</p>
					<p class="text-sm font-normal">Compete for a top 3 spot!</p>
				</div>

				<a href="/signin" class="btn btn-secondary btn-outline w-full rounded-full">
					Join the leaderboards
				</a>
			</div>
		{/if}
	{/if}

	{#if !noButton}
		<div class="flex flex-col gap-4 grow px-8 pt-2">
			<div class="flex gap-4">
				{#if isDailyGame && dailyGame && availableDates.length > 0 && selectedNextDailyGame}
					<div class="w-full flex flex-col gap-4">
						<p class="font-medium">Coming up next</p>

						<ScrollableContainer alignment="right">
							{#each availableDates as date}
								<DayButton
									{date}
									isSelected={selectedNextDailyGame
										? isSameDay(date, selectedNextDailyGame)
										: false}
									onclick={() => {
										selectedNextDailyGame = date;
									}}
									disabled={isLoadingDailyGame}
								/>
							{/each}
						</ScrollableContainer>

						<div class="flex flex-col mt-4">
							<button
								class="btn btn-primary rounded-full grow"
								onclick={(event) => {
									event.stopPropagation();
									onPlayDailyGame(selectedNextDailyGame);
								}}
								disabled={isLoadingDailyGame}
							>
								{#if isLoadingDailyGame}
									<span class="loading loading-spinner"></span>
								{/if}

								Play {Intl.DateTimeFormat('en', {
									day: 'numeric',
									month: 'long',
								}).format(selectedNextDailyGame)} game
							</button>

							<div class="divider">or</div>

							<button
								class="btn btn-soft rounded-full grow"
								onclick={(event) => {
									event.stopPropagation();
									onPlayNewGame();
								}}
								disabled={isLoadingDailyGame}
							>
								Play a new game
							</button>
						</div>
					</div>
				{:else}
					<div class="w-full flex flex-col gap-8">
						{#if isDailyGame && dailyGame}
							<div class="flex flex-col gap-1 items-center">
								<div class="text-5xl mb-4">🥳</div>
								<h2 class="text-xl font-bold text-center">All caught up!</h2>
								<p class="text-md text-center opacity-80">
									A new daily game arrives tomorrow
								</p>
							</div>
						{/if}

						<div class="flex gap-4 items-center">
							<button
								class="btn btn-primary rounded-full grow"
								onclick={(event) => {
									event.stopPropagation();
									onPlayNewGame();
								}}
							>
								Play a new game
							</button>
						</div>
					</div>
				{/if}
			</div>
		</div>
	{/if}
</div>
