/**
 * Simple text shuffler/unshuffler using a character mapping dictionary
 */
export class Mc {
	// private sMap: Map<string, string> = new Map();
	private uMap: Map<string, string> = new Map();

	constructor(seed: string = '') {
		this.generateMaps(seed);
	}

	/**
	 * Generate shuffle and unshuffle character mappings based on a seed
	 */
	private generateMaps(seed: string): void {
		// Create a list of all printable ASCII characters (32-126)
		const chars: string[] = [];
		for (let i = 32; i <= 126; i++) {
			chars.push(String.fromCharCode(i));
		}

		// Create a shuffled version based on the seed
		const shuffledChars = this.sArray([...chars], seed);

		// Create bidirectional mappings
		for (let i = 0; i < chars.length; i++) {
			// this.sMap.set(chars[i], shuffledChars[i]);
			this.uMap.set(shuffledChars[i], chars[i]);
		}
	}

	/**
	 * Shuffle an array based on a seed string (deterministic)
	 */
	private sArray<T>(array: T[], seed: string): T[] {
		const result = [...array];
		let hash = this.hashCode(seed);

		for (let i = result.length - 1; i > 0; i--) {
			// Generate pseudo-random index based on hash
			hash = (hash * 1664525 + 1013904223) % 2147483647;
			const j = Math.abs(hash) % (i + 1);
			[result[i], result[j]] = [result[j], result[i]];
		}

		return result;
	}

	/**
	 * Generate a hash code from a string
	 */
	private hashCode(str: string): number {
		let hash = 0;
		for (let i = 0; i < str.length; i++) {
			const char = str.charCodeAt(i);
			hash = (hash << 5) - hash + char;
			hash = hash & hash; // Convert to 32-bit integer
		}
		return Math.abs(hash);
	}

	/**
	 * Shuffle text using the character mapping
	 */
	// s(text: string): string {
	// 	return text
	// 		.split('')
	// 		.map((char) => this.sMap.get(char) || char)
	// 		.join('');
	// }

	/**
	 * Unshuffle text using the reverse character mapping
	 */
	u(t: string): string {
		return t
			.split('')
			.map((char) => this.uMap.get(char) || char)
			.join('');
	}

	/**
	 * Check if text can be unshuffled (all characters are in the mapping)
	 */
	// canUnshuffle(text: string): boolean {
	// 	return text.split('').every((char) => this.uMap.has(char));
	// }

	/**
	 * Get the current shuffle mapping (for debugging)
	 */
	// getShuffleMap(): Map<string, string> {
	// 	return new Map(this.sMap);
	// }

	/**
	 * Get the current unshuffle mapping (for debugging)
	 */
	// getUnshuffleMap(): Map<string, string> {
	// 	return new Map(this.uMap);
	// }
}
