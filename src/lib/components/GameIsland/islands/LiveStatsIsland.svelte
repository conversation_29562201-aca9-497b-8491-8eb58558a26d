<script lang="ts">
	import TimerComponent from '$lib/components/Timer.svelte';
	import type { Stats } from '$lib/util/Stats.svelte';
	import NumberFlow from '@number-flow/svelte';
	import Time from '$lib/components/Time.svelte';
	import { fade, fly } from 'svelte/transition';
	import { numberOrZero } from '$lib/util/numberOrZero';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import EmptyIsland from './EmptyIsland.svelte';
	import type { Leaderboard } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import { onDestroy, onMount, untrack } from 'svelte';
	import { quadInOut } from 'svelte/easing';
	import { cn } from '$lib/util/cn';
	import { authClient } from '$lib/auth/client';
	import { getOrdinal } from '$lib/functions/getOrdinal';

	interface Props {
		stats?: Stats<any, any>;
		loading?: boolean;
		loadingPrimary?: boolean;
		leaderboard?: Leaderboard;
	}

	let { stats, loading, loadingPrimary, leaderboard, ...props }: Props = $props();
	let isEmpty = $derived.by(() => {
		return !stats?.pinnedStats[0] && !stats?.pinnedStats[1];
	});

	let visibleAt = $state();
	let content = $state<'stats' | 'leaderboard'>('stats');
	let switchContentBackTimeoutId = -1;
	const session = authClient.useSession();

	onMount(() => {
		visibleAt = new Date();
	});

	onDestroy(() => {
		clearTimeout(switchContentBackTimeoutId);
	});

	$effect(() => {
		if (
			islandSettings.settings.leaderboards &&
			islandSettings.settings.showLeaderboardsOnGameOver &&
			$session.data?.user &&
			visibleAt &&
			leaderboard &&
			leaderboard.board.user &&
			leaderboard.lastSendSuccessAt &&
			leaderboard.lastSendSuccessAt > visibleAt &&
			leaderboard.wasPlayerRankUpdated
		) {
			untrack(() => {
				content = 'leaderboard';

				clearTimeout(switchContentBackTimeoutId);

				switchContentBackTimeoutId = setTimeout(() => {
					content = 'stats';
				}, 3000) as unknown as number;
			});
		}
	});

	let textColor = $state('text-base');

	$effect(function watchTimer() {
		if (stats?.timer?.isCountdown) {
			const intervalId = setInterval(() => {
				if (!stats.timer) {
					return;
				}

				if (stats.timer.time < 6_000) {
					textColor = 'text-red-400 colorblind:text-red-300';
				} else if (stats.timer.time < 16_000) {
					textColor = 'text-yellow-400 colorblind:text-yellow-300';
				} else {
					textColor = 'text-base';
				}
			}, 100);

			return () => {
				clearInterval(intervalId);
				textColor = 'text-base';
			};
		}
	});
</script>

<div class={cn('size-full overflow-clip transition-colors duration-300', textColor)}>
	{#if content === 'leaderboard' && leaderboard?.board.user}
		{@const place = leaderboard?.board.user.rank + 1}

		<div
			transition:fade={{
				duration: 300,
				easing: quadInOut,
			}}
			class={cn('flex items-center justify-center text-center px-6 py-1 h-full', {
				'bg-secondary text-base-300 overflow-clip': place > 3,
				'bg-yellow-400 text-base-300': place === 1,
				'bg-gray-500': place === 2,
				'bg-orange-500 text-base-300': place === 3,
			})}
		>
			<span
				class="font-bold"
				in:fly={{ y: 20, duration: 300, easing: quadInOut }}
				out:fly={{ y: -20, duration: 300, easing: quadInOut }}
			>
				{getOrdinal(place)}
			</span>
		</div>
	{:else}
		<div class="h-full overflow-clip">
			{#if loading || !stats}
				<div class="flex items-center justify-center size-full px-6 py-1">
					<span
						class="loading loading-bars loading-xs"
						class:text-primary={loadingPrimary}
					></span>
				</div>
			{:else if isEmpty || !islandSettings.settings.stats}
				<EmptyIsland />
			{:else}
				<div
					class="flex select-none items-center justify-center gap-2 text-center text-base h-full [font-variant-numeric:tabular-nums] px-6 py-1"
					{...props}
				>
					{#each stats.pinnedStats as stat}
						{#if stat !== null}
							{#if stat === stats.liveStats.time}
								<TimerComponent
									timer={stats.timer}
									animated={islandSettings.settings.animatedStats}
								/>
							{:else if stat.unit === 'time'}
								<Time
									time={numberOrZero(stat.value)}
									animated={islandSettings.settings.animatedStats}
								/>
							{:else}
								<NumberFlow
									value={numberOrZero(stat.value)}
									format={{
										roundingMode: 'floor',
										notation: stat.value >= 1_000_000 ? 'compact' : 'standard',
										maximumFractionDigits: 1,
									}}
									animated={islandSettings.settings.animatedStats}
								/>
							{/if}
						{/if}
					{/each}
				</div>
			{/if}
		</div>
	{/if}
</div>
