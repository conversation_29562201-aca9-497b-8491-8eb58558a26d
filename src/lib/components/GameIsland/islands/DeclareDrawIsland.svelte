<script lang="ts">
	import { wait } from '$lib/functions/wait';
	import { onMount } from 'svelte';

	interface Props {
		onClose: () => void;
		onDeclareDraw: () => void;
	}

	let { onDeclareDraw, onClose }: Props = $props();
	let disabled = $state(true);

	onMount(async () => {
		await wait(1500);

		disabled = false;
	});
</script>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div
	class="flex flex-col w-full p-8"
	onclick={(event) => {
		event.stopPropagation();
	}}
>
	<h3 class="text-2xl font-medium mb-1">Declare draw?</h3>

	<div class="flex flex-col gap-2 mt-6">
		<button {disabled} onclick={onClose} class="btn rounded-full grow">Maybe later</button>

		<button {disabled} onclick={onDeclareDraw} class="btn btn-primary rounded-full grow"
			>Declare draw</button
		>
	</div>
</div>
