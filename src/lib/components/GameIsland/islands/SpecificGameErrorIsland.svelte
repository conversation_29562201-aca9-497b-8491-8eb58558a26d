<script lang="ts">
	interface Props {
		onConfirm: () => void;
	}

	let { onConfirm }: Props = $props();
</script>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div
	class="flex flex-col w-full p-8"
	onclick={(event) => {
		event.stopPropagation();
	}}
>
	<h3 class="text-2xl font-medium mb-1">Invalid game</h3>

	<p>The game you are trying to play is not valid. Check the url and try again</p>

	<div class="flex gap-2 items-center justify-center w-full mt-6">
		<button onclick={onConfirm} class="btn btn-primary rounded-full grow">Got it</button>
	</div>
</div>
