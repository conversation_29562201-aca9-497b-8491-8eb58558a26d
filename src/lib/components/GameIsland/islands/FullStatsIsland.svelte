<script lang="ts" generics="LiveKeys extends string, CustomKeys extends string">
	import PinIcon from '$lib/components/Icons/PinIcon.svelte';
	import { wait } from '$lib/functions/wait';
	import { cn } from '$lib/util/cn';
	import type { FixedStatsKey } from '$lib/util/Stats.svelte';
	import Stat from './Stat.svelte';
	import { untrack } from 'svelte';
	import { Leaderboard as LeaderboardModel } from '$lib/util/Leaderboard/Leaderboard.svelte';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Alert from '$lib/components/Alert.svelte';
	import LeaderboardWithDatePicker from './leaderboard/LeaderboardWithDatePicker.svelte';
	import type { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';

	interface Props {
		context: GameContext<any, any, any, any, LiveKeys, CustomKeys, any>;
		onChangeDailyGame: () => void;
		onReset: () => void;
		onSettings: () => void;
	}

	let { context: propsContext, onChangeDailyGame, onReset, onSettings }: Props = $props();

	let context = $derived(propsContext);
	let stats = $state(untrack(() => context.stats))!;
	let contextLeaderboard = $derived(context.leaderboard);
	let gameName = $derived(context.gameName);
	let visibleStats = $derived(context.visibleStats);
	let isDailyGame = $derived(context.isPlayingDailyGame);

	const liveStats = $derived(
		Object.entries(stats.liveStats).map((entry) => {
			return {
				key: entry[0] as LiveKeys,
				value: entry[1],
			};
		}),
	);

	let leaderboard = $state<LeaderboardModel | undefined>(
		untrack(() => contextLeaderboard?.clone({ range: 'top-20' })),
	);

	let tab = $state<'stats' | 'leaderboard'>(
		untrack(() => {
			if (islandSettings.settings.initialTab === 'stats') {
				return 'stats';
			}

			if (!islandSettings.settings.leaderboards) {
				return 'stats';
			}

			if (!leaderboard) {
				return 'stats';
			}

			return 'leaderboard';
		}),
	);

	let canRunAnimations = $state(false);
	let firstAnimationDelay = $state(550);
	let currentVariants = $state(untrack(() => context.variants?.fromGame(context.game)));
	let isDropdownOpenByVariant: Record<string, boolean> = $state(
		untrack(() => {
			if (!context.variants) {
				return {};
			}

			return Object.keys(context.variants.map).reduce((acc, variant) => {
				return {
					...acc,
					[variant]: false,
				};
			}, {});
		}),
	);

	function selectVariant<K extends keyof NonNullable<typeof context.variants>['map']>(
		variantKey: K,
		variantValue: any,
	) {
		if (!currentVariants) {
			console.error('Current variants is not defined');
			return;
		}

		currentVariants[variantKey] = variantValue;

		// Clone stats from context with new game variant
		stats = context.stats!.clone({
			gameVariant: context.variants!.getStatsVariant(currentVariants),
		});

		// Clone leaderboard from context with new game variant if available
		if (context.leaderboard) {
			leaderboard = context.leaderboard.clone({
				range: 'top-20',
				gameVariant: context.variants!.getLeaderboardVariant(currentVariants),
			});
		}
	}

	$effect.pre(() => {
		if (tab === 'stats') {
			canRunAnimations = false;
			firstAnimationDelay = 550;

			wait(300).then(() => {
				canRunAnimations = true;
			});
			wait(550).then(() => {
				firstAnimationDelay = 0;
			});
		}
	});

	const fixedAndCustomStats = $derived.by(() => {
		const values = visibleStats
			.map((stat) => {
				return {
					key: stat,
					value:
						stats.fixedStats[stat as FixedStatsKey] ??
						stats.customStats[stat as CustomKeys],
				};
			})
			.filter((stat) => {
				if (stat.value === null || stat.value === undefined) {
					return false;
				}

				return true;
			});

		return values;
	});
</script>

{#snippet PinnedStat(stat: (typeof stats.pinnedStats)[number])}
	<div class="mb-4">
		{#if stat}
			<div
				class={cn(
					'h-[82px] pinned-stat border border-transparent bg-base-100 w-full  rounded-2xl p-2 pb-0',
					{
						'no-animation-duration': !canRunAnimations,
					},
				)}
			>
				<Stat
					{stat}
					{firstAnimationDelay}
					animated={islandSettings.settings.animatedStats}
					withTooltip
				/>
			</div>
		{:else}
			<div
				style="--tw-border-opacity: 0.5"
				class={cn(
					'h-[82px] empty-stat border border-base-content border-dashed flex items-center justify-center rounded-2xl p-2 pb-0',
					{
						'no-animation-duration': !canRunAnimations,
					},
				)}
			>
				<div class="stat p-0 text-center flex flex-col items-center">
					<div class="stat-title text-gray-300 opacity-60">No pinned stat</div>
					<div class="stat-value py-[9px]">
						<PinIcon class="size-10" />
					</div>
				</div>
			</div>
		{/if}
	</div>
{/snippet}

<button
	class="btn btn-circle btn-ghost btn-sm absolute top-6 left-6"
	onclick={onSettings}
	aria-label="Open island settings"
>
	<SettingsIcon class="size-5" />
</button>

<div class="flex flex-col gap-4 w-full pb-8 pt-12">
	<div class="px-8">
		<div class="text-center text-3xl font-semibold">{gameName}</div>

		{#if isDailyGame && islandSettings.settings.dailyGames}
			<div class="text-center text-xl flex items-center justify-center mt-2">
				<button
					class={cn(
						'link text-primary flex gap-2 items-center justify-center transition-colors duration-300',
						{
							'text-gray-600 line-through': tab === 'leaderboard',
						},
					)}
					disabled={tab === 'leaderboard'}
					onclick={onChangeDailyGame}
				>
					Daily {#if context.dailyGame?.gameVariant}
						{context.dailyGame?.gameVariant}
					{/if} Game
				</button>
			</div>
		{/if}

		<!-- Variant selection dropdowns -->
		{#if context.variants}
			<div class="flex items-center justify-center gap-2 mt-4">
				{#each Object.keys(context.variants.map) as variantKey}
					{@const variant = context.variants.map[variantKey]}
					{@const variantOptions = variant?.allValues}
					{@const formatter = variant?.format}
					{@const currentValue = currentVariants?.[variantKey as any]}
					{@const currentFormatted =
						formatter && currentValue ? formatter(currentValue) : String(currentValue)}

					{#if variantOptions}
						<Dropdown bind:open={isDropdownOpenByVariant[variantKey]}>
							<DropdownButton class="btn-sm w-full min-w-28">
								{currentFormatted}
							</DropdownButton>

							<DropdownContent menu class="w-full min-w-40">
								{#each variantOptions as option}
									{@const optionFormatted = formatter
										? formatter(option)
										: String(option)}

									<DropdownItem>
										<button
											class="w-full"
											class:menu-active={JSON.stringify(currentValue) ===
												JSON.stringify(option)}
											onclick={() => {
												selectVariant(variantKey, option);
												isDropdownOpenByVariant[variantKey] = false;
											}}
										>
											{optionFormatted}
										</button>
									</DropdownItem>
								{/each}
							</DropdownContent>
						</Dropdown>
					{/if}
				{/each}
			</div>
		{/if}
	</div>

	{#if leaderboard && islandSettings.settings.leaderboards && islandSettings.settings.stats}
		<div class="px-8">
			<div class="tabs tabs-box bg-transparent grid grid-cols-2 gap-2">
				<button
					class={cn('tab', {
						'tab-active': tab === 'stats',
					})}
					onclick={() => (tab = 'stats')}
				>
					Stats
				</button>

				<button
					class={cn('tab w-full', {
						'tab-active': tab === 'leaderboard',
					})}
					onclick={() => (tab = 'leaderboard')}
				>
					Leaderboards
				</button>
			</div>
		</div>
	{/if}

	{#if tab === 'stats' || !leaderboard}
		{#if islandSettings.settings.stats}
			<!-- Live Stats -->
			<div class="grid grid-cols-1 gap-2 px-8">
				{#if stats === context.stats}
					<div class="grid grid-cols-2 gap-2">
						<!-- Pinned -->

						{@render PinnedStat(stats.pinnedStats[0])}
						{@render PinnedStat(stats.pinnedStats[1])}

						<!-- Live stats -->
						{#each liveStats as stat}
							{@const isPinned = stats.isPinned(stat.key)}
							<div class="relative">
								<Stat
									stat={stat.value}
									withPin={stats.canPinStats || isPinned}
									{isPinned}
									onPinChange={(isPinned) =>
										isPinned
											? stats.pinStat(stat.key)
											: stats.unpinStat(stat.key)}
									withTooltip
									animated={islandSettings.settings.animatedStats}
									{firstAnimationDelay}
								/>
							</div>
						{/each}
					</div>

					<div class="divider my-1"></div>
				{/if}

				<div class="grid grid-cols-2 gap-2 p-0 mt-2">
					{#each fixedAndCustomStats as stat}
						{@const isPinned = stats.isPinned(stat.key)}
						<Stat
							stat={stat.value}
							withPin={stats.canPinStats || isPinned}
							{isPinned}
							onPinChange={(isPinned) =>
								isPinned ? stats.pinStat(stat.key) : stats.unpinStat(stat.key)}
							withTooltip
							animated={islandSettings.settings.animatedStats}
							{firstAnimationDelay}
						/>
					{/each}
				</div>

				<button
					class="btn btn-full btn-soft rounded-full mt-2"
					onclick={(event) => {
						event.stopPropagation();
						onReset();
					}}
				>
					Reset
				</button>
			</div>
		{:else}
			<div class="flex flex-col items-center justify-center gap-4 px-8">
				<Alert title="Stats are disabled" class="w-full" />

				<button
					class="btn btn-primary rounded-full w-full"
					onclick={() => (islandSettings.settings.stats = true)}
				>
					Enable stats
				</button>
			</div>
		{/if}
	{:else if leaderboard}
		<LeaderboardWithDatePicker {leaderboard} />
	{/if}
</div>

<style>
	@keyframes pinAnimation {
		0% {
			transform: scale(1.5);
			opacity: 0;
		}

		50% {
			transform: scale(0.8);
		}

		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.pinned-stat {
		animation: pinAnimation 300ms ease-in-out;
	}

	@keyframes emptyStatAnimation {
		0% {
			transform: scale(0.8);
			opacity: 0;
		}

		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.empty-stat {
		animation: emptyStatAnimation 300ms ease-in-out;
	}

	.no-animation-duration {
		animation-duration: 0s !important;
	}
</style>
