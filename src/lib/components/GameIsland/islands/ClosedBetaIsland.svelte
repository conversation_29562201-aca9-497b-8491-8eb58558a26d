<script lang="ts">
	import { authClient } from '$lib/auth/client';
	import FeedbackIcon from '$lib/components/Icons/FeedbackIcon.svelte';
	import { cn } from '$lib/util/cn';

	interface Props {
		onClose: () => void;
	}

	let { onClose }: Props = $props();

	let session = authClient.useSession();

	$effect(function autoClose() {
		const unsubscribe = session.subscribe((s) => {
			if (s.data?.user) {
				onClose();
			}
		});

		return () => {
			unsubscribe();
		};
	});
</script>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div
	class="flex flex-col w-full p-8"
	onclick={(event) => {
		event.stopPropagation();
	}}
>
	<h3 class="text-2xl font-medium mb-1">Closed Beta</h3>

	<p>
		The game is in closed beta, so bugs and poor performance on some devices may occur. Feedback <FeedbackIcon
			class="size-6 inline"
		/> is appreciated!
	</p>

	<a
		href="/signin"
		class={cn('btn btn-primary rounded-full grow mt-6', {
			'btn-disabled': $session.isPending,
		})}
	>
		{#if $session.isPending}
			<span class="loading loading-spinner"></span>
		{/if} Sign in to play
	</a>
</div>
