<script lang="ts" generics="EncodedDailyGame">
	import Calendar from '$lib/components/Calendar.svelte';
	import { startOfMonth } from '$lib/functions/date/startOfMonth';
	import { DailyGame } from '$lib/util/DailyGame.svelte';

	interface Props {
		onPlayDailyGame: () => void;
		gameName: string;
		dailyGame: DailyGame<EncodedDailyGame>;
	}

	let { onPlayDailyGame, gameName, dailyGame }: Props = $props();

	const now = new Date();
	let currentMonth = $state(startOfMonth(dailyGame.dateOfLastSuccessfullyPlayedGame ?? now));
	let selectedDate = $state(dailyGame.dateOfLastSuccessfullyPlayedGame ?? now);

	let isLoading = $derived(dailyGame.isFetching);

	async function fetchGameAndPlay() {
		try {
			await dailyGame.fetchAndPlay(selectedDate);
			onPlayDailyGame();
		} catch (_) {
			// Ignore
		}
	}
</script>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div
	class="flex flex-col gap-2 w-full p-8 pt-12"
	onclick={(event) => {
		event.stopPropagation();
	}}
>
	<div class="mb-2">
		<div class="text-center text-2xl font-semibold">Daily Game</div>
		<div class="text-center text-lg">
			{gameName}
		</div>
	</div>

	<Calendar
		disabled={isLoading}
		fromDate={dailyGame.firstAvailableGameDate}
		bind:currentMonth
		bind:selectedDate
		isMarked={(date) => {
			return dailyGame.playedDays.includes(DailyGame.getFormattedDate(date));
		}}
	/>

	<div class="flex flex-col gap-2 w-full">
		<button
			class="btn btn-ghost rounded-full text-primary w-full text-center"
			onclick={() =>
				(currentMonth = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1))}
			disabled={isLoading}
		>
			{Intl.DateTimeFormat('en', {
				day: 'numeric',
				month: 'long',
				year: 'numeric',
			}).format(selectedDate)}
		</button>

		<button
			onclick={fetchGameAndPlay}
			class="btn btn-primary rounded-full grow"
			disabled={isLoading}
			style={isLoading ? '--tw-text-opacity:0.5; --tw-bg-opacity: 0.5' : undefined}
		>
			{#if isLoading}
				<span class="loading loading-spinner"></span>
			{/if}
			Play daily game
		</button>
	</div>
</div>
