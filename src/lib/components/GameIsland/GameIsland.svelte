<script
	lang="ts"
	generics="LiveKeys extends string, Custom<PERSON>eys extends string, SettingsData extends Record<string, any>"
>
	import FullStatsIsland from './islands/FullStatsIsland.svelte';
	import LiveStatsIsland from './islands/LiveStatsIsland.svelte';
	import TimerPausedIsland from './islands/TimerPausedIsland.svelte';
	import NewGameIsland from './islands/NewGameIsland.svelte';
	import PreventLeaveIsland from './islands/PreventLeaveIsland.svelte';
	import { beforeNavigate, goto } from '$app/navigation';
	import { onDestroy, untrack, type Snippet } from 'svelte';
	import { browser } from '$app/environment';
	import Island from './islands/Island.svelte';
	import Backdrop from '../Backdrop.svelte';
	import ResetStatsIsland from './islands/ResetStatsIsland.svelte';
	import throttle from 'lodash/throttle';
	import RefreshPageIsland from './islands/RefreshPageIsland.svelte';
	import BetaIsland from './islands/BetaIsland.svelte';
	import PlayIcon from '../Icons/PlayIcon.svelte';
	import CalendarIcon from '../Icons/CalendarIcon.svelte';
	import DailyGameIsland from './islands/DailyGameIsland.svelte';
	import { fly } from 'svelte/transition';
	import ShareIsland from './islands/ShareIsland.svelte';
	import DailyGameErrorIsland from './islands/DailyGameErrorIsland.svelte';
	import LauncherIsland from './islands/LauncherIsland.svelte';
	import type { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import SettingsIsland from './islands/SettingsIsland.svelte';
	import { authClient } from '$lib/auth/client';
	import ExitIntentIsland from './islands/ExitIntentIsland.svelte';
	import ClosedBetaIsland from './islands/ClosedBetaIsland.svelte';
	import { ads } from '$lib/stores/ads.svelte';
	import Ad from '../Ad/Ad.svelte';
	import WhyAdsButton from '../Ad/WhyAdsButton.svelte';
	import CloseAdsButton from '../Ad/CloseAdsButton.svelte';
	import EfIsland from './islands/EfIsland/EfIsland.svelte';
	import { cn } from '$lib/util/cn';
	import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
	import SpecificGameErrorIsland from './islands/SpecificGameErrorIsland.svelte';
	import DeclareDrawIsland from './islands/DeclareDrawIsland.svelte';

	interface Props {
		gameOver: boolean;
		gameOverStrategy?: 'new-game' | 'best-stats-update' | 'none';
		gameOverIslandDelay?: number;
		class?: string;
		pauseKey?: string | null;
		isBeta?: boolean;
		isClosedBeta?: boolean;
		isFetchingGame?: boolean;
		draw?: {
			onDeclareDraw: () => void;
			onPostponeDraw: () => void;
		};
		onVariantChange?: (previous: IslandVariant, current: IslandVariant) => void;
		onNewGame?: (dailyGameDate?: Date) => void;
		GameOverIsland?: Snippet<[onClose: () => void]>;
		InstructionsIsland?: Snippet;
		onLeaveGame?: (url: URL | null) => void;
	}

	type PropsWithContext = Partial<Props> & {
		context: GameContext<any, any, SettingsData, any, LiveKeys, CustomKeys, any>;
	};

	let props: Props | PropsWithContext = $props();

	let {
		gameName,
		settingsManager,
		gameOverIslandDelay = 2500,
		stats,
		gameOver,
		gameOverStrategy = 'new-game',
		class: classFromProps,
		pauseKey = 'p',
		isBeta = false,
		isClosedBeta = false,
		dailyGame,
		specificGame,
		isDailyGame,
		isFetchingGame,
		leaderboard,
		context,
		onDeclareDraw = () => {},
		onPostponeDraw = () => {},
		onVariantChange,
		InstructionsIsland,
		GameOverIsland,
		onNewGame,
		onLeaveGame,
	} = $derived.by(() => {
		const context = (props as PropsWithContext).context as
			| PropsWithContext['context']
			| undefined;

		return {
			context,
			gameName: context?.gameName ?? '',
			settingsManager: context?.settingsManager,
			gameOverIslandDelay: props.gameOverIslandDelay,
			stats: context?.stats,
			visibleStats: context?.visibleStats,
			gameOver: props.gameOver ?? context?.isOver,
			gameOverStrategy: props.gameOverStrategy,
			class: props.class,
			pauseKey: props.pauseKey,
			isBeta: props.isBeta,
			isClosedBeta: props.isClosedBeta,
			dailyGame: context?.dailyGame,
			isDailyGame: context?.isPlayingDailyGame,
			specificGame: context?.specificGame,
			isFetchingGame: props.isFetchingGame ?? context?.isGameReady === false,
			leaderboard: context?.leaderboard,
			onDeclareDraw: props.draw?.onDeclareDraw,
			onPostponeDraw: props.draw?.onPostponeDraw,
			onVariantChange: props.onVariantChange,
			InstructionsIsland: props.InstructionsIsland,
			GameOverIsland: props.GameOverIsland,
			onLeaveGame: props.onLeaveGame,
			onNewGame:
				props.onNewGame ??
				((dailyGameDate?: Date) => {
					if (dailyGameDate && context?.dailyGame) {
						context.dailyGame.fetchAndPlay(dailyGameDate);
					} else {
						context?.createGame?.();
					}
				}),
		};
	});

	type IslandVariant =
		/** DMCA*/
		| 'ef'
		| 'share'
		| 'beta'
		| 'closed-beta'
		| 'settings-error'
		| 'refresh-page-due-to-stats-error'
		| 'refresh-page-due-to-daily-game-error'
		| 'instructions'
		| 'full-stats'
		| 'live-stats'
		| 'timer-paused'
		| 'game-over'
		| 'prevent-leave'
		| 'daily-game'
		| 'daily-error'
		| 'specific-game-error'
		| 'launcher'
		| 'declare-draw'
		| 'stats-settings'
		| 'exit-intent'
		| 'reset-stats';

	let variant: IslandVariant = $state(
		untrack(() =>
			isBeta
				? 'beta'
				: isClosedBeta
					? 'closed-beta'
					: InstructionsIsland
						? 'instructions'
						: 'live-stats',
		),
	);
	let previousVariant: IslandVariant = $state('live-stats');

	type GameState = 'starting' | 'running' | 'paused' | 'finishing' | 'finished';

	let gameState = $derived.by<GameState>(() => {
		const timer = stats?.timer;
		const running = timer?.running;
		const started = timer?.started;
		const paused = timer?.paused;
		const stopped = timer?.stopped;

		if (gameOver) {
			return 'finished';
		}

		if (!stats || !started) {
			return 'starting';
		}

		if (running) {
			return 'running';
		}

		if (stopped) {
			return 'finishing';
		}

		if (paused) {
			return 'paused';
		}

		return 'running';
	});

	let disabled = $derived.by(() => {
		const disableOnGameFinish = gameOverStrategy === 'new-game';

		if (disableOnGameFinish) {
			return gameState === 'finishing' || gameState === 'finished';
		}

		return false;
	});

	let newGameTimeoutId = -1;

	export function changeVariant(newVariant: IslandVariant) {
		if (variant === newVariant) {
			return;
		}

		if (variant === 'ef') {
			return;
		}

		if (!['exit-intent', 'prevent-leave'].includes(variant)) {
			previousVariant = variant;
		}

		if (newVariant === 'game-over' && variant !== 'exit-intent') {
			newGameTimeoutId = setTimeout(() => {
				if (variant !== 'refresh-page-due-to-stats-error' && variant !== 'settings-error') {
					variant = 'game-over';
				}
				newGameTimeoutId = -1;
			}, gameOverIslandDelay) as unknown as number;
		} else {
			variant = newVariant;
		}

		if (newVariant !== 'live-stats') {
			stats?.timer.pause();
		} else if (stats?.timer.started && !stats?.timer.stopped) {
			stats?.timer.start();
		}
	}

	$effect(function handleNewGame() {
		if (
			variant === 'refresh-page-due-to-stats-error' ||
			variant === 'settings-error' ||
			variant === 'share' ||
			variant === 'exit-intent' ||
			variant === 'ef'
		) {
			return;
		}

		if (gameState === 'finished') {
			const hasNewBestStat =
				stats?.pinnedStats[0]?.isNewBest || stats?.pinnedStats[1]?.isNewBest;

			if (
				gameOverStrategy === 'new-game' ||
				hasNewBestStat ||
				(gameOverStrategy === 'best-stats-update' && isDailyGame && dailyGame)
			) {
				changeVariant('game-over');
			}
		} else {
			if (newGameTimeoutId !== -1) {
				clearTimeout(newGameTimeoutId);
				newGameTimeoutId = -1;
			}

			if (variant === 'game-over') {
				changeVariant('live-stats');
			}
		}
	});

	async function syncTimer() {
		if (gameState === 'paused' && variant === 'live-stats') {
			variant = 'timer-paused';
		} else if (
			gameState === 'running' &&
			(variant === 'timer-paused' ||
				variant === 'instructions' ||
				variant === 'game-over' ||
				variant === 'beta')
		) {
			variant = 'live-stats';
		}
	}

	$effect(() => {
		syncTimer();
	});

	// Prevent leave
	let canLeavePage = false;
	let leaveGameToUrl = null as URL | null;

	const leaveGame = () => {
		const goToUrl = leaveGameToUrl;
		canLeavePage = true;
		leaveGameToUrl = null;

		if (goToUrl) {
			goto(goToUrl);
		}

		onLeaveGame?.(goToUrl);
	};

	beforeNavigate((navigation) => {
		if (gameState !== 'finished' && gameState !== 'starting' && !canLeavePage) {
			leaveGameToUrl = navigation.to?.url ?? null;
			navigation.cancel();
			if (navigation.type !== 'leave') {
				changeVariant('prevent-leave');
			}
		}
	});

	// Overflow

	$effect(function handlePageScroll() {
		if (variant === 'live-stats') {
			document.body.style.overflow = 'initial';
		} else {
			document.body.style.overflow = 'hidden';
		}
	});

	$effect(function handleStatsError() {
		if (stats?.mustRefreshPageDueToError) {
			changeVariant('refresh-page-due-to-stats-error');
		}
	});

	$effect(function handleSettingsError() {
		if (settingsManager?.hasError) {
			changeVariant('settings-error');
		}
	});

	$effect(function handleDailyGameError() {
		if (dailyGame?.error) {
			changeVariant('daily-error');
		}
	});

	$effect(function handleSpecificGameError() {
		if (specificGame?.error) {
			changeVariant('specific-game-error');
		}
	});

	$effect(function handleDailyGameRefreshError() {
		if (dailyGame?.mustRefreshPageDueToError) {
			changeVariant('refresh-page-due-to-daily-game-error');
		}
	});

	/** handleDMCA*/
	$effect(function handleEf() {
		if (context?.ef) {
			changeVariant('ef');
		}
	});

	$effect(function notifyVariantChange() {
		if (previousVariant !== variant) {
			onVariantChange?.(previousVariant, variant);
		}
	});

	onDestroy(() => {
		if (browser) {
			document.body.style.overflow = 'initial';
		}
	});

	// TODO: Refactor this. Put variant into a context and let the island define it
	const variantToIsland: Record<IslandVariant, 'pill' | 'expanded' | 'pill-expanded'> = $derived({
		'full-stats': 'expanded',
		'timer-paused': 'expanded',
		'game-over': 'expanded',
		'prevent-leave': 'expanded',
		'live-stats': 'pill',
		'reset-stats': 'expanded',
		instructions: 'expanded',
		'refresh-page-due-to-stats-error': 'expanded',
		'refresh-page-due-to-daily-game-error': 'expanded',
		'settings-error': 'expanded',
		'daily-game': 'expanded',
		'specific-game-error': 'expanded',
		'daily-error': 'expanded',
		'stats-settings': 'expanded',
		'declare-draw': 'expanded',
		launcher: 'pill-expanded',
		share: 'expanded',
		beta: 'expanded',
		ef: 'expanded',
		'closed-beta': 'expanded',
		'exit-intent': 'expanded',
	});

	function onclose() {
		if (variant === 'timer-paused') {
			stats?.timer.start();
		} else if (variant === 'full-stats') {
			changeVariant('live-stats');
		} else if (variant === 'prevent-leave') {
			changeVariant(previousVariant);
		} else if (variant === 'reset-stats') {
			changeVariant('full-stats');
		} else if (variant === 'instructions') {
			changeVariant('live-stats');
		} else if (variant === 'refresh-page-due-to-stats-error') {
			if (stats) {
				stats.mustRefreshPageDueToError = false;
			}
			changeVariant('live-stats');
		} else if (variant === 'beta') {
			if (InstructionsIsland) {
				changeVariant('instructions');
			} else {
				changeVariant('live-stats');
			}
		} else if (variant === 'settings-error') {
			if (settingsManager) {
				settingsManager.hasError = false;
			}
			changeVariant('live-stats');
		} else if (variant === 'daily-game') {
			changeVariant('live-stats');
		} else if (variant === 'daily-error') {
			if (dailyGame) {
				dailyGame.error = undefined;
			}
			changeVariant('live-stats');
		} else if (variant === 'specific-game-error') {
			if (specificGame) {
				specificGame.error = undefined;
			}
			changeVariant('live-stats');
		} else if (variant === 'refresh-page-due-to-daily-game-error') {
			if (dailyGame) {
				dailyGame.mustRefreshPageDueToError = false;
			}
			changeVariant('live-stats');
		} else if (variant === 'launcher') {
			changeVariant('live-stats');
		} else if (variant === 'stats-settings') {
			changeVariant('full-stats');
		} else if (variant === 'exit-intent') {
			changeVariant(previousVariant);
		} else if (variant === 'declare-draw') {
			changeVariant('live-stats');
		}
	}

	function onkeydown(event: KeyboardEvent) {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		if (pauseKey !== null) {
			const key = event.key.toLocaleLowerCase();

			if (key === pauseKey) {
				throttledOnPauseKeyPressed();
			}
		}

		if (event.key === 'Escape') {
			onclose();
		}
	}

	function onPauseKeyPressed() {
		if (gameState === 'running') {
			stats?.timer.pause();
		} else if (gameState === 'paused') {
			stats?.timer.start();
		}
	}

	const throttledOnPauseKeyPressed = throttle(onPauseKeyPressed, 1000, {
		leading: true,
		trailing: false,
	});

	let showCalendarIcon = $derived(
		islandSettings.settings.dailyGames && !!dailyGame && gameState === 'starting',
	);

	let withBackdrop = $derived(
		(
			[
				'full-stats',
				'timer-paused',
				'prevent-leave',
				'reset-stats',
				'instructions',
				'refresh-page-due-to-stats-error',
				'refresh-page-due-to-daily-game-error',
				'beta',
				'settings-error',
				'daily-game',
				'daily-error',
				'launcher',
				'stats-settings',
				'exit-intent',
			] as IslandVariant[]
		).includes(variant),
	);

	const session = authClient.useSession();

	let desiredExpandedWidth = $derived.by(() => {
		if (leaderboard && islandSettings.settings.leaderboards) {
			if (variant === 'full-stats') {
				return 448;
			}

			if (
				islandSettings.settings.showLeaderboardsOnGameOver &&
				variant === 'game-over' &&
				$session.data?.user &&
				!GameOverIsland
			) {
				return 448;
			}
		}

		if (variant === 'ef') {
			return 448;
		}
	});

	function onmouseleave() {
		if (import.meta.env.VITE_EXIT_INTENT_ISLAND_DISABLED === 'true') {
			return;
		}

		if (!islandSettings.settings.showExitIntent) {
			return;
		}

		if (gameState === 'paused') {
			return;
		}

		changeVariant('exit-intent');
	}
</script>

<svelte:window {onkeydown} />
<svelte:document {onmouseleave} />

{#if withBackdrop}
	<Backdrop aria-label="Close game island" {onclose} class="z-30" />
{/if}

<Island
	class={classFromProps}
	variant={variantToIsland[variant]}
	withBackButton={['stats-settings', 'share'].includes(variant)}
	withCloseButton={['full-stats', 'daily-game'].includes(variant)}
	withLonelyIsland={gameState === 'running' || showCalendarIcon}
	speed={variant === 'exit-intent' ? 'fast' : 'default'}
	onback={() => {
		if (variant === 'share') {
			variant = 'game-over';
		} else if (variant === 'stats-settings') {
			variant = 'full-stats';
		} else {
			onclose();
		}
	}}
	{desiredExpandedWidth}
	{disabled}
	{onclose}
	onclick={() => {
		if (disabled) {
			return;
		}
		if (variant === 'live-stats') {
			changeVariant('full-stats');
		} else if (variant === 'instructions') {
			changeVariant('live-stats');
		}
	}}
	onLonelyIslandClick={() => {
		if (showCalendarIcon) {
			changeVariant('daily-game');
		} else {
			stats?.timer.pause();
		}
	}}
	role={variant === 'live-stats' ? 'button' : 'none'}
	tabindex={variant === 'live-stats' ? 0 : -1}
	aria-label={variant === 'live-stats' ? 'Open game stats and leaderboards' : ''}
	lonelyIslandAriaLabel={showCalendarIcon
		? 'Play daily game'
		: variant === 'live-stats'
			? context?.timer.running
				? 'Pause game'
				: ''
			: ''}
	glow={isDailyGame && variant === 'live-stats'}
>
	{#snippet LonelyIslandIcon(props)}
		{#if showCalendarIcon}
			<g transition:fly={{ y: -10 }}>
				<CalendarIcon class="size-[22px]" {...props} width="22" height="22" y="9" x="44" />
			</g>
		{:else}
			<g transition:fly={{ y: 10 }}>
				<PlayIcon class="size-[32px]" {...props} variant="pause" />
			</g>
		{/if}
	{/snippet}

	{#if variant === 'refresh-page-due-to-stats-error'}
		<RefreshPageIsland
			title="There was an error with your stats"
			description="Please refresh the page to fix the issue"
			onCancel={onclose}
		/>
	{:else if variant === 'settings-error'}
		<RefreshPageIsland
			title="There was an error with your game settings"
			description="Please refresh the page to fix the issue. If the problem continues, ensure cookies are enabled for this website."
			onCancel={onclose}
		/>
	{:else if variant === 'refresh-page-due-to-daily-game-error'}
		<RefreshPageIsland
			title="There was an error with the daily game"
			description="Please refresh the page to fix the issue. If the problem continues, ensure cookies are enabled for this website."
			onCancel={onclose}
		/>
	{:else if variant === 'instructions'}
		{@render InstructionsIsland?.()}
	{:else if variant === 'launcher'}
		<LauncherIsland
			onChange={(island) => {
				if (island === 'stats') {
					changeVariant('full-stats');
				} else if (island === 'daily-game') {
					changeVariant('daily-game');
				} else if (island === 'share') {
					changeVariant('share');
				} else if (island === 'leaderboard') {
					console.log('todo: leaderboard');
				}
			}}
		/>
	{:else if variant === 'daily-game' && dailyGame}
		<DailyGameIsland
			{gameName}
			{dailyGame}
			onPlayDailyGame={() => {
				changeVariant('live-stats');
			}}
		/>
	{:else if variant === 'daily-error'}
		<DailyGameErrorIsland onConfirm={onclose} />
	{:else if variant === 'specific-game-error'}
		<SpecificGameErrorIsland onConfirm={onclose} />
	{:else if variant === 'game-over'}
		{#if GameOverIsland}
			{@render GameOverIsland(() => changeVariant('live-stats'))}
		{:else if stats}
			<NewGameIsland
				{stats}
				{leaderboard}
				{dailyGame}
				{isDailyGame}
				noButton={gameOverStrategy === 'best-stats-update' && !isDailyGame}
				onPlayNewGame={onNewGame}
				onPlayDailyGame={onNewGame}
			/>
		{/if}
	{:else if variant === 'share'}
		<ShareIsland />
	{:else if variant === 'prevent-leave'}
		<PreventLeaveIsland onLeaveGame={leaveGame} onCancel={onclose} />
	{:else if variant === 'beta'}
		<BetaIsland onClose={onclose} />
	{:else if variant === 'closed-beta'}
		<ClosedBetaIsland
			onClose={() => {
				changeVariant('live-stats');
			}}
		/>
	{:else if variant === 'live-stats'}
		<LiveStatsIsland
			{stats}
			{leaderboard}
			loading={isFetchingGame || dailyGame?.isFetching}
			loadingPrimary={isDailyGame || dailyGame?.isFetching}
		/>
	{:else if variant === 'exit-intent'}
		<ExitIntentIsland onClose={onclose} />
	{:else if variant === 'ef'}
		<EfIsland />
	{:else if stats}
		{#if variant === 'full-stats'}
			{#if context}
				<FullStatsIsland
					{context}
					onChangeDailyGame={() => {
						changeVariant('daily-game');
					}}
					onReset={() => {
						changeVariant('reset-stats');
					}}
					onSettings={() => {
						changeVariant('stats-settings');
					}}
				/>
			{/if}
		{:else if variant === 'timer-paused'}
			<TimerPausedIsland
				{pauseKey}
				onPlay={() => {
					stats.timer.start();
				}}
			/>
		{:else if variant === 'reset-stats'}
			<ResetStatsIsland
				onCancel={() => {
					changeVariant('full-stats');
				}}
				onReset={() => {
					stats.reset();
					changeVariant('full-stats');
				}}
			/>
		{:else if variant === 'stats-settings'}
			<SettingsIsland />
		{:else if variant === 'declare-draw'}
			<DeclareDrawIsland
				onClose={() => {
					onPostponeDraw();
					onclose();
				}}
				onDeclareDraw={() => {
					onDeclareDraw();
					onclose();
				}}
			/>
		{/if}
	{/if}
</Island>

{#if ads.canShowGameAds && ['timer-paused', 'exit-intent'].includes(variant)}
	<div
		in:fly={{ y: 80, duration: 500, delay: 1000 }}
		out:fly={{ y: 80, duration: 200 }}
		class={cn(
			'fixed flex flex-col gap-2 bottom-0 left-0 right-0 h-full max-h-[calc(100vh-300px)] lg:max-h-[calc(90vh-300px)] z-40',
			{
				'max-h-[calc(100vh-120px)] lg:max-h-[calc(90vh-120px)]':
					musicPlayer.muted && variant === 'timer-paused',
			},
		)}
	>
		<div class="flex gap-2 items-center justify-end px-4">
			<WhyAdsButton />

			<CloseAdsButton />
		</div>

		<div align="center" class="bg-base-300 size-full p-1">
			<Ad variant="game-paused" />
		</div>
	</div>
{/if}
