<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';
	import { quadInOut } from 'svelte/easing';
	import { fly } from 'svelte/transition';

	interface Props {
		children: Snippet;
		class?: string;
		collapsedHeight?: number;
		alignment?: 'center' | 'end';
	}

	let { children, class: className, collapsedHeight, alignment = 'center' }: Props = $props();

	let element = $state() as HTMLDivElement;

	$effect(() => {
		if (collapsedHeight === undefined) {
			return;
		}

		const observer = new ResizeObserver((entries) => {
			const entry = entries[0];
			element.style.setProperty(
				'--clip-bottom',
				`${entry.contentRect.height - collapsedHeight}px`,
			);
		});

		observer.observe(element);

		return () => observer.disconnect();
	});
</script>

<div
	bind:this={element}
	class={cn(
		'flex flex-col overflow-auto flex-nowrap overscroll-contain bg-base-100 rounded-box p-4 my-1 shadow-md stack-item',
		{
			'stack-item-center': alignment === 'center',
			'stack-item-end': alignment === 'end',
		},
		className,
	)}
	in:fly|global={{ y: 30, duration: 300, easing: quadInOut }}
	out:fly|global={{ y: 30, duration: 150, easing: quadInOut }}
>
	{@render children()}
</div>

<style>
	@keyframes fadeIn {
		from {
			opacity: 0;
		}

		to {
			opacity: 1;
		}
	}

	.stack-item {
		--clip-bottom: 0%;

		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1;
		transition:
			transform 300ms ease-in-out,
			clip-path 300ms ease-in-out 100ms,
			opacity 300ms ease-in-out;
		transform-origin: top center;
		clip-path: inset(0% 0% 0% 0% round var(--radius-box, 1rem));
		will-change: contents;
		max-height: calc(100vh - 6rem);
		overflow: auto;
	}

	.stack-item.stack-item-end {
		left: auto;
	}

	.stack-item:nth-last-child(4) {
		opacity: 0;
		transform: scale(0.7) translateY(-30px);
	}

	.stack-item:nth-last-child(3) {
		opacity: 1;
		transform: scale(0.8) translateY(-20px);
	}

	.stack-item:nth-last-child(2) {
		opacity: 1;
		transform: scale(0.9) translateY(-10px);
	}

	.stack-item:nth-last-child(1) {
		opacity: 1;
		transform: scale(1) translateY(0);
	}

	.stack-item:not(:last-child) {
		pointer-events: none;

		/* Collapse */
		clip-path: inset(0% 0% var(--clip-bottom) 0% round var(--radius-box, 1rem));

		&::after {
			content: '';
			position: absolute;
			inset: 0;
			z-index: 2;
			background: rgba(0, 0, 0, 0.2);
			border-radius: inherit;
			animation: fadeIn 300ms ease-in-out;
		}
	}
</style>
