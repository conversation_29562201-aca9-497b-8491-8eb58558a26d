import { type Component } from 'svelte';
import BreakoutCover from '../../routes/(site)/(games)/breakout/BreakoutCover.svelte';
import CheckersCover from '../../routes/(site)/(games)/checkers/CheckersCover.svelte';
import ColorMemoryCover from '../../routes/(site)/(games)/color-memory/ColorMemoryCover.svelte';
import DinosaurCover from '../../routes/(site)/(games)/dinosaur-game/DinosaurCover.svelte';
import FlappyBirdieCover from '../../routes/(site)/(games)/flappy-birdie/FlappyBirdieCover.svelte';
import MinesweeperCover from '../../routes/(site)/(games)/minesweeper/MinesweeperCover.svelte';
import SnakeCover from '../../routes/(site)/(games)/snake/SnakeCover.svelte';
import SolitaireCover from '../../routes/(site)/(games)/solitaire/SolitaireCover.svelte';
import SudokuCover from '../../routes/(site)/(games)/sudoku/SudokuCover.svelte';
import TentsCover from '../../routes/(site)/(games)/tents/TentsCover.svelte';
import TicTacToeCover from '../../routes/(site)/(games)/tic-tac-toe/TicTacToeCover.svelte';
import TileSlidePuzzleCover from '../../routes/(site)/(games)/tile-slide-puzzle/TileSlidePuzzleCover.svelte';
import WordSearchCover from '../../routes/(site)/(games)/word-search/WordSearchCover.svelte';
import WordleCover from '../../routes/(site)/(games)/wordle/WordleCover.svelte';
import _2048Cover from '../../routes/(site)/(games)/2048/2048Cover.svelte';
import { SettingsManager } from '$lib/util/SettingsManager.svelte';
import { siteSounds } from './siteSounds.svelte';
import JigsawPuzzleCover from '../../routes/(site)/(games)/jigsaw-puzzle/JigsawPuzzleCover.svelte';
import LightsOutCover from '../../routes/(site)/(games)/lights-out/LightsOutCover.svelte';
import MemoryGameCover from '../../routes/(site)/(games)/memory-game/MemoryGameCover.svelte';
import HitoriCover from '../../routes/(site)/(games)/hitori/HitoriCover.svelte';
import StarBattleCover from '../../routes/(site)/(games)/star-battle/StarBattleCover.svelte';

// TODO: Receive it on Context and derive formatted game name from list below
export type GameKey =
	| 'star-battle'
	| 'solitaire'
	| 'tile-slide-puzzle'
	| 'checkers'
	| 'tents'
	| 'breakout'
	| 'flappy-birdie'
	| '2048'
	| 'color-memory'
	| 'word-search'
	| 'minesweeper'
	| 'snake'
	| 'dinosaur'
	| 'wordle'
	| 'tic-tac-toe'
	| 'sudoku'
	| 'jigsaw-puzzle'
	| 'lights-out'
	| 'hitori'
	| 'memory-game';

export type GameInfo = {
	key: GameKey;
	name: string;
	url: string;
	cover: Component;
	coverType?: 'default' | 'full';
	tag?: string;
	daily?: boolean;
};

const favoritesSettings = new SettingsManager({
	key: 'favorites',
	defaultSettings: {
		games: [] as GameKey[],
	},
});

export const favoriteGames = {
	add(game: GameKey) {
		favoritesSettings.settings.games.push(game);
		siteSounds.addFavorite.play();
	},
	remove(game: GameKey) {
		favoritesSettings.settings.games = favoritesSettings.settings.games.filter(
			(key) => key !== game,
		);
		siteSounds.removeFavorite.play();
	},
	isFavorite(game: GameKey) {
		return favoritesSettings.settings.games.includes(game);
	},
	load() {
		favoritesSettings.load();
	},
};

export const games: GameInfo[] = [
	{
		key: 'star-battle',
		name: 'Star Battle',
		cover: StarBattleCover,
		url: '/star-battle',
		tag: 'New',
		daily: true,
	},
	{
		key: 'solitaire',
		name: 'Solitaire',
		cover: SolitaireCover,
		url: '/solitaire',
		daily: true,
	},
	{
		key: 'tile-slide-puzzle',
		name: 'Tile Slide Puzzle',
		cover: TileSlidePuzzleCover,
		url: '/tile-slide-puzzle',
		daily: true,
	},
	{
		key: 'jigsaw-puzzle',
		name: 'Jigsaw Puzzle',
		cover: JigsawPuzzleCover,
		url: '/jigsaw-puzzle',
		tag: 'New puzzles',
	},
	{
		key: 'tents',
		name: 'Tents',
		cover: TentsCover,
		url: '/tents',
		daily: true,
	},
	{
		key: 'flappy-birdie',
		name: 'Flappy Birdie',
		cover: FlappyBirdieCover,
		url: '/flappy-birdie',
	},
	{
		key: 'breakout',
		name: 'Breakout',
		cover: BreakoutCover,
		url: '/breakout',
	},
	{
		key: '2048',
		name: '2048',
		cover: _2048Cover,
		url: '/2048',
	},
	{
		key: 'minesweeper',
		name: 'Minesweeper',
		cover: MinesweeperCover,
		url: '/minesweeper',
	},
	{
		key: 'hitori',
		name: 'Hitori',
		cover: HitoriCover,
		url: '/hitori',
		daily: true,
	},
	{
		key: 'word-search',
		name: 'Word Search',
		cover: WordSearchCover,
		url: '/word-search',
	},
	{
		key: 'wordle',
		name: 'Wordle',
		cover: WordleCover,
		url: '/wordle',
	},
	{
		key: 'checkers',
		name: 'Checkers',
		cover: CheckersCover,
		url: '/checkers',
	},
	{
		key: 'dinosaur',
		name: 'Dinosaur',
		cover: DinosaurCover,
		url: '/dinosaur-game',
	},
	{
		key: 'lights-out',
		name: 'Lights Out',
		cover: LightsOutCover,
		url: '/lights-out',
		daily: true,
	},
	{
		key: 'sudoku',
		name: 'Sudoku',
		cover: SudokuCover,
		url: '/sudoku',
		daily: true,
	},
	{
		key: 'snake',
		name: 'Snake',
		cover: SnakeCover,
		url: '/snake',
	},
	{
		key: 'memory-game',
		name: 'Memory Game',
		cover: MemoryGameCover,
		url: '/memory-game',
		daily: true,
	},
	{
		key: 'color-memory',
		name: 'Color Memory',
		cover: ColorMemoryCover,
		url: '/color-memory',
		daily: true,
	},
	{
		key: 'tic-tac-toe',
		name: 'Tic Tac Toe',
		cover: TicTacToeCover,
		url: '/tic-tac-toe',
	},
];
