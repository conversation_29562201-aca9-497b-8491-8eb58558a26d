import { browser } from '$app/environment';
import {
	menuSoftSelect,
	menuSoftUnselect,
	pop,
	staticNoise,
	switch2,
	switch3,
	tape1,
	tape2,
} from '$lib/data/gameSounds';
import { GameSoundRegistry } from '$lib/util/GameSoundRegistry.svelte';

const siteSoundsResources = {
	lightTheme: switch2,
	darkTheme: switch3,
	toggleOn: switch2,
	toggleOff: switch3,
	changeRadioStation: staticNoise,
	homeChill: tape1,
	homePlay: tape2,
	homeAllGames: switch2,
	homeDailyGames: switch3,
	editFavorites: switch2,
	finishEditingFavorites: switch3,
	addFavorite: menuSoftSelect,
	removeFavorite: menuSoftUnselect,
	adPop: pop,
};

export const siteSounds = GameSoundRegistry.register(siteSoundsResources);

if (browser) {
	GameSoundRegistry.loadAll(siteSounds);
}
