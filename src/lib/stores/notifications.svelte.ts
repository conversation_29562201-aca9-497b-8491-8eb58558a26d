import { goto } from '$app/navigation';
import { page } from '$app/state';
import ToastCard from '$lib/components/ToastCard.svelte';
import { untrack, type Component } from 'svelte';
import { toast } from 'svelte-sonner';

interface Notification {
	title: string;
	description: string;
	key: string;
	date: string;
	read?: boolean;
	link?: {
		name: string;
		url: string;
	};
	cta?: Component;
	feedback?: boolean;
	feedbackContext?: string;
	asToastOnPages?: 'all' | string[];
}

let disabled = $state(false);

const allNotifications: Notification[] = [
	{
		key: 'star-battle-score-adjustments',
		date: '2025-11-07',
		title: 'Leaderboard Adjustments for the Star Battle Game',
		description:
			"We've updated the score calculation to give more weight to completion time, making the leaderboard fairer for speedrunners. Since it's possible to finish games in under 1 second, time now plays a bigger role in your final score.",
	},
	{
		key: 'checkers-crashes',
		date: '2025-11-07',
		title: 'Checkers Stability & Performance Improved',
		description:
			"We've significantly improved the checkers game stability - crashes now should be extremely rare! The CPU opponent is also faster than ever.",
		asToastOnPages: ['/checkers'],
	},
	{
		key: 'star-battle',
		date: '2025-11-06',
		title: 'Star Battle Game in Beta',
		description:
			'New Star Battle puzzle game now available in Beta! Place stars in a grid, where each row, column, and region must contain the same number of stars.',
		link: {
			name: 'Play Star Battle',
			url: '/star-battle',
		},
	},
	{
		key: 'checkers-declare-draw',
		date: '2025-10-25',
		title: 'Better Declare Draw on Checkers',
		description:
			"We've improved the declare draw feature in Checkers. Instead of a button covering the board, it now prompts you through the game island with an option to dismiss it.",
	},
	{
		key: 'hitori-game',
		date: '2025-10-06',
		title: 'Hitori Game in Beta',
		description:
			'New Hitori puzzle game now available in Beta! Eliminate duplicate numbers by clicking cells while keeping all remaining cells connected. A challenging logic puzzle that tests your strategic thinking.',
		link: {
			name: 'Play Hitori',
			url: '/hitori',
		},
	},
];

let notificationsWithReadState: Notification[] = $state([]);

const storageKey = 'notifications';

function readNotification(notification: Notification) {
	if (notification.read) {
		return;
	}

	notificationsWithReadState = notificationsWithReadState.map((n) => {
		if (n.key === notification.key) {
			return {
				...notification,
				read: true,
			};
		}

		return n;
	});

	localStorage.setItem(
		storageKey,
		JSON.stringify(
			notificationsWithReadState.map((notification) => {
				return {
					key: notification.key,
					date: notification.date,
					read: notification.read,
				};
			}),
		),
	);
}

function readAllNotifications() {
	allNotifications.forEach(readNotification);
}

const hasUnreadNotifications = $derived(
	notificationsWithReadState.some((notification) => !notification.read),
);

function handleStorageChange() {
	checkNotificationsFromStorage();
}

function checkNotificationsFromStorage() {
	if (disabled) {
		return;
	}

	const storedNotifications = JSON.parse(localStorage.getItem(storageKey) ?? '[]');

	notificationsWithReadState = allNotifications.map((n) => {
		const item = storedNotifications.find(
			(item: any) => item.key === n.key && item.date === n.date,
		);

		if (item) {
			return {
				...n,
				read: item.read,
			};
		}

		return n;
	});
}

/**
 * Must ignore first check because svelte assigns the pathname to / first,
 * then it changes to the actual page
 **/
let isFirstPageCheck = $state(true);
const toastInitWaitTime = 500;

$effect.root(() => {
	$effect.pre(() => {
		untrack(() => {
			checkNotificationsFromStorage();
		});
	});

	$effect(() => {
		untrack(() => {
			window.addEventListener('storage', handleStorageChange);
		});
	});

	$effect(function showToastOnPageView() {
		if (disabled) {
			return;
		}

		const pathname = page.url.pathname;

		if (isFirstPageCheck) {
			isFirstPageCheck = false;
			return;
		}

		untrack(() => {
			setTimeout(() => {
				const notificationsToShow = notifications.all
					.filter((notifications) => !notifications.read)
					.filter(
						(notification) =>
							notification.asToastOnPages === 'all' ||
							notification.asToastOnPages?.includes(pathname),
					)
					.reverse();

				notificationsToShow.forEach((notification) => {
					toast.custom(ToastCard, {
						id: notification.key,
						componentProps: {
							title: notification.title,
							description: notification.description,
							cancel: notification.link
								? {
										label: 'Got it',
										onClick() {
											notifications.readNotification(notification);
										},
									}
								: undefined,
							action: notification.link
								? {
										label: notification.link.name,
										onClick() {
											if (notification.link) {
												goto(notification.link.url);
											}

											notifications.readNotification(notification);
										},
									}
								: {
										label: 'Got it',
										onClick() {
											notifications.readNotification(notification);
										},
									},
						},
						duration: Number.POSITIVE_INFINITY,
						onDismiss() {
							notifications.readNotification(notification);
						},
					});
				});
			}, toastInitWaitTime);
		});
	});
});

export const notifications = {
	get all() {
		return notificationsWithReadState;
	},
	get hasUnreadNotifications() {
		return hasUnreadNotifications;
	},
	set disabled(newDisabled: boolean) {
		disabled = newDisabled;
	},
	readNotification,
	readAllNotifications,
};
