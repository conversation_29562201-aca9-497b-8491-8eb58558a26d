import type PlaylightSDK from 'https://sdk.playlight.dev/playlight-sdk.es.js';

let _playlightSDK = $state<PlaylightSDK>();
let _playlightGames = $state<PlaylightGame[]>([]);
let _playlightGamesLoadState = $state<'idle' | 'loading' | 'error' | 'success'>('idle');

let resolve: () => void;
let reject: () => void;

const _promise = $state<Promise<void>>(
	new Promise((_resolve, _reject) => {
		resolve = _resolve;
		reject = _reject;
	}),
);

function downloadAndInit() {
	import('https://sdk.playlight.dev/playlight-sdk.es.js')
		.then((module: any) => {
			_playlightSDK = module.default as PlaylightSDK;

			_playlightSDK.init({
				exitIntent: {
					enabled: false,
				},
				sidebar: {
					enableBeta: false,
					// forceVisible: true,
				},
			});
		})
		.then(resolve)
		.catch(reject);
}

interface PlaylightGame {
	boost_factor: number;
	category: string;
	cover_image_url: string;
	cover_video_url: string;
	created_at: string;
	description: string;
	domain: string;
	id: number;
	likes: number;
	logo_url: string;
	name: string;
	ranking_score: number;
}

interface FetchCategoryReponse {
	category: string;
	page: number;
}

async function fetchCategoryList(): Promise<string[]> {
	return fetch('https://api.playlight.dev/platform/categories').then((res) => res.json());
}

function fetchCategory({
	category,
	page,
}: FetchCategoryReponse): Promise<{ games: PlaylightGame[]; pageSize: number }> {
	return fetch(
		`https://api.playlight.dev/platform/suggestions/${category}?without=localhost&page=${page}`,
	).then((res) => res.json());
}

async function loadAllGames() {
	_playlightGamesLoadState = 'loading';

	try {
		const categories = await fetchCategoryList();
		const allGames: PlaylightGame[] = [];

		// Fetch all pages for each category
		for (const category of categories) {
			let page = 1;
			let hasMorePages = true;

			while (hasMorePages) {
				const categoryData = await fetchCategory({ category, page });

				if (categoryData.games.length === 0) {
					hasMorePages = false;
				} else {
					allGames.push(...categoryData.games);
					page++;

					// If we got fewer games than the page size, we've reached the end
					if (categoryData.games.length < categoryData.pageSize) {
						hasMorePages = false;
					}
				}
			}
		}

		// Filter out duplicates based on game ID
		const uniqueGames = allGames.filter(
			(game, index, array) => array.findIndex((g) => g.id === game.id) === index,
		);

		// Convert PlaylightGame to GameInfo format and store
		_playlightGames = uniqueGames;

		_playlightGamesLoadState = 'success';
	} catch {
		_playlightGamesLoadState = 'error';
	}
}

function registerGameClick(gameId: number) {
	return fetch('https://api.playlight.dev/platform/event/click', {
		method: 'POST',
		body: JSON.stringify({
			gameId,
			sourceDomain: import.meta.env.PROD ? 'lofiandgames.com' : 'localhost',
		}),
	});
}

async function waitUntilReady() {
	return _promise;
}

export const Playlight = {
	get sdk() {
		return _playlightSDK;
	},
	get allGames() {
		return _playlightGames;
	},
	get allGamesLoadState() {
		return _playlightGamesLoadState;
	},
	registerGameClick,
	loadAllGames,
	downloadAndInit,
	waitUntilReady,
};
