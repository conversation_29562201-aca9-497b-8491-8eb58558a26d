import { browser } from '$app/environment';

export const themes = ['light', 'dark', 'light-classic', 'dark-classic'] as const;

export type Theme = (typeof themes)[number];

export type ThemeBrightness = 'light' | 'dark';

function getBrightness(theme: Theme) {
	return ['dark', 'dark-classic'].includes(theme) ? 'dark' : 'light';
}

let _theme = $state<Theme>('light');
let _isColorblind = $state(false);
let _loaded = $state(false);
const _themeColor = $derived.by(() => {
	if (_theme === 'light' || _theme === 'light-classic') {
		return '#E5E6E6';
	} else if (_theme === 'dark' || _theme === 'dark-classic') {
		return '#16191E';
	}
});

export const theme = {
	get loaded() {
		return _loaded;
	},
	get isColorBlind() {
		return _isColorblind;
	},
	get themeColor() {
		return _themeColor;
	},
	set isColorBlind(isColorBlind: boolean) {
		_isColorblind = isColorBlind;

		const root = document.querySelector(':root')!;

		root.setAttribute('data-color-blind', `${isColorBlind}`);
		localStorage.setItem('color-blind', `${isColorBlind}`);
	},
	get brightness() {
		return getBrightness(this.value);
	},
	get value() {
		return _theme;
	},
	set value(newTheme: Theme) {
		_theme = newTheme;

		const root = document.querySelector(':root')!;

		root.setAttribute('data-theme', newTheme);
		localStorage.setItem('theme', newTheme);
	},
};

if (browser) {
	const observer = new MutationObserver(function (mutations) {
		mutations.forEach(function (mutation) {
			if (mutation.type === 'attributes') {
				if (mutation.attributeName === 'data-theme') {
					_theme = (mutation.target as HTMLElement).getAttribute('data-theme') as Theme;
				} else if (mutation.attributeName === 'data-color-blind') {
					_isColorblind =
						(mutation.target as HTMLElement).getAttribute('data-color-blind') ===
						'true';
				}
			}
		});
	});
	observer.observe(document.querySelector(':root')!, {
		attributes: true,
	});

	const storedTheme = localStorage.getItem('theme') as Theme | null;
	const storedColorBlind = localStorage.getItem('color-blind');
	const matchesDarkTheme =
		window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
	const fallbackTheme: Theme = matchesDarkTheme ? 'dark' : 'light';
	let finalTheme = storedTheme ?? fallbackTheme;
	const finalIsColorBlind = storedColorBlind === 'true';

	if (!themes.includes(finalTheme)) {
		finalTheme = fallbackTheme;
	}

	_theme = finalTheme;
	_isColorblind = finalIsColorBlind;

	document.querySelector(':root')!.setAttribute('data-theme', finalTheme);
	document.querySelector(':root')!.setAttribute('data-color-blind', `${finalIsColorBlind}`);

	_loaded = true;
}
