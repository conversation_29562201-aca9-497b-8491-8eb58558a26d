import type { Size } from '$lib/models/Size';
import { MediaQuery } from '$lib/util/MediaQuery.svelte';

export type AdScreenSize = 'desktop' | 'tablet' | 'mobile';

const navbarHeight = 64;

export class Ads {
	#desktopMediaQuery = new MediaQuery('min-width: 1280px');
	#tabletMediaQuery = new MediaQuery('min-width: 640px');
	#mobileMediaQuery = new MediaQuery('max-width: 640px');

	canShowGameAds = $state(true);

	isReady = $derived.by(() => {
		return (
			this.#desktopMediaQuery.current ||
			this.#tabletMediaQuery.current ||
			this.#mobileMediaQuery.current
		);
	});

	variant: AdScreenSize | null = $derived.by(() => {
		if (!this.canShowGameAds) {
			return null;
		}

		if (this.#desktopMediaQuery.current) {
			return 'desktop';
		} else if (this.#tabletMediaQuery.current) {
			return 'tablet';
		} else if (this.#mobileMediaQuery.current) {
			return 'mobile';
		}
		return null;
	});

	#size: Size | null = $derived.by(() => {
		if (this.variant === 'desktop') {
			return Ads.desktopGameAdSize;
		}
		if (this.variant === 'tablet') {
			return Ads.tabletGameAdSize;
		}
		if (this.variant === 'mobile') {
			return Ads.mobileGameAdSize;
		}

		return null;
	});

	#sizeWithPadding: Size | null = $derived.by(() => {
		if (this.#size === null) {
			return null;
		}

		return {
			width: this.#size.width + this.padding * 2,
			height: this.#size.height + this.padding * 2,
		};
	});

	/**
	 * Returns the effective ad size for the current variant.
	 * When it's on mobile, width is 0 and height is the ad height.
	 * When it's on desktop, width is the ad width and height is 0.
	 */
	effectiveSize: Size = $derived.by(() => {
		if (this.#sizeWithPadding === null) {
			return {
				width: 0,
				height: 0,
			};
		}

		if (this.variant === 'desktop') {
			return {
				width: this.#sizeWithPadding.width,
				height: 0,
			};
		}

		return {
			width: 0,
			height: this.#sizeWithPadding.height,
		};
	});

	effectiveSizeWithNavbar: Size | null = $derived.by(() => {
		if (this.isHorizontal) {
			if (this.effectiveSize) {
				return {
					width: this.effectiveSize.width,
					height: this.effectiveSize.height + navbarHeight - this.padding,
				};
			}

			return {
				width: 0,
				height: navbarHeight - this.padding,
			};
		}

		return {
			width: this.effectiveSize.width,
			height: navbarHeight,
		};
	});

	get isVertical() {
		return ['desktop'].includes(this.variant!);
	}

	get isHorizontal() {
		return ['mobile', 'tablet'].includes(this.variant!);
	}

	readonly padding = 16;

	static readonly desktopGameAdSize: Size = {
		width: 256,
		height: 500,
	};

	static readonly tabletGameAdSize: Size = {
		width: 566,
		height: 90,
	};

	static readonly mobileGameAdSize: Size = {
		width: 300,
		height: 50,
	};
}

export const ads = new Ads();
