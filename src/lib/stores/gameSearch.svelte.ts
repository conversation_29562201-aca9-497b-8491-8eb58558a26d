import type { Component } from 'svelte';
import { favoriteGames, games } from './games.svelte';
import { Playlight } from './playlightSdk.svelte';

export interface SearchableGame {
	id: number;
	name: string;
	cover?: Component;
	url: string;
	key: string;
	imgUrl?: string;
	type: 'local' | 'playlight';
}

class GameSearch {
	isOpen = $state(false);
	search = $state('');
	localGames: SearchableGame[] = $derived.by(() => {
		return games
			.filter((game) => {
				return game.name.toLocaleLowerCase().includes(this.search.toLocaleLowerCase());
			})
			.sort((a, b) => {
				const aIsFavorite = favoriteGames.isFavorite(a.key);
				const bIsFavorite = favoriteGames.isFavorite(b.key);

				if (aIsFavorite && !bIsFavorite) {
					return -1;
				}

				if (!aIsFavorite && bIsFavorite) {
					return 1;
				}

				return a.name.localeCompare(b.name);
			})
			.map((game) => {
				return {
					...game,
					id: 0,
					type: 'local',
				};
			});
	});
	playlightGames: SearchableGame[] = $derived.by(() => {
		return Playlight.allGames
			.filter((game) => {
				return game.name.toLocaleLowerCase().includes(this.search.toLocaleLowerCase());
			})
			.sort((a, b) => a.name.localeCompare(b.name))
			.map((game) => {
				return {
					id: game.id,
					key: `playlight-${game.id}`,
					name: game.name,
					url: `https://${game.domain}?utm_source=playlight`,
					imgUrl: game.logo_url,
					type: 'playlight',
				};
			});
	});
	games = $derived.by(() => {
		return [...this.localGames, ...this.playlightGames];
	});
}

export const gameSearch = new GameSearch();
