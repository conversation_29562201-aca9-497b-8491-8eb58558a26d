-- DROP FUNCTION random_star_battle(INTEGER,INTEGER)

CREATE OR REPLACE FUNCTION random_star_battle(board_size_param INTEGER, stars_param INTEGER)
RETURNS SETOF star_battle_winnable_games AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM star_battle_winnable_games AS s
  WHERE
    s.board_size = board_size_param::smallint
  AND s.stars = stars_param::smallint
  ORDER BY random()
  LIMIT 2;
END;
$$ LANGUAGE plpgsql;

-- SELECT * FROM random_star_battle(6,1)