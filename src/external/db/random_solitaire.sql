-- DROP FUNCTION IF EXISTS random_solitaire(INTEGER);

CREATE OR REPLACE FUNCTION random_solitaire(draw_param INTEGER)
RETURNS TABLE (game TEXT, draw SMALLINT) AS $$
BEGIN
    RETURN QUERY
    SELECT s.game, s.draw
    FROM solitaire_winnable_games AS s
    WHERE s.draw = CAST(draw_param as SMALLINT)
    ORDER BY RANDOM()
    LIMIT 2; -- Get a random game and the next one
END;
$$ LANGUAGE plpgsql;

-- SELECT * FROM random_solitaire(3);
