/// <reference types="@sveltejs/kit" />

// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
// and what to do when importing types
declare namespace App {
	// interface Locals {}
	// interface Platform {}
	// interface Session {}
	// interface Stuff {}
}

declare module 'https://sdk.playlight.dev/playlight-sdk.es.js' {
	interface PlaylightConfig {
		exitIntent?: { enabled?: boolean; immediate?: boolean };
		sidebar?: {
			enableBeta?: boolean;
			hasFrameworkRoot?: 'auto' | boolean;
			forceVisible?: boolean;
		};
	}

	interface PlaylightSDK {
		init: (props: PlaylightConfig) => void;
		setConfig: (config: PlaylightConfig) => void;
		setDiscovery: (visible?: boolean) => void;
	}

	export default PlaylightSDK;
}
