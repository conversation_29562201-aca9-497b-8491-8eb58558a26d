# Lofi and Games

## Developing

Once you've created a project and installed dependencies with `npm install` (or `pnpm install` or `yarn`), start a development server:

```bash
npm run dev

# or start the server and open the app in a new browser tab
npm run dev -- --open
```

## Building

To create a production version of your app:

```bash
npm run build
```

You can preview the production build with `npm run preview`.

> To deploy your app, you may need to install an [adapter](https://kit.svelte.dev/docs/adapters) for your target environment.

## Convert songs to small mp3

First, install FFMPEG

```bash
brew install ffmpeg
```

### Single song

```bash
ffmpeg -i input.wav -map_metadata -1 -y -vn -ar 44100 -ac 2 -b:a 192k output.mp3
```

### Multiple songs

```bash
mkdir output

find . -type f -iname "*.wav" -d 1 -exec bash -c 'FILE="$1"; ffmpeg -i "${FILE}" -map_metadata -1 -y -vn -ar 44100 -ac 2 -b:a 192k "output/${FILE%.*}.mp3";' _ '{}' \;
```

## Resizing album images

First, install Image Magick

```bash
brew install imagemagick
```

### Resize and convert all cover images on a folder

```bash
mkdir output
magick mogrify -resize 56x56 -format jpg -path ./output *.*
```

### Convert all images to jpg on a folder

```bash
mkdir output
magick mogrify -format jpg -path ./output *.*
```

## Wallpapers

### Resize and convert all wallpaper images on a folder

```bash
mkdir output
magick mogrify -resize 1366x768 -format jpg -path ./output *.*
```

## Wallpapers

### Upscale wallpapers

[Replicate](https://replicate.com/jingyunliang/swinir)

[BigJPG](https://bigjpg.com/)

## S3 file upload

When uploading files to S3, add Cache-Control with value max-age=31536000
