{"editor.wordWrap": "bounded", "editor.wordWrapColumn": 80, "editor.tabSize": 2, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "diffEditor.ignoreTrimWhitespace": false, "svelte.enable-ts-plugin": true, "[svelte]": {"editor.defaultFormatter": "svelte.svelte-vscode"}, "git.confirmSync": false, "eslint.validate": ["javascript", "javascriptreact", "svelte"], "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[jsonc]": {"editor.defaultFormatter": "vscode.json-language-features"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}