{"name": "games", "version": "0.0.1", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "postinstall": "svelte-kit sync", "package": "svelte-kit package", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --check . && eslint .", "format": "prettier --write . && eslint --fix ."}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.39.1", "@sveltejs/kit": "2.46.5", "@tailwindcss/typography": "^0.5.19", "@types/howler": "^2.2.12", "@types/lodash": "^4.17.20", "@typescript-eslint/eslint-plugin": "^8.46.3", "@typescript-eslint/parser": "^8.46.3", "eslint": "^9.39.1", "eslint-config-prettier": "^9.1.2", "eslint-plugin-svelte": "^3.13.0", "globals": "^16.5.0", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "prettier-plugin-tailwindcss": "^0.6.14", "svelte": "5.39.11", "svelte-check": "^4.3.3", "svelte-eslint-parser": "^0.43.0", "svelte-preprocess": "^6.0.3", "tslib": "^2.8.1", "typescript": "^5.9.3", "vite": "^7.2.2"}, "type": "module", "dependencies": {"@babel/preset-typescript": "^7.28.5", "@better-auth/stripe": "^1.3.34", "@emailjs/browser": "^4.4.1", "@number-flow/svelte": "^0.2.3", "@sentry/sveltekit": "^9.46.0", "@supabase/supabase-js": "^2.80.0", "@sveltejs/adapter-static": "^3.0.10", "@sveltejs/vite-plugin-svelte": "^5.1.1", "@sveltejs/vite-plugin-svelte-inspector": "^5.0.1", "@tailwindcss/vite": "^4.1.17", "@thi.ng/random": "^4.1.32", "@tweenjs/tween.js": "^25.0.0", "@types/cloudflare-turnstile": "^0.2.2", "@types/youtube": "^0.1.2", "astar-typescript": "^1.2.7", "autoprefixer": "^10.4.21", "better-auth": "^1.3.34", "clsx": "^2.1.1", "daisyui": "^5.4.7", "dexie": "^4.2.1", "dotenv": "^16.6.1", "embla-carousel": "^8.6.0", "embla-carousel-svelte": "^8.6.0", "embla-carousel-wheel-gestures": "^8.1.0", "excalibur": "^0.30.3", "howler": "^2.2.4", "js-confetti": "^0.12.0", "konva": "^9.3.22", "lodash": "^4.17.21", "minimaxer": "^3.4.0", "motion": "12.15.0", "pixi.js": "^8.14.0", "stripe": "^18.5.0", "svelte-copy": "^2.0.0", "svelte-konva": "1.0.0-next.5", "svelte-meta-tags": "^4.5.0", "svelte-portal": "^2.2.1", "svelte-share-buttons-component": "^3.0.0", "svelte-sonner": "^0.3.28", "tailwind-merge": "^3.4.0", "tailwindcss": "^4.1.17"}}